package com.jlr.ecp.subscription.api.pivi.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPRenewalDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - AMAP相关服务")
public interface AmaPServiceApi {
    String PREFIX = ApiConstants.PREFIX + "/amap";

    @GetMapping(PREFIX+"/getAmaPBatchRenewalIdList")
    @Operation(summary = "查询amap待批量续费的id列表")
    CommonResult<List<List<Long>>> getAmaPBatchRenewalIdList();

    @PostMapping(PREFIX+"/batchSendAmaPRenewal")
    @Operation(summary = "查询amap待批量续费的id列表")
    CommonResult<Integer> batchSendAmaPRenewal(@RequestBody AmaPRenewalDTO amaPRenewalDTO);

    @GetMapping(PREFIX+"/getAmaPChargeProgressIdList")
    @Operation(summary = "获取批量续费高德的id列表")
    CommonResult<List<Long>> getAmaPChargeProcessIdList();

    @PostMapping(PREFIX+"/getAmaPChargeProgressIdList")
    @Operation(summary = "获取批量续费高德的id列表")
    CommonResult<Integer> batchQueryAmaPChargeResult(@RequestBody List<Long> amaPRenewalIdList);

    @GetMapping(PREFIX+"/getAmaPQueryIdList")
    @Operation(summary = "获取amap批量查询的为查询中的id列表")
    CommonResult<List<Long>> getAmaPQueryProgressIdList();


    @PostMapping(PREFIX+"/batchQueryAmaPProgress")
    @Operation(summary = "批量查询amap的带查询数据")
    CommonResult<Integer> batchQueryAmaPProgress(@RequestBody List<Long> amaPQueryIdList);
}
