package com.jlr.ecp.subscription.api.vcsorderfufilment.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarVinAndServiceTypeDTO implements Serializable {
    /**
     * 车辆VIN;车辆VIN
     */
    private String carVin;

    /**
     *  服务履约类型：1：远程车控Remote Service； 2：PIVI Subscription Service；
     * */
    private Integer serviceType;

}
