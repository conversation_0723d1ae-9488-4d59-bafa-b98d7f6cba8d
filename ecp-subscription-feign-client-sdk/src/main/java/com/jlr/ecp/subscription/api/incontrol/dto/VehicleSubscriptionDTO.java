package com.jlr.ecp.subscription.api.incontrol.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "车辆服务到期信息")
@Data
public class VehicleSubscriptionDTO {
    /**
     *  jilId
     * */
    private String jlrId;

    /**
     *  icrId
     * */
    private String incontrolId;

    /**
     *  车辆编码
     * */
    private String carVin;

    /**
     *  服务类型
     * */
    private Integer serviceType;

    /**
     *  品牌code
     * */
    private String brandCode;

    /**
     * 到期时间
     * */
    private LocalDateTime expiryDate;
}

