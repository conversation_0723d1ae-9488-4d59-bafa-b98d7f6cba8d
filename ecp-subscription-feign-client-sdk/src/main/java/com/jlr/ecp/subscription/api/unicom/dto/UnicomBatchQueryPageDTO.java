package com.jlr.ecp.subscription.api.unicom.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Schema(description = "联通批量查询分页入参")
public class UnicomBatchQueryPageDTO extends PageParam {

    @Schema(description = "操作时间排序, 正序:asc, 倒叙:desc")
    @NotEmpty
    private String operateTimeSort;

    @Schema(description = "查询编号")
    private List<Long> batchNoList;

    @Schema(description = "查询状态")
    private String queryStatus;

    @Schema(description = "VIN")
    private String carVin;

    @Schema(description = "操作人员")
    private String operator;

    @Schema(description = "操作开始时间")
    private String startTime;

    @Schema(description = "操作结束时间")
    private String endTime;

}
