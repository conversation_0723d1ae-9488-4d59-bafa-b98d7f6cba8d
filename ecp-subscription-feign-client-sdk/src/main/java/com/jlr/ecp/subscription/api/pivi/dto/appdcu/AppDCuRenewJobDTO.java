package com.jlr.ecp.subscription.api.pivi.dto.appdcu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AppDCuRenewJobDTO {
    /**
     *  AppDCuRenewRecords的主键Id
     * */
    private List<Long> appDCuIdList;

    /**
     *  appDCu的续费状态
     * */
    private Integer renewStatus;

    /**
     *  结束标记
     * */
    private Boolean endFlag;
}
