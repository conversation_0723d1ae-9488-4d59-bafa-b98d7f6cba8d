package com.jlr.ecp.subscription.api.pivi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPExpireDateResponseDTO {
    /**
     *  状态吗
     * */
    private String code;

    /**
     * 错误描述
     * */
    private String errDetail;

    /**
     *  vin码
     * */
    private String carVin;

    /**
     *  amap到期时间
     * */
    private LocalDateTime amaPExpireDate;
}
