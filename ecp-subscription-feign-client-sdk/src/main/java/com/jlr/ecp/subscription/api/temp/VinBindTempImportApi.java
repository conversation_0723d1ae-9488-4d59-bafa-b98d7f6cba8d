package com.jlr.ecp.subscription.api.temp;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.temp.dto.ImportProcessResultDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - vin绑定从临时表导入人车关系API")
public interface VinBindTempImportApi {

    String PREFIX = ApiConstants.PREFIX + "/vinBindTempImport";

    @GetMapping(PREFIX + "/getIdListByStatusInVehicle")
    @Operation(summary = "获取有车辆的临时导入表ID")
    CommonResult<List<Long>> getIdListByStatusInVehicle(@RequestParam("status") Integer status);

    @GetMapping(PREFIX + "/getIdListByStatusNonVehicle")
    @Operation(summary = "获取没有车辆的临时导入表ID")
    CommonResult<List<Long>> getIdListByStatusNonVehicle(@RequestParam("status") Integer status);

    @PostMapping(PREFIX + "/processInVehicleByIdList")
    @Operation(summary = "处理vin在车辆表中的数据")
    CommonResult<List<ImportProcessResultDTO>> processInVehicleByIdList(@RequestBody List<Long> idList);

    @PostMapping("/processNonVehicleByIdList")
    @Operation(summary = "处理vin不在车辆表中的数据")
    CommonResult<List<ImportProcessResultDTO>> processNonVehicleByIdList(@RequestBody List<Long> idList);

}
