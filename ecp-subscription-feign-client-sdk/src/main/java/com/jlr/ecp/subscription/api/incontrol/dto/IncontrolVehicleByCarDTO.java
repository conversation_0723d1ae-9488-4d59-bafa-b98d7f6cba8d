package com.jlr.ecp.subscription.api.incontrol.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class IncontrolVehicleByCarDTO {
    /**
     * 车辆VIN;车辆VIN
     */
    @Schema(description = "车辆VIN", required = true, example = "VIN123456789")
    private String carVin;

    /**
     * incontrol账号;incontrol账号
     */
    @Schema(description = "incontrol账号", required = true, example = "incontrol123")
    private String incontrolId;

    /**
     * 用户编码;用户编码
     */
    @Schema(description = "用户编码", required = true, example = "consumer123")
    private String consumerCode;

    /**
     * 车辆型号;车辆型号
     */
    @Schema(description = "车辆型号", required = true, example = "series123")
    private String seriesCode;

    /**
     * 车辆名称
     */
    @Schema(description = "车辆名称", example = "Series Name")
    private String seriesName;

    /**
     * 品牌CODE
     */
    @Schema(description = "品牌CODE", example = "brand123")
    private String brandCode;

    /**
     * 品牌名
     */
    @Schema(description = "品牌名", example = "Brand Name")
    private String brandName;

    /**
     * 配置code
     */
    @Schema(description = "配置code", example = "config123")
    private String configCode;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "Config Name")
    private String configName;

    /**
     * 车辆年款;车辆年款
     */
    @Schema(description = "车辆年款", required = true, example = "2021")
    private String modelYear;

    /**
     * 车机型号;车机型号，PIVI，通过计算获得
     */
    @Schema(description = "车机型号，PIVI，通过计算获得", example = "PIVI Pro")
    private String carSystemModel;

    /**
     * House of Brand 英文描述
     */
    @Schema(description = "House of Brand 英文描述", example = "House of Brand EN")
    private String hobEn;

    /**
     * 产品类型EN
     */
    @Schema(description = "产品类型EN", example = "Product Type EN")
    private String productionEn;

    /**
     * 绑定时间;绑定时间
     */
    @Schema(description = "绑定时间", required = true, example = "2021-01-01T12:00:00")
    private LocalDateTime bindTime;
}
