package com.jlr.ecp.subscription.api.subscripiton.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FulfilmentPIVICompensationDTO implements Serializable {

    /**
     * 页编号
     * */
    private Integer pageNo;

    /**
     * 每页数量
     * */
    private Integer pageSize;

}
