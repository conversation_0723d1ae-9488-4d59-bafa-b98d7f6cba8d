package com.jlr.ecp.subscription.api.manual;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.manual.dto.CheckRecordsInTransitDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.security.PermitAll;
import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 手动续费相关服务")
public interface ManualRenewServiceApi {
    String PREFIX = ApiConstants.PREFIX + "/manualRenew";

    @PostMapping(PREFIX + "/checkRecordsInTransit")
    @Operation(summary = "校验是否存在在途手动续费订单")
    @PermitAll
    CommonResult<Boolean> checkRecordsInTransit(@RequestBody List<CheckRecordsInTransitDTO> dtoList);
}
