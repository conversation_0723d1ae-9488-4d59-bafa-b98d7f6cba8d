package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;


//import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandProductInfoVO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 以一张vcs单子为维度进行拆分
 *
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 我的订阅 不同品牌下 订单的 Response VO，其中订单列表以不同car_vin为一张卡片")
@Data
public class OrderSubscriptionRespVO {
    /**
     * 1.车型信息 seriesCode seriesName carVin
     */
    @Schema(description = "车型信息 seriesCode seriesName carVin")
    private SubVehicleInfoVO vehicleInfo;

    /**
     * 2.商品信息 productItemInfo 只包括 最新一条的商品信息
     */
    @Schema(description = "商品信息 productItemInfo 只包括 最新一条的商品信息")
    private SubProductItemInfoVO productInfo;

    /**
     * 3.我的订阅 这辆车（现在一辆车只有一类商品）这一类商品的 历史订阅时间段 list（不包括当前正在订阅的时间段）
     */
    @Schema(description = "我的订阅 这辆车（现在一辆车只有一类商品）这一类商品的 历史订阅时间段 list（不包括当前正在订阅的时间段）")
    private List<SubDurationHistoryVO> durationHistoryList;

}
