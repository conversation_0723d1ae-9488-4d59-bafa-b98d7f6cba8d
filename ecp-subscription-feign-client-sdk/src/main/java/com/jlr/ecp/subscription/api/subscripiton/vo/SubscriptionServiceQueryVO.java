package com.jlr.ecp.subscription.api.subscripiton.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/***
 * <AUTHOR>
 */
@Data
@Schema(description = "API - 查询DTO ")
public class SubscriptionServiceQueryVO implements Serializable {

    /**
     * 用户incontrol账号
     */
    private String incontrolId;

    /**
     * 车架号
     */
    private String carVin;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime expiryDate;

    /**
     * 高德过期时间
     */
    @Schema(description = "高德过期时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime aMapExpiryDate;
}
