package com.jlr.ecp.subscription.api.bau.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class HandleFileResultVO implements Serializable {

    @Schema(description = "解析VIN总数")
    private Integer total =0;

    @Schema(description = "VIN过滤去重之后")
    private Integer canInsert = 0;

    @Schema(description = "需要入库的VIN数量")
    private Integer insert = 0;


    @Schema(description = "入库失败的VIN数量")
    private Integer insertFail = 0;

    @Schema(description = "错误信息描述")
    private String errorDesc;

}
