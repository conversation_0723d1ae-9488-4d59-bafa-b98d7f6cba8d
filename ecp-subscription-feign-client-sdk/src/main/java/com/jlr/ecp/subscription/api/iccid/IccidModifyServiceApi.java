package com.jlr.ecp.subscription.api.iccid;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.iccid.dto.IccidModifyBatchDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - Iccid相关服务")
public interface IccidModifyServiceApi {

    String PREFIX = ApiConstants.PREFIX + "/iccid";

    @GetMapping(PREFIX + "/getModifyRecordIds")
    @Operation(summary = "查询待处理的ICCID修改记录ID列表")
    CommonResult<List<Long>> getModifyRecordIds(@RequestParam("batchNo") Long batchNo);

    @PostMapping(PREFIX + "/processModifyRecords")
    @Operation(summary = "批量处理ICCID修改记录")
    CommonResult<Integer> processModifyRecords(@RequestBody IccidModifyBatchDTO iccidModifyBatchDTO);
}