package com.jlr.ecp.subscription.api.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class RemoteRenewJobDTO {
    /**
     *  RemoteRenewDetailRecords的主键Id
     * */
    private List<Long> remoteIdList;

    /**
     *  结束标记
     * */
    private Boolean endFlag;
}
