package com.jlr.ecp.subscription.api.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 -  车型年款创建DTO ")
@ToString(callSuper = true)
public class VehicleModelMasterDataCreateDTO {
    /**
     * 车机：PIVI
     */
    @Schema(description = "车机", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车机不能为空")
    private String carSystemModel;

    /**
     * 车型编码
     */
    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型编码不能为空")
    @Length(max = 30, message = "车型编码不能超过30个字符")
    private String seriesCode;

    /**
     * 型号年款
     */
    @Schema(description = "年限组合", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "年限组合不能为空")
    private List<ModelYearDTO> modelYearList;


}
