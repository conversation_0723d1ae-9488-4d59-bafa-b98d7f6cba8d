package com.jlr.ecp.subscription.enums.fulfilment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 表示服务类型的枚举类。
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ServiceTypeEnum {

    /**
     * 远程车控Remote Service
     */
    REMOTE(1, "远程车控Remote Service"),

    /**
     * PIVI Subscription Service
     */
    PIVI(2, "PIVI Subscription Service");



    private final Integer code;
    private final String description;

}
