package com.jlr.ecp.subscription.api.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序 -  DP返回结果 ")
@ToString(callSuper = true)
public class DPResultVO implements Serializable {

    @Schema(description = "车架号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String vin;

    @Schema(description = "品牌编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brandCode;

    @Schema(description = "品牌", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brandName;

    @Schema(description = "车款年限", requiredMode = Schema.RequiredMode.REQUIRED)
    private String modelYear;

    @Schema(description = "配置编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configCode;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configName;

    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seriesCode;

    @Schema(description = "车型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seriesName;

    @Schema(description = "产品名称EN", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productionEn;

    @Schema(description = "House of Brand", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hobEn;

}
