package com.jlr.ecp.subscription.api.subscripiton.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ServiceExpireStatisticVo {
    /**
     * 总数
     */
    private Integer total;

    /**
     * 没有车辆数据集合
     */
    private Integer noVehicleCount;

    /**
     * 黑名单过滤的数据
     */
    private Integer blackFilterCount;

    /**
     * 联通实名认证过滤的数据
     */
    private Integer unicomFilterCount;

    /**
     * 高德到期时间小于ecp到期时间过滤数据
     */
    private Integer ecpFilterCount;

    /**
     *  幂等过滤的数量
     * */
    private Integer idempotentFilterCount;

    /**
     * 有效数据集合
     */
    private List<ServiceExpireVo> validDataList;


    /**
     *  查询结束的标志
     * */
    private Boolean endFlag;

}
