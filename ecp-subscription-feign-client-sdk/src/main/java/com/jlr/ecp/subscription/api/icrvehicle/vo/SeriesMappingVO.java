package com.jlr.ecp.subscription.api.icrvehicle.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "InControl 车辆信息响应")
@Builder
public class SeriesMappingVO implements Serializable {

    /**
     * 车辆型号编码
     */
    private String seriesCode;


    /**
     * 车辆型号名称
     */
    private String seriesName;

    
    /**
     * 品牌显示名
     */
    private String brandNameView;
}
