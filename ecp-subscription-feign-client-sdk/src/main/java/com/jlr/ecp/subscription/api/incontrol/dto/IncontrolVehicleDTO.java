package com.jlr.ecp.subscription.api.incontrol.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @since 2023-12-20 14:45:58
 */
@Data
@Schema(description = "管理后台 -  车机信息 ")
public class IncontrolVehicleDTO {

    /**
     * 主键
     */
    @Schema(description = "id", required = true, example = "快反")
	private Long id;
    /**
     * 用户车辆编码;用户车辆编码，ECP自定义编码
     */
    @Schema(description = "用户车辆编码", required = true, example = "快反")
    private String carNo;
    /**
     * 用户编码;用户编码
     */
    @Schema(description = "用户编码", required = true, example = "快反")
    private String consumerCode;
    /**
     * incontrol账号;incontrol账号
     */
    @Schema(description = "incontrol账号", required = true, example = "快反")
    private String incontrolId;
    /**
     * 车辆型号;车辆型号
     */
    @Schema(description = "车辆型号", required = true, example = "快反")
    private String seriesCode;

    /**
     * 车辆型号名称;车辆型号名称
     */
    @Schema(description = "车辆型号名称", required = true, example = "快反")
    private String seriesName;
    /**
     * 车辆VIN;车辆VIN
     */
    @Schema(description = "车辆VIN", required = true, example = "快反")
    private String carVin;
    /**
     * 车辆年款;车辆年款
     */
    @Schema(description = "车辆年款", required = true, example = "快反")
    private String modelYear;
    /**
     * 车机型号;车机型号，PIVI，通过计算获得
     */
    @Schema(description = "车机型号，PIVI，通过计算获得", required = true, example = "快反")
    private String carSystemModel;
    /**
     * 绑定时间;绑定时间
     */
    @Schema(description = "绑定时间", required = true, example = "快反")
    @JsonFormat(pattern= "yyyy-MM-dd:HH:mm:ss")
    private LocalDateTime bindTime;

    @Schema(description = "品牌", required = true, example = "快反")
    private String brandCode;

    @Schema(description = "品牌", required = true, example = "快反")
    private String brandName;

    @Schema(description = "配置", required = true, example = "快反")
    private String configCode;

    @Schema(description = "配置", required = true, example = "快反")
    private String configName;
    /**
     * House of Brand 英文描述描述
     */
    @Schema(description = "英文描述描述", required = true, example = "快反")
    private String hobEn;
    /**
     * 产品类型EN
     */
    @Schema(description = "产品类型EN", required = true, example = "快反")
    private String productionEn;

    /**
     *  发票时间
     * */
    @Schema(description = "发票时间", required = true, example = "快反")
    private LocalDateTime dmsInvoiceDate;

}