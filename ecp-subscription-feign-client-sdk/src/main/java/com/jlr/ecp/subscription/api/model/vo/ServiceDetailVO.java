package com.jlr.ecp.subscription.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序 -  userCarList返回结果 ")
@ToString(callSuper = true)
public class ServiceDetailVO {

    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime expiryDate;

    /**
     * 小程序通知文案
     */
    @Schema(description = "小程序通知文案")
    private String notifyContent;

    /**
     * 弹窗交互按钮文案
     */
    @Schema(description = "弹窗交互按钮文案")
    private String buttonContent;

    /**
     * 服务名中文
     */
    @Schema(description = "服务名中文")
    private String serviceNameCn;

    /**
     * 能否选购
     */
    @Schema(description = "能否选购")
    private Boolean enable;
}
