package com.jlr.ecp.subscription.api.fallbackfactory;


import com.jlr.ecp.subscription.api.cdp.CdpAPI;
import com.jlr.ecp.subscription.api.fallback.CdpFeignClientFallBack;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-02-29
 */
@Component
public class CdpApiFeignClientFallbackFactory implements FallbackFactory<CdpAPI> {

    @Override
    public CdpAPI create(Throwable throwable) {
        CdpFeignClientFallBack couponAccountFallback = new CdpFeignClientFallBack();
        couponAccountFallback.setThrowable(throwable);
        return couponAccountFallback;
    }
}
