package com.jlr.ecp.subscription.api.bau.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class TransferFileVO implements Serializable {

    @Schema(description = "文件数量")
    private Integer fileNum;



    @Schema(description = "错误信息描述")
    private String errorDesc;

}
