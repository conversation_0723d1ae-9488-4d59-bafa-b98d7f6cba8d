package com.jlr.ecp.subscription.api.bau.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * cdp入参dto
 *
 * <AUTHOR>
 */
@Data
public class PreCheckListRequest {

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    private LocalDate endDate;

    @Schema(description = "数据处理状态, 0：待处理 2:处理失败")
    private Integer status;

    @Schema(description = "APPD刷数标识，1：是 0：否")
    private Integer appdJobFlag;
}
