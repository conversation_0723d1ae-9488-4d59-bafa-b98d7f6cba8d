package com.jlr.ecp.subscription.api.remotepackage.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - 批量上传服务包编号响应VO ")
public class RemotePackageListRespVO {
    /**
     * 车机：PIVI
     */
    @Schema(description = "车机", requiredMode = Schema.RequiredMode.REQUIRED)
    private String carSystemModel;

    /**
     * 服务包编码
     */
    @Schema(description = "服务包编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "CHND45A-E1H1R1T1_J")
    private String packageCode;

    /**
     * 最新添加时间
     */
    @Schema(description = "通过创建时间排序方式（asc：升序，desc：降序）")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 上传用户
     */
    @Schema(description = "上传用户", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createdUser;


}
