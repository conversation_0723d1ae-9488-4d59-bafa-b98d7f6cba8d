package com.jlr.ecp.subscription.api.subscripiton.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "管理后台 -  订阅服务保存DTO ")
public class SubscriptionServiceSaveDTO implements Serializable {

    @Schema(description = "单个订阅服务信息", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotEmpty(message = "subscriptionServices不能为空")
    private List<SubscriptionServiceBO> subscriptionServices;
}
