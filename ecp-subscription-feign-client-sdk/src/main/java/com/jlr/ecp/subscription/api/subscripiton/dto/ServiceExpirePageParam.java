package com.jlr.ecp.subscription.api.subscripiton.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceExpirePageParam implements Serializable {
    /**
     * 页编号
     * */
    private Integer pageNo;

    /**
     * 每页数量
     * */
    private Integer pageSize;

    /**
     * 过期天数
     * */
    private Integer expireDay;

    /**
     *  指定日期
     * */
    private LocalDate appointedDate;

    /**
     *  服务类型
     * */
    private Integer serviceType;
}
