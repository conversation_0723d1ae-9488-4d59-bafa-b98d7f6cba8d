package com.jlr.ecp.subscription.api.cdp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序 -  Cdp返回结果 ")
@ToString(callSuper = true)
public class CdpResultVO implements Serializable {

    @Schema(description = "车架号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String vin;


    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(description = "全部手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> allMobile;

}
