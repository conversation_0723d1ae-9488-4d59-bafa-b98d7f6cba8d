package com.jlr.ecp.subscription.api.subscripiton.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 商品详情
 * <AUTHOR>
 */
@Schema(description = "小程序 - 商品详情的 DTO")
@Data
@ToString(callSuper = true)
public class ProductInfoDTO {

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品包含的履约类型")
    private Set<Integer> childFulfilmentType;

    @Schema(description = "过期时间")
    // @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime expireDate;
}
