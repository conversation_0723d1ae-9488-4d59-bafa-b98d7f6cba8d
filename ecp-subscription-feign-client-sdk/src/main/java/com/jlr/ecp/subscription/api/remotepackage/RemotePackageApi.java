package com.jlr.ecp.subscription.api.remotepackage;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageCreateDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackagePageReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.BatchUploadRespVO;
import com.jlr.ecp.subscription.api.remotepackage.vo.RemotePackageListRespVO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - Remote服务包编号配置")
public interface RemotePackageApi {
    String PREFIX = "feign/v1/subscription/remote/package";

    /**
     * 创建服务包API接口
     *
     * @param createDTO 入参
     * @return CommonResult<String>
     */
    @PostMapping(PREFIX + "/create")
    @Operation(summary = "添加服务包")
    CommonResult<String> createRemotePackage(@Validated @RequestBody RemotePackageCreateDTO createDTO);

    /**
     * 服务包列表API
     *
     * @param dto
     * @return CommonResult<List < RemotePackageListRespVO>>
     */
    @GetMapping(PREFIX + "/page")
    @Operation(summary = "服务包列表查询接口")
    CommonResult<PageResult<RemotePackageListRespVO>> page(@SpringQueryMap @Validated RemotePackagePageReqDTO dto);

    /**
     * 删除服务包
     * @param packageCode 服务包编码
     * @return CommonResult<String>
     */
    @PostMapping(PREFIX + "/delete")
    @Operation(summary = "删除服务包")
    CommonResult<String> deleteByPackageCode(@RequestParam(value = "packageCode") String packageCode);

    /**
     * 批量上传服务包编号API接口
     *
     * @param file Excel文件
     * @return CommonResult<BatchUploadRespVO>
     */
    @PostMapping(PREFIX + "/batch/upload")
    @Operation(summary = "批量上传服务包编号")
    CommonResult<BatchUploadRespVO> batchUpload(@RequestParam("file") MultipartFile file, @NotBlank(message = "车机不能为空") @RequestParam("carSystemModel") String carSystemModel);

    /**
     * 下载服务包编号模板API接口
     *
     * @return CommonResult<String>
     */
    @GetMapping(PREFIX + "/download/template")
    @Operation(summary = "下载服务包编号模板")
    CommonResult<String> downloadTemplateUrl();


}
