package com.jlr.ecp.subscription.api.unicom.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 */
@Data
public class UnicomRnrBatchQueryListVO{


    /**
     *批次号;批量查询批次号
     */
    @Schema(description = "批次号")
    private Long batchNo;


    @Schema(description = "VIN")
    private String carVin;


    @Schema(description = "查询状态")
    private Integer queryStatus;


    @Schema(description = "查询状态文本")
    private String queryStatusTxt;

    @Schema(description = "iccid")
    private String iccid;

    @Schema(description = "实名制状态")
    private Integer realNameFlag;

    @Schema(description = "实名制状态文本")
    private String realNameFlagTxt;

    @Schema(description = "最新实名时间")
    private String realNameUpdateTime;

    @Schema(description = "卡状态")
    private String cardState;

    @Schema(description = "卡状态文本")
    private String cardStateTxt;

    @Schema(description = "操作人")
    private String operator;

    /**
     * 查询失败原因;失败原因 1：VIN格式校验错误  2：ICCID查询失败  3：联通查询失败
     */
    @Schema(description = "查询失败原因")
    private Integer failedType;

    @Schema(description = "查询失败原因文本")
    private String failedTypeTxt;

    @Schema(description = "失败原因")
    private String errorDesc;

    @Schema(description = "操作时间")
    private String operateTime;
}

