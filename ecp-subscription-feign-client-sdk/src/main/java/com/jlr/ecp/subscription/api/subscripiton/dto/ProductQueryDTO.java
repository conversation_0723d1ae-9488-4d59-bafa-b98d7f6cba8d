package com.jlr.ecp.subscription.api.subscripiton.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/***
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序 - 根据商品查询最早过期时间DTO")
@ToString(callSuper = true)
public class ProductQueryDTO implements Serializable {

    /**
     * jlrId
     */
    private String jlrId;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 商品列表
     */
    private List<ProductInfoDTO> productList;
}
