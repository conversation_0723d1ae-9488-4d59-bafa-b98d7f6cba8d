package com.jlr.ecp.subscription.api.pivi.appdcu;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.pivi.dto.appdcu.AppDCuRenewJobDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - AppDCu相关服务")
public interface AppDCuRenewalServiceApi {
    String PREFIX = ApiConstants.PREFIX + "/appdcu";

    @GetMapping(PREFIX+"/getAppDCuBatchRenewalId")
    @Operation(summary = "查询appd待批量续费的id列表")
    CommonResult<List<List<Long>>> getAppDCuRenewalIdList(@RequestParam(value = "renewStatus") Integer renewStatus);

    @PostMapping(PREFIX+"/batchSendAppDCuRenewal")
    @Operation(summary = "批量续费appd待续费")
    CommonResult<Integer> batchSendAppDRenewal(@RequestBody AppDCuRenewJobDTO appDCuRenewJobDTO);

}
