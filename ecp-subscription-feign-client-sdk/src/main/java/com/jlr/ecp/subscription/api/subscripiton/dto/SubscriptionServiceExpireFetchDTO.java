package com.jlr.ecp.subscription.api.subscripiton.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/***
 * <AUTHOR>
 */
@Data
@Schema(description = "API - 查询DTO ")
public class SubscriptionServiceExpireFetchDTO implements Serializable {

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED,example = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime fromDateTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED,example = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime toDateTime;
}
