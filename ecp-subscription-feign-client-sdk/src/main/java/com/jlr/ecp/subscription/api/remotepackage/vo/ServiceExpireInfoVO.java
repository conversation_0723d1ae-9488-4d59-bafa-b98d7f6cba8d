package com.jlr.ecp.subscription.api.remotepackage.vo;

import cn.hutool.core.lang.tree.TreeUtil;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "管理后台/查询中心-到期服务时间查询出参")
public class ServiceExpireInfoVO {

    @Schema(description = "服务名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceName;

    // @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String expireDate;

    // @JsonBackReference
    @Schema(description = "子服务", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<ServiceExpireInfoVO> child;
}
