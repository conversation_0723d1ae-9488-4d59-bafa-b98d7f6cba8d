package com.jlr.ecp.subscription.api.vcsorderfufilment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * eg.
 * 点击揽胜tab，传来Map<揽胜, Map<car_vin, series_code:series_name> > map
 *
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 前端传入的 当前用户所拥有的根据tab分类下的car_vin DTO")
@Data
public class SubBrandListDTO {

    @NotNull(message = "brandVehicles不能为空")
    @Schema(description = "eg.点击揽胜tab，传来Map<揽胜, Map<car_vin, series_code:series_name> > map")
    private Map<String, Map<String, String>> brandVehicles;

    @Schema(description = "brandCode,从header中获取")
    private String brandCode;
}
