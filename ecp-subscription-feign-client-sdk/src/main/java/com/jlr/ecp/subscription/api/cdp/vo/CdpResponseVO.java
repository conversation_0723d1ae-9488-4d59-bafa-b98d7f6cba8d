package com.jlr.ecp.subscription.api.cdp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@Schema(description = "小程序 -  Cdp返回结果 ")
@ToString(callSuper = true)
public class CdpResponseVO {
    @Schema(description = "车架号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String vin;


    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;
}
