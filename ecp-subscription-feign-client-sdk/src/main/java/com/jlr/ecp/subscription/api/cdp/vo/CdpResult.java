package com.jlr.ecp.subscription.api.cdp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序 -  Cdp返回结果 ")
@ToString(callSuper = true)
public class CdpResult implements Serializable {

    private Integer code;

    private String msg;

    private String data;

    private List<String> allData;

}
