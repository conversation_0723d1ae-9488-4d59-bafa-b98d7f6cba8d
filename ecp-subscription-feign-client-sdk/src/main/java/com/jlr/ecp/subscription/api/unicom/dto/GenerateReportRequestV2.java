package com.jlr.ecp.subscription.api.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.YearMonth;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GenerateReportRequestV2 {
    /**
     * 任务ID（雪花算法生成）
     */
    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long jobId;

    /**
     * 任务执行时间（自动触发时的日期）
     */
    @Schema(description = "任务执行时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务执行时间不能为空")
    private LocalDateTime jobDate;

    /**
     * 手动触发时指定的年月参数（格式：yyyy-MM）
     */
    @Schema(description = "手动触发参数（格式：yyyy-MM）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String jobParam;

    /**
     * 统计开始时间（目标月份的第一天）
     */
    @Schema(description = "统计开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "统计开始时间不能为空")
    private LocalDateTime beginDate;

    /**
     * 统计结束时间（目标月份的最后一天）
     */
    @Schema(description = "统计结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "统计结束时间不能为空")
    private LocalDateTime endDate;

    /**
     * 统计月份（数字格式，例如：4）
     */
    @Schema(description = "统计月份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "统计月份不能为空")
    private Integer jobMonth;

    /**
     * 统计年份（例如：2025）
     */
    @Schema(description = "统计年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "统计年份不能为空")
    private Integer jobYear;

    /**
     * 完整的年月对象（用于生成文件名等场景）
     */
    @Schema(description = "目标年月对象", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标年月对象不能为空")
    private YearMonth targetYearMonth;
}