package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订阅 商品分类下 车型信息")
public class SubVehicleInfoVO {
    /**
     * 车辆型号 编码
     */
    @Schema(description = "车辆型号")
    private String seriesCode;

    /**
     * 车辆名称
     */
    @Schema(description = "车辆名称")
    private String seriesName;

    /**
     * 车辆VIN
     */
    @Schema(description = "车辆VIN")
    private String carVin;


    /**
     * 可续订标识
     */
    @Schema(description = "可续订标识")
    private Boolean reNew;
}
