package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "车辆服务状态VO")
public class FulfilmentServiceStatusVO {
    /**
     * 履约号ID;履约号ID，雪花算法
     */
    private String fufilmentId;

    /**
     * 订单号;订单号
     */
    private String orderCode;

    /**
     * VCS订单编码
     * */
    private String vcsOrderCode;

    /**
     * 订单item编码;订单item编码
     */
    private String orderItemCode;

    /**
     * 车辆VIN;车辆VIN
     */
    private String carVin;

    /**
     * 服务结束时间;服务结束时间
     */
    private LocalDateTime vofServiceEndDate;

    /**
     * 服务状态;服务状态，1：激活中 2：已激活
     */
    private Integer vofServiceStatus;

    /**
     * 订单号;订单号
     */
    private String refundOrderCode;

    /**
     * 退单履约ID
     */
    private String rollbackFufilmentId;


    /**
     * 服务结束时间;服务结束时间
     */
    private LocalDateTime vorServiceEndDate;

    /**
     * 服务状态;服务状态，  1:激活关闭中 2：激活关闭
     */
    private Integer vorServiceStatus;
}