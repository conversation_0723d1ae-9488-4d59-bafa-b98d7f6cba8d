package com.jlr.ecp.subscription.api.subscripiton.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/***
 * <AUTHOR>
 */
@Data
@Schema(description = "API - 查询DTO ")
public class SubscriptionServiceQueryDTO implements Serializable {

    /**
     * 用户incontrol账号
     */
    private String incontrolId;

    /**
     * 车架号
     */
    private String carVin;

    /**
     * 服务类型
     */
    private Integer serviceType;
}
