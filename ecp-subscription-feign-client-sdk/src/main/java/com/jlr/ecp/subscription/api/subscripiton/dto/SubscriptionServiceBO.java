package com.jlr.ecp.subscription.api.subscripiton.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2023-12-26 15:17:19
 */
@Data
public class SubscriptionServiceBO{
    /**
     * 车辆编码;用户车辆编码
     */
    @Schema(description = "车辆编码", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "车辆编码")
    private String carNo;
    /**
     * incontrol ID
     */
    @Schema(description = "incontrol", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "incontrol")
    private String incontrolId;
    /**
     * 车架号;车架号
     */
    @Schema(description = "车架号", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "车架号")
    private String carVin;
    /**
     * 车架号;车架号
     */
    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED,example = "+8613121956551")
    private String phone;
    /**
     * 服务名;服务名
     */
    @Schema(description = "服务名", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "服务名")
    private String serviceName;
    /**
     * 服务包名;服务包名
     */
    @Schema(description = "服务包名", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "服务包名")
    private String servicePackage;
    /**
     * 过期时间;过期时间
     */
    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "过期时间")
    private LocalDateTime expiryDateUTC0;
    /**
     * 服务包类型;服务包类型，1：remote vcs服务 2：非remote服务
     */
    @Schema(description = "服务包类型", requiredMode = Schema.RequiredMode.REQUIRED,example = "1：remote vcs服务 2：非remote服务")
    @NotBlank(message = "服务包类型")
    private Integer serviceType;

    /**
     * 订阅服务ID，雪花算法ID
     */
    private String subscriptionId;

    /**
     * TSDP侧 userid
     */
    private String userid;
    /**
     * 用户姓
     */
    private String firstName;
    /**
     * 用户名
     */
    private String surname;
}