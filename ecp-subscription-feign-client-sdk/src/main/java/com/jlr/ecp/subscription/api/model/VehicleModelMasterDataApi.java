package com.jlr.ecp.subscription.api.model;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.model.dto.UserDPResultDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataCreateDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataUpdateDTO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataPageVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataVO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 适用车型年款配置")
public interface VehicleModelMasterDataApi {
    String PREFIX = ApiConstants.PREFIX+"/v1/subscription/api/vehicleModel";

    /**
     * 创建车型年款API接口
     * @param createDTO 入参
     * @return CommonResult<String>
     */
    @PostMapping(PREFIX + "/create")
    @Operation(summary = "添加车型年款")
    CommonResult<String> createVehicleModelMasterData(@Validated @RequestBody VehicleModelMasterDataCreateDTO createDTO);

    /**
     * 编辑车型年款API接口
     * @param updateDTO 编辑入参
     * @return String
     */
    @PutMapping(PREFIX + "/edit")
    @Operation(summary = "编辑车型年款")
    CommonResult<String> editVehicleModelMasterData(@Validated @RequestBody VehicleModelMasterDataUpdateDTO updateDTO);


    /**
     * 车型年款详情API
     * @param seriesCode 父级品牌 非必传
     * @return CommonResult<List<VehicleModelMasterDataListRespVO>>
     */
    @GetMapping(PREFIX + "/view")
    @Operation(summary = "车型年款查询接口")
    @Parameter(name = "seriesCode", description = "品牌CODE")
    CommonResult<VehicleModelMasterDataVO> view(@RequestParam(value = "seriesCode") String seriesCode);



    /**
     * 车型年款列表API
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return CommonResult<List<VehicleModelMasterDataListVO>>
     */
    @GetMapping(PREFIX + "/page")
    @Operation(summary = "车型年款列表查询接口")
    @Parameters({
            @Parameter(name = "pageNo", description = "页码", required = true, example = "0"),
            @Parameter(name = "pageSize", description = "页大小", required = true, example = "10")
    })
    CommonResult<PageResult<VehicleModelMasterDataPageVO>> page(@RequestParam(name = "pageNo", defaultValue = "10") Integer pageNo,
                                                                @RequestParam(name = "pageSize",defaultValue = "1") Integer pageSize);


    /**
     * 删除车型年款
     * @param seriesCode 车型编码
     * @return CommonResult<String>
     */
    @DeleteMapping(PREFIX + "/delete")
    @Operation(summary = "删除车型年款")
    @Parameter(name = "seriesCode", description = "编号", required = true)
    CommonResult<String> deleteBySeriesCode(@RequestParam("seriesCode") String seriesCode);


    /**
     * 获取DP配置List
     * @param resultDTO VinList
     * @return List<UserDPResultVO>
     */
    @PostMapping( PREFIX + "/findDpList")
    @Operation(summary = "")
    @PermitAll
    CommonResult<List<UserDPResultVO>> findDpList(@Validated @RequestBody UserDPResultDTO resultDTO);

}
