package com.jlr.ecp.subscription.api.bau.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 同步状态数量响应
 *
 * <AUTHOR>
 */
@Data
public class SyncResultResp {

    @Schema(description = "appD无需同步数量")
    private Integer appDNotNeedCount = 0;

    @Schema(description = "appD同步成功数量")
    private Integer appDSuccessCount = 0;

    @Schema(description = "appD同步失败数量")
    private Integer appDFailCount = 0;

    @Schema(description = "cu同步成功数量")
    private Integer cuSuccessCount = 0;

    @Schema(description = "cu同步失败数量")
    private Integer cuFailCount = 0;

    @Schema(description = "处理成功VIN数量")
    private Integer successCount = 0;

    @Schema(description = "处理失败VIN数量")
    private Integer failCount = 0;
}
