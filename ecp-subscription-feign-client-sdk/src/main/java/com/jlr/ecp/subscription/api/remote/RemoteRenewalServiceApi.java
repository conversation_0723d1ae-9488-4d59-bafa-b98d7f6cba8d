package com.jlr.ecp.subscription.api.remote;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.remote.dto.RemoteRenewJobDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - Remote相关服务")
public interface RemoteRenewalServiceApi {
    String PREFIX = ApiConstants.PREFIX + "/remote";

    @GetMapping(PREFIX+"/getRenewalIdList")
    @Operation(summary = "查询remote待批量续费的id列表")
    CommonResult<List<Long>> getRenewalIdList();

    @PostMapping(PREFIX+"/batchSendRenewal")
    @Operation(summary = "批量续费remote待续费")
    CommonResult<Integer> batchSendRenewal(@RequestBody RemoteRenewJobDTO remoteRenewJobDTO);

}
