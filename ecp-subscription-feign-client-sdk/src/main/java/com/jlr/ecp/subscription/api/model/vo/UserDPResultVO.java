package com.jlr.ecp.subscription.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序 -  DP返回结果 ")
@ToString(callSuper = true)
public class UserDPResultVO {

    @Schema(description = "车架号")
    private String vin;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌")
    private String brandName;

    @Schema(description = "车款年限")
    private String modelYear;

    @Schema(description = "配置编码")
    private String configCode;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "车型编码")
    private String seriesCode;

    @Schema(description = "车型名称")
    private String seriesName;

    @Schema(description = "产品名称EN")
    private String productionEn;

    @Schema(description = "House of Brand")
    private String hobEn;

    /**
     * 车机型号;车机型号 PIVI
     */
    @Schema(description = "车型PIVI")
    private String carSystemModel;

    /**
     * oss查询结果
     */
    private String queryResult;

}
