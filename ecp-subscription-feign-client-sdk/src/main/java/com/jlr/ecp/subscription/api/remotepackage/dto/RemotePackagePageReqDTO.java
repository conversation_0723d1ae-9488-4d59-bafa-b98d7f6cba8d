package com.jlr.ecp.subscription.api.remotepackage.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "平台管理 - package 分页req dto")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RemotePackagePageReqDTO extends PageParam {
    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;
}
