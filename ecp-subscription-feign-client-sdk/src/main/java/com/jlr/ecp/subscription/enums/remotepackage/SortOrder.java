package com.jlr.ecp.subscription.enums.remotepackage;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 排序方式
 */
@AllArgsConstructor
@Getter
public enum SortOrder {
    /**
     * 升序
     * 表示结果应按升序（从小到大）排序
     */
    ASCENDING("asc", "升序"),

    /**
     * 降序
     * 表示结果应按降序（从大到小）排序
     */
    DESCENDING("desc", "降序");

    /**
     * 排序代码
     * 用于在查询中指定排序顺序
     */
    private final String code;

    /**
     * 描述
     * 对排序顺序的文字描述
     */
    private final String description;

}
