package com.jlr.ecp.subscription.api.remotepackage.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - 批量上传服务包编号响应VO")
@AllArgsConstructor
public class BatchUploadRespVO {
    /**
     * Excel中的总记录数
     */
    @Schema(description = "Excel中的总记录数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer totalRecords;

    /**
     * 重复的记录数
     */
    @Schema(description = "重复的记录数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer duplicateRecords;

    /**
     * 成功上传的记录数
     */
    @Schema(description = "成功上传的记录数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer successfulRecords;
}
