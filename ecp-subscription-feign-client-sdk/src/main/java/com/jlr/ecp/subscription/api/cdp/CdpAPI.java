package com.jlr.ecp.subscription.api.cdp;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResponseVO;
import com.jlr.ecp.subscription.api.fallbackfactory.CdpApiFeignClientFallbackFactory;

import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME,
        fallbackFactory = CdpApiFeignClientFallbackFactory.class)
@Tag(name = "RPC 服务 - cdp ")
public interface CdpAPI {

    String PREFIX = ApiConstants.PREFIX + "/cdp";


    @PostMapping(PREFIX+"/getCdpMobile")
    @Operation(summary = "获取CDP手机号")
    CommonResult<List<CdpResponseVO>> getCdpMobile(@RequestBody List<String> vinList);

}
