package com.jlr.ecp.subscription.api.sota.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class SOTATokenVO  implements Serializable{
    /**
     * token
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * token类型
     */
    @JsonProperty("token_type")
    private String tokenType;

    /**
     * 过期时间
     */
    @JsonProperty("expires_in")
    private String expiresIn;


    private String scope;


    private String jti;
}
