package com.jlr.ecp.subscription.api.subscripiton.vo;

import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProcessFulfilmentVO {

    /**
     * 车辆服务状态VO
     */
    private FulfilmentServiceStatusVO fulfilmentServiceStatusVO;

    /**
     * TSDP数据
     */
    private List<VinsAndServiceDTO> vinAndService;

    /**
     * 过滤后的packageCode
     */
    private List<String> existPackageCode;
}
