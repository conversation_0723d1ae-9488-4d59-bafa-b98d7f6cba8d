package com.jlr.ecp.subscription.api.vininit;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务 - TSDP-Job初始化车辆相关服务")
public interface VehicleInitByTSDPJobAPI {

    String PREFIX = ApiConstants.PREFIX + "/vehicleInit";

    @PostMapping(PREFIX + "/processRemoteServiceData")
    @Operation(summary = "处理原始remote服务数据")
    @Parameter(name = "dataNo", description = "数据批次编号 yyyy-MM-dd")
    CommonResult<Map<Integer, Integer>> processRemoteServiceData(@RequestParam(value = "dataNo", required = false) String dataNo,
                                                                 @RequestParam(value = "pageNo") Long pageNo,
                                                                 @RequestParam(value = "pageSize") Long pageSize,
                                                                 @RequestParam(value = "status") Integer status);
}
