package com.jlr.ecp.subscription.api.subscripiton;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPExpireDateRequestDTO;
import com.jlr.ecp.subscription.api.pivi.dto.UnicomExpireDateDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.*;
import com.jlr.ecp.subscription.api.subscripiton.vo.ProcessFulfilmentVO;
import com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireStatisticVo;
import com.jlr.ecp.subscription.api.subscripiton.vo.SubscriptionServiceQueryVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - subscription相关服务")
public interface SubscriptionServiceApi {

    String PREFIX = ApiConstants.PREFIX + "/subscription";

   /**
    * 提供给订单定时任务的 根据vin号查询对应的车辆和服务信息
    * @param vins
    * @return Boolean
    */
   @PostMapping(PREFIX+"/fetchServiceExpireInfoByVins")
   @Operation(summary = "根据vin号查询对应的车辆和服务信息")
   CommonResult<List<VinsAndServiceDTO>> fetchServiceExpireInfoByVins(@Validated @RequestBody List<String> vins);

    /***
     * 查询服务过期时间
     * @param queryDTO 查询
     * @return SubscriptionServiceQueryVO
     */
    @PostMapping(PREFIX+"/findServiceExpiryDate")
    @Operation(summary = "查询服务过期时间")
    CommonResult<SubscriptionServiceQueryVO>  findServiceExpiryDate(@Validated  @RequestBody SubscriptionServiceQueryDTO queryDTO);


    /***
     * 分页获取Remote服务到期的信息
     * @return CommonResult<List<ServiceExpireVo>>
     * */
    @PostMapping(PREFIX+"/expire/info/page")
    @Operation(summary = "分页查询要过期的服务的信息")
    @PermitAll
    CommonResult<ServiceExpireStatisticVo> getRemoteExpireDateInfoByPage(@RequestBody ServiceExpirePageParam pageParam);

    /***
     * 分页获取PIVI服务到期的信息
     * @return CommonResult<List<ServiceExpireVo>>
     * */
    @PostMapping(PREFIX+"/expire/info/page/pivi")
    @Operation(summary = "分页查询要过期的服务的信息")
    @PermitAll
    CommonResult<ServiceExpireStatisticVo> getPIVIExpireDateInfoByPage(@RequestBody ServiceExpirePageParam pageParam);


    /***
     * 分页获取PIVI服务已过期的信息
     * @return CommonResult<List<ServiceExpireVo>>
     * */
    @PostMapping(PREFIX+"/expire/info/page/pivi/after")
    @Operation(summary = "分页获取PIVI服务已过期的信息")
    CommonResult<ServiceExpireStatisticVo> getPIVIExpireDateInfoByPageAfter(@RequestBody ServiceExpirePageParam pageParam);

    /***
     * 未实名-分页查询要过期的信息
     * @return CommonResult<List<ServiceExpireVo>>
     * */
    @PostMapping(PREFIX+"/expire/info/page/pivi/unnamed")
    @Operation(summary = "未实名-分页查询要过期的信息")
    CommonResult<ServiceExpireStatisticVo> getPIVIExpireDateInfoByUnnamed(@RequestBody ServiceExpirePageParam pageParam);


    /***
     * 提供给定时任务使用的每天定时查询一个月过期服务的订阅信息同步到订阅服务表
     * @param queryDTO 查询
     * @return Integer 单词任务拉取到的数据量
     */
    @PostMapping(PREFIX+"/fetchServiceExpiryDateByExpiryRange")
    @Operation(summary = "定时查询一个月过期服务的订阅信息同步到订阅服务表")
    CommonResult<Integer>  fetchServiceExpiryDateByExpiryRange(@Validated  @RequestBody SubscriptionServiceExpireFetchDTO queryDTO);



    /***
     * 提供给定时任务使用的抓取TSDP的过期服务数据并保存服务
     * @param queryDTO 查询
     * @return Integer 单词任务拉取到的数据量
     */
    @PostMapping(PREFIX+"/scrapeAndSaveRemoteServiceData")
    @Operation(summary = "抓取TSDP的过期服务数据并保存")
    CommonResult<Integer>  scrapeAndSaveRemoteServiceData(@Validated  @RequestBody SubscriptionServiceExpireFetchDTO queryDTO);


    /***
     * 提供给定时任务使用的查询需要被处理的原始remote服务数据分页结果
     * @return Integer 返回该批次需要被处理的原始数据条数
     */
    @PostMapping(PREFIX + "/queryNeedProcessRemoteServiceNum")
    @Operation(summary = "查询需要被处理的原始remote服务数据条数")
    @Parameter(name = "dataNo", description = "数据批次编号 yyyy-MM-dd")
    CommonResult<Long> queryNeedProcessRemoteServiceNum(@RequestParam(value = "dataNo", required = false) String dataNo,
                                                        @RequestParam(value = "status") Integer status);


    /***
     * 提供给定时任务使用的处理原始remote服务数据的方法
     * @return Map<Integer, Integer> 返回该批次调用 处理成功数据VS处理失败数据 的键值对
     */
    @PostMapping(PREFIX + "/processRemoteServiceData")
    @Operation(summary = "处理原始remote服务数据")
    @Parameter(name = "dataNo", description = "数据批次编号 yyyy-MM-dd")
    CommonResult<Map<Integer, Integer>> processRemoteServiceData(@RequestParam(value = "dataNo", required = false) String dataNo,
                                                              @RequestParam(value = "pageNo") Long pageNo,
                                                              @RequestParam(value = "pageSize") Long pageSize,
                                                              @RequestParam(value = "status") Integer status);


    /***
     * 履约定时服务
     * @return Boolean
     */
    @PostMapping(PREFIX+"/checkFulfilmentStatus")
    @Operation(summary = "履约定时服务")
    @PermitAll
    CommonResult<Integer>  checkFulfilmentStatus(@RequestBody ProcessFulfilmentVO processFulfilmentVO);


    /***
     * 查询正在激活中和激活关闭中的履约
     * @return Boolean
     */
    @GetMapping(PREFIX+"/findActiveService")
    @Operation(summary = "查询正在激活中和激活关闭中的履约")
    @PermitAll
    CommonResult<List<FulfilmentServiceStatusVO>>  findActiveService(@RequestParam("total") Integer total);


    @PostMapping(PREFIX+"/getExistPackageCode")
    @Operation(summary = "获取过滤的服务包code")
    CommonResult<List<String>> getExistPackageCode(@RequestBody List<String> packageCodes);

    @PostMapping(PREFIX + "/query/carVin/phone")
    @Operation(summary = "根据vin号查询对应的手机号")
    CommonResult<Map<String, String>> queryCarVinToPhoneMap(@RequestBody List<String> vinList);


    @GetMapping(PREFIX + "/getExpireDateByVin")
    @Operation(summary = "根据vin号查询到期服务日期查询")
    CommonResult<List<ServiceExpireInfoVO>> getExpireDateByVin(@RequestParam("carVin") String carVin);


    @PostMapping(PREFIX + "/getAmapRealExpireDate")
    @Operation(summary = "根据vin号查询高德服务真实过期日期")
    @Parameter(name = "carVins", description = "vin号集合")
    CommonResult<Map<String, String>> getAmapRealExpireDate(@RequestBody List<String> carVins);

    @PostMapping(PREFIX + "/getUnicomExpireDate")
    @Operation(summary = "根据vin号查询联通真实过期日期")
    @Parameter(name = "carVins", description = "vin号集合")
    CommonResult<Map<String, LocalDateTime>> getUnicomExpireDate(@RequestBody List<String> carVins);

    @PostMapping(PREFIX + "/getLatestExpireDateByProducts")
    @Operation(summary = "根据vin号查询到期服务日期查询")
    CommonResult<List<ProductInfoDTO>> getLatestExpireDateByProducts(@RequestBody ProductQueryDTO productQueryDTO);

    @PostMapping(PREFIX + "/allUnicomExpireDate")
    @Operation(summary = "获取unicom全量的过期数据")
    void getAllUnicomExpireDate(@RequestBody UnicomExpireDateDTO unicomExpireDateDTO);

    @PostMapping(PREFIX + "/updateAmaPExpireDate")
    @Operation(summary = "更新amaP的过期时间")
    void updateAmaPExpireDate(@RequestBody AmaPExpireDateRequestDTO amaPExpireDateDTO);


    @PostMapping(PREFIX+"/expire/info/page/condition")
    @Operation(summary = "按照条件分页查询过期通知")
    CommonResult<ServiceExpireStatisticVo> expireInfoPageByCondition(@RequestBody AutoTaskNotifyConditionDTO pageParam);
}
