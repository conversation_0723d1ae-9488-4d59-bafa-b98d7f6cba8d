package com.jlr.ecp.subscription.api.pivi.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 服务到期批量查询相关功能")
public interface ServiceExpireBatchQueryApi {
    String PREFIX = ApiConstants.PREFIX + "/service/expire";

    @GetMapping(PREFIX+"/batchQuery")
    @Operation(summary = "服务到期批量查询")
    CommonResult<String> batchQuery(@RequestParam(value = "intervalTimeMinutes") Integer intervalTimeMinutes);

}