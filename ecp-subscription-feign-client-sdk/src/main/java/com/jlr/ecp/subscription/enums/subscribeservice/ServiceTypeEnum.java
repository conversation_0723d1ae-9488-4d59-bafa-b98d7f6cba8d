package com.jlr.ecp.subscription.enums.subscribeservice;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ServiceTypeEnum {


    /**
     * remote vcs服务
     */
    REMOTE_VCS(1,"remote vcs服务"),
    /**
     * 非remote服务
     */
    NO_REMOTE(2,"非remote服务"),
    /**
     * PIVI Subscription Service
     */
    PIVI_SUBSCRIPTION(3,"PIVI Subscription Service");


    /**
     * code
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

}
