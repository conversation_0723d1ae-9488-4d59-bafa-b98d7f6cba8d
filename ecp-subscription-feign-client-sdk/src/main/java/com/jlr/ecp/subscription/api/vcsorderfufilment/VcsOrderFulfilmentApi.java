package com.jlr.ecp.subscription.api.vcsorderfufilment;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.subscripiton.dto.FulfilmentPIVICompensationDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 履约订单")
public interface VcsOrderFulfilmentApi {
    String PREFIX = ApiConstants.PREFIX+"/v1/subscription/api/vcs/order/fulfilment";

    /**
     * 履约订单详情API
     *
     * @param orderItemCode 订单item编码
     * @return
     */
    @GetMapping(PREFIX + "/view")
    @Operation(summary = "履约订单 查询接口")
    @Parameter(name = "orderItemCode", description = "订单item编码")
    @PermitAll
    CommonResult<VcsOrderFulfilmentRespVO> view(@RequestParam(value = "orderItemCode") String orderItemCode);

    /**
     * 履约订单详情API 获取每个orderItemCode根据MaxId对应的最新履约记录
     *
     * @param orderItemCodeList 订单item编码
     * @return CommonResult<List < VcsOrderFulfilmentRespVO>>
     */
    @PostMapping(PREFIX + "/view/list")
    @Operation(summary = "履约订单详情API 获取每个orderItemCode根据MaxId对应的最新履约记录")
    @Parameter(name = "orderItemCodeList", description = "订单item编码list")
    @PermitAll
    CommonResult<List<VcsOrderFulfilmentRespVO>> fulfilmentViewList(@RequestBody List<String> orderItemCodeList);


    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     *      1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    @PostMapping(PREFIX + "/check")
    @Operation(summary = "服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买")
    @PermitAll
    CommonResult<Boolean> checkCanBuyService(@RequestBody List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     *      1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    @PostMapping(PREFIX + "/checkV2")
    @Operation(summary = "服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买")
    @PermitAll
    CommonResult<Boolean> checkCanBuyServiceV2(@RequestBody List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    @PostMapping(PREFIX + "/checkCanBuyServiceForPC")
    @Operation(summary = "代客下单校验")
    CommonResult<Boolean> checkCanBuyServiceForPC(@RequestBody List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

//    /**
//     * 历史履约订单详情API
//     * <p>
//     * 逻辑有改，
//     */
//    @GetMapping(PREFIX + "/history/view")
//    @Operation(summary = "历史履约订单 查询接口")
//    @Parameter(name = "orderItemCodeList", description = "订单item编码list")
//    @PermitAll
//    CommonResult<List<VcsOrderFulfilmentRespVO>> fulfilmentViewList(@RequestParam(value = "orderItemCodeList") String orderItemCodeList);

    @PostMapping(PREFIX+"/getFulfilmentListByCarVinList")
    @Operation(summary = "根据CarVin去查询正在激活中的履约")
    CommonResult<List<String>> getFulfilmentListByCarVinList(@RequestBody List<String> carvinList);


    @PostMapping(PREFIX+"/findVinAndServiceDate")
    @Operation(summary = "查询车辆对应的履约服务时间")
    CommonResult<List<VinsAndServiceDateDTO>> findVinAndServiceDate(@RequestBody List<String> carvinList);


    @PostMapping(PREFIX+"/processTsdpFailedTasks")
    @Operation(summary = "处理TSDP履约失败订单的补偿任务")
    @Parameter(name = "upcastDays", description = "上溯天数 可不传则处理全部")
    CommonResult<Integer> processTsdpFailedTasks(@RequestParam(value = "upcastDays",required = false) Integer upcastDays);

    @PostMapping(PREFIX + "/fulfilment/piviCompensation")
    @Operation(summary = "PIVI履约失败的补偿")
    CommonResult<Integer> fulfilmentPIVICompensation(@RequestBody FulfilmentPIVICompensationDTO compensationDTO);

    @PostMapping(PREFIX + "/fulfilment/piviCancelCompensation")
    @Operation(summary = "PIVI履约关闭失败的补偿")
    CommonResult<Integer> fulfilmentPIVICancelCompensation(@RequestBody FulfilmentPIVICompensationDTO compensationDTO);

    @PostMapping(PREFIX + "/fulfilment/amaPUpdatePIVI")
    @Operation(summary = "AMAP更新PIVI履约")
    CommonResult<Integer> amaPUpdateFulfilmentPIVI(@RequestParam(value = "days") Integer days);

    @GetMapping(PREFIX + "/checkStatus")
    @Operation(summary = "检查订单包含的履约服务是否有激活失败状态")
    @PermitAll
    CommonResult<Boolean> checkStatus(@RequestParam(value = "orderCode") String orderCode);

    @GetMapping(PREFIX + "/getFailOrders")
    @Operation(summary = "检查是否有激活失败的订单")
    @PermitAll
    CommonResult<Set<String>> getFailOrders();
}
