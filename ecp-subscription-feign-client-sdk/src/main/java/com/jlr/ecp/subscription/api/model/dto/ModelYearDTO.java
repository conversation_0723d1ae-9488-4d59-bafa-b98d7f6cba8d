package com.jlr.ecp.subscription.api.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 -  车型年款DTO ")
@ToString(callSuper = true)
public class ModelYearDTO {

    /**
     * 型号年款
     */
    @Schema(description = "型号年款", requiredMode = Schema.RequiredMode.REQUIRED)
    private String modelYear;

    /**
     * 数据id
     */
    @Schema(description = "数据id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 版本号
     */
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer revision;

}
