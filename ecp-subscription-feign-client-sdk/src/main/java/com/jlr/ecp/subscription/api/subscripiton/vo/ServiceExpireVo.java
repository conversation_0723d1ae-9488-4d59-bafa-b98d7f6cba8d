package com.jlr.ecp.subscription.api.subscripiton.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ServiceExpireVo {
    /**
     * uuid, 标识每一条数据
     * */
    private String uuid;

    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 车辆编码
     */
    private String carNo;

    /**
     * 用户incontrol账号
     */
    private String incontrolId;

    /**
     * 车架号
     */
    private String carVin;

    /**
     * 服务名
     */
    private String serviceName;

    /**
     * 服务包名
     */
    private String servicePackage;

    /**
     * 过期时间
     */
    private LocalDateTime expiryDate;

    /**
     * 服务包类型 1：remote vcs服务 2：非remote服务'
     */
    private Integer serviceType;

    /**
     * 车辆型号;
     */
    private String seriesCode;

    /**
     * 车辆名称;
     */
    private String seriesName;

    /**
     * 品牌CODE; LALANDJAG
     */
    private String brandCode;

    /**
     * 品牌名;车型品牌
     */
    private String brandName;

    /**
     *  车主手机号
     * */
    private String incontrolPhone;

    /**
     * 联通ICCID
     */
    private String iccid;
}
