package com.jlr.ecp.subscription.api.icrvehicle.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "InControl 车辆信息响应")
@Builder
public class IcrVehicleListRespVO {
    @Schema(description = "车辆VIN", requiredMode = Schema.RequiredMode.REQUIRED)
    private String carVin;

    @Schema(description = "车辆型号编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seriesCode;

    @Schema(description = "车辆型号名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seriesName;

    @Schema(description = "品牌编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "House of Brand 英文描述")
    private String hobEn;

    @Schema(description = "产品类型（英文）")
    private String productionEn;

    @Schema(description = "配置编码")
    private String configCode;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "车辆年款", requiredMode = Schema.RequiredMode.REQUIRED)
    private String modelYear;

    @Schema(description = "车机型号")
    private String carSystemModel;

}
