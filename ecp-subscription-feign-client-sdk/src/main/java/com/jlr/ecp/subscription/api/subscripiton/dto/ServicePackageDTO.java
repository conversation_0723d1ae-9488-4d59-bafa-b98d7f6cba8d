package com.jlr.ecp.subscription.api.subscripiton.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ServicePackageDTO implements Serializable {

    /**
     * 服务包名
     */
    private String servicePackageName;

    /**
     * 服务名
     */
    private String serviceName;
    /**
     * 过期时间
     */
    private LocalDateTime expireDate;

    /**
     * 过期时间 UTC0 时间
     */
    private LocalDateTime expireDateUTC0;

}
