package com.jlr.ecp.subscription.api.pivi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPRenewalDTO implements Serializable {
    private List<Long> amaPRenewalIdList;

    private Integer total;

    private Boolean endFlag;
}
