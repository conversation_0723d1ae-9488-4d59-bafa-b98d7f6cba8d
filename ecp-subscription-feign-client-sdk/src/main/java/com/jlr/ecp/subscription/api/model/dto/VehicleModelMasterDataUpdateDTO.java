package com.jlr.ecp.subscription.api.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 -  车型年款编辑DTO ")
@ToString(callSuper = true)
public class VehicleModelMasterDataUpdateDTO extends VehicleModelMasterDataCreateDTO{

    /**
     * 车型编码
     */
    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型编码不能为空")
    @Length(max = 30, message = "车型编码不能超过30个字符")
    private String newSeriesCode;
}
