package com.jlr.ecp.subscription.api.cdp.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 *  cdp返回结果
 * <AUTHOR>
 * */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CdpResponse {
    private Long code;

    private String msg;

    private Map<String, String> properties;

    private Map<String, String> tags;

    private Map<String, String> propertiesMapping;

    private Map<String, String> tagsMapping;
}
