package com.jlr.ecp.subscription.api.incontrol;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.VehicleSubscriptionDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - IncontrolVehicle")
public interface IncontrolVehicleAPI {

    String PREFIX = ApiConstants.PREFIX + "/incontrolVehicle";

    @GetMapping(PREFIX+"/getIncontrolVehicleByConsumerCode")
    @Operation(summary = "incontrolVehicle查询服务")
    @Parameter(name = "consumerCode", description = "consumerCode", required = true, example = "fsdfsfsdfs")
    @Parameter(name = "brandCode", description = "brandCode", required = true, example = "brandCode")
    CommonResult<List<IncontrolVehicleDTO>> getIncontrolVehicleByConsumerCode(@RequestParam("consumerCode") String consumerCode,@RequestParam("brandCode") String brandCode);

    @GetMapping(PREFIX+"/getIncontrolVehicleByCarVinList")
    @Operation(summary = "incontrolVehicle查询服务,通过carVinList")
    @Parameter(name = "carVinList", description = "carVinList", required = true)
    CommonResult<List<IncontrolVehicleByCarDTO>> getIncontrolVehicleByCarVin(@RequestParam("carVinList") List<String> carVinList);

    @PostMapping(PREFIX+"/saveVehicleForICRConsumer")
    @Operation(summary = "保存用户的车机信息")
    CommonResult<Boolean> saveVehicleForICRConsumer(@RequestBody List<IncontrolVehicleDTO> vehicleList);

    /** 查询并存储车辆信息
     * @param inControlId
     * @return
     */
    @PutMapping(PREFIX+"/getAndStoreCarInfo")
    @Operation(summary = "查询并存储车辆信息")
    @Parameters({
            @Parameter(name = "consumerCode", description = "consumerCode", required = true, example = "consumerCode"),
            @Parameter(name = "inControlId", description = "inControlID", required = true, example = "consumerCode")
    })
    CommonResult<Boolean> getAndStoreCarInfo(@RequestParam("inControlId")String inControlId,@RequestParam("consumerCode")String consumerCode);

    @PostMapping(PREFIX+"/getMp/expire/carVinInfo")
    @Operation(summary = "查询mp的到期车辆信息")
    CommonResult<Map<String, List<VehicleSubscriptionDTO>>> getMpExpireCarVinInfo(@RequestBody List<String> jlrIdList);
}
