package com.jlr.ecp.subscription.api.fallback;

import com.jlr.ecp.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.cdp.CdpAPI;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResponseVO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CdpFeignClientFallBack implements CdpAPI {
    @Setter
    private Throwable throwable;

    @Override
    public CommonResult<List<CdpResponseVO>> getCdpMobile(List<String> vinList) {
        String apiUrl = CdpAPI.PREFIX+"/getCdpMobile";
        log.error(apiUrl + " fallback:{}", throwable.getMessage(), throwable);
        return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(),
                GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getMsg());
    }
}
