package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订阅 商品item下 商品历史订阅时间段")
@AllArgsConstructor
@NoArgsConstructor
public class SubDurationHistoryVO {
    @Schema(description = "历史订阅开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    LocalDateTime historyStartTime;

    @Schema(description = "历史订阅结束时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    LocalDateTime historyEndTime;


}
