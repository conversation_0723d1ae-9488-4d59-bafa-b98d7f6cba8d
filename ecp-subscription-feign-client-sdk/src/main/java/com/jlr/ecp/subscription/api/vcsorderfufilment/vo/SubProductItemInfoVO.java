package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订阅 商品分类下 商品item")
public class SubProductItemInfoVO {

    /**
     * 订单item编码
     */
    @Schema(description = "订单item编码")
    private String orderItemCode;

    /**
     * 商品快照编码
     */
    @Schema(description = "商品快照编码")
    private String productVersionCode;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String productCode;

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    /**
     * 商品主图URL
     */
    @Schema(description = "商品主图URL")
    private String productImageUrl;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer productQuantity;

    /**
     * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
     */
    @Schema(description = "stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI")
    private String productAttribute;

    /**
     * 标价
     */
    @Schema(description = "标价")
    private String productMarketPrice;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private String productSalePrice;

    /**
     * 当前商品 最新一个服务 -》服务状态
     */
    @Schema(description = "当前商品 最新一个服务 -》服务状态")
    private Integer serviceStatus;

    /**
     * 当前商品 最新一个服务 -》服务状态描述
     */
    @Schema(description = "当前商品 最新一个服务 -》服务状态描述")
    private String serviceStatusDesc;

    /**
     * 当前商品 最新一个服务 -》起始日期
     */
    @Schema(description = "当前商品 最新一个服务 -》起始日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceBeginDate;

    /**
     * 当前商品 最新一个服务 -》截止日期
     */
    @Schema(description = "当前商品 最新一个服务 -》截止日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceEndDate;
}
