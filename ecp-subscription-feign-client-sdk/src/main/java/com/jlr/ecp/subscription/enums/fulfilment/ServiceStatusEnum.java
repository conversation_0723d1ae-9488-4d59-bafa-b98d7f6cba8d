package com.jlr.ecp.subscription.enums.fulfilment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 表示服务状态的枚举类。
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ServiceStatusEnum {

    /**
     * 未激活
     * 服务状态；1：未激活 订单详情显示未激活
     */
    INACTIVE(1, "未激活"),

    /**
     * 已激活
     * 服务状态；2：已激活 订单详情显示已激活
     */
    ACTIVE(2, "已激活"),

    /**
     * 激活失败
     * 服务状态；3：激活失败 订单详情显示激活失败
     */
    ACTIVATION_FAILED(3, "激活失败");



    private final Integer code;
    private final String description;

    /**
     * 根据服务状态的整数值获取对应的描述。
     *
     * @param code 服务状态的整数值
     * @return 对应的服务状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        // 或者返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (ServiceStatusEnum status : ServiceStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid service status code: " + code);
    }
}
