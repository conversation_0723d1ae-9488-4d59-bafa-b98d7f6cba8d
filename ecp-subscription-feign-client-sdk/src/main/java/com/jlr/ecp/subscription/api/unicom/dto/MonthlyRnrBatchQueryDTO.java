package com.jlr.ecp.subscription.api.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MonthlyRnrBatchQueryDTO {

    @Schema(description = "执行id", requiredMode = Schema.RequiredMode.REQUIRED)
   private List<Long> idList;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
   private Long jobId;

    @Schema(description = "最后标记", requiredMode = Schema.RequiredMode.REQUIRED)
   private Boolean lastFlag;
}
