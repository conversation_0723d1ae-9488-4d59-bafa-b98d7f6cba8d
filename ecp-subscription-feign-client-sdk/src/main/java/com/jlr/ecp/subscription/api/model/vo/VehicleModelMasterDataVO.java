package com.jlr.ecp.subscription.api.model.vo;

import com.jlr.ecp.subscription.api.model.dto.ModelYearDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 -  车型年款创建VO ")
@ToString(callSuper = true)
public class VehicleModelMasterDataVO {




    /**
     * 车机：PIVI
     */
    @Schema(description = "车机", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车机不能为空")
    private String carSystemModel;

    /**
     * 车型编码
     */
    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seriesCode;

    /**
     * 型号年款
     */
    @Schema(description = "年限组合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ModelYearDTO> modelYearList;


}
