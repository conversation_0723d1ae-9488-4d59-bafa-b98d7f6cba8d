package com.jlr.ecp.subscription.api.consumer;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.consumer.dto.ConsumerIncontrolDTO;
import com.jlr.ecp.subscription.api.subscripiton.dto.SubscriptionServiceSaveDTO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - subscription相关服务")
public interface ConsumerServiceApi {

    String PREFIX = ApiConstants.PREFIX + "/incontrol";

    /**
     * 绑定ICR用户
     * @param consumerCode
     */
    @PostMapping(PREFIX+"/bindIRC")
    @Operation(summary = "绑定ICR用户")
    @Parameters({
            @Parameter(name = "consumerCode", description = "consumerCode", required = true, example = "consumerCode"),
            @Parameter(name = "inControlID", description = "inControlID", required = true, example = "consumerCode"),
            @Parameter(name = "clientId", description = "clientId", required = true, example = "clientId")
    })
    CommonResult<ConsumerIncontrolDTO> bindIRC(@RequestParam("consumerCode") String consumerCode,@RequestParam("inControlID") String inControlID,@RequestParam("clientId") String clientId);


    /**
     * 解绑ICR用户
     * @param consumerCode
     */
    @PutMapping(PREFIX+"/unBindIRC")
    @Operation(summary = "解绑ICR用户")
    @Parameters({
            @Parameter(name = "consumerCode", description = "consumerCode", required = true, example = "consumerCode"),
            @Parameter(name = "inControlID", description = "inControlID", required = true, example = "consumerCode"),
            @Parameter(name = "clientId", description = "clientId", required = true, example = "clientId")
    })
    CommonResult<ConsumerIncontrolDTO> unBindIRC(@RequestParam("consumerCode") String consumerCode,@RequestParam("inControlID") String inControlID,@RequestParam("clientId") String clientId);

    /**
     * 根据consumerCode或ICR查绑定icr用户
     * @param consumerCode
     */
    @GetMapping(PREFIX+"/getConsumerByCodeOrICR")
    @Operation(summary = "根据consumerCode或ICR查绑定icr用户")
    @Parameters({
            @Parameter(name = "consumerCode", description = "consumerCode", required = false, example = "consumerCode"),
            @Parameter(name = "inControlID", description = "inControlID", required = false, example = "consumerCode"),
            @Parameter(name = "clientId", description = "clientId", required = true, example = "clientId")
    })
    CommonResult<ConsumerIncontrolDTO> getConsumerByCodeOrICR(@RequestParam( value = "consumerCode",required = false) String consumerCode,@RequestParam(value = "inControlID",required = false) String inControlID,@RequestParam("clientId") String clientId);

}
