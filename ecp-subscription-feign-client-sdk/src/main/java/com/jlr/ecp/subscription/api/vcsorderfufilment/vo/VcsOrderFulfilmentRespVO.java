package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "订单项服务详情")
public class VcsOrderFulfilmentRespVO {
    @Schema(description = "履约号ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fulfilmentId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderCode;

    @Schema(description = "订单item编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderItemCode;

    @Schema(description = "车辆VIN", requiredMode = Schema.RequiredMode.REQUIRED)
    private String carVin;

    @Schema(description = "服务起始时间", requiredMode = Schema.RequiredMode.REQUIRED)
//    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceBeginDate;

    @Schema(description = "服务结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
//    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceEndDate;

    @Schema(description = "服务状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer serviceStatus;

    @Schema(description = "服务状态描述", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceStatusDesc;

    @Schema(description = "订单item项备注信息")
    private String orderItemRemark;
}
