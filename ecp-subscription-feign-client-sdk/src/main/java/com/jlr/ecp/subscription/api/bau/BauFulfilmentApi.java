package com.jlr.ecp.subscription.api.bau;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.bau.dto.*;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - BauFulfilment ")
public interface BauFulfilmentApi {

    String PREFIX = ApiConstants.PREFIX + "/bau/fulfilment";

    @PostMapping(PREFIX+"/preCheckList")
    @Operation(summary = "待校验的id列表")
    CommonResult<List<Long>> getPreCheckList(@RequestBody PreCheckListRequest request);

    @PostMapping(PREFIX+"/check")
    @Operation(summary = "校验idList对应的vinList是否存在于各个系统")
    CommonResult<CheckedVinResp> check(@RequestBody List<Long> idList);


    /**
     * OSS到S3文件传输
     * @param dateStr YYYYMMDD
     */
    @GetMapping(PREFIX+"/transferFile")
    @Operation(summary = "OSS到S3文件传输")
    CommonResult<TransferFileVO> transferFile(@RequestParam(value = "dateStr",required = false) String dateStr);

    /**
     * OSS到S3文件传输
     * @param dateStr YYYYMMDD
     */
    @GetMapping(PREFIX+"/handleFile")
    @Operation(summary = "处理S3上的DMS文件")
    CommonResult<HandleFileResultVO> handleFile(@RequestParam(value = "dateStr",required = false) String dateStr);

    @PostMapping(PREFIX+"/preSyncIdList")
    @Operation(summary = "获取待同步的id列表")
    CommonResult<Map<Long, List<Long>>> getPreSyncIdList(@RequestBody PreCheckListRequest request);

    @PostMapping(PREFIX+"/initialize")
    @Operation(summary = "初始化履约")
    CommonResult<SyncResultResp> initialize(@RequestBody List<Long> idList);

    @PostMapping(PREFIX+"/updateSyncResult")
    @Operation(summary = "更新同步结果")
    CommonResult<Boolean> updateSyncResult(@RequestBody Set<Long> bauJobIdSet);
}
