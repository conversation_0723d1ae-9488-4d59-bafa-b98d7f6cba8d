package com.jlr.ecp.subscription.enums;

/**
 * System 字典类型的枚举类
 *
 * <AUTHOR>
 */
public final class DictTypeConstants {

    public static final String USER_TYPE = "user_type"; // 用户类型
    public static final String COMMON_STATUS = "common_status"; // 系统状态

    // ========== SYSTEM 模块 ==========

    public static final String USER_SEX = "system_user_sex"; // 用户性别

    public static final String OPERATE_TYPE = "system_operate_type"; // 操作类型

    public static final String LOGIN_TYPE = "system_login_type"; // 登录日志的类型
    public static final String LOGIN_RESULT = "system_login_result"; // 登录结果

    public static final String ERROR_CODE_TYPE = "system_error_code_type"; // 错误码的类型枚举

    public static final String SMS_CHANNEL_CODE = "system_sms_channel_code"; // 短信渠道编码
    public static final String SMS_TEMPLATE_TYPE = "system_sms_template_type"; // 短信模板类型
    public static final String SMS_SEND_STATUS = "system_sms_send_status"; // 短信发送状态
    public static final String SMS_RECEIVE_STATUS = "system_sms_receive_status"; // 短信接收状态

}
