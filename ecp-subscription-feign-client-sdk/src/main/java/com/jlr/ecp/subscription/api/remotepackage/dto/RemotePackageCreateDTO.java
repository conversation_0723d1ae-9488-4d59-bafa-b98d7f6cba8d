package com.jlr.ecp.subscription.api.remotepackage.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 管理后台 -  车型年款创建DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 -  服务包创建DTO ")
public class RemotePackageCreateDTO {
    /**
     * 车机：PIVI
     */
    @Schema(description = "车机", requiredMode = Schema.RequiredMode.REQUIRED,example = "PIVI")
    @NotBlank(message = "车机不能为空")
    private String carSystemModel;

    /**
     * 服务包编码
     */
    @Schema(description = "服务包编码", requiredMode = Schema.RequiredMode.REQUIRED,example = "CHND45A-E1H1R1T1_J")
    @NotBlank(message = "服务包编码不能为空")
    @Length(max = 30, message = "服务包编码不能超过30个字符")
    private String packageCode;
}
