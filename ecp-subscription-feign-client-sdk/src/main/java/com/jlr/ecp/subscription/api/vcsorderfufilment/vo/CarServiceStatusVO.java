package com.jlr.ecp.subscription.api.vcsorderfufilment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "车辆服务状态VO")
public class CarServiceStatusVO {
    /**
     * 车辆VIN码
     */
    @Schema(description = "车辆VIN")
    private String carVin;

    /**
     * 服务履约类型
     */
    @Schema(description = "服务履约类型")
    private Integer serviceType;

    /**
     * 激活中的服务数量
     */
    @Schema(description = "激活中的服务数量")
    private Integer activeCount;

    /**
     * 退款激活中的服务数量
     */
    @Schema(description = "退款激活中的服务数量")
    private Integer rollbackActiveCount;
}