package com.jlr.ecp.subscription.api.remotepackage.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 管理后台 -  Excel导入 DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class RemotePackageImportExcelDTO {

    @ExcelProperty("Remote服务包编号")
    private String packageCode;
}
