package com.jlr.ecp.subscription.api.unicom.vo;

import lombok.*;

/**
 * t_unicom_todo_order
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnicomTodoOrderVO  {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
    private Long id;

     /**
     * 订阅服务ID;订阅服务ID
     */    
    private String iccid;

     /**
     * 请求类型;order：订购 cancel：退订 change：变更
     */    
    private String requestType;

    /**
     * 订购ID;订购ID
     */
    private String extBookId;


    /**
     * 产品ID
     */
    private String productId;

    /**
     * 订购状态
     */
    private String bookStatus;

    /**
     * 激活时间;激活时间yyyyMMddHHmmSS
     */
    private String activeTime;


    /**
     * 到期时间;到期时间yyyyMMddHHmmSS
     */
    private String expireTime;


    /**
     * 当前处理状态;状态 0：待处理 1：处理完成 2：处理失败
     */
    private Integer status;

    /**
     * 处理次数;处理次数，默认0，大于3次不再处理，日志告警
     */
    private Integer requestCount;

    /**
     * 待校验订购ID;待校验订购ID
     */
    private String validExtBookId;

    /**
     * 需要校验的订购状态;active：生效 cancel：作废
     */
    private String validBookStatus;

}

