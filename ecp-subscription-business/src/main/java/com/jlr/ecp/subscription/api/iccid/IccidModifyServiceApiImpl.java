package com.jlr.ecp.subscription.api.iccid;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.iccid.dto.IccidModifyBatchDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.iccid.ModifyStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.iccid.IccidSingleModifyService;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum.SYSTEM_ERROR;

@RestController
@Validated
@Slf4j
public class IccidModifyServiceApiImpl implements IccidModifyServiceApi {

    @Resource
    private IccidModifyRecordsDOMapper iccidModifyRecordsDOMapper;

    @Resource
    private IccidModifyBatchRecordsDOMapper iccidModifyBatchRecordsDOMapper;

    @Resource
    private PIVIUnicomService unicomService;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;


    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private IccidSingleModifyService iccidSingleModifyService;

    @Resource
    private AppDCuSingleRenewalService appDCuSingleRenewalService;

    @Resource
    private OrderCheckService orderCheckService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Override
    public CommonResult<List<Long>> getModifyRecordIds(Long batchNo) {
        List<Long> modifyRecordIdList = getWaitedModifyRecordsByStatus(batchNo);
        log.info("获取待修改ID的列表, 待修改的总数量：{}", modifyRecordIdList.size());
        return CommonResult.success(modifyRecordIdList);
    }

    /**
     * 根据修改状态获取待修改记录
     *
     * @return 返回符合指定条件的修改记录列表
     */
    private List<Long> getWaitedModifyRecordsByStatus(Long batchNo) {
        LambdaQueryWrapper<IccidModifyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyRecordsDO::getModifyStatus, ModifyStatusEnum.IN_PROGRESS.getStatus())
                .eq(IccidModifyRecordsDO::getIsDeleted, false)
                .eq(IccidModifyRecordsDO::getModifyNo, batchNo)
                .select(IccidModifyRecordsDO::getId);
        List<IccidModifyRecordsDO> records = iccidModifyRecordsDOMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>();
        }
        return records.stream().map(IccidModifyRecordsDO::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Integer> processModifyRecords(IccidModifyBatchDTO iccidModifyBatchDTO) {
        // 校验入参
        if (Objects.isNull(iccidModifyBatchDTO) || CollUtil.isEmpty(iccidModifyBatchDTO.getRecordIds())) {
            log.info("job 批量处理iccid修改记录, 输入参数为空, iccidModifyBatchDTO:{}", iccidModifyBatchDTO);
            return CommonResult.success(0);
        }

        // 获取待处理的记录
        List<Long> recordIds = iccidModifyBatchDTO.getRecordIds();
        log.info("job 批量处理iccid修改记录, recordIds的数量:{}, endFlag:{}", recordIds.size(), iccidModifyBatchDTO.getEndFlag());
        List<IccidModifyRecordsDO> recordsList = getRecordsByIdList(recordIds);
        if (CollUtil.isEmpty(recordsList)) {
            log.info("job 批量处理iccid修改记录, 查询的iccid总数量：{}, 无需处理", recordsList.size());
            return CommonResult.success(0);
        }

        // 调用UNICOM接口 & 不同情况进行落库
        updateRecordsAndPiviPackage(recordsList);

        //处理最后一批数据 update batch_records.deal_status为COMPLETED
        if (Boolean.TRUE.equals(iccidModifyBatchDTO.getEndFlag())) {
            updateBatchRecordStatus(recordsList.get(0).getModifyNo());
        }
        return CommonResult.success(recordsList.size());
    }

    /**
     * 调用UNICOM接口 & 不同情况进行落库
     * 1. 更新modify_recordsDO 的modify_status，error_desc
     * 2. 成功 更新pivi_package中的iccid
     * 3. iccid修改成功 落库 t_manual_modify_log
     *
     * @param recordsList
     */
    private void updateRecordsAndPiviPackage(List<IccidModifyRecordsDO> recordsList) {
        List<IccidModifyRecordsDO> recordsDOList = new ArrayList<>();
        List<IccidModifyRecordsDO> manualModifyLogDOList = new ArrayList<>();
        // 待续费列表
        List<PIVIPackageDO> preRenewalList = new ArrayList<>();

        // extract vin
        List<String> carVinList = recordsList.stream().filter(Objects::nonNull) // 过滤掉 null 对象
                .map(IccidModifyRecordsDO::getCarVin).collect(Collectors.toList());

        // vinList 查询pivi_package 返回的map key是carvin value是piviPackageDO
        List<PIVIPackageDO> piviPackageDOList = piviPackageDOMapper.selectList(new LambdaQueryWrapperX<PIVIPackageDO>().in(PIVIPackageDO::getVin, carVinList).eq(PIVIPackageDO::getIsDeleted, false));
        Map<String, PIVIPackageDO> carVinMap = new HashMap<>();
        if (CollUtil.isNotEmpty(piviPackageDOList)) {
            carVinMap = piviPackageDOList.stream().collect(Collectors.toMap(PIVIPackageDO::getVin, Function.identity()));
        }

        for (IccidModifyRecordsDO record : recordsList) {
            // 调用UNICOM接口
            String newIccid = record.getModifyAfterIccid();
            UnicomRespVO simCardInfo = unicomService.getSimCardInfo(newIccid);
            log.info("根据newIccid:{}, 查询联通结果:{}", newIccid, simCardInfo);

            if (StrUtil.isNotBlank(simCardInfo.getQueryResult())) {
                // a. 查询时异常
                // 插入 t_iccid_modify_records.error_desc 记录错误为系统异常
                log.info("a.根据newIccid:{}, 查询联通结果异常, getQueryResult():{}", newIccid, simCardInfo.getQueryResult());
                buildAndInsertFailureRecord(record, SYSTEM_ERROR.getDesc(), recordsDOList);
            } else if (!UnicomResultEnum.SUCCESS.getDesc().equals(simCardInfo.getResponseDesc())) {
                // b.simCardInfo.getResponseDesc 不等于 UnicomResultEnum.SUCCESS.getDesc
                // 插入 t_iccid_modify_records.error_desc 记录错误 responseDesc的值
                log.info("b.根据newIccid:{}, 查询联通结果为 不成功 , errorDesc:{}", newIccid, simCardInfo.getResponseDesc());
                buildAndInsertFailureRecord(record, simCardInfo.getResponseDesc(), recordsDOList);
            } else {
                UnicomRespData unicomRespData = simCardInfo.getUnicomRespData();
                if (Objects.isNull(unicomRespData) || Objects.isNull(unicomRespData.getSimCardInfo()) || StrUtil.isBlank(unicomRespData.getSimCardInfo().getIccid())) {
                    // c.unicomRespData.getSimCardInfo().getIccid()查不到
                    log.info("c.根据newIccid:{}, 查询联通结果成功，但newIccid查不到", newIccid);
                    buildAndInsertFailureRecord(record, SYSTEM_ERROR.getDesc(), recordsDOList);
                } else { // d.成功情况
                    PIVIPackageDO piviPackageDOByCarVin = carVinMap.get(record.getCarVin());
                    // 1.更新 t_pivi_package 中的 iccid
                    piviPackageDOByCarVin.setIccid(newIccid);
                    piviPackageDOByCarVin.setUpdatedTime(LocalDateTime.now());
                    executeUpdateCuExpireDate(record, piviPackageDOByCarVin, recordsDOList, preRenewalList, manualModifyLogDOList);
                }
            }
        }
        iccidModifyRecordsDOMapper.updateBatch(recordsDOList);
        if (CollUtil.isNotEmpty(preRenewalList)) {
            piviPackageDOMapper.updateBatch(preRenewalList);
            //更新完之后插入log记录表
            manualModifyLogDOService.recordLogIccIdModifyList(manualModifyLogDOList);
        }
    }

    /**
     * 执行更新ICCID的到期日期，并根据结果构建相应的记录
     *
     * @param recordsDO 单个ICCID修改记录对象
     * @param piviPackageDOByCarVin 根据车架号查询到的PIVI套餐信息
     * @param recordsDOList 所有ICCID修改记录的列表
     */
    private void executeUpdateCuExpireDate(IccidModifyRecordsDO recordsDO, PIVIPackageDO piviPackageDOByCarVin,
                                           List<IccidModifyRecordsDO> recordsDOList, List<PIVIPackageDO> preRenewalList,
                                           List<IccidModifyRecordsDO> manualModifyLogDOList) {
        CommonResult<UnicomRespVO> result = iccidSingleModifyService.updateCuExpireDate(piviPackageDOByCarVin);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            buildAndInsertFailureRecord(recordsDO, result.getMsg(), recordsDOList);
        } else if (UnicomResultEnum.SUCCESS.getDesc().equals(result.getData().getResponseDesc())) {
            buildAndInsertSuccessRecord(recordsDO, recordsDOList);
            preRenewalList.add(piviPackageDOByCarVin);
            manualModifyLogDOList.add(recordsDO);
        } else {
            buildAndInsertFailureRecord(recordsDO, result.getData().getResponseDesc(), recordsDOList);
        }
    }


    private void buildAndInsertSuccessRecord(IccidModifyRecordsDO iccidModifyRecordsDO, List<IccidModifyRecordsDO> recordsDOList) {
        iccidModifyRecordsDO.setModifyStatus(ModifyStatusEnum.SUCCESS.getStatus());
        recordsDOList.add(iccidModifyRecordsDO);
    }

    private void buildAndInsertFailureRecord(IccidModifyRecordsDO iccidModifyRecordsDO, String errorDesc, List<IccidModifyRecordsDO> recordsDOList) {
        iccidModifyRecordsDO.setErrorDesc(errorDesc);
        iccidModifyRecordsDO.setModifyStatus(ModifyStatusEnum.FAILURE.getStatus());
        recordsDOList.add(iccidModifyRecordsDO);
    }


    /**
     * 更新 批量修改记录状态
     *
     * @param modifyNo
     */
    private void updateBatchRecordStatus(Long modifyNo) {
        IccidModifyBatchRecordsDO batchRecordsDO = iccidModifyBatchRecordsDOMapper.selectOne(
                new LambdaQueryWrapper<IccidModifyBatchRecordsDO>()
                        .eq(IccidModifyBatchRecordsDO::getBatchNo, modifyNo)
                        .eq(IccidModifyBatchRecordsDO::getIsDeleted, false)
        );
        log.info("更新ICCID批量修改记录状态, batchNo:{}, batchRecordsDO:{}", modifyNo, batchRecordsDO);
        if (Objects.isNull(batchRecordsDO)) {
            log.info("更新ICCID批量修改记录状态, 查询结果为空");
            return;
        }
        batchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        iccidModifyBatchRecordsDOMapper.updateById(batchRecordsDO);
    }

    /**
     * 根据id列表获取记录
     *
     * @param recordIds id列表
     * @return
     */
    private List<IccidModifyRecordsDO> getRecordsByIdList(List<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<IccidModifyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IccidModifyRecordsDO::getId, recordIds).eq(IccidModifyRecordsDO::getModifyStatus, ModifyStatusEnum.IN_PROGRESS.getStatus()).eq(IccidModifyRecordsDO::getIsDeleted, false);
        return iccidModifyRecordsDOMapper.selectList(queryWrapper);
    }
}
