package com.jlr.ecp.subscription.api.vininit.config;

import com.jlr.ecp.subscription.api.vininit.constants.VehicleProcessConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 车辆处理配置类
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class VehicleProcessConfig {
    
    /**
     * 车辆处理专用线程池
     *
     * @return 线程池执行器
     */
    @Bean("vehicleProcessExecutor")
    public ThreadPoolTaskExecutor vehicleProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(VehicleProcessConstants.ThreadPoolConfig.CORE_POOL_SIZE);
        
        // 最大线程数
        executor.setMaxPoolSize(VehicleProcessConstants.ThreadPoolConfig.MAX_POOL_SIZE);
        
        // 队列容量
        executor.setQueueCapacity(VehicleProcessConstants.ThreadPoolConfig.QUEUE_CAPACITY);
        
        // 线程名前缀
        executor.setThreadNamePrefix(VehicleProcessConstants.ThreadPoolConfig.THREAD_NAME_PREFIX);
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(VehicleProcessConstants.ThreadPoolConfig.KEEP_ALIVE_SECONDS);
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();

        log.info("车辆处理线程池初始化完成: CPU核心数={}, corePoolSize={}, maxPoolSize={}, queueCapacity={}",
            VehicleProcessConstants.ThreadPoolConfig.getCpuCount(),
            VehicleProcessConstants.ThreadPoolConfig.CORE_POOL_SIZE,
            VehicleProcessConstants.ThreadPoolConfig.MAX_POOL_SIZE,
            VehicleProcessConstants.ThreadPoolConfig.QUEUE_CAPACITY);

        return executor;
    }
}
