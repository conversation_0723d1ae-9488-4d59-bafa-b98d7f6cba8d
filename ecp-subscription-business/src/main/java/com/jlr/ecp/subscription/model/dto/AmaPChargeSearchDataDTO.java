package com.jlr.ecp.subscription.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AmaPChargeSearchDataDTO {
    private String cid;

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("cus_order_id")
    private String cusOrderId;

    @JsonProperty("quantity")
    private Integer quantity;

    @JsonProperty("unit_price")
    private Double unitPrice;

    @JsonProperty("hit_strategy_no")
    private String hitStrategyNo;

    @JsonProperty("hit_strategy_info")
    private String hitStrategyInfo;

    private Integer amount;

    @JsonProperty("finish_time")
    private String finishTime;

    @JsonProperty("charge_records")
    private List<AmaPChargeSearchRecordDTO> chargeRecords;
}
