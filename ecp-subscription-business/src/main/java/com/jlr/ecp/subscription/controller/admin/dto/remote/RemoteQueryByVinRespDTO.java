package com.jlr.ecp.subscription.controller.admin.dto.remote;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "根据vin查询TSDP信息DTO")
public class RemoteQueryByVinRespDTO {
    @Schema(description = "设备状态")
    private String deviceState;

    @Schema(description = "客户绑定信息")
    private BoundToCustomer boundToCustomer;

    @Schema(description = "最早过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime earliestExpiryDate;

    @Schema(description = "订阅服务列表")
    private List<Subscription> subscriptions;

    @Schema(description = "车辆基本信息")
    private VehicleInformation vehicleInformation;

    @Schema(description = "车辆唯一标识码")
    private String vin;


    @Data
    @Schema(description = "客户绑定信息")
    public static class BoundToCustomer {
        @Schema(description = "客户邮箱")
        private String email;

        @Schema(description = "客户名")
        private String firstName;

        @Schema(description = "姓氏")
        private String surname;

        @Schema(description = "联系电话")
        private String phone;

        @Schema(description = "语言设置")
        private String language;

        @Schema(description = "地址信息")
        private Address address;
    }

    @Data
    @Schema(description = "地址信息")
    public static class Address {
        @Schema(description = "街道1")
        private String street1;

        @Schema(description = "街道2")
        private String street2;

        @Schema(description = "县/州")
        private String countyOrState;

        @Schema(description = "城市")
        private String city;

        @Schema(description = "邮编")
        private String zip;

        @Schema(description = "国家")
        private String country;
    }

    @Data
    @Schema(description = "订阅服务")
    public static class Subscription {
        @Schema(description = "服务包名称")
        private String servicePackage;

        @Schema(description = "过期时间")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime expiryDate;

        @Schema(description = "服务状态")
        private String serviceState;

        @Schema(description = "服务列表")
        private List<String> services;
    }

    @Data
    @Schema(description = "车辆基本信息")
    public static class VehicleInformation {
        @Schema(description = "车辆唯一标识码")
        private String vin;

        @Schema(description = "制造商")
        private String make;

        @Schema(description = "昵称")
        private String nickName;

        @Schema(description = "品牌")
        private String brand;

        @Schema(description = "车型")
        private String model;

        @Schema(description = "车牌号")
        private String registrationNumber;
    }
}
