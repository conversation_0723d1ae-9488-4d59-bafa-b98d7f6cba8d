package com.jlr.ecp.subscription.service.businesserror;

import com.jlr.ecp.framework.common.util.monitor.TracerUtils;
import com.jlr.ecp.subscription.dal.dataobject.businesserror.InterBusiErrorLogDO;
import com.jlr.ecp.subscription.dal.mysql.businesserror.InterBusiErrorLogMapper;
import com.jlr.ecp.subscription.enums.busierror.BusiErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 业务异常日志表服务接口实现
 *
 * <AUTHOR>
 * @since 2024-02-05 13:46:20
 * @description 由 Mybatisplus Code Generator 创建
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InterBusiErrorLogServiceImpl implements InterBusiErrorLogService {
    private final InterBusiErrorLogMapper interBusiErrorLogMapper;

    @Override
    public InterBusiErrorLogDO saveOrUpdateErrorLog(String businessId, String response, String businessParams, BusiErrorEnum errorEnum) {
        InterBusiErrorLogDO errorLog = interBusiErrorLogMapper.selectOne(InterBusiErrorLogDO::getBusinessId, businessId);
        if ( errorLog != null){
            errorLog.setErrorMessage(response);
            errorLog.setBusinesParams(businessParams);
            errorLog.setBusinessType(errorEnum.getCode());
            errorLog.setErrorCount(errorLog.getErrorCount() + 1);
            interBusiErrorLogMapper.updateById(errorLog);
            return errorLog;
        }else {
            InterBusiErrorLogDO errorLogDO = new InterBusiErrorLogDO();
            errorLogDO.setTraceId(TracerUtils.getTraceId());
            errorLogDO.setBusinessId(businessId);
            errorLogDO.setErrorMessage(response);
            errorLogDO.setBusinesParams(businessParams);
            errorLogDO.setBusinessType(errorEnum.getCode());
            errorLogDO.setErrorCount(1);
            interBusiErrorLogMapper.insert(errorLogDO);
            return errorLogDO;
        }
    }

}