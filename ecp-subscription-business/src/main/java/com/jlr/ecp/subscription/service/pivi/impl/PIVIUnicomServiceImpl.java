package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.annotation.ApiLimitation;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.*;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomTodoOrderDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomTodoOrderMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fufil.CallActivationStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.unicom.*;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.SimCardInfoDTO;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RateIntervalUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestClientException;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.CALL_UNICOM_RENEWAL_ERROR;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR;

/**
 * Unicom续期接口实现
 */
@Service
@Slf4j
public class PIVIUnicomServiceImpl implements PIVIUnicomService {

    private static final String TEST_REDIS_KEY = "unicom:return:test:key";



    @Value("${unicom.industryCode}")
    private String industryCode;

    @Value("${unicom.version}")
    private String version;

    @Value("${unicom.appKey}")
    private String appKey;

    @Value("${unicom.productId}")
    private String productId;


    @Value("${unicom.url}")
    private String url;

    @Resource
    private RedisService redisService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private VcsOrderFulfilmentCallMapper callMapper;

    private static final Integer GROUP_SIZE = 2;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleMapper;

    @Resource
    private UnicomTodoOrderMapper unicomTodoOrderMapper;

    @Resource
    private SubscriptionServiceLogMapper subscriptionServiceLogMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private SOTAService sotaService;

    @Resource
    private Redisson redisson;
    @Resource
    private Environment environment;

    private static final String REAL_NAME_REDIS_KEY = "real-name-redis-key:";

    /**
     * 订阅、修改联通到期时间
     * @param message
     * @return
     */
    @Override
    public Boolean callUnicomService(FufilmentMessage message,String fulfilmentId) {
        boolean callFlag = false;
        PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(message.getVin());
        String iccid = "";
        if(serviceDO != null ) {
            iccid = serviceDO.getIccid() == null?iccid:serviceDO.getIccid();
        }else {
            //TODO 需要将该情况存入saveOrUpdateErrorLog信息，记录业务数据不匹配的错误，然后返回失败
            log.error("getSimCarInfoByCarVin联通查询CarVin:{} ICCID配置信息不存在",message.getVin());
        }
        //组装参数
        UnicomReqVO requestData = buildCallUnicomParam(message, iccid,UnicomRequestTypeEnum.ORDER.getType());
        ObjectMapper mapper = new ObjectMapper();
        //解析参数
        try {
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String requestBodyJson = mapper.writeValueAsString(requestData);
            CommonResult<String> result = concurrentCallUnicomRenew(message, iccid);
            if (!result.isSuccess()) {
                return false;
            }
            String body = result.getData();
            UnicomRespVO unicomRespVO =  mapper.readValue(body, UnicomRespVO.class);
            //判断请求
            callFlag = UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc());
            Integer status = CallActivationStatusEnum.FAILED.getStatus();
            if(callFlag){
                status = CallActivationStatusEnum.SUCCESS.getStatus();
            }
            //保存call记录
            handleCall(fulfilmentId, requestBodyJson, body , status, message.getTenantId());
            return callFlag;
        }catch (Exception e){
            log.info("联通车辆卡订购异常:{}", e);
            throw new RestClientException("Unicom order call exception");
        }
    }

    /**
     * 手动续费联通服务
     *
     * @param carVin 车辆识别号（Vehicle Identification Number），用于唯一标识车辆
     * @param endDate 续费结束日期，指定续费服务到何时结束
     * @return 返回联通API的响应对象，包含续费结果信息如果遇到异常或无效数据，返回null
     */
    @Override
    public CommonResult<UnicomRespVO> unicomManualRenewal(String carVin, LocalDateTime endDate) {
        try {
            FufilmentMessage message = buildManualFufilmentMessage(carVin, endDate);
            String iccid = getIccidByCarVin(carVin);
            if (StringUtils.isBlank(iccid)) {
                log.info("手动续费联通服务Iccid为空, carVin:{}", carVin);
                return CommonResult.error(ErrorCodeConstants.APPDUC_ICCID_EMPTY);
            }
            ObjectMapper mapper = new ObjectMapper();
            CommonResult<String> result = concurrentCallUnicomRenew(message, iccid);
            if (!result.isSuccess()) {
                return CommonResult.error(result.getCode(), result.getMsg());
            }
            String body = result.getData();
            log.info("手动续费联通服务, body:{}, carVin:{}", body, carVin);
            if (StringUtils.isBlank(body)) {
                log.info("手动续费联通服务返回结果为空, carVin:{}, endDate:{}", carVin, endDate);
                return CommonResult.success(new UnicomRespVO());
            }
            return CommonResult.success(mapper.readValue(body, UnicomRespVO.class));
        } catch (Exception e) {
            log.info("手动续费联通服务异常:{}", e.getMessage());
        }
        return CommonResult.success(null);
    }

    /**
     * 手动续费联通服务(job补偿)
     *
     * @param carVin 车辆识别号（Vehicle Identification Number），用于唯一标识车辆
     * @param endDate 续费结束日期，指定续费服务到何时结束
     * @param cusOrderId 订单号，用于标识本次续费操作，方便后续查询续费状态
     * @return 返回联通API的响应对象，包含续费结果信息如果遇到异常或无效数据，返回null
     */
    @Override
    public CommonResult<UnicomRespVO> unicomManualRenewalJob(String carVin, LocalDateTime endDate, String cusOrderId) {
        try {
            FufilmentMessage message = buildManualFufilmentMessageJob(carVin, endDate, cusOrderId);
            String iccid = getIccidByCarVin(carVin);
            if (StringUtils.isBlank(iccid)) {
                log.info("手动续费联通服务(job补偿)Iccid为空, carVin:{}", carVin);
                return CommonResult.error(ErrorCodeConstants.APPDUC_ICCID_EMPTY);
            }
            ObjectMapper mapper = new ObjectMapper();
            CommonResult<String> result = concurrentCallUnicomRenew(message, iccid);
            if (!result.isSuccess()) {
                return CommonResult.error(result.getCode(), result.getMsg());
            }
            String body = result.getData();
            log.info("手动续费联通服务(job补偿), body:{}, carVin:{}", body, carVin);
            if (StringUtils.isBlank(body)) {
                log.info("手动续费联通服务(job补偿)回结果为空, carVin:{}, cusOrderId:{}, endDate:{}", carVin, cusOrderId, endDate);
                return CommonResult.success(new UnicomRespVO());
            }
            return CommonResult.success(mapper.readValue(body, UnicomRespVO.class));
        } catch (Exception e) {
            log.info("手动续费联通服务(job补偿)服务异常:{}, carVin:{}", e.getMessage(), carVin);
        }
        return CommonResult.success(null);
    }

    /**
     * 联通车辆查询订购
     *
     * @param message 履约消息对象，包含车辆信息
     * @param iccid 联通卡的ICCID，用于识别特定的SIM卡
     * @return 返回联通API的响应体字符串
     */
    @ApiLimitation(tokenBucketName="callUnicomRequest", time = 1, timeUnit = RateIntervalUnit.SECONDS, limitCount = 15)
    public String callUnicomRequest(FufilmentMessage message, String iccid) {
        try {
            //组装参数
            UnicomReqVO requestData = buildCallUnicomParam(message, iccid, UnicomRequestTypeEnum.ORDER.getType());
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String requestBodyJson = mapper.writeValueAsString(requestData);
            log.info("联通车辆查询订购 requestBodyJson:{}",requestBodyJson);
            // 使用HttpUtil.post发送请求
            String body = HttpUtil.post(url, requestBodyJson);
            log.info("联通车辆订购结果:{}", body);
            return body;
        } catch (Exception e) {
            log.info("联通车辆查询订购异常:", e);
        }
        return "";
    }

    private void handleCall(String fulfilmentId,String req,String resp ,Integer status,Long tenantId){
        VcsOrderFufilmentCall fulfilmentCall = new VcsOrderFufilmentCall();
        fulfilmentCall.setRequestParam(req);
        fulfilmentCall.setRequestResult(resp);
        fulfilmentCall.setActivationStatus(status);
        addAppDFulfilmentRecord(fulfilmentId, fulfilmentCall, tenantId);
    }


    @Override
    public Boolean cancelUnicomService(CancelMessage message,String fulfilmentId) {

        PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(message.getVin());
        String iccid = "";
        if(serviceDO != null ) {
            iccid = serviceDO.getIccid() == null?iccid:serviceDO.getIccid();
        }else {
            log.info("getSimCarInfoByCarVin 联通CarVin:{} ICCID配置信息不存在",message.getVin());
        }
        boolean callFlag = false;
        //组装参数
        UnicomReqVO requestData = buildCancelUnicomParam(message.getVcsOrderCode(), iccid,UnicomRequestTypeEnum.CANCEL.getType());
        ObjectMapper mapper = new ObjectMapper();
        //调用
        UnicomRespVO unicomRespVO = null;
        //解析参数
        try {
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String requestBodyJson = mapper.writeValueAsString(requestData);
            log.info("联通车辆取消 requestBodyJson:{}",requestBodyJson);
            // 使用HttpUtil.post发送请求
            String body = HttpUtil.post(url, requestBodyJson);
            log.info("联通车辆取消:{}", body);
            unicomRespVO = mapper.readValue(body, UnicomRespVO.class);
            callFlag = UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc());
            Integer status = CallActivationStatusEnum.FAILED.getStatus();;
            if(callFlag){
                status = CallActivationStatusEnum.SUCCESS.getStatus();
            }
            //保存call记录
            handleCall( fulfilmentId, requestBodyJson, body , status, message.getTenantId());
            return callFlag;
        }catch (Exception e){
            log.error("联通车辆取消异常:",e);
        }
        return callFlag;
    }

    @Override
    public Boolean newCancelUnicomService(CancelMessage cancelMessage, String fulfilmentId) {
        //联通取消订单成功
        Boolean aBoolean = false;
        log.info("联通取消订单成功, cancelMessage={}", cancelMessage);

        // 存在激活失败的服务，不用查询log表
        if (Objects.nonNull(cancelMessage.getRefreshBeforeDate())) {
            //上次的服务到期时间
            LocalDateTime refreshBeforeDate = cancelMessage.getRefreshBeforeDate();
            aBoolean = cancelResult(cancelMessage, refreshBeforeDate);
        }else {
            //查询serviceLog表，取出refresh之前的之间 根据fulfilmentId
            SubscriptionServiceLogDO logDO = subscriptionServiceLogMapper
                    .findLastLogByFulfilmentId(cancelMessage.getFufilmentId());
            if(logDO != null){
                //上次的服务到期时间
                LocalDateTime refreshBeforeDate = logDO.getRefreshBeforeDate();
                //手动设置的服务到期时间
                aBoolean = cancelResult(cancelMessage, refreshBeforeDate);
            }else {
                log.error("该履约没有服务log记录,cancelMessage={}",cancelMessage);
            }
        }
        return aBoolean;
    }

    /**
     * 处理取消服务的结果
     * 此方法旨在根据取消请求的细节和当前时间，决定是重新续约还是直接取消服务
     *
     * @param cancelMessage 包含取消服务所需信息的对象
     * @param refreshBeforeDate 用于计算的参考日期，通常为上次服务到期日
     * @return Boolean 表示取消或续约操作的结果
     */
    private Boolean cancelResult(CancelMessage cancelMessage, LocalDateTime refreshBeforeDate) {
        Boolean aBoolean;
        //手动设置的服务到期时间
        LocalDateTime serviceEndDate = cancelMessage.getServiceEndDate();
        //当前时间
        LocalDateTime startDate = LocalDateTime.now();
        if (startDate.isBefore(refreshBeforeDate)) {
            startDate = refreshBeforeDate;
        }
        //手动设置的服务到期时间> 上次服务到期
        if (serviceEndDate.isAfter(refreshBeforeDate)) {
            log.info("===联通重新续约开始===");
            FufilmentMessage fufilmentMessage = new FufilmentMessage();
            fufilmentMessage.setServiceBeginDate(startDate);
            fufilmentMessage.setServiceEndDate(serviceEndDate);
            fufilmentMessage.setTenantId(cancelMessage.getTenantId());
            fufilmentMessage.setVin(cancelMessage.getVin());
            fufilmentMessage.setVcsOrderCode(cancelMessage.getVcsOrderCode() + "-R");
            log.info("===联通重新续约入参fufilmentMessage:{}", fufilmentMessage);
            aBoolean = callUnicomService(fufilmentMessage, cancelMessage.getFufilmentId());
            log.info("====联通重新续约结束===");
            if (aBoolean) {
                //插入todoOrder去等待取消
                saveCancelTodo(cancelMessage);
            }
        } else {
            //直接退单
            aBoolean = cancelUnicomService(cancelMessage, cancelMessage.getFufilmentId());
        }
        return aBoolean;
    }

    @Override
    public Boolean saveCancelTodo(CancelMessage message) {
        PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(message.getVin());
        String iccid = "";
        if(serviceDO != null ) {
            iccid = serviceDO.getIccid() == null?iccid:serviceDO.getIccid();
        }else {
            log.info("saveCancelTodo 联通CarVin:{} ICCID配置信息不存在",message.getVin());
            return false;
        }
        UnicomTodoOrderDO unicomTodoOrderDO = new UnicomTodoOrderDO();
        unicomTodoOrderDO.setIccid(iccid);
        unicomTodoOrderDO.setRequestType(UnicomRequestTypeEnum.CANCEL.getType());
        unicomTodoOrderDO.setExtBookId(message.getVcsOrderCode());
        unicomTodoOrderDO.setProductId(productId);
        unicomTodoOrderDO.setBookStatus(UnicomBookStatusEnum.CANCEL.getType());
        unicomTodoOrderDO.setStatus(UnicomTodoStatusEnum.WAITING.getCode());
        unicomTodoOrderDO.setRequestCount(0);
        unicomTodoOrderDO.setValidExtBookId(message.getVcsOrderCode()+"-R");
        unicomTodoOrderDO.setValidBookStatus(UnicomRequestTypeEnum.ORDER.getType());
        int insert = unicomTodoOrderMapper.insert(unicomTodoOrderDO);
        return insert>0;
    }

    @Override
    public Integer executeTodoOrder(UnicomTodoOrderVO unicomTodoOrderVO) {
        int i = 0;
        log.info("executeTodoOrder 入参:{}",unicomTodoOrderVO);
        String iccid = unicomTodoOrderVO.getIccid();
        UnicomTodoOrderDO unicomTodoOrderDO = unicomTodoOrderMapper
                .selectOne(new LambdaQueryWrapperX<UnicomTodoOrderDO>()
                        .eq(UnicomTodoOrderDO::getId,unicomTodoOrderVO.getId()));
        //先进行查询该订单是否已经激活
        UnicomRespVO unicomRespVO = getSimCardInfo(iccid);
        if(unicomRespVO!=null && UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc())
            &&unicomRespVO.getUnicomRespData() !=null && CollUtil.isNotEmpty(unicomRespVO.getUnicomRespData().getProductBookInfoList())){
            //请求查询成功之后 过滤查询订单验证
            List<ProductBookInfo> productBookInfoList = unicomRespVO.getUnicomRespData().getProductBookInfoList();
            ProductBookInfo productBookInfo = productBookInfoList.stream().filter(vo ->
                    vo.getExternalBookId().equals(unicomTodoOrderVO.getValidExtBookId()))
                    .findFirst().orElse(null);
            if(productBookInfo != null){
                log.info("查询出先激活的履约服务productBookInfo:{}",productBookInfo);
                //发现是激活状态说明可以发起退订，否则不做处理
                if(UnicomBookStatusEnum.ACTIVE.getType().equals(productBookInfo.getBookStatus())){
                    //调用成功发起退款申请
                    UnicomReqVO requestData = buildCancelUnicomParam(unicomTodoOrderVO.getExtBookId(), iccid, UnicomRequestTypeEnum.CANCEL.getType());
                    ObjectMapper mapper = new ObjectMapper();
                    //调用
                    UnicomRespVO cancelRespVO = null;
                    //解析参数
                    try {
                        //调用得时候请求次数+1
                        unicomTodoOrderDO.setRequestCount(unicomTodoOrderDO.getRequestCount()+1);
                        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                        String requestBodyJson = mapper.writeValueAsString(requestData);
                        log.info("联通车辆取消 requestBodyJson:{}",requestBodyJson);
                        // 使用HttpUtil.post发送请求
                        String body = HttpUtil.post(url, requestBodyJson);
                        log.info("联通车辆取消:{}", body);
                        cancelRespVO = mapper.readValue(body, UnicomRespVO.class);
                        //调用成功 修改unicomTodoOrder记录
                        setOrderStatus(cancelRespVO, unicomTodoOrderDO);
                    }catch (Exception e){
                      log.error("联通车辆取消异常:",e);
                        unicomTodoOrderDO.setStatus(UnicomTodoStatusEnum.FAIL.getCode());
                    }
                }
            }else {
                log.info("unicomTodoOrder的校验单号异常ValidExtBookId:{}",unicomTodoOrderVO.getValidExtBookId());
                unicomTodoOrderDO.setStatus(UnicomTodoStatusEnum.FAIL.getCode());
            }
            i = unicomTodoOrderMapper.updateById(unicomTodoOrderDO);
        }
        return i;
    }

    /**
     * 根据取消订单的响应结果设置订单状态
     * 此方法用于更新订单的状态，以反映取消操作的结果
     *
     * @param cancelRespVO 取消订单的响应对象，包含取消操作的结果信息
     * @param unicomTodoOrderDO 订单对象，其状态将根据取消操作的结果进行更新
     */
    private static void setOrderStatus(UnicomRespVO cancelRespVO, UnicomTodoOrderDO unicomTodoOrderDO) {
        if(UnicomResultEnum.SUCCESS.getDesc().equals(cancelRespVO.getResponseDesc())){
            unicomTodoOrderDO.setStatus(UnicomTodoStatusEnum.COMPLETE.getCode());
        }else {
            unicomTodoOrderDO.setStatus(UnicomTodoStatusEnum.FAIL.getCode());
        }
    }

    @Override
    public CommonResult<UnicomRnrQueryVO> getRnrQuery(String carVin) {
        if (!CarVinUtil.checkVinFormat(carVin)) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        UnicomRnrQueryVO unicomRnrQueryVO = new UnicomRnrQueryVO();
        unicomRnrQueryVO.setCarVin(carVin);
        String iccid = getIccid(carVin);
        if (StringUtils.isEmpty(iccid)) {
            unicomRnrQueryVO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
            unicomRnrQueryVO.setQueryStatusTxt(UnicomRnrQueryStatusEnum.FAILED.getDesc());
            unicomRnrQueryVO.setErrorDesc(ErrorCodeConstants.RNR_QUERY_ICCID_NOT_FOUND.getMsg());
            return CommonResult.success(unicomRnrQueryVO);
        }

        UnicomRespVO unicomRespVO = getSimCardInfo(iccid);

        if (unicomRespVO == null || !UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc()) || StrUtil.isNotBlank(unicomRespVO.getQueryResult()) || unicomRespVO.getUnicomRespData() == null) {
            setQueryFailed(unicomRnrQueryVO, unicomRespVO);
            return CommonResult.success(unicomRnrQueryVO);
        }

        SimCardInfo simCardInfo = unicomRespVO.getUnicomRespData().getSimCardInfo();
        if (simCardInfo == null || StringUtils.isEmpty(simCardInfo.getRealnameFlag())) {
            setQueryFailed(unicomRnrQueryVO, unicomRespVO);
            return CommonResult.success(unicomRnrQueryVO);
        }

        handleRealNameTime(unicomRnrQueryVO, unicomRespVO,simCardInfo);

        unicomRnrQueryVO.setQueryStatus(UnicomRnrQueryStatusEnum.SUCCESS.getType());
        unicomRnrQueryVO.setQueryStatusTxt(UnicomRnrQueryStatusEnum.SUCCESS.getDesc());
        unicomRnrQueryVO.setCardState(simCardInfo.getCardState());
        unicomRnrQueryVO.setIccid(simCardInfo.getIccid());
        unicomRnrQueryVO.setCardStateTxt(UnicomCardStateEnum.getDescriptionByCode(simCardInfo.getCardState()));
        unicomRnrQueryVO.setRealNameFlag(Integer.parseInt(simCardInfo.getRealnameFlag()));
        unicomRnrQueryVO.setRealNameFlagTxt(UnicomRealnameFlagEnum.getDescriptionByCode(unicomRnrQueryVO.getRealNameFlag()));

        return CommonResult.success(unicomRnrQueryVO);
    }

    //处理最新实名的时间
    private void handleRealNameTime(UnicomRnrQueryVO unicomRnrQueryVO, UnicomRespVO unicomRespVO,SimCardInfo simCardInfo) {
        List<UnicomRealNameOrder> realNameOrderList = unicomRespVO.getUnicomRespData().getRealNameOrderList();
        unicomRnrQueryVO.setRealNameTime("-");
        if(UnicomRealnameFlagEnum.TRUE.getCode().equals(simCardInfo.getRealnameFlag()) && CollUtil.isNotEmpty(realNameOrderList)){
            //过滤realNameOrderList时间倒叙排，取值状态为active的第一条
            UnicomRealNameOrder unicomRealNameOrder = realNameOrderList.stream().filter(vo -> UnicomBookStatusEnum.ACTIVE.getType().equals(vo.getOrderType())).max(Comparator.comparing(UnicomRealNameOrder::getOrderTime)).orElse(null);
            log.info("处理realname时间过滤内容unicomRealNameOrder:{}",unicomRealNameOrder);
            if(unicomRealNameOrder == null){
                unicomRnrQueryVO.setRealNameTime(UnicomRealnameFlagEnum.FAIL.getDesc());
            }else {
                //将unicomRealNameOrder.getOrderTime()时间戳字符串转换成yyyy/MM/dd HH:mm:ss格式的字符串
                unicomRnrQueryVO.setRealNameTime(TimeFormatUtil.timeToStringByFormat(TimeFormatUtil.stringToLocalDate(unicomRealNameOrder.getOrderTime(),TimeFormatUtil.formatter_4), TimeFormatUtil.formatter_6));
            }
        }

    }

    @Override
    public CommonResult<UnicomRespVO> unicomRenewalByIccid(FufilmentMessage message, String iccid) {
        try {
            if (StringUtils.isBlank(iccid)) {
                log.info("BAU同步联通到期时间Iccid为空, carVin:{}", message.getVin());
                return CommonResult.error(ErrorCodeConstants.APPDUC_ICCID_EMPTY);
            }
            ObjectMapper mapper = new ObjectMapper();
            CommonResult<String> result = concurrentCallUnicomRenew(message, iccid);
            if (!result.isSuccess()) {
                return CommonResult.error(result.getCode(), result.getMsg());
            }
            String body = result.getData();
            return CommonResult.success(mapper.readValue(body, UnicomRespVO.class));
        } catch (Exception e) {
            log.info("BAU同步联通到期时间异常:{}", e.getMessage());
            return CommonResult.error(ErrorCodeConstants.SYNC_CU_ERROR);
        }
    }

    private String getIccid(String carVin) {
        PIVIPackageDO serviceDO = piviPackageDOMapper.findICCIDByCarVin(carVin);
        if (serviceDO != null && StringUtils.isNotEmpty(serviceDO.getIccid())) {
            return serviceDO.getIccid();
        }
        SOTAResultVO sotaInfoByVin = sotaService.getSOTAInfoByVin(carVin);
        if(StringUtils.isNotEmpty(sotaInfoByVin.getQueryResult())){
            log.info("请求sota报错:{}",sotaInfoByVin.getQueryResult());
        }
        return StringUtils.isNotEmpty(sotaInfoByVin.getEsimiccid())? sotaInfoByVin.getEsimiccid() : "";
    }

    private void setQueryFailed(UnicomRnrQueryVO unicomRnrQueryVO, UnicomRespVO unicomRespVO) {
        unicomRnrQueryVO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
        unicomRnrQueryVO.setQueryStatusTxt(UnicomRnrQueryStatusEnum.FAILED.getDesc());
        unicomRnrQueryVO.setErrorDesc(ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
        log.info("查询失败，ICCID: {}, 错误描述: {}", unicomRnrQueryVO.getIccid(), unicomRnrQueryVO.getErrorDesc());
    }

    private UnicomReqVO buildCancelUnicomParam(String externalBookId, String iccid, String type) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化当前时间 yyyyMMddHHmmss
        String timestamp = TimeFormatUtil.timeToStringByFormat(now,TimeFormatUtil.formatter_4);
        UnicomReqVO unicomReqVO = new UnicomReqVO();
        unicomReqVO.setIndustryCode(industryCode);
        unicomReqVO.setMethod(UnicomFunctionEnum.DO_CARD_PRODUCT_ORDER.getCode());
        unicomReqVO.setVersion(version);
        unicomReqVO.setTimestamp(timestamp);

        UnicomRequestData unicomRequestData = new UnicomRequestData();
        unicomRequestData.setIccid(iccid);
        unicomRequestData.setRequestId(ecpIdUtil.nextIdStr());
        unicomRequestData.setRequestType(type);

        ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setProductId(productId);
        productBookInfo.setExternalBookId(externalBookId);
        productBookInfo.setBookStatus(UnicomBookStatusEnum.CANCEL.getType());
        unicomRequestData.setProductBookInfo(List.of(productBookInfo));
        String requestBodyJson = "";
        ObjectMapper mapper = new ObjectMapper();

        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            requestBodyJson = mapper.writeValueAsString(unicomRequestData);
        } catch (Exception e) {
            log.error("组装参数异常",e);
            log.info("组装参数异常：{}",e.getMessage());
        }
        String signData = getSignData(requestBodyJson, UnicomFunctionEnum.DO_CARD_PRODUCT_ORDER.getCode(),timestamp);
        unicomReqVO.setSignData(signData);
        unicomReqVO.setUnicomRequestData(unicomRequestData);
        return unicomReqVO;

    }


    @Override
    public UnicomRespVO getSimCarInfoByCarVin(String carVin){
        PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(carVin);
        log.info("获取sim的信息，查询的PIVIPackage:{}", serviceDO);
        if (Objects.isNull(serviceDO)) {
            log.info("获取sim的信息,查询的PIVIPackage数据为空,carVin:{}", carVin);
            return new UnicomRespVO();
        }
        return getSimCardInfo(serviceDO.getIccid());
    }

    @Override
    @ApiLimitation(time = 1, timeUnit = RateIntervalUnit.SECONDS, tokenBucketName = "getSimCardInfo")
    public UnicomRespVO getSimCardInfo(String iccid) {
        log.info(" getSimCardInfo入参 =========================iccid:{}",iccid);
        //组装参数
        UnicomReqVO unicomReqVO = buildGetSimCardInfo(iccid);

        //调用 //解析返回值
        UnicomRespVO unicomRespVO = new UnicomRespVO();
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String requestBodyJson = mapper.writeValueAsString(unicomReqVO);
            log.info("联通车辆查询获取卡信息 requestBodyJson:{}",requestBodyJson);
            String env = environment.getProperty(Constants.PROFILE_ACTIVE);
            if ("dev".equals(env) || "test".equals(env)) {
                // 定义 Redis 缓存 key
                String redisKey = "mock_simcard_info:" + iccid;
                String mockSimCardInfo = redisService.getCacheObject(redisKey);
                if (StringUtils.isNotBlank(mockSimCardInfo)) {
                    return mapper.readValue(mockSimCardInfo, UnicomRespVO.class);
                }
            }

            // 使用HttpUtil.post发送请求
            String body = HttpUtil.post(url, requestBodyJson);
            log.info("联通车辆查询结果:{}", body);
            unicomRespVO = mapper.readValue(body, UnicomRespVO.class);
        }catch (Exception e){
            log.info("联通车辆卡信息查询异常:", e);
            unicomRespVO.setQueryResult(e.getMessage());
        }

        return unicomRespVO;
    }

    /**
     * 批量+限流,根据CarVin和ICCID 查询联通SIM卡信息
     * @param simCardInfoDTOList simCar查询的dto
     * @return List<UnicomRespVO>
     */
    @Override
    public List<UnicomRespVO> getSimCardInfoList(List<SimCardInfoDTO> simCardInfoDTOList) {
        log.info("批量+限流,根据CarVin和ICCID查询联通SIM卡信息, 待查询数据总量:{}", simCardInfoDTOList.size());
        List<List<SimCardInfoDTO>> simCardListGroup = getSimCardDTOByGroup(simCardInfoDTOList);
        if (CollUtil.isEmpty(simCardListGroup)) {
            return new ArrayList<>();
        }
        List<UnicomRespVO> respVOList = new ArrayList<>();
        for (List<SimCardInfoDTO> simCardInfoDTOS : simCardListGroup) {
            List<UnicomRespVO> unicomRespVOList = queryUnicomRespVOListByLimit(simCardInfoDTOS);
            if (CollUtil.isNotEmpty(unicomRespVOList)) {
                respVOList.addAll(unicomRespVOList);
            }
        }
        log.info("批量+限流,根据CarVin和ICCID 查询联通SIM卡信息，查询的总数:{}", respVOList.size());
        return respVOList;
    }


    /**
     * 同步获取联通SIM卡信息
     *
     * @param simCardInfoDTOList 包含SIM卡信息的数据传输对象列表，需要包含iccid和vin字段
     * @return 包含联通SIM卡查询结果的响应对象列表
     */
    @Override
    public List<UnicomRespVO> getSimCardInfoSync(List<SimCardInfoDTO> simCardInfoDTOList) {
        log.info("同步获取联通SIM卡信息, 待查询数据总量:{}", simCardInfoDTOList.size());
        if (CollUtil.isEmpty(simCardInfoDTOList)) {
            return new ArrayList<>();
        }
        PIVIUnicomServiceImpl beanSelf = applicationContext.getBean(getClass());
        List<UnicomRespVO> respVOList = new ArrayList<>();
        for (SimCardInfoDTO simCardInfoDTO : simCardInfoDTOList) {
            long startTime = System.currentTimeMillis();
            UnicomRespVO unicomRespVO = beanSelf.getSimCardInfoByCarVin(simCardInfoDTO.getIccid(), simCardInfoDTO.getVin());
            if (Objects.nonNull(unicomRespVO)) {
                respVOList.add(unicomRespVO);
            } else {
                log.info("同步获取联通SIM卡信息,查询无结果, SimCardInfoDTO:{}", simCardInfoDTO);
            }
            long endTime = System.currentTimeMillis();
            if (endTime - startTime < 1000) {
                try {
                    Thread.sleep(1000 - (endTime - startTime));
                } catch (Exception e) {
                    log.info("同步获取联通SIM卡信息,休眠异常:{}", e.getMessage());
                }
            }
        }
        log.info("同步获取联通SIM卡信息，查询的总数:{}", respVOList.size());
        return respVOList;
    }


    /**
     * 根据限制条件查询UnicomRespVO列表
     *
     * @param simCardInfoDTOList SIM卡信息列表
     * @return UnicomRespVO列表
     */
    private List<UnicomRespVO> queryUnicomRespVOListByLimit(List<SimCardInfoDTO> simCardInfoDTOList) {
        long startTime = System.currentTimeMillis();
        if (CollUtil.isEmpty(simCardInfoDTOList)) {
            return new ArrayList<>();
        }
        List<CompletableFuture<UnicomRespVO>> futureList = new ArrayList<>();
        for (SimCardInfoDTO simCardInfoDTO : simCardInfoDTOList) {
            CompletableFuture<UnicomRespVO> future = CompletableFuture.supplyAsync(
                    () -> getSimCardInfoByCarVin(simCardInfoDTO.getIccid(), simCardInfoDTO.getVin()), subscribeAsyncThreadPool)
                    .exceptionally(ex -> {
                        log.error("根据限制条件查询UnicomRespVO列表: ", ex);
                        return new UnicomRespVO().setResponseCode("error").setResponseDesc("根据限制条件查询UnicomRespVO列表");
            });
            futureList.add(future);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        List<UnicomRespVO> unicomRespVOList = futureList.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        long endTime = System.currentTimeMillis();
        long finishTime = endTime - startTime;
        log.info("批量获取UNICOM到期时间，用时:{}毫秒", finishTime);
        if (finishTime < 1000) {
            try {
                Thread.sleep(1010 - finishTime);
            } catch (InterruptedException e) {
                log.error("批量获取UNICOM到期时间暂停失败:", e);
            }
        }
        return unicomRespVOList;
    }

    /**
     * 根据车辆识别码(VIN)获取SIM卡信息
     *
     * @param iccid  SIM卡的集成电路卡身份码
     * @param vin    车辆识别码，用于唯一标识一辆汽车
     * @return       返回一个UnicomRespVO对象，包含查询到的SIM卡信息如果查询失败或出现异常，返回null
     */
    public UnicomRespVO getSimCardInfoByCarVin(String iccid, String vin) {
        log.info("根据车辆识别码(VIN)获取SIM卡信息,iccid:{},vin :{}",iccid,vin);
        //组装参数
        UnicomReqVO unicomReqVO = buildGetSimCardInfo(iccid);

        //调用 //解析返回值
        UnicomRespVO unicomRespVO = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String requestBodyJson = mapper.writeValueAsString(unicomReqVO);
            log.info("根据车辆识别码(VIN)获取SIM卡信息, requestBodyJson:{}",requestBodyJson);
            // 使用HttpUtil.post发送请求
            String body = HttpUtil.post(url, requestBodyJson);
            //log.info("根据车辆识别码(VIN)获取SIM卡信息结果:{}", body);
            unicomRespVO = mapper.readValue(body, UnicomRespVO.class);
        } catch (Exception e){
            log.info("根据车辆识别码(VIN)获取SIM卡信息异常: {}", e.getMessage());
        }
        return unicomRespVO;
    }

    /**
     * 根据分组获取SIM卡信息DTO列表
     *
     * @param simCardInfoDTOList SIM卡信息DTO列表，包含所有的SIM卡信息
     * @return 返回一个列表，该列表的每个元素都是一个包含指定数量SIM卡信息DTO的子列表
     */
    private List<List<SimCardInfoDTO>> getSimCardDTOByGroup(List<SimCardInfoDTO> simCardInfoDTOList) {
        List<List<SimCardInfoDTO>> resp = new ArrayList<>();
        if (CollUtil.isEmpty(simCardInfoDTOList)) {
            return resp;
        }
        for (int i = 0; i <  simCardInfoDTOList.size(); i += GROUP_SIZE) {
            int endIdx = Math.min(simCardInfoDTOList.size(), i+GROUP_SIZE);
            List<SimCardInfoDTO> subSimCardList = simCardInfoDTOList.subList(i, endIdx);
            resp.add(subSimCardList);
        }
        return resp;
    }


    private UnicomReqVO buildCallUnicomParam(FufilmentMessage message,String iccid,String type) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化当前时间 yyyyMMddHHmmss
        String timestamp = TimeFormatUtil.timeToStringByFormat(now,TimeFormatUtil.formatter_4);
        UnicomReqVO unicomReqVO = new UnicomReqVO();
        unicomReqVO.setIndustryCode(industryCode);
        unicomReqVO.setMethod(UnicomFunctionEnum.DO_CARD_PRODUCT_ORDER.getCode());
        unicomReqVO.setVersion(version);
        unicomReqVO.setTimestamp(timestamp);

        UnicomRequestData unicomRequestData = new UnicomRequestData();
        unicomRequestData.setIccid(iccid);
        unicomRequestData.setRequestId(ecpIdUtil.nextIdStr());
        unicomRequestData.setRequestType(type);

        ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setProductId(productId);
        productBookInfo.setExternalBookId(message.getVcsOrderCode());
        productBookInfo.setBookStatus(UnicomBookStatusEnum.ACTIVE.getType());
        productBookInfo.setActiveTime(TimeFormatUtil.timeToStringByFormat(message.getServiceBeginDate(),TimeFormatUtil.formatter_4));
        productBookInfo.setExpireTime(TimeFormatUtil.timeToStringByFormat(message.getServiceEndDate(),TimeFormatUtil.formatter_4));
        unicomRequestData.setProductBookInfo(List.of(productBookInfo));
        String requestBodyJson = "";
        ObjectMapper mapper = new ObjectMapper();

        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
             requestBodyJson = mapper.writeValueAsString(unicomRequestData);
        } catch (Exception e) {
            log.error("组装参数异常",e);
            log.info("组装参数异常：{}",e.getMessage());
        }
        String signData = getSignData(requestBodyJson, UnicomFunctionEnum.DO_CARD_PRODUCT_ORDER.getCode(),timestamp);
        unicomReqVO.setSignData(signData);
        unicomReqVO.setUnicomRequestData(unicomRequestData);
        return unicomReqVO;
    }

    private UnicomReqVO buildGetSimCardInfo(String iccid) {

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化当前时间 yyyyMMddHHmmss
        String timestamp = TimeFormatUtil.timeToStringByFormat(now,TimeFormatUtil.formatter_4);
        UnicomReqVO unicomReqVO = new UnicomReqVO();
        unicomReqVO.setIndustryCode(industryCode);
        unicomReqVO.setMethod(UnicomFunctionEnum.GET_CARD_INFO.getCode());
        unicomReqVO.setVersion(version);
        unicomReqVO.setTimestamp(timestamp);
        UnicomRequestData unicomRequestData = new UnicomRequestData();
        unicomRequestData.setIccid(iccid);
        String requestData = JSON.toJSONString(unicomRequestData);
        String signData = getSignData(requestData, UnicomFunctionEnum.GET_CARD_INFO.getCode(),timestamp);
        unicomReqVO.setSignData(signData);
        unicomReqVO.setUnicomRequestData(unicomRequestData);
        return unicomReqVO;
    }


    /**
     * 获取签名方法
     */
    private  String getSignData(String requestData, String method, String timestamp){
        String data = industryCode  + method + timestamp + requestData + appKey;
        return DigestUtils.md5DigestAsHex(data.getBytes());
    }


    @Override
    public List<String> addRealNameList(UnicomReqDTO unicomReqDTO){
        redisService.deleteObject(TEST_REDIS_KEY);
        long l = redisService.setCacheList(TEST_REDIS_KEY, unicomReqDTO.getCarVinList());
        if(l>0){
            return redisService.getCacheList(TEST_REDIS_KEY);
        }
        return null;
    }


    @Override
    public CommonResult<List<EsimInfoVO>> esimstate(UnicomReqDTO unicomReqDTO) {
        List<String> carVinList = unicomReqDTO.getCarVinList();
         //过滤只需要PIVI的carvin
        carVinList = getPIVICarList(carVinList);
        log.info("esimstate过滤后的PIVI车架号为:{}",carVinList);
        List<EsimInfoVO> voList = new ArrayList<>();
        if(CollUtil.isNotEmpty(carVinList)) {
            //查询PIVI Package 获取ICCID
            Map<String, String> map = findICCIDByCarVinList(carVinList);
            if(MapUtil.isEmpty(map)){
                return CommonResult.success(voList);
            }
            for (String carVin : carVinList) {
                String iccid = map.get(carVin);
                if(StringUtils.isEmpty(iccid)){
                    return CommonResult.success(voList);
                }
                // 调用联通的接口
                try {
                    // 调用联通的接口
                    executeFindSimInfo(voList, carVin, iccid);
                } catch (Exception e){
                    log.info("查询esimstate方法获取联通数据错误:{}",e.getMessage());
                    return CommonResult.success(voList);
                }

            }
        }
        return CommonResult.success(voList);
    }

    /**
     * 执行
     * @param voList
     * @param carVin
     * @param iccid
     */
    private void executeFindSimInfo(List<EsimInfoVO> voList, String carVin, String iccid) {
        UnicomRespVO unicomRespVO = getSimCardInfo(iccid);
        SimCardInfo simCardInfo = unicomRespVO.getUnicomRespData().getSimCardInfo();
        if(simCardInfo!=null){
            EsimInfoVO esimInfoVO = new EsimInfoVO();
            esimInfoVO.setVin(carVin);
            esimInfoVO.setIccid(simCardInfo.getIccid());
            esimInfoVO.setRealnameFlag(simCardInfo.getRealnameFlag());
            esimInfoVO.setCardState(simCardInfo.getCardState());
            esimInfoVO.setCardStateTxt(UnicomCardStateEnum.getDescriptionByCode(simCardInfo.getCardState()));
            voList.add(esimInfoVO);
        }
    }

    @Override
    public EsimInfoVO checkVinRNRInfo(String carVin) {
        PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(carVin);
        String iccid = serviceDO.getIccid();
        // 构造Redis缓存的key
        String cacheKey = REAL_NAME_REDIS_KEY + iccid;
        // 尝试从Redis缓存中获取数据
        EsimInfoVO cachedEsimInfoVO = redisService.getCacheObject(cacheKey);
        if (cachedEsimInfoVO != null) {
            // 如果缓存中存在数据，直接使用缓存数据
            return cachedEsimInfoVO;
        } else {
            // 如果缓存中不存在数据，调用getSimCardInfo方法获取数据
            UnicomRespVO unicomRespVO = getSimCardInfo(iccid);
            SimCardInfo simCardInfo = unicomRespVO.getUnicomRespData().getSimCardInfo();
            if (simCardInfo != null) {
                EsimInfoVO esimInfoVO = new EsimInfoVO();
                esimInfoVO.setVin(carVin);
                esimInfoVO.setIccid(simCardInfo.getIccid());
                esimInfoVO.setRealnameFlag(simCardInfo.getRealnameFlag());
                esimInfoVO.setCardState(simCardInfo.getCardState());
                esimInfoVO.setCardStateTxt(UnicomCardStateEnum.getDescriptionByCode(simCardInfo.getCardState()));
                // 将结果存入Redis缓存，设置过期时间为2小时
                if(UnicomRealnameFlagEnum.TRUE.getCode().equals(esimInfoVO.getRealnameFlag())){
                    redisService.setCacheObject(cacheKey, esimInfoVO, 2L, TimeUnit.HOURS);
                }
                return esimInfoVO;
            }
        }
        return null;
    }



    /**
     *
     * @param carVinList 车架号List
     * @return Map<String,String> Vin Iccid
     */
    private Map<String,String> findICCIDByCarVinList(List<String> carVinList){
        List<PIVIPackageDO> piviPackageDOS = piviPackageDOMapper.selectList(new LambdaQueryWrapperX<PIVIPackageDO>()
                .eq(BaseDO::getIsDeleted, false)
                .isNotNull(PIVIPackageDO::getIccid)
                .in(PIVIPackageDO::getVin, carVinList)
                .select(PIVIPackageDO::getVin, PIVIPackageDO::getIccid));
        Map<String,String> map = null;
        if(CollUtil.isNotEmpty(piviPackageDOS)){
            map = piviPackageDOS.stream()
                    .collect(Collectors.toMap(
                            PIVIPackageDO::getVin,
                            PIVIPackageDO::getIccid,
                            (oldValue, newValue) -> oldValue,
                            HashMap::new
                    ));
        }
        return map;
    }


    /**
     * 添加APPD履行记录。
     * 该方法用于创建并插入一个应用部署履行记录到数据库中。它详细记录了部署过程中的关键信息，
     *
     * @param fulfilmentId 履行ID
     * @param call 调用记录
     * @param tenantId 租户ID
     */
    private void addAppDFulfilmentRecord(String fulfilmentId, VcsOrderFufilmentCall call, Long tenantId) {
        call.setServicePackage(ServicePackageEnum.DATA_PLAN.getPackageName());
        call.setServiceName(ServiceNameEnum.UNICOM.getServiceName());
        call.setFufilmentId(fulfilmentId);
        call.setTenantId(tenantId.intValue());
        callMapper.insert(call);
    }



    private List<String> getPIVICarList(List<String> carVinList) {
        List<String> vinList  = new ArrayList<>();
        if(CollUtil.isEmpty(carVinList)){
            return vinList;
        }
        List<IncontrolVehicleDO> incontrolVehicleDOS = incontrolVehicleMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(BaseDO::getIsDeleted, false)
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode()));
        if(CollUtil.isNotEmpty(incontrolVehicleDOS)){
            vinList = incontrolVehicleDOS.stream().map(IncontrolVehicleDO::getCarVin).distinct().collect(Collectors.toList());
        }
        return vinList;
    }

    /**
     * 根据车辆识别号（VIN）获取ICCID
     * @param carVin 车辆识别号
     * @return 返回ICCID，如果没有找到则返回空字符串
     */
    private String getIccidByCarVin(String carVin) {
        PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(carVin);
        String iccid = "";
        if(Objects.nonNull(serviceDO)) {
            iccid = serviceDO.getIccid() == null?iccid:serviceDO.getIccid();
        } else {
            //TODO 需要将该情况存入saveOrUpdateErrorLog信息，记录业务数据不匹配的错误，然后返回失败
            log.error("getSimCarInfoByCarVin联通查询CarVin:{} ICCID配置信息不存在", carVin);
        }
        return iccid;
    }

    /**
     * 构建一个手动履行消息对象
     *
     * @param carVin 车辆识别号，用于识别特定的车辆
     * @param endDate 服务的结束日期和时间，标志着服务的有效期
     * @return 返回构建好的FufilmentMessage对象，该对象包含了车辆服务的相关信息
     */
    private FufilmentMessage buildManualFufilmentMessage(String carVin, LocalDateTime endDate) {
        FufilmentMessage message = new FufilmentMessage();
        message.setServiceBeginDate(LocalDateTime.now().minusDays(1));
        message.setServiceEndDate(endDate);
        message.setVcsOrderCode(ecpIdUtil.nextIdStr());
        message.setVin(carVin);
        return message;
    }

    /**
     * 构建一个手动履行消息对象
     *
     * @param carVin 车辆识别号，用于识别特定的车辆
     * @param endDate 服务的结束日期和时间，标志着服务的有效期
     * @param cusOrderId 客户订单号
     * @return 返回构建好的FufilmentMessage对象，该对象包含了车辆服务的相关信息
     */
    private FufilmentMessage buildManualFufilmentMessageJob(String carVin, LocalDateTime endDate, String cusOrderId) {
        FufilmentMessage message = new FufilmentMessage();
        message.setServiceBeginDate(LocalDateTime.now().minusDays(1));
        message.setServiceEndDate(endDate);
        message.setVcsOrderCode(cusOrderId);
        message.setVin(carVin);
        return message;
    }

    /**
     * 并发调用中国联通续费服务
     *
     * @param message 包含续费信息的消息对象
     * @param iccid 要续费的ICCID
     * @return CommonResult 结果对象，包含续费调用的结果
     */
    public CommonResult<String> concurrentCallUnicomRenew(FufilmentMessage message, String iccid) {
        PIVIUnicomServiceImpl bean = applicationContext.getBean(getClass());
        // sprint47:并发控制
        RLock lock = redisson.getLock(Constants.REDIS_KEY.CONCURRENT_UNICOM_RENEWAL_KEY + message.getVin());
        // 尝试获取锁，不等待
        if (!lock.tryLock()) {
            log.warn("获取unicom续费锁失败, carVin:{}", message.getVin());
            // 未获取到锁
            return CommonResult.error(ORDER_IN_TRANSIT_ERROR);
        }
        try {
            return CommonResult.success(bean.callUnicomRequest(message, iccid));
        } catch (Exception e) {
            log.warn("AppD续约请求异常:{}", e.getMessage());
            return CommonResult.error(CALL_UNICOM_RENEWAL_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock(); // 仅在当前线程持有锁时释放锁
            }
        }
    }
}
