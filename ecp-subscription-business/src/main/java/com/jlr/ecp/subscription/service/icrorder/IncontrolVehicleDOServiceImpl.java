package com.jlr.ecp.subscription.service.icrorder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.consumer.api.consumer.ConsumerApi;
import com.jlr.ecp.consumer.api.consumer.dto.ConsumerChannelDTO;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchLocalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.vehicle.ConsumerInfoVO;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerIncontrolDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerIncontrolMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_incontrol_vehicle(t_incontrol_vehicle)】的数据库操作Service实现
 * @createDate 2024-01-16 09:42:36
 */
@Service
@Slf4j
public class IncontrolVehicleDOServiceImpl extends ServiceImpl<IncontrolVehicleDOMapper, IncontrolVehicleDO>
        implements IncontrolVehicleDOService {

    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;
    @Resource
    private IncontrolVehicleService incontrolVehicleService;

    @Resource
    private SubscriptionService subscriptionService;

    @Resource
    private ConsumerIncontrolMapper consumerIncontrolMapper;

    @Resource
    private ConsumerApi consumerApi;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Resource
    private SeriesBrandMappingDataDOService seriesBrandMappingDataDOService;

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private VinInitializeService vinInitializeService;

    @Override
    public IcrVehicleRespVO getOneByCarVin(String carVin) {
        IncontrolVehicleDO incontrolVehicleDO = incontrolVehicleDOMapper.selectOne(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                        .eq(IncontrolVehicleDO::getCarVin, carVin)
                        .eq(IncontrolVehicleDO::getIsDeleted, 0)
                        .orderByDesc(IncontrolVehicleDO::getId)
                        .last("limit 1")
                //TODO 通过id索引查,不用子查询
        );

        if (incontrolVehicleDO != null) {
            IcrVehicleRespVO vo = new IcrVehicleRespVO();

            BeanUtils.copyProperties(incontrolVehicleDO, vo);
            return vo;
        }
        return null;
    }

    @Override
    public List<IcrVehicleListRespVO> getCarVinInfo(List<String> carVinList) {
        log.info("CRC getCarVinInfo :{}",carVinList);
        List<IncontrolVehicleDO> list = incontrolVehicleDOMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getIsDeleted, 0)
                .orderByDesc(IncontrolVehicleDO::getId));
        if (CollUtil.isNotEmpty(list)) {

            return list.stream().map(incontrolVehicleDO->{
                IcrVehicleListRespVO vo = new IcrVehicleListRespVO();
                BeanUtils.copyProperties(incontrolVehicleDO, vo);
                return vo;
            }).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public CommonResult<ConsumerInfoVO> searchVinInfo(String carVin, String icr) {
        log.info("searchVinInfo searchVinInfo :{},icr:{}", carVin, icr);
        //carVin校验 如果carVIN存在 校验carvin长度
        if (StringUtils.isNotBlank(carVin)) {
            return getConsumerInfoVOByVin(carVin);
        }

        if (StringUtils.isNotBlank(icr)) {
            return getConsumerInfoVOByICR(icr);
        }

        return CommonResult.error(ErrorCodeConstants.VIN_ICR_NOT_NULL);
    }

    /**
     * 根据车辆识别号（VIN）获取消费者信息视图对象
     * 此方法首先验证VIN格式，然后查询车辆信息，并根据查询结果构建消费者信息视图对象
     *
     * @param carVin 车辆识别号，用于查询车辆信息
     * @return 返回一个CommonResult对象，其中包含构建的ConsumerInfoVO或错误信息
     */
    private CommonResult<ConsumerInfoVO> getConsumerInfoVOByVin(String carVin) {
        if (!CarVinUtil.checkVinFormat(carVin)) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }

        // 查询并存储车辆信息ByVin
        try {
            //boolean unbind = incontrolVehicleService.getAndStoreCarInfoByVin(carVin);
            boolean unbind = vinInitializeService.vinInitializeByQueryVin(carVin);
            //通过vin在t_incontrol_vehicle查 List<IncontrolVehicleDO> incontrolVehicleDOS
            IncontrolVehicleDO incontrolVehicleDO = incontrolVehicleDOMapper.selectOneByCarVin(carVin);
            if (Objects.isNull(incontrolVehicleDO)) {
                log.info("carVin未找到对应的incontrolVehicle carVin:{}", carVin);
                return CommonResult.error(ErrorCodeConstants.ICR_NOT_FOUND);
            }
            CommonResult<ConsumerInfoVO> result = getConsumerInfoVO(incontrolVehicleDO.getIncontrolId(), List.of(incontrolVehicleDO));
            result.getData().setUnbind(unbind);
            return result;
        } catch (ServiceException e) {
            log.error("getAndStoreCarInfoByVin失败, {}", e.getMessage());
            if (ErrorCodeConstants.VIN_NOT_FOUND_ERROR.getCode().equals(e.getCode()) ||
                    ErrorCodeConstants.GET_EXPIRE_DATE_ERROR.getCode().equals(e.getCode())) {
                return CommonResult.error(ErrorCodeConstants.ICR_NOT_FOUND);
            }
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("getAndStoreCarInfoByVin失败", e);
            return CommonResult.error(ErrorCodeConstants.GUEST_ORDER_ERROR);
        }
    }

    /**
     * 根据icr获取消费者信息VO
     * 此方法首先通过icr查询TSDP系统中的车辆信息列表，然后与本地系统中的车辆信息进行对比
     * 找到交集后，使用交集数据来构造并返回消费者信息VO
     *
     * @param icr 车辆识别号
     * @return 包含消费者信息VO的通用结果对象
     */
    private CommonResult<ConsumerInfoVO> getConsumerInfoVOByICR(String icr) {
        //通过icr查询TSDP下 List<VinsAndServiceDTO> vinList
        log.info("通过icr查询TSDP下 List<VinsAndServiceDTO>");
        List<VinsAndServiceDTO> vinList = null;
        try {
            //vinList = incontrolVehicleService.queryAndStoreVinsAndServiceInfo(icr);
            vinList = remoteCallService.getByICR(icr);
        } catch (Exception e) {
            log.error("TSDP查询失败", e);
            return CommonResult.error(ErrorCodeConstants.ICR_FOUND_EMPTY);
        }

        // TSDP返回为空更新车辆和ICR的绑定
        if (CollUtil.isEmpty(vinList)) {
            log.info("TSDP未查询到{}的车机数据", icr);
            // 将车辆与icr关系表更新, icr字段置为空
            vinInitializeService.updateInControlIdNull(icr);
            return CommonResult.error(ErrorCodeConstants.ICR_FOUND_ERROR);
        }
        Set<String> vinSet = vinList.stream().map(VinsAndServiceDTO::getVin).collect(Collectors.toSet());
        // 保存或更新用户车辆信息
        log.info("代客下单根据inControlId查询TSDP续期服务, 初始化车辆信息, inControlId:{}, infoListFromTSDP:{}", icr, vinList);
        //incontrolVehicleService.getAndStoreCarInfoByICR(icr, vinSet);
        vinInitializeService.vinInitializeByQueryICR(icr, vinList);
        log.info("byICR保存用户车辆及服务信息成功，icr={}, vinSet={}", icr, vinSet);

        //通过icr在t_incontrol_vehicle查 List<IncontrolVehicleDO> incontrolVehicleDOS
        List<IncontrolVehicleDO> incontrolVehicleDOS = incontrolVehicleDOMapper.selectListByICR(icr);
        //取incontrolVehicleDOS 和 vinList 的交集 vin 返回 List<IncontrolVehicleDO>
        if (CollUtil.isEmpty(incontrolVehicleDOS)) {
            log.info("icr 未找到对应的incontrolVehicle icr:{}", icr);
            return CommonResult.error(ErrorCodeConstants.ICR_FOUND_ERROR);
        }
        //交集
        List<IncontrolVehicleDO> intersection = incontrolVehicleDOS.stream()
                .filter(vehicleDO -> vinSet.contains(vehicleDO.getCarVin()))
                .collect(Collectors.toList());

        log.info("通过交集得到list：{}", intersection);

        if (CollUtil.isEmpty(intersection)) {
            log.info("icr 未找到对应的交集 icr:{}", icr);
            return CommonResult.error(ErrorCodeConstants.ICR_FOUND_ERROR);
        }
        return getConsumerInfoVO(icr, intersection);
    }

    /**
     * 根据管控ID和车辆信息列表获取消费者信息VO对象
     *
     * @param icr 管控ID，用于查询消费者信息
     * @param vehicleDOList 车辆信息列表，包含与消费者相关的车辆数据
     * @return 返回一个包含消费者信息的CommonResult对象
     */
    private CommonResult<ConsumerInfoVO> getConsumerInfoVO(String icr, List<IncontrolVehicleDO> vehicleDOList) {
        ConsumerInfoVO consumerInfoVO = new ConsumerInfoVO();
        consumerInfoVO.setIcr(icr);
        ConsumerIncontrolDO consumerIncontrolDO = consumerIncontrolMapper.selectNewByIncontrolId(icr);
        //设置手机号
        assembleConsumerInfoVOMobile(consumerInfoVO, vehicleDOList, consumerIncontrolDO);

        //获取服务包的信息
        List<SubscriptionSearchLocalDTO> subscriptionSearchResultDTOList = getSubscriptionSearchLocalDTOS(vehicleDOList);

        consumerInfoVO.setSubscriptionSearchResultDTOList(subscriptionSearchResultDTOList);

        return CommonResult.success(consumerInfoVO);
    }

    /**
     * 根据交集信息获取订阅搜索本地DTO列表
     * 此方法主要用于处理一组车辆数据，通过车辆系列代码映射获取系列名称，
     * 并调用服务获取每辆车的订阅信息，最终返回一个订阅搜索本地DTO列表
     *
     * @param intersection 代表车辆数据的交集列表，用于获取订阅信息
     * @return 返回一个SubscriptionSearchLocalDTO对象列表，包含处理后的订阅搜索信息
     */
    private List<SubscriptionSearchLocalDTO> getSubscriptionSearchLocalDTOS(List<IncontrolVehicleDO> intersection) {
        List<SubscriptionSearchLocalDTO> subscriptionSearchResultDTOList = new ArrayList<>();

        //seriesBrandMappingDataDOService
        Map<String, SeriesMappingVO> seriesMapping = seriesBrandMappingDataDOService.getSeriesMapping();
        for (IncontrolVehicleDO incontrolVehicleDO : intersection) {

            SeriesMappingVO seriesMappingVO = seriesMapping.get(incontrolVehicleDO.getSeriesCode());
            //seriesMappingVO不为空 seriesMappingVO.getSeriesName()也不为空 就取seriesMappingVO.getSeriesName()否则取原值
            incontrolVehicleDO.setSeriesName(seriesMappingVO!=null
                    && StringUtils.isNotBlank(seriesMappingVO.getSeriesName())
                    ? seriesMappingVO.getSeriesName():incontrolVehicleDO.getSeriesName());

            SubscriptionSearchLocalDTO resultDTO = subscriptionService.getExpireDateByVinLocal(incontrolVehicleDO);
            subscriptionSearchResultDTOList.add(resultDTO);
        }
        return subscriptionSearchResultDTOList;
    }

    /**
     * 设置手机号
     * @param consumerInfoVO 响应对象
     * @param incontrolVehicleDOS 车辆信息
     * @param consumerIncontrolDO 用户信息
     */
    private void assembleConsumerInfoVOMobile(ConsumerInfoVO consumerInfoVO, List<IncontrolVehicleDO> incontrolVehicleDOS, ConsumerIncontrolDO consumerIncontrolDO) {
        //取icrVehiclePhone:incontrolVehicleDOS里面取第一条获取icrPhone 解密
        String decodeText = piplDataUtil.getDecodeText(incontrolVehicleDOS.get(0).getIncontrolPhone());
        consumerInfoVO.setIcrMobile(incontrolVehicleDOS.get(0).getIncontrolPhone());
        consumerInfoVO.setIcrMobileMix(StringUtils.isNotBlank(decodeText)? (String) PhoneUtil.hideBetween(decodeText) :"-");

        //设置ICR加密和mix
        String icr = consumerInfoVO.getIcr();
        consumerInfoVO.setIcrMix(PIPLDataUtil.getIncontrolIdMix(icr));
        consumerInfoVO.setIcr(piplDataUtil.getEncryptText(icr));

        if(consumerIncontrolDO != null && StringUtils.isNotBlank(consumerIncontrolDO.getConsumerCode())){
            CommonResult<ConsumerChannelDTO> consumerChannelByCode = null;
            try {
                consumerChannelByCode = consumerApi.getConsumerChannelByCode(consumerIncontrolDO.getConsumerCode());
            } catch (Exception e) {
                log.warn("通过consumerCode查询失败",e);
            }
            if (consumerChannelByCode != null && consumerChannelByCode.getData() != null) {
                String phoneEncrypt = consumerChannelByCode.getData().getChannelPhoneEncrypt();
                String phone = piplDataUtil.getDecodeText(phoneEncrypt);
                consumerInfoVO.setWxMobile(phoneEncrypt);
                consumerInfoVO.setWxMobileMix(StringUtils.isNotBlank(phone)? (String) PhoneUtil.hideBetween(phone) :"-");
            }

        }
    }

}




