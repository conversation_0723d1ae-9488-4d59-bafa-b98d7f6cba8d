package com.jlr.ecp.subscription.service.fufilment;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;

/**
* <AUTHOR>
* @description 针对表【t_vcs_order_rollback(t_vcs_order_rollback)】的数据库操作Service
* @createDate 2024-01-19 14:15:09
*/
public interface VcsOrderRollbackDOService extends IService<VcsOrderRollbackDO> {

    /**
     * 更新服务状态为失败
     */
    void updateServiceStatusFail(String rollbackFulfilmentId);
}
