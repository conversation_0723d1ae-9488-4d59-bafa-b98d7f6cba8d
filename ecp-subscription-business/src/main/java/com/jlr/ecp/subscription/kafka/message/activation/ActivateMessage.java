package com.jlr.ecp.subscription.kafka.message.activation;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivateMessage {
    /**
     * 消息Id
     * */
    private String messageId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     *  任务code
     * */
    private String taskCode;

    /**
     * 租户id
     * */
    private Long tenantId;

    /**
     *  服务名称
     * */
    private String serviceName;

    /**
     * 订单编号
     * */
    private String orderNumber;

    /**
     * 服务到期时间
     * */
    private String validityDate;

    /**
     * 1路虎, 2捷豹
     * */
    private Integer brandCode;
}


