package com.jlr.ecp.subscription.enums.remote;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RemoteModifyStatusEnum {
    MODIFY_PROGRESS(1, "进行中"),
    MODIFY_SUCCESS(2, "成功"),
    MODIFY_FAIL(3, "失败");

    private final Integer status;

    private final String desc;

    /**
     * 根据状态码获取描述
     *
     * @param status 状态码，类型为Integer
     * @return 返回与状态码对应的状态描述如果找不到匹配的状态码，则返回空字符串
     */
    public static String getDescByStatus(Integer status) {
        for (RemoteModifyStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
