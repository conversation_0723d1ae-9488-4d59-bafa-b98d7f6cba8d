package com.jlr.ecp.subscription.excel.listener.remote;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalResultExcel;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.*;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RemoteBatchRenewalCheckListener extends AnalysisEventListener<RemoteBatchRenewalExcel> {

    private static final int BATCH_COUNT = 500;

    private List<RemoteBatchRenewalExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<RemoteBatchRenewalExcel> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<RemoteBatchRenewalResultExcel> resultExcelList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Set<String> vinSet = new HashSet<>();

    private Boolean formatError = false;

    private Boolean checkResult = true;

    private static final String VIN_TITLE = "VIN";

    private static final String RENEWAL_TITLE = "远程车控服务续期日期(yyyy/mm/dd)";

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(RemoteBatchRenewalExcel remoteBatchRenewalExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(remoteBatchRenewalExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                checkUploadExcel(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("Remote批量续费解析Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("Remote批量续费处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            checkUploadExcel(dataList);
        } catch (Exception e) {
            log.info("Remote批量续费处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("Remote批量续费所有数据解析完成!");
    }

    public void checkUploadExcel(List<RemoteBatchRenewalExcel> renewalExcelList) {
        if (headers.isEmpty() ||  headers.get(0).size() < 2 || !VIN_TITLE.equals(headers.get(0).get(0)) ||
                !RENEWAL_TITLE.equals(headers.get(0).get(1))) {
            formatError = true;
            log.info("Remote批量续费的Excel文件title的格式错误");
            return ;
        }
        for (RemoteBatchRenewalExcel renewalExcel : renewalExcelList) {
            RemoteBatchRenewalResultExcel resultExcel = new RemoteBatchRenewalResultExcel();
            resultExcel.setCarVin(renewalExcel.getCarVin());
            resultExcel.setRenewalDate(renewalExcel.getRenewalDate());
            if (checkCarVin(renewalExcel, resultExcel) && checkDate(renewalExcel, resultExcel)) {
                resultExcel.setCheckResult("有效");
            }
            vinSet.add(CarVinUtil.carVinToUpperCase(renewalExcel.getCarVin()));
            resultExcelList.add(resultExcel);
        }
    }

    private boolean checkCarVin(RemoteBatchRenewalExcel renewalExcel, RemoteBatchRenewalResultExcel resultExcel) {
        if (StringUtils.isBlank(renewalExcel.getCarVin())) {
            resultExcel.setCheckResult("VIN缺失");
            checkResult = false;
            return false;
        } else if (!CarVinUtil.checkVinFormat(renewalExcel.getCarVin())) {
            resultExcel.setCheckResult("VIN需由17位数字及字母组成");
            checkResult = false;
            return false;
        } else if (vinSet.contains(CarVinUtil.carVinToUpperCase(renewalExcel.getCarVin()))) {
            resultExcel.setCheckResult("VIN重复");
            checkResult = false;
            return false;
        }
        return true;
    }

    private boolean checkDate(RemoteBatchRenewalExcel renewalExcel, RemoteBatchRenewalResultExcel resultExcel) {
        if(StringUtils.isBlank(renewalExcel.getRenewalDate())) {
            resultExcel.setCheckResult("日期为空");
            checkResult = false;
            return false;
        }
        return checkDateFormat(renewalExcel.getRenewalDate(), resultExcel);
    }

    private boolean checkDateFormat(String date, RemoteBatchRenewalResultExcel resultExcel) {
        if (!SubscribeTimeFormatUtil.isValidDate(date, SubscribeTimeFormatUtil.FORMAT_1)) {
            resultExcel.setCheckResult("日期格式不正确");
            checkResult = false;
            return false;
        } else if (LocalDate.now().isAfter(SubscribeTimeFormatUtil.stringToLocalDateByFormat(date))) {
            resultExcel.setCheckResult("日期小于当前日期");
            checkResult = false;
            return false;
        }
        return true;
    }

}
