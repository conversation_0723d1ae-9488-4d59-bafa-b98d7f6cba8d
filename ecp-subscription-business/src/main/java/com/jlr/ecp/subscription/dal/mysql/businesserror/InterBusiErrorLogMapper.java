package com.jlr.ecp.subscription.dal.mysql.businesserror;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.businesserror.InterBusiErrorLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 业务异常日志表(t_inter_busi_error_log)数据Mapper
 *
 * <AUTHOR>
 * @since 2024-02-05 13:46:20
 * @description 由 Mybatisplus Code Generator 创建
*/
@Mapper
public interface InterBusiErrorLogMapper extends BaseMapperX<InterBusiErrorLogDO> {

}
