package com.jlr.ecp.subscription.enums.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卡状态
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomCardStateEnum {

    OPEN("open", "开机"),

    CANCEL("cancel", "销户"),

    STOP("stop", "停机");

    /**
     * 类型
     * */
    public final String type;

    /**
     * 描述
     * */
    public final String desc;


    public static String getDescriptionByCode(String code) {
        // 或者返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (UnicomCardStateEnum status : UnicomCardStateEnum.values()) {
            if (status.getType().equals(code)) {
                return status.getDesc();
            }
        }
        return code;
    }
}
