package com.jlr.ecp.subscription.excel.listener.appduc;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.jlr.ecp.subscription.excel.pojo.appduc.AppDCuBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.appduc.AppDCuBatchRenewalResultExcel;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;


import java.time.LocalDate;
import java.util.*;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AppDUcBatchRenewalCheckListener extends AnalysisEventListener<AppDCuBatchRenewalExcel> {

    private static final int BATCH_COUNT = 500;

    private List<AppDCuBatchRenewalExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<AppDCuBatchRenewalExcel> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<AppDCuBatchRenewalResultExcel> resultExcelList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Set<String> vinSet = new HashSet<>();

    private Boolean formatError = false;

    private Boolean checkResult = true;

    private static final String VIN_TITLE = "VIN";

    private static final String APPD_RENEWAL_TITLE = "信息娱乐服务续期日期(yyyy/mm/dd)";
    private static final String UNICOM_RENEWAL_TITLE = "网络流量续期日期(yyyy/mm/dd)";

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(AppDCuBatchRenewalExcel appDUcBatchRenewalExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(appDUcBatchRenewalExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                checkUploadExcel(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("AppDUc批量续费解析Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("AppDUc批量续费处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            checkUploadExcel(dataList);
        } catch (Exception e) {
            log.info("AppDUc批量续费处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("AppDUc批量续费所有数据解析完成!");
    }

    public void checkUploadExcel(List<AppDCuBatchRenewalExcel> renewalExcelList) {
        if (headers.isEmpty() ||  headers.get(0).size() < 2 || !VIN_TITLE.equals(headers.get(0).get(0)) ||
                !APPD_RENEWAL_TITLE.equals(headers.get(0).get(1)) || !UNICOM_RENEWAL_TITLE.equals(headers.get(0).get(2))) {
            formatError = true;
            log.info("AppDUc批量续费的Excel文件title的格式错误");
            return ;
        }
        for (AppDCuBatchRenewalExcel renewalExcel : renewalExcelList) {
            AppDCuBatchRenewalResultExcel resultExcel = new AppDCuBatchRenewalResultExcel();
            resultExcel.setCarVin(renewalExcel.getCarVin());
            resultExcel.setAppDRenewalDate(renewalExcel.getAppDRenewalDate());
            resultExcel.setUnicomRenewalDate(renewalExcel.getUnicomRenewalDate());
            if (checkCarVin(renewalExcel, resultExcel) && checkDateAllEmpty(renewalExcel, resultExcel)
                    && checkAppDCuDate(renewalExcel, resultExcel)) {
                resultExcel.setCheckResult("有效");
            }
            vinSet.add(CarVinUtil.carVinToUpperCase(renewalExcel.getCarVin()));
            resultExcelList.add(resultExcel);
        }
    }

    private boolean checkCarVin(AppDCuBatchRenewalExcel renewalExcel, AppDCuBatchRenewalResultExcel resultExcel) {
        if (StringUtils.isBlank(renewalExcel.getCarVin())) {
            resultExcel.setCheckResult("VIN 缺失");
            checkResult = false;
            return false;
        } else if (!CarVinUtil.checkVinFormat(renewalExcel.getCarVin())) {
            resultExcel.setCheckResult("VIN需由17位数字及字母组成");
            checkResult = false;
            return false;
        } else if (vinSet.contains(CarVinUtil.carVinToUpperCase(renewalExcel.getCarVin()))) {
            resultExcel.setCheckResult("VIN重复");
            checkResult = false;
            return false;
        }
        return true;
    }

    private boolean checkDateAllEmpty(AppDCuBatchRenewalExcel renewalExcel, AppDCuBatchRenewalResultExcel resultExcel) {
        if(StringUtils.isBlank(renewalExcel.getAppDRenewalDate())
                && StringUtils.isBlank(renewalExcel.getUnicomRenewalDate())) {
            resultExcel.setCheckResult("日期为空");
            checkResult = false;
            return false;
        }
        return true;
    }

    private boolean checkAppDCuDate(AppDCuBatchRenewalExcel renewalExcel, AppDCuBatchRenewalResultExcel resultExcel) {
        return checkDateFormat(renewalExcel.getAppDRenewalDate(), resultExcel)
                && checkDateFormat(renewalExcel.getUnicomRenewalDate(), resultExcel);
    }

    private boolean checkDateFormat(String date, AppDCuBatchRenewalResultExcel resultExcel) {
        if (StringUtils.isBlank(date)) {
            return true;
        }
        if (!SubscribeTimeFormatUtil.isValidDate(date, SubscribeTimeFormatUtil.FORMAT_1)) {
            resultExcel.setCheckResult("日期格式不正确");
            checkResult = false;
            return false;
        } else if (LocalDate.now().isAfter(SubscribeTimeFormatUtil.stringToLocalDateByFormat(date))) {
            resultExcel.setCheckResult("日期小于当前日期");
            checkResult = false;
            return false;
        }
        return true;
    }

}
