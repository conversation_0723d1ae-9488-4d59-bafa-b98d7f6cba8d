package com.jlr.ecp.subscription.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "查询中心，在线服务批量查询上传记录VO")
public class OnlineServiceBatchQueryRecordVO {

    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "批量查询文件上传状态")
    private String status;

    @Schema(description = "报表编号")
    private String reportNo;
}
