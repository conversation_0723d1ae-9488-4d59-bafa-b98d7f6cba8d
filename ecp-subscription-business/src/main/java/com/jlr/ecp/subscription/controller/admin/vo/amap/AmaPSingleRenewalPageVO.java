package com.jlr.ecp.subscription.controller.admin.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP单个手动更新的VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPSingleRenewalPageVO {
    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "车俩VIN")
    private String vin;

    @Schema(description = "更新人")
    private String operateUser;

    @Schema(description = "续费编号")
    private Long renewNo;
}
