package com.jlr.ecp.subscription.enums.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AmaPRenewalYearEnum {
     ONE(1, "一年"),
     THREE(3, "三年");

     private final Integer code;

     private final String desc;

     /**
      * 判断给定的续订年代码是否存在于预定义的年代码枚举中。
      *
      * @param renewalYearCode 续订年代码，类型为Integer，用于比较。
      * @return 返回布尔值，如果给定的年代码存在于枚举中，则返回true，否则返回false。
      */
     public static boolean containsRenewalYearCode(Integer renewalYearCode) {
          AmaPRenewalYearEnum[] yearEnums = AmaPRenewalYearEnum.values();
          for (AmaPRenewalYearEnum yearEnum : yearEnums) {
               if (yearEnum.getCode().equals(renewalYearCode)) {
                    return true;
               }
          }
          return false;
     }

     /**
      * 根据描述获取代码
      *
      * @param renewalYear 续订年的描述
      * @return 对应的代码，如果没有匹配的描述，则返回null
      */
     public static Integer getCodeByDesc(String renewalYear) {
          AmaPRenewalYearEnum[] yearEnums = AmaPRenewalYearEnum.values();
          for (AmaPRenewalYearEnum yearEnum : yearEnums) {
               if (yearEnum.getDesc().equals(renewalYear)) {
                    return yearEnum.getCode();
               }
          }
          return null;
     }

     /**
      * 根据code获取描述
      *
      * @param renewalYearCode 续保年份的代码
      * @return 如果找到了对应的描述，则返回该描述；否则返回null
      */
     public static String getDescByCode(Integer renewalYearCode) {
          AmaPRenewalYearEnum[] yearEnums = AmaPRenewalYearEnum.values();
          for (AmaPRenewalYearEnum yearEnum : yearEnums) {
               if (yearEnum.getCode().equals(renewalYearCode)) {
                    return yearEnum.getDesc();
               }
          }
          return "";
     }
}
