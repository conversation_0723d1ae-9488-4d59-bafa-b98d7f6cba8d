package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleConfigGroupDO;
import com.jlr.ecp.subscription.dal.mysql.vehicle.VehicleConfigGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * t_vehicle_config_group表服务实现类
 * <AUTHOR>
 */

@Service("vehicleConfigGroupService")
@Validated
@Slf4j
public class VehicleConfigGroupServiceImpl implements VehicleConfigGroupService {

    @Resource
    private VehicleConfigGroupMapper vehicleConfigGroupMapper;

    @Override
    public VehicleConfigGroupDO getByConfigCode(String configCode) {
        return vehicleConfigGroupMapper.selectOne(new LambdaQueryWrapperX<VehicleConfigGroupDO>()
                .eq(VehicleConfigGroupDO::getConfigCode, configCode)
                .eq(VehicleConfigGroupDO::getIsDeleted, false)
                .last("limit 1"));
    }
}

