package com.jlr.ecp.subscription.excel.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalResultExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class RemoteExcelUtil {
    private static final String EXCEL_FORMATTER = ".xlsx";

    private static final int BATCH_COUNT = 500;

    /**
     * 批量写入Remote源文件
     *
     * @param resultExcels 包含Remote批量续订Excel对象的列表
     * @return 如果结果列表不为空，则返回写入后的文件；否则返回null
     */
    public static File batchRenewalWriteSourceFile(List<RemoteBatchRenewalExcel> resultExcels) {
        if (CollectionUtils.isEmpty(resultExcels)) {
            log.info("批量写入Remote源文件为空");
            return null;
        }
        List<List<RemoteBatchRenewalExcel>> groupList = new ArrayList<>();
        for (int i = 0; i < resultExcels.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, resultExcels.size());
            List<RemoteBatchRenewalExcel> group = resultExcels.subList(i, endIdx);
            groupList.add(group);
        }
        return batchWriteSourceFileByGroup(groupList);
    }


    /**
     * 按组批量写入Remote续费源文件
     *
     * @param batchRenewalExcelList 包含多个续费Excel列表的集合，每个列表代表一个组
     * @return 返回生成的临时文件，如果输入列表为空或发生异常，则返回null
     */
    public static File batchWriteSourceFileByGroup(List<List<RemoteBatchRenewalExcel>> batchRenewalExcelList) {
        if (CollectionUtils.isEmpty(batchRenewalExcelList)) {
            return null;
        }
        String fileName = "RemoteBatchRenewalExcel" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, RemoteBatchRenewalExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("RemoteBatchRenewalExcel").build();
                for (List<RemoteBatchRenewalExcel> data : batchRenewalExcelList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入Remote批量续费原始Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.warn("写入Remote批量续费原始Excel文件，数据异常:", e);
        }
        return null;
    }

    /**
     * 批量写入Remote续费结果到文件
     *
     * @param resultExcels 包含续费结果的列表
     * @return 返回写入结果的文件，如果输入列表为空，则返回null
     */
    public static File batchRenewalWriteResultFile(List<RemoteBatchRenewalResultExcel> resultExcels) {
        if (CollectionUtils.isEmpty(resultExcels)) {
            log.info("批量写入Remote续费结果文件为空");
            return null;
        }
        List<List<RemoteBatchRenewalResultExcel>> groupList = new ArrayList<>();
        for (int i = 0; i < resultExcels.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, resultExcels.size());
            List<RemoteBatchRenewalResultExcel> group = resultExcels.subList(i, endIdx);
            groupList.add(group);
        }
        return batchWriteResultFileByGroup(groupList);
    }


    /**
     * 按组批量写入Remote续费结果到Excel文件
     *
     * @param batchRenewalExcelList 包含多个批次续费结果的列表如果列表为空或null，则返回null
     * @return 成功创建的临时Excel文件如果写入过程中发生异常或输入列表为空，则返回null
     */
    public static File batchWriteResultFileByGroup(List<List<RemoteBatchRenewalResultExcel>> batchRenewalExcelList) {
        if (CollectionUtils.isEmpty(batchRenewalExcelList)) {
            return null;
        }
        String fileName = "RemoteBatchRenewalResultExcel" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, RemoteBatchRenewalResultExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("RemoteBatchRenewalResultExcel").build();
                for (List<RemoteBatchRenewalResultExcel> data : batchRenewalExcelList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入Remote批量续费结果Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.warn("写入Remote批量续费结果Excel文件，数据异常:", e);
        }
        return null;
    }
}