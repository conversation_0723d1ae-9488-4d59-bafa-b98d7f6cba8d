package com.jlr.ecp.subscription.service.vin.expiry.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class VinExpireServiceDTO implements Serializable {

    private String carVin;

    private Boolean jlrSubscriptionIdIsNull;

    private LocalDateTime remoteExpireDate;

    private LocalDateTime piviExpireDate;

    private String iccid;

    private String serviceType;

    private LocalDateTime expiryDate;
}
