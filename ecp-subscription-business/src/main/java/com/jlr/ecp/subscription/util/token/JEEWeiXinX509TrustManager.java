package com.jlr.ecp.subscription.util.token;

import org.apache.commons.lang3.StringUtils;

import javax.net.ssl.X509TrustManager;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

public class JEEWeiXinX509TrustManager implements X509TrustManager {
    private static final String CERTIFICATE_EXCEPTION = "CertificateException";
    @Override
    public void checkClientTrusted(X509Certificate[] chain, String authType)
            throws CertificateException {
        if(StringUtils.isNotBlank(authType)&& CERTIFICATE_EXCEPTION.equals(authType)){
            throw new CertificateException();
        }
        //trust any client
    }

    @Override
    public void checkServerTrusted(X509Certificate[] chain, String authType)
            throws CertificateException {
        if(StringUtils.isNotBlank(authType)&& CERTIFICATE_EXCEPTION.equals(authType)){
            throw new CertificateException();
        }
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        if(StringUtils.isNotBlank("")){
            throw new RuntimeException();
        }
        //trust any client
        return new X509Certificate[0];
    }
}
