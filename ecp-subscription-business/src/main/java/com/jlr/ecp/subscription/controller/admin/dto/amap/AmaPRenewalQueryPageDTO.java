package com.jlr.ecp.subscription.controller.admin.dto.amap;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "AMAP续费记录查询DTO")
public class AmaPRenewalQueryPageDTO extends PageParam {
    @Schema(description = "车辆编号")
    private String carVin;

    @Schema(description = "续费批次号")
    private List<String> batchNoList;

    @Schema(description = "操作开始时间")
    private String operateStartTime;

    @Schema(description = "操作结束时间")
    private String operateEndTime;

    @Schema(description = "续费状态, 0,1:进行中 2:成功 3:失败")
    private Integer renewalStatus;

    @Schema(description = "操作人员")
    private String operator;

    @Schema(description = "续费时长排序 asc:正序 desc:倒叙")
    private String renewalYearTimeSort;

    @Schema(description = "操作时间排序 asc:正序 desc:倒叙")
    private String operateTimeSort;
}
