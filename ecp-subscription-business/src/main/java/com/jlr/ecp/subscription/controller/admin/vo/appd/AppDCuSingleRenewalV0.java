package com.jlr.ecp.subscription.controller.admin.vo.appd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "APPD和联通单个续费结果VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AppDCuSingleRenewalV0 {
    @Schema(description = "续费结果")
    private String renewalResult;

    @Schema(description = "APPD续费Code")
    private Integer appDResultCode;

    @Schema(description = "APPD续费描述")
    private String appDResultDesc;

    @Schema(description = "联通续费Code")
    private Integer unicomResultCode;

    @Schema(description = "联通续费描述")
    private String unicomResultDesc;
}
