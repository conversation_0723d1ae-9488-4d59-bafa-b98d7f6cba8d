package com.jlr.ecp.subscription.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "查询中心，amap的VO")
public class AmaPSearchCenterVO {

    @Schema(description = "查询状态")
    private Integer queryStatus;

    @Schema(description = "查询状态描述")
    private String queryStatusDesc;

    /**
     * 在线导航服务实际到期日
     * */
    @Schema(description = "在线导航服务实际到期日")
    private String amaPExpireDate;

    /**
     *  Incontrol在线服务到期日
     * */
    @Schema(description = "Incontrol在线服务到期日")
    private String ecpExpireDate;

    /**
     * 在线导航服务状态
     * */
    @Schema(description = "在线导航服务状态")
    private Integer amaPServiceStatus;

    /**
     * 在线导航服务状态
     * */
    @Schema(description = "在线导航服务状态描述")
    private String amaPServiceStatusDesc;

    /**
     *  查询结果状态码
     * */
    @Schema(description = "查询结果状态码")
    private String resultCode;

    /**
     * 错误描述
     * */
    @Schema(description = "在线导航服务状态描述")
    private String errorDesc;

    /**
     * 车机类型
     * */
    @Schema(description = "车机类型")
    private String carSystemModel;
}
