package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 联通返回数据包
 * <AUTHOR>
 */
@Data
public class UnicomRespData implements Serializable {


    /**
     * sim卡信息
     */
    @JsonProperty("sim_card_info")
    private SimCardInfo simCardInfo;

    /**
     * 订购记录信息
     */
    @JsonProperty("product_book_info")
    private List<ProductBookInfo> productBookInfoList;


    /**
     * 实名订单列表
     */
    @JsonProperty("real_name_order")
    private List<UnicomRealNameOrder> realNameOrderList;
}