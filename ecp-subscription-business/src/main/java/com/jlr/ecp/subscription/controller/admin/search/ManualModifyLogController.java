package com.jlr.ecp.subscription.controller.admin.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageReqDTO;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageRespVO;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogTypeVO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.manuallog.ManualModifyLogTypeEnum;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jodd.util.StringUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/manual/log")
@Tag(name = "查询中心-系统修改日志查询")
public class ManualModifyLogController {


    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @PostMapping("/page")
    @Operation(summary = "系统修改日志列表、分页")
    @PreAuthorize("@ss.hasPermission('query:system-log:forms')")
    CommonResult<PageResult<ManualLogPageRespVO>> getLogPage(@RequestBody ManualLogPageReqDTO dto){

        if (StringUtil.isNotEmpty(dto.getVin()) && !CarVinUtil.checkVinFormat(dto.getVin())) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        // 为了兼容前端传入的大小写，这里统一转换为小写
        if (dto.getOperateTimeSort() != null) {
            dto.setOperateTimeSort(dto.getOperateTimeSort().toLowerCase(Locale.ROOT));
        }

        PageResult<ManualLogPageRespVO> pageResult = manualModifyLogDOService.getLogPage(dto);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/getModifyType")
    @Operation(summary = "操作类型下拉列表")
    @PermitAll
    CommonResult<List<ManualLogTypeVO>> getFulfilmentTypeList() {
        List<ManualLogTypeVO> list = new ArrayList<>();
        for (ManualModifyLogTypeEnum value : ManualModifyLogTypeEnum.values()) {
            ManualLogTypeVO vo = new ManualLogTypeVO();
            vo.setStatus(value.getType());
            vo.setText(value.getDesc());
            list.add(vo);
        }
        return CommonResult.success(list);
    }



}
