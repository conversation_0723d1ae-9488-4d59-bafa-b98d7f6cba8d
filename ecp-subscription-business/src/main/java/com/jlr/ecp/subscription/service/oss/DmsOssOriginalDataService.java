package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.bau.dto.PreCheckListRequest;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceBatchQueryRecordVO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeStatusVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * OSS原数据(DmsOssOriginalData)表服务接口
 *
 * <AUTHOR>
 */
public interface DmsOssOriginalDataService {

    /**
     * 查询待处理的idList
     * @param request request
     */
    List<Long> getPreCheckIdList(PreCheckListRequest request);

    /**
     * 根据idList查询记录列表
     * @param idList idList
     */
    List<DmsOssOriginalDataDO> getListByIdList(List<Long> idList);

    /**
     * 批量更新
     * @param updateList updateList
     */
    void updateBatch(List<DmsOssOriginalDataDO> updateList);

    /**
     * 查询待初始化车辆
     * @param request request
     */
    Map<Long, List<Long>> getPreSyncIdList(PreCheckListRequest request);

    /**
     * 单个VIN初始化状态查询
     * @param carVin carVin
     */
    OnlineServiceInitializeStatusVO getStatusByVin(String carVin);

    /**
     * 查询模板地址
     */
    String queryExcelUrl();

    /**
     * 批量查询上传
     */
    CommonResult<Boolean> uploadBatchSearch(MultipartFile multipartFile);

    /**
     * 生成未初始化原因文件
     */
    CommonResult<Boolean> generateReasonFile(Long bauJobId);


    /**
     * 批量查询上传记录查询
     */
    PageResult<OnlineServiceBatchQueryRecordVO> getBatchQueryRecords(VinInitialLogPageDTO logPageDTO);

    /**
     * 新增
     * @param originalDataDO originalDataDO
     */
    void insert(DmsOssOriginalDataDO originalDataDO);

    /**
     * 更新
     * @param originalDataDO originalDataDO
     */
    void update(DmsOssOriginalDataDO originalDataDO);

    /**
     * 根据vin查询记录
     * @param vin vin
     */
    DmsOssOriginalDataDO getByVin(String vin);
}

