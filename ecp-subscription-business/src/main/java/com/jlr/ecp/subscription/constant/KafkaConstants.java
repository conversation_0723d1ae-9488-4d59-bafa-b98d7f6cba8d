package com.jlr.ecp.subscription.constant;

/**
 * kafka topic
 * <AUTHOR>
 *
 */
public class KafkaConstants {

    public static final String ORDER_CANCEL_TOPIC = "order-cancel-topic";
    public static final String ORDER_SUCCESSFUL_TOPIC = "order-successful-topic";
    public static final String ORDER_FUFILMENT_TOPIC = "order-fufilment-topic";
    public static final String SERVICE_ACTIVATE_TOPIC = "service-activate-topic";

    public static final String ORDER_TIMEOUT_CANCEL_TOPIC = "order-timeout-cancel-topic";

    public static final String ORDER_REFUND_SUCCESS_TOPIC = "order-refund-success-topic";

    public static final String PAY_TIMEOUT_CANCEL_TOPIC = "pay-timeout-cancel-topic";
    public static final String ORDER_COMPLETE_STATUS_UPDATES_TOPIC = "order-complete-status-updates-topic";
    public static final String ORDER_ROLLBACK_STATUS_UPDATES_TOPIC = "order-rollback-status-updates-topic";
}
