package com.jlr.ecp.subscription.dal.mysql.vehicle;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.AmapDataDO;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleEsimDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * t_amap_initial_data表数据库访问层
 * <AUTHOR>
 */
@Mapper
public interface AmapDataMapper extends BaseMapperX<AmapDataDO> {

}
