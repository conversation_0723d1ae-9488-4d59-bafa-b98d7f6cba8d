package com.jlr.ecp.subscription.dal.dataobject.vin.expiry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.*;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_vin_expiry_report_data")
public class VinExpiryReportDataDO extends BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
 
    @TableField("car_vin")
    private String carVin;

    @TableField("job_id")
    private Long jobId;

    @TableField("job_date")
    private LocalDateTime jobDate;
 
    @TableField("expiry_date")
    private LocalDateTime expiryDate;
 
    @TableField("service_type")
    private String serviceType;
 
    @TableField("imported_en")
    private String importedEn;
 
    @TableField("brand")
    private String brand;
 
    @TableField("series_name")
    private String seriesName;
 
    @TableField("tenant_id")
    private Integer tenantId;

}