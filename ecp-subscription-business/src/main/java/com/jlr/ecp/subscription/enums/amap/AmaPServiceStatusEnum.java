package com.jlr.ecp.subscription.enums.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AmaPServiceStatusEnum {
    //status：服务状态 0：未激活 2：过期 3：使用中

    UNACTIVATED(0,"未激活"),
    EXPIRED(2,"过期"),
    USING(3,"使用中");


    private final Integer status;

    private final String desc;

    /**
     * 根据状态码获取描述信息
     *
     * @param status 状态码，用于标识不同的状态
     * @return 与状态码对应的状态描述信息，如果没有匹配项，则为空字符串
     */
    public static String getDescByStatus(Integer status) {
        for (AmaPServiceStatusEnum value : AmaPServiceStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
