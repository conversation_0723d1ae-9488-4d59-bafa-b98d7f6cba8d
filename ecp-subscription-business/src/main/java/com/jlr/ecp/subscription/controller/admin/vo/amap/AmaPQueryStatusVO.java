package com.jlr.ecp.subscription.controller.admin.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "查询中心-AMAP查询状态VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPQueryStatusVO {
    @Schema(description = "查询状态code")
    private Integer queryStatusCode;

    @Schema(description = "查询状态描述")
    private String queryStatusDesc;
}
