package com.jlr.ecp.subscription.enums.fufil;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AppDSubscriptionEventEnum {
    PROVISION("PROVISION", "准备订阅"),
    ACTIVATE("ACTIVATE", "订阅中"),
    DEACTIVATE("DEACTIVATE", "停止订阅"),
    DEPROVISION("DEPROVISION", "取消订阅");

    /**
     * 订阅事件
     * */
    public final String event;

    /**
     * 状态描述
     * */
    public final String desc;
}
