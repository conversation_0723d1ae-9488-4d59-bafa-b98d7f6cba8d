package com.jlr.ecp.subscription.controller.admin.dto.seriesMapping;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *车型展示名称配置 列表VO
 * <AUTHOR>
 */

@Schema(description = "车型展示名称配置 page VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class SeriesBrandMappingDataPageVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 车型系列编码;车型系列编码
     */
    private String seriesCode;

    /**
     * 车型系列中文名;车型系列中文名
     */
    private String dpSeriesName;

    /**
     * 车型系列中文名;车型系列中文名
     */
    private String seriesName;

    /**
     * 品牌显示名;品牌显示名
     */
    private String brandNameView;


    /**
     * 版本号
     */
    private Integer revision;
}