package com.jlr.ecp.subscription.dal.dataobject.remotepackage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value ="t_pivi_package")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PIVIPackageDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     *  服务包编码
     * */
    @TableField(value = "package_code")
    private String packageCode;

    /**
     * 服务名
     * */
    @TableField(value = "service_name")
    private String serviceName;

    /**
     *  服务特性编码
     * */
    @TableField(value = "vehicle_feature_code")
    private String vehicleFeatureCode;

    /**
     *  服务特性名称
     * */
    @TableField(value = "vehicle_feature_name")
    private String vehicleFeatureName;

    /**
     *  服务唯一实例ID
     * */
    @TableField(value = "element_instance_id")
    private String elementInstanceId;

    /**
     *  短码
     * */
    @TableField(value = "short_code")
    private String shortCode;

    /**
     *   服务唯一实例名
     * */
    @TableField(value = "element_name")
    private String elementName;

    /**
     *  订阅ID
     * */
    @TableField(value = "jlr_subscription_id")
    private Long jlrSubscriptionId;

    /**
     * 联通ICCID
     */
    private String iccid;

    /**
     *  车辆VIN
     * */
    @TableField(value = "vin")
    private String vin;

    /**
     *  AMAP到期时间
     * */
    @TableField(value = "amap_expire_date")
    private LocalDateTime amaPExpireDate;

    /**
     *  发票时间
     * */
    @TableField(value = "dms_invoice_date")
    private LocalDateTime dmsInvoiceDate;

    /**
     *  服务过期时间
     * */
    @TableField(value = "expire_date")
    private LocalDateTime expiryDate;

    /**
     * 过期时间utc0
     */
    @TableField(value = "expire_date_utc0")
    private LocalDateTime expireDateUtc0;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
