package com.jlr.ecp.subscription.config;

import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.dt.dataphin.client.ApiClient;
import com.alibaba.dt.dataphin.client.ApiClientBuilderParams;
import com.alibaba.dt.dataphin.schema.OrderBy;
import com.alibaba.dt.dataphin.schema.QueryParamRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jlr.ecp.subscription.annotation.ApiLimitation;
import com.jlr.ecp.subscription.annotation.ApiRateLimitation;
import com.jlr.ecp.subscription.api.model.vo.DPResponseVO;
import com.jlr.ecp.subscription.api.model.vo.DPResultVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RateIntervalUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class DPService {


    private static final String API_TYPE = "list";

    private static final String API_RETURN_FIELDS = "vin,brand_code,brand_name,model_year,config_code,config_name,series_code,series_name,production_en,hob_en";
    private static final String LIST_TYPE = "LIST";
    private static final int CODE = 200;

    @Value("${dp.appKey:null}")
    String appKey ;
    @Value("${dp.appSecret:null}")
    String appSecret;
    @Value("${dp.host:null}")
    String host ;
    String appId = "10144";
    @Value("${dp.appId:null}")
    private String appIdOld;

    @SuppressWarnings("all")
    //@ApiRateLimitation(limitCount = 10, time = 1, timeUnit = RateIntervalUnit.SECONDS, tokenBucketName = "callApiNonDiscard")
    @ApiLimitation(time = 1, timeUnit = RateIntervalUnit.SECONDS, tokenBucketName = "callDp", limitCount = 10)
    public DPResponseVO callApi(List<String> vinList) {
        //创建请求参数 ---------------------------------------
        QueryParamRequest queryParamRequest = new QueryParamRequest();
        //构造请求参数对象
        //---条件参数
        //添加查询条件,其中key为对应的查询字段,value为查询字段对应的值, 例如这里的id为请求字段名,1为id对应的值,可以设置多个查询参数
        HashMap<String, Object> condition = Maps.newHashMap();
        condition.put("vin_list", vinList);
        //注意：如果是 IN 类型的参数，使用 list 包装参数值。
        queryParamRequest.setConditions(condition);

        //-- 排序(可选设置)
        // 注意oracle和sqlServer使用分页需要同时使用排序
        // 排序字段,根据返回参数指定升序或者降序, 例如返回结果按id进行升序, 可设置多个字段进行升序或者降序
        // 使用分页则必须指定排序字段，并且要使用排序稳定的字段（例如主键、联合主键）保证每次排序结果相同，避免分页不准确
        ArrayList<OrderBy> orderList = Lists.newArrayList();
        //OrderBy.Order asc = OrderBy.Order.ASC;
        //OrderBy orderByColumn1 = new OrderBy("your order column", asc);
        //OrderBy orderByColumn2 = new OrderBy("your order column", asc);
        //orderList.add(orderByColumn1);
        //orderList.add(orderByColumn2);
        queryParamRequest.setOrderBys(orderList);

        //指定返回有权限的参数
        List<String> returnFiles = Lists.newArrayList(Splitter.on(",").split(API_RETURN_FIELDS));
        queryParamRequest.setReturnFields(returnFiles);

        //进行分页(可选).不设置，默认取1~1000条数据
//        queryParamRequest.setPageStart(1);
//        queryParamRequest.setPageSize(10);

        // 是否缓存查询结果，开启则会缓存同一个API相同条件、想通返回字段的查询结果
        // 适用于数据不变化的查询
        // 缓存时长默认30分钟, 3.5.6 版本后，在开发API时可设置缓存时长
        queryParamRequest.setUseResultCache(true);
        queryParamRequest.setKeepColumnCase(true);
        //结束创建请求参数 ---------------------------------------

        ApiClient apiClient = createHttpClient(appKey, appSecret);
        // log.info("callApi查询host:{}", host);
        // log.info("callApi查询appIdOld:{}", appIdOld);
        // log.info("callApi查询appKey:{}", appKey);
        // log.info("callApi查询appSecret:{}", appSecret);
        // log.info("callApi查询appId:{}", appId);
        // log.info("callApi查询queryParamRequest:{}", JSON.toJSONString(queryParamRequest));
        List<DPResultVO> dpResultVOList = new ArrayList<>();
        DPResponseVO dpResponseVO = new DPResponseVO();
        try {
            ApiResponse response = LIST_TYPE.equalsIgnoreCase(API_TYPE) ? apiClient.listSync(appId, queryParamRequest)
                    : apiClient.getSync(appId, queryParamRequest);

            // log.info("callApi查询result:{}", JSON.toJSONString(response));
            String result = new String(response.getBody());
            // String code = JSONObject.parseObject(result).get("code").toString();
//            log.info("DP system response:{}",getResultString(response));
            JSONArray resultsArray = JSONObject.parseObject(result).getJSONArray("results");
            ObjectMapper objectMapper = new ObjectMapper();
//            objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
            if (resultsArray != null) {
                for (int i = 0; i < resultsArray.size(); i++) {
                    JSONObject obj = resultsArray.getJSONObject(i);
                    // 将下划线命名转换为驼峰命名
                    JSONObject camelCaseObj = new JSONObject();
                    for (String key : obj.keySet()) {
                        String camelCaseKey = convertToCamelCase(key);
                        camelCaseObj.put(camelCaseKey, obj.get(key));
                    }
                    String jsonString = camelCaseObj.toJSONString();
                    DPResultVO dpResultVO = objectMapper.readValue(jsonString, DPResultVO.class);
                    dpResultVOList.add(dpResultVO);
                }
            }
            dpResponseVO.setResult(dpResultVOList);
            return dpResponseVO;
        } catch (Exception e) {
            dpResponseVO.setQueryResult(e.getMessage());
            e.printStackTrace();
        }
        dpResponseVO.setResult(dpResultVOList);
        return dpResponseVO;
    }
    private static String convertToCamelCase(String input) {
        StringBuilder camelCase = new StringBuilder();
        boolean nextUpperCase = false;
        for (char c : input.toCharArray()) {
            if (c == '_') {
                nextUpperCase = true;
            } else if (nextUpperCase) {
                camelCase.append(Character.toUpperCase(c));
                nextUpperCase = false;
            } else {
                camelCase.append(c);
            }
        }
        return camelCase.toString();
    }

    private ApiClient createHttpClient(String appKey, String appSecret) {
        ApiClientBuilderParams params = new ApiClientBuilderParams();
        params.setAppKey(appKey);
        params.setAppSecret(appSecret);
        params.setHost(host);
        //默认为http协议, 如果API 支持 HTTPS, 这里也可以设置HTTPS
        params.setScheme(Scheme.HTTP);
        params.setStage("RELEASE");
        params.setEnv("PROD");
        return new ApiClient(params);
    }
}
