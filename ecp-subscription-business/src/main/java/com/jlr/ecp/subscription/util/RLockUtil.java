package com.jlr.ecp.subscription.util;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;

import java.util.concurrent.TimeUnit;

@Slf4j
public class RLockUtil {
    /**
     * 尝试获取分布式锁。
     *
     * @param rLock 分布式锁对象，实现ReentrantLock接口。
     * @return boolean 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果线程在等待锁时被中断，抛出此运行时异常。
     */
    public static boolean tryLock(RLock rLock) {
        try {
            return tryLock(rLock,10, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 尝试获取分布式锁。
     *
     * @param rLock 分布式锁对象，实现ReentrantLock接口。
     * @param waitTime 获取锁的最大等待时间。
     * @param unit 等待时间的单位。
     * @return 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果在尝试获取锁的过程中发生异常。
     */
    public static boolean tryLock(RLock rLock, int waitTime, TimeUnit unit) {
        try {
            return rLock.tryLock(waitTime,  unit);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 尝试获取分布式锁。
     *
     * @param rLock 分布式锁对象，实现ReentrantLock接口。
     * @param waitTime 获取锁的最大等待时间。
     * @param leaseTime 锁的持有时间。
     * @param unit 时间单位。
     * @return 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果尝试获取锁时发生异常。
     */
    public static boolean tryLock(RLock rLock, int waitTime, int leaseTime, TimeUnit unit) {
        try {
            return rLock.tryLock(waitTime, leaseTime, unit);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 尝试释放一个锁，并提供重试机制。
     *
     * @param lock 需要被释放的锁，类型为RLock，是可重入锁的接口。
     * @param retryCount 重试次数，表示如果第一次释放锁失败，将尝试再次释放的次数。
     * @return 返回一个布尔值，如果锁成功释放，则返回true；否则返回false。
     */
    public static boolean unlock(RLock lock, int retryCount) {
        return releaseLockWithRetry(lock, retryCount);
    }

    /**
     * 尝试释放一个锁，并提供重试机制。
     *
     * @param lock 需要被释放的锁，类型为RLock，是可重入锁的接口。
     * @param retryCount 重试次数，表示如果第一次释放锁失败，将尝试再次释放的次数。
     * @return 返回一个布尔值，如果锁成功释放，则返回true；否则返回false。
     */
    private static boolean releaseLockWithRetry(RLock lock, int retryCount) {
        if (retryCount <= 0) {
            return false;
        }
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                return true;
            }
        } catch (Exception e) {
            log.info("释放锁第:{}次重试异常:", retryCount, e);
            // 增加重试间隔时间
            try {
                Thread.sleep(100); // 简单延时后重试
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                log.error("线程中断异常：", ex);
            }
        }
        return releaseLockWithRetry(lock, retryCount - 1);
    }
}
