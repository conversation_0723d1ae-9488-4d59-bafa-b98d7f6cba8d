package com.jlr.ecp.subscription.api.vininit.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.subscription.api.vininit.constants.VehicleProcessConstants;
import com.jlr.ecp.subscription.api.vininit.model.ProcessResult;
import com.jlr.ecp.subscription.api.vininit.service.VehicleDataPersistService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.SubscriptionServiceRepository;
import com.jlr.ecp.subscription.enums.remote.RemoteDataErrorType;
import com.jlr.ecp.subscription.service.incontrol.IncontrolCustomerService;
import com.jlr.ecp.subscription.service.remoteservice.RemoteOriginalDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 车辆数据持久化服务实现
 *
 */
@Service
@Slf4j
public class VehicleDataPersistServiceImpl implements VehicleDataPersistService {
    
    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;
    
    @Resource
    private SubscriptionServiceRepository subscriptionServiceRepository;
    
    @Resource
    private IncontrolCustomerService incontrolCustomerService;
    
    @Resource
    private RemoteOriginalDataService remoteOriginalDataService;
    
    @Override
    public ProcessResult persistBatchData(List<IncontrolVehicleDO> insertVehicleList,
                                          List<IncontrolVehicleDO> updateVehicleList,
                                         List<SubscriptionServiceDO> serviceDOList,
                                         Set<IncontrolCustomerDO> customerSet,
                                         Map<String, Set<Long>> vinIdMap,
                                         Set<String> failVinSet) {
        
        log.info("开始持久化批次数据，insert数量: {}, update车数量: {}, service数量: {}, customer数量: {}, 失败VIN: {}",
                insertVehicleList.size(), updateVehicleList.size(), serviceDOList.size(), customerSet.size(), failVinSet.size());
        
        // 按VIN分组数据
        Map<String, List<SubscriptionServiceDO>> serviceDOMap = serviceDOList.stream()
            .collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin));
        
        Map<String, IncontrolVehicleDO> insetVehicleDOMap = convertMap(insertVehicleList,
            IncontrolVehicleDO::getCarVin, v -> v, (v1, v2) -> v1);
        Map<String, IncontrolVehicleDO> updateVehicleDOMap = convertMap(updateVehicleList,
            IncontrolVehicleDO::getCarVin, v -> v, (v1, v2) -> v1);

        // 获取需要处理的VIN集合
        Set<String> allVinSet = new HashSet<>();
        allVinSet.addAll(serviceDOMap.keySet());
        allVinSet.addAll(insetVehicleDOMap.keySet());
        allVinSet.addAll(updateVehicleDOMap.keySet());
        allVinSet.removeAll(failVinSet);

        //更新dp查询失败的vin的原始数据
        updateDpFindFailed(vinIdMap, failVinSet);
        
        // 分批处理每个VIN的数据，避免数据库连接池耗尽
        Set<String> successVin = Collections.synchronizedSet(new HashSet<>());
        Set<String> processFailVin = Collections.synchronizedSet(new HashSet<>());
        Map<String, Exception> vinExceptionMap = Collections.synchronizedMap(new HashMap<>());

        // 分批处理，每批最多处理连接池数量的一半，避免连接池耗尽
        int batchSize = Math.min(VehicleProcessConstants.BatchConfig.VIN_PERSIST_BATCH_SIZE, allVinSet.size());
        List<String> vinList = new ArrayList<>(allVinSet);

        log.info("开始分批处理VIN数据，总数量: {}, 批次大小: {}", vinList.size(), batchSize);

        for (int i = 0; i < vinList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, vinList.size());
            List<String> batchVins = vinList.subList(i, endIndex);
            log.info("处理第{}批VIN数据，范围: {}-{}, 数量: {}", (i / batchSize + 1), i + 1, endIndex, batchVins.size());

            // 并行处理当前批次
            batchVins.parallelStream().forEach(vin -> {
                try {
                    boolean success = saveVehicleAndServiceForVin(vin,
                        serviceDOMap.get(vin), insetVehicleDOMap.get(vin), updateVehicleDOMap.get(vin));
                    if (success) {
                        successVin.add(vin);
                    } else {
                        processFailVin.add(vin);
                    }
                } catch (Exception e) {
                    log.info("保存VIN数据异常: {}, 涉及原始数据ID: {}", vin, vinIdMap.get(vin), e);
                    vinExceptionMap.put(vin, e);
                    processFailVin.add(vin);
                }
            });

            log.info("第{}批VIN数据处理完成", (i / batchSize + 1));
        }

        // 批量处理异常数据的状态更新
        if (!vinExceptionMap.isEmpty()) {
            for (Map.Entry<String, Exception> entry : vinExceptionMap.entrySet()) {
                String vin = entry.getKey();
                Exception e = entry.getValue();

                String errorMessage = StrUtil.maxLength(ExceptionUtil.getRootCauseMessage(e),
                    VehicleProcessConstants.ERROR_DESC_MAX_LENGTH);

                Set<Long> failIds = vinIdMap.get(vin);
                if (CollUtil.isNotEmpty(failIds)) {
                    remoteOriginalDataService.updateDataFail(failIds,
                        RemoteDataErrorType.SAVE_FAIL.getType(), errorMessage);
                }
            }
        }
        
        // 保存IncontrolCustomer信息
        saveCustomerData(customerSet);

        // 更新处理状态
        updateProcessStatus(vinIdMap, successVin, processFailVin);
        
        // 计算结果
        Set<Long> successIds = calculateSuccessIds(vinIdMap, successVin);
        Set<Long> failedIds = calculateFailedIds(vinIdMap, processFailVin);
        
        ProcessResult result = ProcessResult.of(CollUtil.size(successIds), CollUtil.size(failedIds)+CollUtil.size(failVinSet));
        log.info("批次数据持久化完成: {}", result);
        return result;
    }

    public void updateDpFindFailed(Map<String, Set<Long>> vinIdMap, Set<String> failVinSet) {
        if (CollUtil.isEmpty(failVinSet)) {
            log.info("更新Dp查询失败, 当前无查询失败的vin");
            return ;
        }
        log.info("更新Dp查询失败, DP查询失败的VIN数量: {}, failVinSet:{}", CollUtil.size(failVinSet), failVinSet);
        Set<Long> lostIds = vinIdMap.entrySet().stream()
                .filter(e -> failVinSet.contains(e.getKey()))
                .flatMap(v -> v.getValue().stream())
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(lostIds)) {
            remoteOriginalDataService.updateDataFail(lostIds,
                    RemoteDataErrorType.DP_FAIL.getType(), RemoteDataErrorType.DP_FAIL.getDesc());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveVehicleAndServiceForVin(String vin, 
                                              List<SubscriptionServiceDO> serviceDOList, 
                                              IncontrolVehicleDO insertVehicleDO,
                                               IncontrolVehicleDO updateVehicleDO) {
        try {
            log.info("保存或更新车和服务, VIN数据: {}, 待插入车:{}, 待更新车：{}, 服务数量: {}", vin,
                    Objects.nonNull(insertVehicleDO), Objects.nonNull(updateVehicleDO), CollUtil.size(serviceDOList));
            // 保存服务数据
            if (CollUtil.isNotEmpty(serviceDOList)) {
                saveServiceData(serviceDOList);
            }
            // 保存车辆数据
            if (insertVehicleDO != null) {
                saveVehicleData(insertVehicleDO);
            }
            if(updateVehicleDO != null) {
                updateVehicleData(updateVehicleDO);
            }
            return true;
        } catch (Exception e) {
            log.info("保存VIN数据失败: {}", vin, e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }
    
    /**
     * 保存服务数据
     */
     public void saveServiceData(List<SubscriptionServiceDO> serviceDOList) {
        if (CollUtil.isEmpty(serviceDOList)) {
            log.info("服务数据列表为空，跳过保存");
            return;
        }
        // 过滤空对象和关键字段为空的数据，然后去重
        Map<String, SubscriptionServiceDO> filterServiceMap = serviceDOList.stream()
            .filter(Objects::nonNull)
            .filter(service -> StrUtil.isNotBlank(service.getCarVin()) &&
                              StrUtil.isNotBlank(service.getServicePackage()) &&
                              StrUtil.isNotBlank(service.getServiceName()))
            .collect(Collectors.toMap(
                k -> buildServiceKey(k.getCarVin(), k.getServicePackage(), k.getServiceName()),
                v -> v,
                (v1, v2) -> {
                    log.info("发现重复服务键，使用第一个: {}", buildServiceKey(v1.getCarVin(), v1.getServicePackage(), v1.getServiceName()));
                    return v1;
                }));

        if (filterServiceMap.isEmpty()) {
            log.info("过滤后无有效服务数据需要保存");
            return;
        }
        subscriptionServiceRepository.saveOrUpdateBatch(filterServiceMap.values());
    }
    
    /**
     * 保存车辆数据
     */
     public void saveVehicleData(IncontrolVehicleDO vehicleDO) {
        incontrolVehicleRepository.saveOrUpdate(vehicleDO);
    }

    public void updateVehicleData(IncontrolVehicleDO vehicleDO) {
        incontrolVehicleRepository.updateBatchWithNulls(List.of(vehicleDO));
    }
    
    /**
     * 保存客户数据
     */
    private void saveCustomerData(Set<IncontrolCustomerDO> customerSet) {
        if (CollUtil.isNotEmpty(customerSet)) {
            log.info("保存客户数据，数量: {}", customerSet.size());
            incontrolCustomerService.saveOrUpdateIncontrolCustomerWithTSDP(customerSet);
        }
    }
    
    /**
     * 更新处理状态
     */
    private void updateProcessStatus(Map<String, Set<Long>> vinIdMap, 
                                   Set<String> successVin, 
                                   Set<String> failedVin) {
        log.info("更新处理状态, vinIdMap数量:{}, successVin数量:{}, failedVin数量:{}", CollUtil.size(vinIdMap), CollUtil.size(successVin), CollUtil.size(failedVin));
        // 更新成功状态
        Set<Long> successIds = calculateSuccessIds(vinIdMap, successVin);
        if (CollUtil.isNotEmpty(successIds)) {
            remoteOriginalDataService.updateDataSuccess(successIds);
            log.info("更新成功状态，数量: {}", successIds.size());
        }

        //更新失败状态
        Set<Long> failedIds = calculateFailedIds(vinIdMap, failedVin);
        log.info("更新处理状态, failedIds数量: {}", CollUtil.size(failedIds));
        if (CollUtil.isNotEmpty(failedIds)) {
            remoteOriginalDataService.updateDataFail(failedIds, RemoteDataErrorType.SAVE_FAIL.getType(), RemoteDataErrorType.SAVE_FAIL.getDesc());
        }
    }
    
    /**
     * 计算成功的ID集合
     */
    private Set<Long> calculateSuccessIds(Map<String, Set<Long>> vinIdMap, Set<String> successVin) {
        return vinIdMap.entrySet().stream()
            .filter(e -> successVin.contains(e.getKey()))
            .flatMap(v -> v.getValue().stream())
            .collect(Collectors.toSet());
    }
    
    /**
     * 计算失败的ID集合
     */
    private Set<Long> calculateFailedIds(Map<String, Set<Long>> vinIdMap, Set<String> failedVin) {
        return vinIdMap.entrySet().stream()
            .filter(e -> failedVin.contains(e.getKey()))
            .flatMap(v -> v.getValue().stream())
            .collect(Collectors.toSet());
    }
    
    /**
     * 构建服务键
     */
    private String buildServiceKey(String carVin, String servicePackage, String serviceName) {
        return carVin + Constants.DEFAULT_CONCAT_STR + servicePackage + Constants.DEFAULT_CONCAT_STR + serviceName;
    }
}
