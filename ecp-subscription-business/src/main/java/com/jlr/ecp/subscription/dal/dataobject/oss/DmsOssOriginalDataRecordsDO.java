package com.jlr.ecp.subscription.dal.dataobject.oss;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_dms_oss_original_data_records表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_dms_oss_original_data_records")
public class DmsOssOriginalDataRecordsDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * BAU任务ID;BAU任务ID
     */
    @TableField(value = "bau_job_id")
    private Long bauJobId;

    /**
     * 数据ID;数据ID
     */
    @TableField(value = "data_id")
    private Long dataId;

    /**
     * vin
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * sota查询结果;sota查询结果
     */
    @TableField(value = "sota_result")
    private String sotaResult;

    /**
     * appd查询结果;appd查询结果
     */
    @TableField(value = "appd_result")
    private String appdResult;

    /**
     * 联通查询结果;cu查询结果
     */
    @TableField(value = "cu_result")
    private String cuResult;

    /**
     * amap查询结果;amap查询结果
     */
    @TableField(value = "amap_result")
    private String amapResult;

    /**
     * DP查询结果;dp查询结果
     */
    @TableField(value = "dp_result")
    private String dpResult;

    /**
     * appd过期同步结果;appd过期同步结果 0：失败 1：成功
     */
    @TableField(value = "appd_sync_result")
    private Integer appdSyncResult;

    /**
     * cu过期同步结果;cu过期同步结果 0：失败 1：成功
     */
    @TableField(value = "cu_sync_result")
    private Integer cuSyncResult;
}

