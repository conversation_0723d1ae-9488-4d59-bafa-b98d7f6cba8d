package com.jlr.ecp.subscription.enums.brand;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum BrandCodeEnum {

    /**
     * 路虎品牌
     */
    LAND_ROVER("LR,LAN", 1, "路虎"),

    /**
     * 捷豹品牌
     */
    JAGUAR("JAG", 2, "捷豹");

    /**
     * 品牌编码，用于标识品牌
     */
    private final String brandCode;

    /**
     * 数据库中的品牌ID
     */
    private final Integer brandId;

    /**
     * 品牌名称
     */
    private final String brandName;



    /**
     * 根据数据库中的品牌名称获取品牌id
     *
     * @param  brandCode 品牌名称
     * @return 对应的品牌名称
     */
    public static Integer getBrandIdByCode(String brandCode) {
        for (BrandCodeEnum brand : BrandCodeEnum.values()) {
            if (brand.getBrandCode().contains(brandCode)) {
                return brand.getBrandId();
            }
        }
        throw new IllegalArgumentException("Invalid brand code: " + brandCode);
    }
}
