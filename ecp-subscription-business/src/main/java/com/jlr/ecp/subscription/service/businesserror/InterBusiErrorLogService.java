package com.jlr.ecp.subscription.service.businesserror;


import com.jlr.ecp.subscription.dal.dataobject.businesserror.InterBusiErrorLogDO;
import com.jlr.ecp.subscription.enums.busierror.BusiErrorEnum;

/**
 * 业务异常日志表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-05 13:46:20
 * @description 由 Mybatisplus Code Generator 创建
 */
public interface InterBusiErrorLogService {

    /**
     * 记录业务错误日志
     * @param businessId 业务id
     * @param response   业务出错报文
     * @param businessParams  业务参数
     * @param errorEnum  业务类型枚举
     * @return 新增或更新的错误日志对象
     */
    InterBusiErrorLogDO saveOrUpdateErrorLog(String businessId, String response, String businessParams, BusiErrorEnum errorEnum);

}
