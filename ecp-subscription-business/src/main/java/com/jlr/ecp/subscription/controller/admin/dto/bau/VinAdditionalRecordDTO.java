package com.jlr.ecp.subscription.controller.admin.dto.bau;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@Schema(description = "VIN补录DTO")
@Validated
public class VinAdditionalRecordDTO {

    @Schema(description = "vin")
    @NotBlank(message = "vin不能为空")
    private String vin;

    @Schema(description = "发票日期")
    @NotBlank(message = "发票日期不能为空")
    private String invoiceDate;

    @Schema(description = "ICCID")
    @NotBlank(message = "ICCID不能为空")
    @Pattern(regexp = "^\\d{20}$", message = "ICCID需由20位数字组成")
    private String iccid;
}
