package com.jlr.ecp.subscription.dal.dataobject.fufilment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_vcs_order_fufilment
 *
 * @TableName t_vcs_order_fufilment
 */
@TableName(value = "t_vcs_order_fufilment")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsOrderFufilmentDO extends BaseDO {
    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 履约号ID;履约号ID，雪花算法
     */
    @TableField(value = "fufilment_id")
    private String fufilmentId;

    /**
     * 订单号;订单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * VCS订单编码
     * */
    @TableField(value = "vcs_order_code")
    private String vcsOrderCode;

    /**
     * 订单item编码;订单item编码
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;


    /**
     * brand_code
     */
    @TableField(value = "brand_code")
    private String brandCode;

    /**
     * 车辆VIN;车辆VIN
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 服务起始时间;服务起始时间
     */
    @TableField(value = "service_begin_date")
    private LocalDateTime serviceBeginDate;

    /**
     * 服务结束时间;服务结束时间
     */
    @TableField(value = "service_end_date")
    private LocalDateTime serviceEndDate;

    /**
     * 服务状态;服务状态，1：未激活 2：已激活 3：激活关闭 4：激活失败
     */
    @TableField(value = "service_status")
    private Integer serviceStatus;

    /**
     * 订单item项备注信息;订单item项备注信息
     */
    @TableField(value = "order_item_remark")
    private String orderItemRemark;

    /**
     * 服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；
     */
    @TableField(value = "service_type")
    private Integer serviceType;


}