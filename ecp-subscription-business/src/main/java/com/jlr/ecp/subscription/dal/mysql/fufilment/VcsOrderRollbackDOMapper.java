package com.jlr.ecp.subscription.dal.mysql.fufilment;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【t_vcs_order_rollback(t_vcs_order_rollback)】的数据库操作Mapper
* @createDate 2024-01-19 14:15:09
* @Entity generator.domain.VcsOrderRollbackDO
*/
@Mapper
public interface VcsOrderRollbackDOMapper extends BaseMapperX<VcsOrderRollbackDO> {


    default VcsOrderRollbackDO getOneByRollBackId(String rollbackFufilmentId){
       return selectOne(new LambdaQueryWrapperX<VcsOrderRollbackDO>()
               .eq(VcsOrderRollbackDO::getRollbackFufilmentId,rollbackFufilmentId)
               .eq(BaseDO::getIsDeleted,false));
    }
}




