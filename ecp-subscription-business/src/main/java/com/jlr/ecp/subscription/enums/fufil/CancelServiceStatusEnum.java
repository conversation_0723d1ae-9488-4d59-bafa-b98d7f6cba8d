package com.jlr.ecp.subscription.enums.fufil;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CancelServiceStatusEnum {
    UNACTIVATED(1, "激活关闭中", "已激活"),
    ACTIVATE_OFF(2, "激活关闭", "关闭激活"),
    ACTIVATE_OFF_FAILURE(3, "激活关闭失败", "已激活"),
    BUSINESS_ACTIVATE_OFF_FAILURE(4, "业务激活关闭失败", "已激活"),
    ;

    /**
     * 状态码
     * */
    public final Integer status;

    /**
     * 状态描述
     * */
    public final String desc;

    /**
     * 给前端的状态描述
     * */
    public final String descForApp;

    /**
     * 根据服务状态的整数值获取对应的描述。
     *
     * @param code 服务状态的整数值
     * @return 对应的服务状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        // 如果没有找到对应状态，可选择返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (CancelServiceStatusEnum status : CancelServiceStatusEnum.values()) {
            if (status.getStatus().equals(code)) {
                return status.getDescForApp();
            }
        }
        throw new IllegalArgumentException("Invalid reverse activation service status code: " + code);
    }
}
