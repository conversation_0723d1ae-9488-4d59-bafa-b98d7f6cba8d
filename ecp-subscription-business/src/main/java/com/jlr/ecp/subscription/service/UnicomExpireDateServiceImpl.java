package com.jlr.ecp.subscription.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.SimCardInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.UnicomCheckDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.vehicle.UnicomCheckMapper;
import com.jlr.ecp.subscription.enums.unicom.UnicomBookStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnicomExpireDateServiceImpl implements UnicomExpireDateService{


    @Resource
    private UnicomCheckMapper unicomCheckMapper;


    @Resource
    private RedisTemplate<String,Integer> redisTemplate;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private PIVIUnicomService piviUnicomService;


    @Override
    public void getUnicomExpireDate(int pageNo, int totalPage,int pageSize) {

        for (int i = pageNo; i < totalPage; i++) {
            Page<UnicomCheckDO> page = new Page<>(i, pageSize);
            Page<UnicomCheckDO> piviUnicomCheckDOPage = unicomCheckMapper.selectPage(page, new LambdaQueryWrapperX<UnicomCheckDO>()
                    .isNull(UnicomCheckDO::getExpiryTime));
            if (CollUtil.isEmpty(piviUnicomCheckDOPage.getRecords())) {
                log.info("UNICOM查询当前读取第：{}页，每页size:{}", i, pageSize);
                return;
            }

            long startTime = System.currentTimeMillis();
            List<CompletableFuture<UnicomCheckDO>> futures = getCompletableFutures(piviUnicomCheckDOPage);

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            List<UnicomCheckDO> amapDataDOList = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            unicomCheckMapper.updateBatch(amapDataDOList);
            String key = "unicom:expire:date:page";
            redisTemplate.opsForValue().set(key, i, 2, TimeUnit.DAYS);

            long endTime = System.currentTimeMillis();
            long finishTime = endTime - startTime;
            log.info("获取UNICOM到期时间，第:{}页，用时:{}毫秒", i, finishTime);
            if (finishTime < 1000) {
                try {
                    Thread.sleep(1010 - finishTime);
                } catch (InterruptedException e) {
                    log.error("获取UNICOM到期时间暂停失败:", e);
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 根据PiviUnicomCheckDOPage中的记录生成CompletableFuture列表
     * 该方法用于异步处理每个记录，以提高处理效率
     *
     * @param piviUnicomCheckDOPage 包含UnicomCheckDO对象的页面
     * @return CompletableFuture任务列表，每个任务在完成后将返回UnicomCheckDO对象
     */
    private List<CompletableFuture<UnicomCheckDO>> getCompletableFutures(Page<UnicomCheckDO> piviUnicomCheckDOPage) {
        return piviUnicomCheckDOPage.getRecords().stream()
                .map(piviPackageDO -> CompletableFuture.supplyAsync(() -> {
                    try{
                        UnicomRespVO unicomRespVO = piviUnicomService.getSimCardInfo(piviPackageDO.getIccid());

                        if(unicomRespVO!=null && UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc())){
                            UnicomRespData unicomRespData = unicomRespVO.getUnicomRespData();
                            return buildPiviPackageDO(piviPackageDO, unicomRespData);
                        }else {
                            log.info("ICCID没查询到unicomRespData数据:{}",piviPackageDO.getIccid());
                            return piviPackageDO;
                        }
                    }catch (Exception e){
                        log.info("ICCID异常数据:{}",piviPackageDO.getIccid());
                        return UnicomCheckDO.builder().iccid(piviPackageDO.getIccid()).errorMsg(e.getMessage()).build();
                    }
                }, subscribeAsyncThreadPool))
                .collect(Collectors.toList());
    }

    /**
     * 构建Pivi包装类对象
     * 此方法用于根据联通响应数据更新Pivi包装类对象的信息
     * 主要逻辑包括：
     * 1. 检查联通响应数据是否有效
     * 2. 提取并设置ICCID、过期时间、激活时间等信息
     * 3. 处理特殊情况，如响应数据为空或产品预订信息列表为空
     *
     * @param piviPackageDO 待更新的Pivi包装类对象
     * @param unicomRespData 联通响应数据，包含SIM卡信息和产品预订信息列表
     * @return 更新后的Pivi包装类对象
     */
    private static UnicomCheckDO buildPiviPackageDO(UnicomCheckDO piviPackageDO, UnicomRespData unicomRespData) {
        if(unicomRespData !=null){
            SimCardInfo simCardInfo = unicomRespData.getSimCardInfo();
            List<ProductBookInfo> productBookInfoList = unicomRespData.getProductBookInfoList();
            if(simCardInfo!=null && CollUtil.isNotEmpty(productBookInfoList)){
                ProductBookInfo latestProductBookInfo = productBookInfoList.stream().filter(e -> UnicomBookStatusEnum.ACTIVE.getType().equals(e.getBookStatus()))
                        .max(Comparator.comparingLong(e -> Long.parseLong(e.getExpireTime()))).get();
                piviPackageDO.setIccid(piviPackageDO.getIccid());
                piviPackageDO.setExpiryTime(latestProductBookInfo.getExpireTime());
                piviPackageDO.setActiveTime(latestProductBookInfo.getActiveTime());
                piviPackageDO.setTenantId(1);
                piviPackageDO.setBookStatus(latestProductBookInfo.getBookStatus());
                piviPackageDO.setExtBookId(latestProductBookInfo.getExternalBookId());
                return piviPackageDO;
            }else {
                log.info("ICCID没查询到productBookInfoList数据:{}", piviPackageDO.getIccid());
                return piviPackageDO;
            }
        }else {
            log.info("ICCID没查询到unicomRespData数据:{}", piviPackageDO.getIccid());
            return piviPackageDO;
        }
    }

}