package com.jlr.ecp.subscription.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

@Configuration
@Slf4j
public class RestTemplateConfig {
//
//    @Bean
//    RestTemplate restTemplate () throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
//
//        SSLContextBuilder builder = new SSLContextBuilder();
//        builder.loadTrustMaterial(null, (chain, authType) -> true); // 信任所有证书
//
//        CloseableHttpClient httpClient = HttpClients.custom()
//                .setSSLContext(builder.build())
//                .setSSLHostnameVerifier(new NoopHostnameVerifier()) // 禁用主机名验证
//                .build();
//
//        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
//
//        log.info("注入自定义restTemplate成功");
//        return new RestTemplate(requestFactory);
//    }
}
