package com.jlr.ecp.subscription.enums.amap;

import com.jlr.ecp.subscription.enums.oss.AMapResultEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AmaPErrorCode {
    UNEXPECTED_ERROR(500, "0", "Unexpected error.", "未知服务端错误", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    SUCCESSFUL(200, "1", "Successful.", "成功执行", "成功执行", AMapResultEnum.SUCCESS.getCode()),
    PARAMS_ERROR(400, "3", "Params error.", "参数错误", "未找到该VIN", AMapResultEnum.VIN_NOT_FOUND.getCode()),
    SIGNATURE_VERIFICATION_FAILED(401, "4", "Signature verification failed.", "签名错误","系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    NOT_FOUND(404, "7", "Not found.", "数据或接口未找到", "未找到该VIN", AMapResultEnum.VIN_NOT_FOUND.getCode()),
    HTTP_BODY_EMPTY(400, "9", "HTTP body empty.", "请求体为空","系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    REQUEST_METHOD_NOT_SUPPORTED(405, "11", "Request method not supported", "请求方式不支持", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    PERMISSION_DENIED(403, "13", "Permission denied.", "无权限访问", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    OUT_OF_RANGE(400, "21", "Out of range.", "数据超出允许范围", "续费已超额", AMapResultEnum.SYSTEM_ERROR.getCode()),
    TIMEOUT(504, "25", "Timeout.", "请求超时", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    REQUEST_JSON_SCHEMA_ERROR(400, "101", "Request json schema error.", "请求体结构错误", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    AMOUNT_OUT_OF_LIMIT(400, "110", "Amount out of limit.", "订单内数量超出限制", "续费已超额", AMapResultEnum.SYSTEM_ERROR.getCode()),
    VID_LENGTH_ERROR(400, "111", "VID length error.", "设备码长度错误", "VIN号格式错误", AMapResultEnum.SYSTEM_ERROR.getCode()),
    CUSTOM_ORDER_ID_DUPLICATED(400, "112", "Custom order ID duplicated.", "客户订单号重复", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode()),
    VID_DUPLICATED_WITH_EXISTING_ORDER(400, "121", "VID is duplicated with existing order", "设备码与历史订单重复", "系统异常", AMapResultEnum.SYSTEM_ERROR.getCode());

    private final Integer httpStatus;
    private final String code;
    private final String message;
    private final String description;
    private final String manualRenewalDesc;
    private final Integer resultCode;


    /**
     * 通过错误码获取手动续订描述
     *
     * @param code 错误码，用于识别特定的错误情况
     * @return 返回与错误码对应的续订描述如果找不到对应的错误码，则返回空字符串
     */
    public static String getManualRenewalDescByCode(String code) {
        AmaPErrorCode[] amaPErrorCodes = AmaPErrorCode.values();
        for (AmaPErrorCode amaPErrorCode : amaPErrorCodes) {
            if (amaPErrorCode.getCode().equals(code)) {
                return amaPErrorCode.getManualRenewalDesc();
            }
        }
        return "";
    }

    /**
     * 通过错误码获取高德校验结果
     */
    public static Integer getAMapResultByCode(String code) {
        AmaPErrorCode[] errorCodes = AmaPErrorCode.values();
        for (AmaPErrorCode errorCode : errorCodes) {
            if (errorCode.getCode().equals(code)) {
                return errorCode.getResultCode();
            }
        }
        return null;
    }
}
