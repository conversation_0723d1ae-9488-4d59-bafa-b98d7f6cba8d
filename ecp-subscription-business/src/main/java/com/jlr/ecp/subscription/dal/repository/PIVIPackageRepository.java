package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * PIVI包信息Repository接口
 *
 */
public interface PIVIPackageRepository extends IService<PIVIPackageDO> {

    /**
     * 根据车辆VIN查找ICCID
     *
     * @param carVin 车辆VIN
     * @return PIVI包信息
     */
    PIVIPackageDO findICCIDByCarVin(String carVin);

    /**
     * 根据车辆VIN列表查询PIVI包信息
     *
     * @param carVinCollection 车辆VIN列表
     * @return PIVI包信息列表
     */
    List<PIVIPackageDO> selectPIVIPackageDOByCarVinList(Collection<String> carVinCollection);

    Map<String, PIVIPackageDO> queryPIVIPackageDOMap(Collection<String> carVinCollection);

    /**
     * 分页查询VIN过期服务（按数据范围）
     *
     * @param page 分页对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return VIN过期服务DTO列表
     */
    List<VinExpireServiceDTO> getVinExpireServiceByDataRange(Page<VinExpireServiceDTO> page,
                                                             LocalDateTime startTime,
                                                             LocalDateTime endTime);

    /**
     * 查询VIN过期服务（按数据范围，不分页）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return VIN过期服务DTO列表
     */
    List<VinExpireServiceDTO> getVinExpireServiceByDataRangeForAllNoPage(LocalDateTime startTime,
                                                                         LocalDateTime endTime);

    /**
     * 根据VIN获取指定的PIVI数据
     *
     * @param vin 车辆VIN
     * @return PIVI包信息
     */
    PIVIPackageDO selectOnePiviDataByVin(String vin);

    /**
     * 根据车辆VIN查询单个PIVI包信息
     *
     * @param carVin 车辆VIN
     * @return PIVI包信息
     */
    PIVIPackageDO selectOneByCarVin(String carVin);
}
