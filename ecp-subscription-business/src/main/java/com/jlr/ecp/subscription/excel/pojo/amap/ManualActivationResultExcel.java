package com.jlr.ecp.subscription.excel.pojo.amap;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = false)
public class ManualActivationResultExcel {
    @ExcelProperty("VIN码")
    private String carVin;

    @ExcelProperty("续费状态")
    private String renewalFeeStatus;

    @ExcelProperty("到期时间")
    private String endDate;

    @ExcelProperty("详情信息")
    private String detailInfo;

    @ExcelProperty("操作者")
    private String operator;

    @ExcelProperty("更新者")
    private String updater;

    @ExcelProperty("操作日期")
    private String operationDate;

    @ExcelProperty("更新时间")
    private String updateTime;

    @ExcelProperty("续费操作ID")
    private String renewalOperationId;

    @ExcelProperty("续费数额(年)")
    private Integer renewalAnnual;

    @ExcelProperty("高德订单ID")
    private String amaPOrderId;

    // 字符串的头背景设置成粉红 IndexedColors.PINK.getIndex()
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 14)
    // 字符串的内容的背景设置成天蓝 IndexedColors.SKY_BLUE.getIndex()
    @ContentStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 40)
    @ExcelProperty("ECP激活结果")
    private String ecpActivationResult;
}
