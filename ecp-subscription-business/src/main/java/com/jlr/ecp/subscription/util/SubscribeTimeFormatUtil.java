package com.jlr.ecp.subscription.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 时间格式工具类
 * */
@Slf4j
public class SubscribeTimeFormatUtil {
    public static final DateTimeFormatter FORMAT_1 = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    public static final DateTimeFormatter FORMAT_2 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    public static final DateTimeFormatter FORMAT_3 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static final DateTimeFormatter ISO_ZONED_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_ZONED_DATE_TIME;

    /**
     * LocalDateTime按照指定格式转化为String
     * @param time 时间
     * @param formatter 格式
     * @return String
     * */
    public static String timeToStringByFormat(LocalDateTime time, DateTimeFormatter formatter) {
        if (Objects.isNull(time)) {
            return "";
        }
        return time.format(formatter);
    }

    /**
     * 将符合指定格式的字符串转换为LocalDateTime对象。
     *
     * @param time 需要转换的日期时间字符串。
     * @param formatter 用于解析日期时间字符串的DateTimeFormatter对象，定义了字符串的格式。
     * @return 转换后的LocalDateTime对象。如果输入的字符串为空或格式不正确，则返回null。
     */
    public static LocalDateTime stringToTimeByFormat(String time, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(time) || time.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(time, formatter);
        } catch (Exception e) {
            log.error("将符合指定格式的字符串转换为LocalDateTime对象解析时间异常，time:{}, formatter:{}", time, formatter);
            return null;
        }
    }

    /**
     * 将字符串日期转换为LocalDate对象
     *
     * @param time 字符串形式的日期
     * @return 如果转换成功，则返回解析后的LocalDate对象；如果输入字符串为空或解析失败，则返回null
     */
    public static LocalDate stringToLocalDateByFormat(String time) {
        if (StringUtils.isBlank(time) || time.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(time, FORMAT_1);
        } catch (Exception e) {
            log.info("将字符串日期转换为LocalDate对象解析时间异常，time:{}", time);
            return null;
        }
    }

    /**
     * 判断给定的日期字符串是否为有效日期
     *
     * @param time 日期字符串，格式由第二个参数formatter指定
     * @param formatter 日期字符串的格式化器，用于解析日期字符串
     * @return 如果日期字符串有效，则返回true；否则返回false
     */
    public static boolean isValidDate(String time, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(time)) {
            return false;
        }
        try {
            LocalDate date = LocalDate.parse(time, formatter);
            String formattedDate = date.format(formatter);
            return formattedDate.equals(time);
        } catch (Exception e) {
            log.info("判断给定的日期字符串是否为有效日期异常：{}, time:{}, formatter:{}", e.getMessage(), time, formatter);
            return false;
        }
    }

    /**
     * 将ISO 8601格式的时间字符串转换为LocalDateTime对象。
     *
     * @param time 需要转换的日期时间字符串。
     * @return 转换后的LocalDateTime对象。如果输入的字符串为空或格式不正确，则返回null。
     */
    public static LocalDateTime stringToTimeByISOZonedDateTimeFormat(String time) {
        if (StringUtils.isBlank(time) || time.trim().isEmpty()) {
            return null;
        }
        try {
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(time, ISO_ZONED_DATE_TIME_FORMATTER);
            return zonedDateTime.toLocalDateTime();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据指定的日期字符串格式，解析出日期时间。
     * 该方法尝试匹配日期字符串的格式为dd/MM/yyyy hh:mm，其中mm是可选的。
     * 如果字符串格式匹配，则解析出年、月、日、小时和分钟，并返回一个LocalDateTime对象。
     * 如果字符串格式不匹配，则记录错误日志并返回null。
     *
     * @param date 日期字符串，格式应为dd/MM/yyyy hh:mm
     * @return 解析成功的日期时间对象，或在格式不匹配时返回null
     */
    public static LocalDateTime dmsInvoiceDateFormatChange(String date) {
        if (org.apache.commons.lang3.StringUtils.isBlank(date)) {
            return null;
        }
        String dateStr = date.trim();
        Pattern pattern = Pattern.compile("(\\d{1,2})/(\\d{1,2})/(\\d{4})\\s+(\\d{1,2}):?(\\d{0,2})");
        Matcher matcher = pattern.matcher(dateStr);
        if (matcher.matches()) {
            int day = Integer.parseInt(matcher.group(1));
            int month = Integer.parseInt(matcher.group(2));
            int year = Integer.parseInt(matcher.group(3));
            int hour = Integer.parseInt(matcher.group(4));
            int minute = matcher.group(5).isEmpty() ? 0 : Integer.parseInt(matcher.group(5));

            return LocalDateTime.of(year, month, day, hour, minute);
        }
        log.error("解析发票时间, invalid date format:{}", date);
        return null;
    }
}