package com.jlr.ecp.subscription.controller.admin.vo.remote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Remote枚举查询的VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class RemoteEnumQueryVO {
    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "描述")
    private String desc;
}
