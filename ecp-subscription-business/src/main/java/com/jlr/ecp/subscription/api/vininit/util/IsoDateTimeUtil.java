package com.jlr.ecp.subscription.api.vininit.util;

import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * ISO 8601日期时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class IsoDateTimeUtil {
    
    /**
     * ISO 8601 UTC格式
     */
    private static final String ISO_8601_UTC_PATTERN = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    
    /**
     * ISO 8601 带时区格式
     */
    private static final String ISO_8601_ZONE_PATTERN = "yyyy-MM-dd'T'HH:mm:ssXXX";
    
    /**
     * 将ISO 8601字符串转换为LocalDateTime（UTC时间）
     *
     * @param isoDateString ISO 8601格式的日期时间字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseIsoToLocalDateTime(String isoDateString) {
        if (isoDateString == null || isoDateString.isEmpty()) {
            return null;
        }
        
        try {
            if (isoDateString.endsWith("Z")) {
                // UTC时间格式: 2025-04-08T10:17:45Z
                return ZonedDateTime.parse(isoDateString)
                    .toLocalDateTime();
            } else if (isoDateString.contains("+") || isoDateString.contains("-")) {
                // 带时区格式: 2025-04-08T10:17:45+08:00
                return ZonedDateTime.parse(isoDateString)
                    .toLocalDateTime();
            } else {
                // 无时区格式: 2025-04-08T10:17:45
                return LocalDateTime.parse(isoDateString);
            }
        } catch (DateTimeParseException e) {
            log.info("解析ISO日期时间失败: {}, 错误: {}", isoDateString, e.getMessage());
            return null;
        }
    }
    
    /**
     * 将ISO 8601字符串转换为LocalDateTime（转换为指定时区）
     *
     * @param isoDateString ISO 8601格式的日期时间字符串
     * @param zoneId        目标时区
     * @return 指定时区的LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseIsoToLocalDateTime(String isoDateString, ZoneId zoneId) {
        if (isoDateString == null || isoDateString.isEmpty() || zoneId == null) {
            return null;
        }
        
        try {
            ZonedDateTime utcTime;
            
            if (isoDateString.endsWith("Z")) {
                // UTC时间格式
                utcTime = ZonedDateTime.parse(isoDateString);
            } else if (isoDateString.contains("+") || isoDateString.contains("-")) {
                // 带时区格式
                utcTime = ZonedDateTime.parse(isoDateString);
            } else {
                // 无时区格式，假设为UTC
                utcTime = LocalDateTime.parse(isoDateString).atZone(ZoneId.of("UTC"));
            }
            
            // 转换为目标时区
            return utcTime.withZoneSameInstant(zoneId).toLocalDateTime();
            
        } catch (DateTimeParseException e) {
            log.info("解析ISO日期时间失败: {}, 错误: {}", isoDateString, e.getMessage());
            return null;
        }
    }
    
    /**
     * 将ISO 8601字符串转换为中国时间（UTC+8）
     *
     * @param isoDateString ISO 8601格式的日期时间字符串
     * @return 中国时区的LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseIsoToChineseTime(String isoDateString) {
        return parseIsoToLocalDateTime(isoDateString, ZoneId.of("Asia/Shanghai"));
    }
    
    /**
     * 将LocalDateTime转换为ISO 8601 UTC格式字符串
     *
     * @param localDateTime LocalDateTime对象
     * @return ISO 8601 UTC格式字符串
     */
    public static String formatToIsoUtc(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        
        // 转换为UTC时间
        ZonedDateTime utcTime = localDateTime.atZone(ZoneId.systemDefault())
            .withZoneSameInstant(ZoneId.of("UTC"));
        
        return DateTimeFormatter.ofPattern(ISO_8601_UTC_PATTERN).format(utcTime);
    }
    
    /**
     * 将LocalDateTime转换为带时区的ISO 8601格式字符串
     *
     * @param localDateTime LocalDateTime对象
     * @param zoneId        时区
     * @return 带时区的ISO 8601格式字符串
     */
    public static String formatToIsoWithZone(LocalDateTime localDateTime, ZoneId zoneId) {
        if (localDateTime == null || zoneId == null) {
            return null;
        }
        
        ZonedDateTime zonedTime = localDateTime.atZone(zoneId);
        return DateTimeFormatter.ofPattern(ISO_8601_ZONE_PATTERN).format(zonedTime);
    }
}
