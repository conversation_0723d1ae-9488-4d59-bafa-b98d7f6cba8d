package com.jlr.ecp.subscription.dal.mysql.vehicle;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.vin.expiry.VinExpiryReportDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface VinExpiryReportDataDOMapper extends BaseMapperX<VinExpiryReportDataDO> {


    int insertRemoteDataFromDetail(@Param("jobId") Long jobId);

    int insertNonRemoteDataFromDetail(@Param("jobId") Long jobId);
 
}