package com.jlr.ecp.subscription.enums.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomRnrQueryStatusEnum {

    QUERYING(1, "查询中"),

    SUCCESS(2, "查询成功"),

    FAILED(3, "查询失败");

    /**
     * 类型
     * */
    public final Integer type;

    /**
     * 描述
     * */
    public final String desc;

    public static String getDescriptionByCode(Integer code) {
        // 或者返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (UnicomRnrQueryStatusEnum status : UnicomRnrQueryStatusEnum.values()) {
            if (status.getType().equals(code)) {
                return status.getDesc();
            }
        }
        return code.toString();
    }
}
