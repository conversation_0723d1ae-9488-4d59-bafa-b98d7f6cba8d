package com.jlr.ecp.subscription.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "查询中心，在线服务初始化状态VO")
public class OnlineServiceInitializeStatusVO {

    @Schema(description = "车架号")
    private String carVin;

    @Schema(description = "是否已进行初始化")
    private String initializedResult;

    @Schema(description = "DMS")
    private String dmsResult;

    @Schema(description = "SOTA")
    private String sotaResult;

    @Schema(description = "APPD")
    private String appdResult;

    @Schema(description = "AMAP")
    private String amapResult;

    @Schema(description = "China Unicom")
    private String cuResult;

    @Schema(description = "DP")
    private String dpResult;

    @Schema(description = "是否PIVI车机")
    private String piviResult;

    @Schema(description = "具备信息娱乐服务相关配置")
    private String specialVinResult;

    @Schema(description = "到期日刷新是否成功")
    private String syncResult;
}
