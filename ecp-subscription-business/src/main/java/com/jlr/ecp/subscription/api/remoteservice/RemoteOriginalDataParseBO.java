package com.jlr.ecp.subscription.api.remoteservice;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RemoteOriginalDataParseBO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 数据批次号，以时间存储，20240812
     */
    private String dataNo;

    private String serviceName;

    private String servicePackage;

    private LocalDateTime expiryDateUTC0;

    private String carVin;

    private String incontrolId;

    private String phone;

    private String userid;

    private String firstName;

    private String surname;

    private Integer serviceType;

    /**
     * 数据状态:1待处理 2处理成功 3处理失败
     */
    private Integer status;
    /**
     * 处理失败类型:
     * RAW_ERROR：数据解析错误
     * DP_FAIL：DP数据不匹配
     * SAVE_FAIL：系统保存异常
     */
    private String failType;
    /**
     * 处理失败的描述
     */
    private String failDesc;
    /**
     * 处理失败次数默认0，超过5次不再处理
     */
    private Integer missCount;
}
