package com.jlr.ecp.subscription.api.vininit.exception;

/**
 * 车辆处理异常
 *
 * <AUTHOR>
 */
public class VehicleProcessException extends RuntimeException {
    
    private final String errorCode;
    
    public VehicleProcessException(String message) {
        super(message);
        this.errorCode = "VEHICLE_PROCESS_ERROR";
    }
    
    public VehicleProcessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public VehicleProcessException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "VEHICLE_PROCESS_ERROR";
    }
    
    public VehicleProcessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 数据解析异常
     */
    public static class DataParseException extends VehicleProcessException {
        public DataParseException(String message) {
            super("DATA_PARSE_ERROR", message);
        }
        
        public DataParseException(String message, Throwable cause) {
            super("DATA_PARSE_ERROR", message, cause);
        }
    }
    
    /**
     * 数据处理异常
     */
    public static class DataProcessException extends VehicleProcessException {
        public DataProcessException(String message) {
            super("DATA_PROCESS_ERROR", message);
        }
        
        public DataProcessException(String message, Throwable cause) {
            super("DATA_PROCESS_ERROR", message, cause);
        }
    }
    
    /**
     * 数据持久化异常
     */
    public static class DataPersistException extends VehicleProcessException {
        public DataPersistException(String message) {
            super("DATA_PERSIST_ERROR", message);
        }
        
        public DataPersistException(String message, Throwable cause) {
            super("DATA_PERSIST_ERROR", message, cause);
        }
    }
}
