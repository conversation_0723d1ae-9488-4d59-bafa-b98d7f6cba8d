package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * PIVI查询结果枚举
 */
@AllArgsConstructor
@Getter
public enum PIVIResultEnum {

    SUCCESS(1, "是", null),

    INTERFACE_ERROR(2, "否，接口异常", null),

    NOT_PIVI_CAR(3, "否，非PIVI车机", "非PIVI车机");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 错误信息
     * */
    public final String errorMessage;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (PIVIResultEnum status : PIVIResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    public static String getErrorMessage(Integer code){
        if (code == null) {
            return null;
        }
        for (PIVIResultEnum status : PIVIResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getErrorMessage();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
