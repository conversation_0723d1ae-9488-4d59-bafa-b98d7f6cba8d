package com.jlr.ecp.subscription.service.vin.expiry.dto;

import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * t_incontrol_vehicle(t_incontrol_vehicle)实体类
 *
 * <AUTHOR>
 * @since 2023-12-20 14:45:58
 * @description 由 Mybatisplus Code Generator 创建
 */
@Data
public class IncontrolVehicleDTO extends TenantBaseDO {


    /**
     * incontrol账号;incontrol账号
     */
    @Schema(description = "incontrol账号", required = true, example = "incontrol123")
    private String incontrolId;

    private String carVin;

    /**
     * 用户编码;用户编码
     */
    @Schema(description = "用户编码", required = true, example = "consumer123")
    private String consumerCode;

    /**
     * 车辆型号;车辆型号
     */
    @Schema(description = "车辆型号", required = true, example = "series123")
    private String seriesCode;

    /**
     * 车辆名称
     */
    @Schema(description = "车辆名称", example = "Series Name")
    private String seriesName;

    /**
     * 品牌CODE
     */
    @Schema(description = "品牌CODE", example = "brand123")
    private String brandCode;

    /**
     * 品牌名
     */
    @Schema(description = "品牌名", example = "Brand Name")
    private String brandName;

    /**
     * 配置code
     */
    @Schema(description = "配置code", example = "config123")
    private String configCode;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "Config Name")
    private String configName;

    /**
     * 车辆年款;车辆年款
     */
    @Schema(description = "车辆年款", required = true, example = "2021")
    private String modelYear;

    /**
     * 车机型号;车机型号，PIVI，通过计算获得
     */
    @Schema(description = "车机型号，PIVI，通过计算获得", example = "PIVI Pro")
    private String carSystemModel;

    /**
     *     production_en    varchar(90)  null comment '产品类型EN;产品类型EN',
     *     LOCAL or IMPORTED
     */
    @Schema(description = "产品类型EN", example = "PIVI Pro")
    private String productionEn;

}
