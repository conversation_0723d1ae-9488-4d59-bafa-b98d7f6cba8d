package com.jlr.ecp.subscription.controller.admin.dto.remote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "远程续费校验返回值")
public class RemoteVerifyDTO {

    @Schema(description = "vin")
    private String carVin;

    @Schema(description = "续费服务")
    private String serviceName;

    @Schema(description = "续费前服务到期日")
    private String beforeExpiryDate;

    @Schema(description = "服务指定到期日")
    private String afterExpiryDate;
}
