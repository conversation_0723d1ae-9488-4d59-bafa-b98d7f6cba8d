package com.jlr.ecp.subscription.util.redis.listener;

import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.dal.mysql.serialnumgen.SerialNumGenMapper;
import com.jlr.ecp.subscription.enums.config.BusiCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 业务编码服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisCodePrefixRunner implements CommandLineRunner {

    @Resource
    private RedisService redisService;

    @Resource
    SerialNumGenMapper serialNumGenMapper;

    public final static String CODE_PREFIX = "CODE_PREFIX:";

    @Override
    public void run(String... args) {
        //包含枚举类中所有枚举值的数组
        BusiCodeEnum[] enums = BusiCodeEnum.values();
        TenantContextHolder.setTenantId(1L);
        for (BusiCodeEnum eachEnum : enums) {
            Long serialNum = serialNumGenMapper.getSerialNum(eachEnum.getTableName());
            if (serialNum != null){
                redisService.setCacheObject(CODE_PREFIX + eachEnum.getTableName(), serialNum);
            }
        }
    }
}