package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerVehicleMapper;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.enums.consumer.ConsumerVehicleSourceEnum;

import java.util.List;

/**
 * ConsumerVehicle Repository接口
 *
 */
public class ConsumerVehicleRepositoryImpl extends ServiceImpl<ConsumerVehicleMapper, ConsumerVehicleDO> implements ConsumerVehicleRepository {

    @Override
    public ConsumerVehicleDO getOneByCarVinAndJlrId(String carVin, String jlrId) {
        return getOne(new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .eq(ConsumerVehicleDO::getCarVin, carVin)
                .eq(ConsumerVehicleDO::getConsumerCode, jlrId)
                .eq(ConsumerVehicleDO::getSource, ConsumerVehicleSourceEnum.MINI_HOME.getCode())
                .eq(ConsumerVehicleDO::getIsDeleted, false)
                .orderByDesc(ConsumerVehicleDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    @Override
    public List<ConsumerVehicleDO> getListByJlrId(String jlrId) {
        return list(new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .eq(ConsumerVehicleDO::getConsumerCode, jlrId)
                .eq(ConsumerVehicleDO::getIsDeleted, false));
    }

    @Override
    public List<ConsumerVehicleDO> getListByVinList(List<String> carVinList) {
        return list((new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .in(ConsumerVehicleDO::getCarVin, carVinList)
                .eq(ConsumerVehicleDO::getIsDeleted, false)));
    }
}
