package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 联通返回公共参数
 * <AUTHOR>
 */
@Data
public class UnicomReqVO implements Serializable {


    /**
     * 厂商标识
     */
    @JsonProperty("industry_code")
    private String industryCode;


    /**
     * 请求方法
     */
    @JsonProperty("method")
    private String method;

    /**
     * 发起的请求时间
     * yyyyMMddHHmmSS
     * 北京时间，接口会校验该时间的时效性，与上海联通侧的系统时间进行比较，超过正负15分钟范围内时会报错。
     */
    @JsonProperty("timestamp")
    private String timestamp;

    /**
     * 版本号 1.0 非必传
     */
    @JsonProperty("version")
    private String version;

    /**
     * md5验签
     */
    @JsonProperty("sign_data")
    private String signData;

    /**
     * 流水号 非必传
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 请求结构体
     */
    @JsonProperty("request_data")
    private UnicomRequestData unicomRequestData;


}