package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * t_unicom_todo_order
 *
 * <AUTHOR>
 */
@Data
public class UnicomRnrBatchRecordListVO {


    /**
     *批次号;批量查询批次号
     */
    @Schema(description = "批次号")
    private Long batchNo;

    /**
     *批次号;批量查询批次号
     */
    @Schema(description = "批量查询文件上传状态")
    private String state;


    @Schema(description = "操作时间")
    private String operateTime;


    @Schema(description = "操作人")
    private String operator;
}

