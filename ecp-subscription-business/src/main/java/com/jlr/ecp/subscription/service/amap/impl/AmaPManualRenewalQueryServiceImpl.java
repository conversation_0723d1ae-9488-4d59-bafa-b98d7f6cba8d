package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalStatusVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPErrorCode;
import com.jlr.ecp.subscription.enums.amap.AmaPRenewStatusEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPRenewalYearEnum;
import com.jlr.ecp.subscription.service.amap.AmaPManualRenewalQueryService;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class AmaPManualRenewalQueryServiceImpl implements AmaPManualRenewalQueryService {
    @Resource
    private AmaPRenewBatchRecordsDOMapper amaPRenewBatchRecordsDOMapper;

    @Resource
    private AmaPRenewRecordsDOMapper amaPRenewRecordsDOMapper;

    /**
     * 根据批次号查询AMAP的批次号
     *
     * @param batchNo 批次号字符串
     * @return 如果找到对应的批次记录，则返回批次号字符串；否则返回null
     */
    @Override
    public String queryAmaPRenewalBatchNo(String batchNo) {
        log.info("根据批次号查询AMAP的批次号, batchNo:{}", batchNo);
        try {
            Long batchNoLong = Long.valueOf(batchNo);
            AmaPRenewBatchRecordsDO amaPRenewBatchRecordsDO = queryAmaPRenewBatchByBatchNo(batchNoLong);
            if (Objects.isNull(amaPRenewBatchRecordsDO)) {
                //查询单表
                AmaPRenewRecordsDO amaPRenewRecordsDO = queryAmaPRenewByBatchNo(batchNoLong);
                if (Objects.isNull(amaPRenewRecordsDO)) {
                    return null;
                }
                return String.valueOf(amaPRenewRecordsDO.getRenewNo());
            }
            return String.valueOf(amaPRenewBatchRecordsDO.getBatchNo());
        } catch (Exception e) {
            log.info("根据批次号查询AMAP的批次号异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 查询AMP续订状态
     *
     * @return 返回一个AmaPRenewalStatusVO对象的列表，每个对象代表一个AMP续订状态
     */
    @Override
    public List<AmaPRenewalStatusVO> queryAmaPRenewalStatus() {
        List<AmaPRenewalStatusVO> resp = new ArrayList<>();
        Set<String> displaySet = new HashSet<>();
        AmaPRenewStatusEnum[] values = AmaPRenewStatusEnum.values();
        for (AmaPRenewStatusEnum statusEnum : values) {
            if (displaySet.contains(statusEnum.getDisplay())) {
                continue;
            }
            displaySet.add(statusEnum.getDisplay());
            AmaPRenewalStatusVO amaPRenewalStatusVO = AmaPRenewalStatusVO.builder()
                    .renewalStatusCode(statusEnum.getStatus())
                    .renewalStatusDesc(statusEnum.getDisplay())
                    .build();
            resp.add(amaPRenewalStatusVO);
        }
        return resp;
    }

    /**
     * 查询续签记录的分页列表
     *
     * @param queryPageDTO 查询参数对象，用于分页和筛选查询结果
     * @return 返回续签记录的分页结果对象
     */
    @Override
    public PageResult<AmaPRenewalQueryPageVO> queryRenewalRecordPageList(AmaPRenewalQueryPageDTO queryPageDTO) {
        log.info("查询续签记录的分页列表, queryPageDTO:{}", queryPageDTO);
        Page<AmaPRenewRecordsDO> amaPRenewalPage = queryAmaPRenewRecordPage(queryPageDTO);
        if(Objects.isNull(amaPRenewalPage) || CollUtil.isEmpty(amaPRenewalPage.getRecords())) {
            return PageResult.empty();
        }
        List<AmaPRenewRecordsDO> renewRecordsDOList = amaPRenewalPage.getRecords();
        List<AmaPRenewalQueryPageVO> resp = buildAmaPRenewalQueryPageVOList(renewRecordsDOList);
        return new PageResult<>(resp, amaPRenewalPage.getTotal());
    }

    /**
     * 构建AMAP视图
     *
     * @param renewRecordsDOList 续费记录的数据库对象列表，从数据库中查询到的续费记录集合
     * @return List<AmaPRenewalQueryPageVO> 返回一个视图对象列表，该列表包含所有续费记录的视图对象
     */
    private List<AmaPRenewalQueryPageVO> buildAmaPRenewalQueryPageVOList(List<AmaPRenewRecordsDO> renewRecordsDOList) {
        List<AmaPRenewalQueryPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(renewRecordsDOList)) {
            return resp;
        }
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : renewRecordsDOList) {
            resp.add(buildAmaPRenewalQueryPageVO(amaPRenewRecordsDO));
        }
        return resp;
    }

    /**
     * 构建AMAP视图
     *
     * @param amaPRenewRecordsDO 续订记录数据对象，包含车辆续订相关信息
     * @return 返回一个构建好的AmaPRenewalQueryPageVO对象，如果输入参数为null，则返回null
     */
    private AmaPRenewalQueryPageVO buildAmaPRenewalQueryPageVO(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        if (Objects.isNull(amaPRenewRecordsDO)) {
            return null;
        }
        return AmaPRenewalQueryPageVO.builder()
                .batchNo(String.valueOf(amaPRenewRecordsDO.getRenewNo()))
                .carVin(amaPRenewRecordsDO.getCarVin())
                .renewalStatus(amaPRenewRecordsDO.getRenewStatus())
                .renewalStatusDesc(AmaPRenewStatusEnum.getDisplayByStatus(amaPRenewRecordsDO.getRenewStatus()))
                .renewalYearCode(amaPRenewRecordsDO.getRenewYear())
                .renewalYearDesc(AmaPRenewalYearEnum.getDescByCode(amaPRenewRecordsDO.getRenewYear()))
                .renewalBeforeExpiryDate(SubscribeTimeFormatUtil.timeToStringByFormat(amaPRenewRecordsDO.getRenewBeforeExpiryDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .renewalAfterExpiryDate(SubscribeTimeFormatUtil.timeToStringByFormat(amaPRenewRecordsDO.getRenewAfterExpiryDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(amaPRenewRecordsDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(amaPRenewRecordsDO.getOperator())
                .errorDesc(getAmaPRenewalErrorDesc(amaPRenewRecordsDO))
                .build();
    }

    /**
     * 根据给定的操作年份，将其加到指定的日期时间上，并返回格式化后的日期字符串。
     *
     * @param befDateTime 原始的日期时间对象，表示续费前的到期日期。
     * @param operateYear 操作年份，表示需要增加的年份数。
     * @return 返回一个格式化后的日期字符串，该字符串是将操作年份加到原始日期时间后的结果。
     *         如果操作年份或原始日期时间为空，则返回空字符串。
     */
    private String getRenewalAfterExpiryDate(LocalDateTime befDateTime, Integer operateYear) {
        if (Objects.isNull(operateYear) || Objects.isNull(befDateTime)) {
            return "";
        }
        return SubscribeTimeFormatUtil.timeToStringByFormat(befDateTime.plusYears(operateYear),
                SubscribeTimeFormatUtil.FORMAT_1);
    }

    /**
     * 获取亚AMAP支付续费的错误描述
     *
     * @param amaPRenewRecordsDO 亚马逊支付续费记录对象，包含查询结果代码和订单结果代码等信息
     * @return 错误描述字符串如果记录对象为空或相关代码为空，则返回空字符串
     */
    private String getAmaPRenewalErrorDesc(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        if (Objects.isNull(amaPRenewRecordsDO)) {
            return "";
        }
        if (AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPRenewRecordsDO.getOrderResultCode())
                && AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPRenewRecordsDO.getQueryResultCode())) {
            return "";
        }
        if (!AmaPRenewStatusEnum.RENEW_FAIL.getStatus().equals(amaPRenewRecordsDO.getRenewStatus())) {
            return "";
        }
        if (StringUtils.isBlank(amaPRenewRecordsDO.getQueryResultCode())) {
            String descByCode = AmaPErrorCode.getManualRenewalDescByCode(amaPRenewRecordsDO.getOrderResultCode());
            return StringUtils.isBlank(descByCode) ? amaPRenewRecordsDO.getErrorDesc() : descByCode;
        }
        return AmaPErrorCode.getManualRenewalDescByCode(amaPRenewRecordsDO.getQueryResultCode());
    }

    /**
     * 查询AMAP的续费记录
     *
     * @param queryPageDTO 分页查询参数对象
     * @return 返回分页数据对象，包含查询结果和分页信息
     */
    private Page<AmaPRenewRecordsDO> queryAmaPRenewRecordPage(AmaPRenewalQueryPageDTO queryPageDTO) {
        log.info("查询AMAP的续费记录, queryPageDTO:{}", queryPageDTO);
        if (Objects.isNull(queryPageDTO)) {
            return null;
        }
        Page<AmaPRenewRecordsDO> pageParam = new Page<>(queryPageDTO.getPageNo(), queryPageDTO.getPageSize());
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = buildAmaPRenewQueryWrapper(queryPageDTO);
        if (Objects.isNull(queryWrapper)) {
            return null;
        }
        try {
            return amaPRenewRecordsDOMapper.selectPage(pageParam, queryWrapper);
        } catch (Exception e) {
            log.info("查询AMAP的续费记录异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 构建查询包装器方法
     *
     * @param queryPageDTO AmaPRenewalQueryPageDTO对象，包含查询条件
     * @return 构建的LambdaQueryWrapper对象，如果queryPageDTO为null则返回null
     */
    private LambdaQueryWrapper<AmaPRenewRecordsDO> buildAmaPRenewQueryWrapper(AmaPRenewalQueryPageDTO queryPageDTO) {
        if (Objects.isNull(queryPageDTO)) {
            return null;
        }
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(queryPageDTO.getCarVin()), AmaPRenewRecordsDO::getCarVin, queryPageDTO.getCarVin());
        if (CollUtil.isNotEmpty(queryPageDTO.getBatchNoList())) {
            List<Long> renewNoList = new ArrayList<>();
            for (String batchNo : queryPageDTO.getBatchNoList()) {
                Long renewNo = Long.valueOf(batchNo);
                renewNoList.add(renewNo);
            }
            queryWrapper.in(AmaPRenewRecordsDO::getRenewNo, renewNoList);
        }
        if (StringUtils.isNotBlank(queryPageDTO.getOperateStartTime()) && StringUtils.isNotBlank(queryPageDTO.getOperateEndTime())) {
            LocalDateTime startTime = SubscribeTimeFormatUtil.stringToTimeByFormat(queryPageDTO.getOperateStartTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            LocalDateTime endTime = SubscribeTimeFormatUtil.stringToTimeByFormat(queryPageDTO.getOperateEndTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            queryWrapper.ge(AmaPRenewRecordsDO::getCreatedTime, startTime)
                    .le(AmaPRenewRecordsDO::getCreatedTime, endTime);
        }
        if (Objects.nonNull(queryPageDTO.getRenewalStatus())) {
            if (AmaPRenewStatusEnum.WAIT_RENEW.getStatus().equals(queryPageDTO.getRenewalStatus())
                    || AmaPRenewStatusEnum.RENEW_PROCESS.getStatus().equals(queryPageDTO.getRenewalStatus())) {
                queryWrapper.in(AmaPRenewRecordsDO::getRenewStatus,
                        List.of(AmaPRenewStatusEnum.WAIT_RENEW.getStatus(), AmaPRenewStatusEnum.RENEW_PROCESS.getStatus()));
            } else {
                queryWrapper.eq(AmaPRenewRecordsDO::getRenewStatus, queryPageDTO.getRenewalStatus());
            }
        }
        queryWrapper.eq(StringUtils.isNotBlank(queryPageDTO.getOperator()), AmaPRenewRecordsDO::getOperator, queryPageDTO.getOperator());
        handleTimeSort(queryPageDTO, queryWrapper);
        return queryWrapper;
    }

    /**
     * 处理时间排序逻辑
     * 根据查询参数中的排序方式，对续期记录进行时间排序
     *
     * @param queryPageDTO 查询参数对象，包含需要排序的时间字段和排序方式
     * @param queryWrapper 查询包装器，用于构建查询条件和排序条件
     */
    private static void handleTimeSort(AmaPRenewalQueryPageDTO queryPageDTO, LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper) {
        if (StringUtils.isNotBlank(queryPageDTO.getRenewalYearTimeSort())) {
            if (SortTypeEnum.ASC.getSortType().equals(queryPageDTO.getRenewalYearTimeSort())) {
                queryWrapper.orderByAsc(AmaPRenewRecordsDO::getRenewYear);
                queryWrapper.orderByAsc(AmaPRenewRecordsDO::getId);
            } else {
                queryWrapper.orderByDesc(AmaPRenewRecordsDO::getRenewYear);
                queryWrapper.orderByDesc(AmaPRenewRecordsDO::getId);
            }
        }
        if (StringUtils.isNotBlank(queryPageDTO.getOperateTimeSort())) {
            if (SortTypeEnum.ASC.getSortType().equals(queryPageDTO.getOperateTimeSort())) {
                queryWrapper.orderByAsc(AmaPRenewRecordsDO::getCreatedTime);
                queryWrapper.orderByAsc(AmaPRenewRecordsDO::getId);
            } else {
                queryWrapper.orderByDesc(AmaPRenewRecordsDO::getCreatedTime);
                queryWrapper.orderByDesc(AmaPRenewRecordsDO::getId);
            }
        }
    }

    /**
     * 根据批次编号查询续费批次记录
     *
     * @param batchNo 批次编号，用于标识特定的续费批次
     * @return 返回查询到的续费批次记录，如果没有查询到，则返回null
     */
    private AmaPRenewBatchRecordsDO queryAmaPRenewBatchByBatchNo(Long batchNo) {
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapper<AmaPRenewBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewBatchRecordsDO::getBatchNo, batchNo)
                .eq(AmaPRenewBatchRecordsDO::getIsDeleted, false);
        List<AmaPRenewBatchRecordsDO> resp = new ArrayList<>();
        try {
            resp = amaPRenewBatchRecordsDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据批次编号查询续费批次记录异常：{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询续费批次记录,数量大于1, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    /**
     * 根据批次编号查询续订记录
     *
     * @param batchNo 批次编号，用于标识特定的续订记录
     * @return 返回查询到的续订记录，如果没有查询到，则返回null
     */
    private AmaPRenewRecordsDO queryAmaPRenewByBatchNo(Long batchNo) {
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewRecordsDO::getRenewNo, batchNo)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        List<AmaPRenewRecordsDO> resp = new ArrayList<>();
        try {
            resp = amaPRenewRecordsDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据批次编号查询续订记录异常：{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询续订记录, 数量大于1, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }
}
