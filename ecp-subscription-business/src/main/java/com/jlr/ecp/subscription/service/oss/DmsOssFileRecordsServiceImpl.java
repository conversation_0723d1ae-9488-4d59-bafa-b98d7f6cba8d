package com.jlr.ecp.subscription.service.oss;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.bau.dto.HandleFileResultVO;
import com.jlr.ecp.subscription.api.bau.dto.TransferFileVO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssFileRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssFileRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.oss.*;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.model.dto.VinMatchCountDto;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeLogVO;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.constant.Constants.LIMIT_ONE;

/**
 * t_dms_oss_file_records(DmsOssFileRecords)表服务实现类
 * <AUTHOR>
 */

@Service("dmsOssFileRecordsService")
@Validated
@Slf4j
public class DmsOssFileRecordsServiceImpl implements DmsOssFileRecordsService {

    @Resource
    private DmsOssFileRecordsMapper dmsOssFileRecordsMapper;



    @Resource
    private FileService fileService;

    @Value("${bau.oss.ak}")
    String OSS_ACCESS_KEY_ID;
    @Value("${bau.oss.sk}")
    String OSS_ACCESS_KEY_SECRET ;
    @Value("${bau.oss.bucket}")
    String OSS_BUCKET_NAME ;
    @Value("${bau.oss.endpoint}")
    String OSS_ENDPOINT;

    private static final String AWS_S3_FILE ="AWS_S3_FILE";
    private static final String AWS_S3_FLAG_PATH = "/flag/transfer_success.txt";

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private DmsOssOriginalDataMapper dmsOssOriginalDataMapper;


    /**
     * OSS传输到S3
     * @param dateStr YYYYMMDD
     * @return
     */
    @Override
    public TransferFileVO transferFile(String dateStr) {
        TransferFileVO transferFileVO = new TransferFileVO();
        OSS ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET);
        String prefix = "Inc/" + dateStr + "/dms_prod-tt_invoice_info/";
        String flagPrefix = "Inc/" + dateStr + "/flag/";

        //首先判断是否重复处理了 有没有success文件 没有的话就需要处理 然后再进行flag文件判断 如果oss上没有flag文件 也不处理
        //判断S3是否存在Flag文件 不存在就可以上传
        boolean jungleFileHandle = jungleFileHandle(dateStr);

        if(!jungleFileHandle){
            log.info("S3已经存在flag文件无需上传文件");
            transferFileVO.setErrorDesc(ErrorCodeConstants.BAU_TRANSFER_S3_CHECK_FLAG_FAIL.getMsg());
            return transferFileVO;
        }
        //判断OSS的flag文件是否存在
        Boolean transferFlagFile = transferFlagFile(ossClient,flagPrefix);
        if(!transferFlagFile){
            log.info("OSS上没有flag文件");
            transferFileVO.setErrorDesc(ErrorCodeConstants.BAU_TRANSFER_OSS_CHECK_FLAG_FAIL.getMsg());
            return transferFileVO;
        }

        // 获取所有以prefix开头的对象
        ObjectListing objectListing = ossClient.listObjects(OSS_BUCKET_NAME, prefix);
        List<OSSObjectSummary> objectSummaries = objectListing.getObjectSummaries();
        //循环上传AWS S3
        List<String> ossFile = new ArrayList<>();

        List<String> dmsFile = new ArrayList();

        for (OSSObjectSummary summary : objectSummaries) {
            String key = summary.getKey();

            OSSObject fileObject = ossClient.getObject(OSS_BUCKET_NAME, key);
            // 确保文件是.csv文件
            if (key.startsWith(prefix + "dms_prod-tt_invoice_info") && key.endsWith(".csv")) {
                //上传到AWS S3
                fileCreated(fileObject,key);
                ossFile.add(key);
                dmsFile.add(key);
            }
        }
        if(CollUtil.isEmpty(dmsFile)){
            log.info("未找到OSS上的CSV文件 dateStr:{}",dateStr);
            transferFileVO.setErrorDesc(ErrorCodeConstants.BAU_TRANSFER_OSS_FILE_NOT_FOUND.getMsg());
            return transferFileVO;
        }
        //上传完毕之后再处理flag文件
        Boolean aBoolean = uploadSuccessFile(dateStr,transferFileVO);
        transferFileVO.setFileNum(dmsFile.size());
        //入库JOB
        DmsOssFileRecordsDO ossFileRecordsDO = new DmsOssFileRecordsDO();
        ossFileRecordsDO.setJobDate(LocalDateTime.now());
        ossFileRecordsDO.setJobParam(dateStr);
        ossFileRecordsDO.setBauJobId(ecpIdUtil.nextId());
        //设置默认统计值为0
        ossFileRecordsDO.setTotalVinNum(0);
        ossFileRecordsDO.setFailedVinNum(0);
        ossFileRecordsDO.setSuccessVinNum(0);
        ossFileRecordsDO.setDmsOssFile(JSON.toJSONString(ossFile));
        ossFileRecordsDO.setEcpS3File(JSON.toJSONString(dmsFile));
        dmsOssFileRecordsMapper.insert(ossFileRecordsDO);
        return transferFileVO;
    }

    private Boolean uploadSuccessFile(String dateStr,TransferFileVO transferFileVO) {
        String path = "Inc/" + dateStr + AWS_S3_FLAG_PATH;
        byte[] content = "SUCCESS".getBytes();
        try{
            String file = fileService.createFile("transfer_success.txt", path, content, AWS_S3_FILE);
            log.info("上传成功success文件，路径为:{}",file);
            return true;
        } catch (Exception e) {
            log.info("Error writing to file: {}" , e.getMessage());
            transferFileVO.setErrorDesc("Error writing to file:"+e.getMessage());
        }
        return false;
    }

    private boolean jungleFileHandle(String dateStr) {
        //flag文件路径
        String path = "Inc/" + dateStr + AWS_S3_FLAG_PATH;
        try {
            byte[] fileContent = fileService.getFileContent(AWS_S3_FILE, path);
            if(fileContent.length>0){
                return false;
            }
        } catch (Exception e) {
            log.info("jungleFileHandle方法出错 error:{}",e.getMessage());
            return true;
        }
        return true;
    }

    @Override
    public HandleFileResultVO handleFile(String dateStr) {
        HandleFileResultVO handleFileResultVO = new HandleFileResultVO();
        DmsOssFileRecordsDO ossFileRecordsDO = dmsOssFileRecordsMapper.selectOne(new LambdaQueryWrapperX<DmsOssFileRecordsDO>()
                .eq(DmsOssFileRecordsDO::getJobParam, dateStr)
                .eq(BaseDO::getIsDeleted, false)
                .last(LIMIT_ONE));
        if(ossFileRecordsDO == null){
            log.info("需要处理的ossFileRecords为空");
            handleFileResultVO.setErrorDesc("需要处理的ossFileRecords为空");
            return handleFileResultVO;
        }
        log.info("ossFileRecordsDO :{}",ossFileRecordsDO);

        List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS = dmsOssOriginalDataMapper.selectList(new LambdaQueryWrapperX<DmsOssOriginalDataDO>()
                .eq(DmsOssOriginalDataDO::getBauJobId, ossFileRecordsDO.getBauJobId()));
        int total = 0;
        int canInsert = 0;
        int insert = 0;
        int insertFail = 0;
        //flag文件路径
        String path = "Inc/" + dateStr + AWS_S3_FLAG_PATH;
        try {
            byte[] fileContent = fileService.getFileContent(AWS_S3_FILE, path);
            if(fileContent.length>0){
                String ecpS3Files = ossFileRecordsDO.getEcpS3File();
                List<String> ecpS3FileList = JSON.parseObject(ecpS3Files,ArrayList.class);

                List<DmsOssOriginalDataDO> list = parseS3FileList(ecpS3FileList, ossFileRecordsDO, handleFileResultVO);
                total = list.size();

                //去掉重复VIN 并且保留时间未最早的那个VIN
                List<DmsOssOriginalDataDO> collect = getEarliestVinList(list);
                canInsert = collect.size();
                if(CollUtil.isNotEmpty(collect)){
                    removeExistsVin(dmsOssOriginalDataDOS, collect);
                    insert = collect.size();
                    insertFail = insertOriginalData(insert, collect, insertFail, ossFileRecordsDO);
                    log.info("总处理VIN:{},去重之后的VIN:{},过滤之前VIN后需要入库的VIN:{},入库失败的VIN数量:{}",total,canInsert,insert,insertFail);
                    handleFileResultVO.setTotal(total);
                    handleFileResultVO.setCanInsert(canInsert);
                    handleFileResultVO.setInsert(insert);
                    handleFileResultVO.setInsertFail(insertFail);
                }
            }
        } catch (Exception e) {
            handleFileResultVO.setErrorDesc("处理文件异常:"+e.getMessage());
            log.info("处理文件error:{}",e.getMessage());
        }
        return handleFileResultVO;
    }

    public int insertOriginalData(int insert, List<DmsOssOriginalDataDO> collect, int insertFail, DmsOssFileRecordsDO ossFileRecordsDO) {
        if(insert >0){
            for (DmsOssOriginalDataDO dmsOssOriginalDataDO : collect) {
                try {
                    dmsOssOriginalDataMapper.insert(dmsOssOriginalDataDO);
                } catch (Exception ex) {
                    log.info("dmsOssOriginalData insert error:{}",ex.getMessage());
                    insertFail++;
                    log.info("dmsOssOriginalData插入失败，dmsOssOriginalDataDO:{}",dmsOssOriginalDataDO);
                }
            }
            ossFileRecordsDO.setTotalVinNum(insert - insertFail);
            dmsOssFileRecordsMapper.updateById(ossFileRecordsDO);
        }
        return insertFail;
    }

    private List<DmsOssOriginalDataDO> parseS3FileList(List<String> ecpS3FileList, DmsOssFileRecordsDO ossFileRecordsDO, HandleFileResultVO handleFileResultVO) throws Exception {
        List<DmsOssOriginalDataDO> list = new ArrayList<>();
        for (String url : ecpS3FileList) {
            byte[] ossFile = fileService.getFileContent(AWS_S3_FILE, url);
            if (ossFile != null && ossFile.length > 0){
                //处理CSV文档
                list.addAll(parseCSV(ossFile, ossFileRecordsDO.getBauJobId(), handleFileResultVO));
            }
        }
        return list;
    }

    /**
     * 移除已存在的VIN号
     * 此方法旨在从第二个列表中移除所有在第一个列表中已存在的VIN号的记录，以及VIN号、DMS发票日期为空的记录
     * 主要用于数据预处理阶段，确保数据的唯一性和有效性
     *
     * @param dmsOssOriginalDataDOS 第一个列表，包含原始数据记录，用于参考已存在的VIN号
     * @param collect 第二个列表，收集的记录，将从此列表中移除重复及无效的记录
     */
    private static void removeExistsVin(List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS, List<DmsOssOriginalDataDO> collect) {
        if(CollUtil.isNotEmpty(dmsOssOriginalDataDOS)){
            //找到已经存在的carvin
            Set<String> existingVins = dmsOssOriginalDataDOS.stream()
                    .map(DmsOssOriginalDataDO::getCarVin)
                    .collect(Collectors.toSet());

            // 从collect去掉dmsOssOriginalDataDOS内容
            collect.removeIf(dataDO -> existingVins.contains(dataDO.getCarVin())
                    ||StringUtils.isEmpty(dataDO.getCarVin())
                    || StringUtils.isEmpty(dataDO.getDmsInvoiceDate()));
        }
    }

    /**
     * 从给定列表中获取每个车架号（VIN）最早的记录
     * 此方法的目的是为了处理可能包含重复车架号的原始数据列表，
     * 并筛选出每个车架号对应发票日期最早的记录
     *
     * @param list 原始数据列表，包含多个DmsOssOriginalDataDO对象
     * @return 包含每个车架号最早记录的新列表
     */
    private static List<DmsOssOriginalDataDO> getEarliestVinList(List<DmsOssOriginalDataDO> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                DmsOssOriginalDataDO::getCarVin,
                                data -> data,
                                (existing, replacement) -> {
                                    LocalDateTime existingDate = LocalDateTime.parse(existing.getDmsInvoiceDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                    LocalDateTime replacementDate = LocalDateTime.parse(replacement.getDmsInvoiceDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                    return existingDate.isBefore(replacementDate) ? existing : replacement;
                                }
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    private void fileCreated(OSSObject fileObject, String key) {
        try {
            Path path = Paths.get(key);
            String fileName = path.getFileName().toString();
            String file = fileService.createFile(fileName, key, fileObject.getObjectContent().readAllBytes(), AWS_S3_FILE);
            log.info("transferFile上传AWSS3的地址为:{}",file);
        }catch (Exception e){
            log.info("上传文件对象 error:{}",e.getMessage());
        }

    }


    private Boolean transferFlagFile(OSS ossClient, String flagPrefix) {
        log.info("校验flag文件开始");
        // 获取所有以flagPrefix开头的对象
        ObjectListing flagObjectListing = ossClient.listObjects(OSS_BUCKET_NAME, flagPrefix);
        List<OSSObjectSummary> flagObjectSummaries = flagObjectListing.getObjectSummaries();

        // 检查flag文件夹下的所有.txt文件
        for (OSSObjectSummary flagSummary : flagObjectSummaries) {
            String flagKey = flagSummary.getKey();
            // 确保文件是.txt文件
            if (flagKey.endsWith(".txt")) {
                OSSObject flagObject = ossClient.getObject(OSS_BUCKET_NAME, flagKey);
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(flagObject.getObjectContent()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 检查行是否包含dms_prod-tt_invoice_info
                        if (line.contains("dms_prod-tt_invoice_info")) {
                            fileCreated(flagObject, flagKey);
                            log.info("校验flag文件结束,dms_prod-tt_invoice_info文件并且已经上传完毕");
                            return true;
                        }
                    }
                } catch (Exception e) {
                    log.info("读取解析flag文件错误,flagPrefix = {},message = {}", flagPrefix, e.getMessage());
                    return false;
                }
            }
        }
        log.info("校验flag文件结束，未找到上传文件");
        // 如果没有找到，返回false
        return false;
    }



    private List<DmsOssOriginalDataDO> parseCSV(byte[] csvData,Long bauJobId,HandleFileResultVO handleFileResultVO) {
        List<DmsOssOriginalDataDO> result = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(csvData), StandardCharsets.UTF_8))) {
            // 读取所有行
            List<String> lines = br.lines().collect(Collectors.toList());
            // 获取表头行
            String headerLine = lines.get(0);
            String[] headers = headerLine.split(",", -1);
            // 去掉双引号
            for (int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].replaceAll("^\"|\"$", "");
            }
            // 找到VIN和INVOICEDATE的索引
            int vinIndex = -1;
            int invoiceDateIndex = -1;
            int statusIndex = -1;
            int invoiceTypeIndex = -1;
            for (int i = 0; i < headers.length; i++) {
                if ("VIN".equalsIgnoreCase(headers[i])) {
                    vinIndex = i;
                } else if ("INVOICEDATE".equalsIgnoreCase(headers[i])) {
                    invoiceDateIndex = i;
                } else if ("STATUS".equalsIgnoreCase(headers[i])) {
                    statusIndex = i;
                }else if ("INVOICE_TYPE".equalsIgnoreCase(headers[i])) {
                    invoiceTypeIndex = i;
                }
            }

            //只要有一个字段未找到则不解析并打印日志提示
            if (vinIndex == -1 || invoiceDateIndex == -1 || statusIndex == -1|| invoiceTypeIndex == -1) {
                log.info("未找到vin列或者invoiceDate列,vinIndex:{}, invoiceDateIndex:{}, statusIndex:{}, invoiceType:{}",vinIndex,invoiceDateIndex,statusIndex,invoiceTypeIndex);
                handleFileResultVO.setErrorDesc("未找到vin列或者invoiceDate列,vinIndex:"+vinIndex +"invoiceDateIndex:"+invoiceDateIndex+"statusIndex:"+statusIndex+"invoiceTypeIndex:"+invoiceTypeIndex );
                return result;
            }
            // 解析数据行
            return parseLines(bauJobId, lines, vinIndex, invoiceDateIndex, statusIndex, invoiceTypeIndex, result);
        } catch (IOException e) {
            log.warn("解析DMS的CSV文件出错:{}",e);
            handleFileResultVO.setErrorDesc("解析DMS的CSV文件出错:"+e.getMessage());

        }
        return result;
    }

    private List<DmsOssOriginalDataDO> parseLines(Long bauJobId, List<String> lines, int vinIndex, int invoiceDateIndex, int statusIndex, int invoiceTypeIndex, List<DmsOssOriginalDataDO> result) {
        for (String line : lines.subList(1, lines.size())) {
            // 使用-1参数确保空字段不会被忽略
            String[] values = line.split(",", -1);
            //vinIndex 在最后一个应该是最大 所以不用管
            if (values.length > Math.max(vinIndex, invoiceDateIndex)) {
                DmsOssOriginalDataDO dmsOssOriginalDataDO = new DmsOssOriginalDataDO();
                String status = cleanString(values[statusIndex]);
                String invoiceType = cleanString(values[invoiceTypeIndex]);
                String carVin = cleanString(values[vinIndex]);
                String dmsInvoiceDate = cleanString(values[invoiceDateIndex]);
                boolean statusOrInvoiceTypeEmpty = StringUtils.isEmpty(status) || StringUtils.isEmpty(invoiceType);
                boolean notNormal = !DMSStatusEnum.REGULAR.getCode().equals(status) || !DMSInvoiceTypeEnum.REGULAR.getCode().equals(invoiceType);
                boolean vinOrDateEmpty = StringUtils.isEmpty(carVin) || StringUtils.isEmpty(dmsInvoiceDate);
                if (statusOrInvoiceTypeEmpty || notNormal || vinOrDateEmpty) {
                    printErrorLog(statusOrInvoiceTypeEmpty, notNormal);
                    continue;
                }
                dmsOssOriginalDataDO.setCarVin(carVin.toUpperCase());
                dmsOssOriginalDataDO.setDmsInvoiceDate(dmsInvoiceDate);
                dmsOssOriginalDataDO.setBauJobId(bauJobId);
                dmsOssOriginalDataDO.setSyncStatus(SyncStatusEnum.PENDING.getCode());
                dmsOssOriginalDataDO.setJobDate(LocalDateTime.now());
                dmsOssOriginalDataDO.setStatus(HandleStatusEnum.PENDING.getCode());
                dmsOssOriginalDataDO.setDataId(ecpIdUtil.nextId());
                dmsOssOriginalDataDO.setSourceType(SourceTypeEnum.BAU_JOB.getCode());
                result.add(dmsOssOriginalDataDO);
            }
        }
        return result;
    }

    private static void printErrorLog(boolean statusOrInvoiceTypeEmpty, boolean notNormal) {
        if (statusOrInvoiceTypeEmpty) {
            log.info("发票状态或发票类型为空");
            //如果不是正常发票 或者不是正常红冲票就忽略
        } else if (notNormal) {
            log.info("不是正常发票或者不是正常红冲票忽略");
        } else {
            log.info("车架号或发票日期为空");
        }
    }

    //处理特殊空格字符串等
    public static String cleanString(String input) {
        if (input == null) {
            return null;
        }
        // 去掉首尾的双引号
        input = input.replaceAll("^\"|\"$", "");
        // 去掉所有特殊的空格字符（包括制表符、换行符、回车符等）
        input = input.replaceAll("[\\s\\p{Zs}]+", " ").trim();
        return input;
    }



    @Override
    public PageResult<OnlineServiceInitializeLogVO> getLogPageList(VinInitialLogPageDTO logPageDTO) {
        log.info("VIN在线服务初始化日志查询, logPageDTO:{}", logPageDTO);
        if (Objects.isNull(logPageDTO)) {
            return new PageResult<>();
        }
        Page<DmsOssFileRecordsDO> page = new Page<>(logPageDTO.getPageNo(), logPageDTO.getPageSize());
        LambdaQueryWrapperX<DmsOssFileRecordsDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(DmsOssFileRecordsDO::getBauJobId, logPageDTO.getBauJobId());
        queryWrapper.eq(DmsOssFileRecordsDO::getIsDeleted, false);
        setCondition(logPageDTO, queryWrapper);
        Page<DmsOssFileRecordsDO> recordsDOPage = dmsOssFileRecordsMapper.selectPage(page, queryWrapper);
        if (Objects.isNull(recordsDOPage) || CollUtil.isEmpty(recordsDOPage.getRecords())) {
            return new PageResult<>();
        }
        List<DmsOssFileRecordsDO> queryList = recordsDOPage.getRecords();
        List<OnlineServiceInitializeLogVO> resp = new ArrayList<>();
        for (DmsOssFileRecordsDO recordsDO : queryList) {
            OnlineServiceInitializeLogVO vo = new OnlineServiceInitializeLogVO();
            BeanUtil.copyProperties(recordsDO, vo);
            vo.setProcessDate(TimeFormatUtil.localDateTimeToString(recordsDO.getUpdatedTime(), TimeFormatUtil.formatter_1));
            vo.setCreatedTime(TimeFormatUtil.localDateTimeToString(recordsDO.getCreatedTime(), TimeFormatUtil.formatter_1));
            // 失败数量>0时展示下载按钮
            vo.setShowButton(vo.getFailedVinNum() != null && vo.getFailedVinNum() > 0);
            resp.add(vo);
        }
        return new PageResult<>(resp, recordsDOPage.getTotal());
    }

    private void setCondition(VinInitialLogPageDTO logPageDTO, LambdaQueryWrapperX<DmsOssFileRecordsDO> queryWrapper) {
        if(StringUtils.isNotBlank(logPageDTO.getCreatedTimeSort())){
            if (SortTypeEnum.ASC.getSortType().equals(logPageDTO.getCreatedTimeSort())) {
                queryWrapper.orderByAsc(DmsOssFileRecordsDO::getCreatedTime);
                queryWrapper.orderByAsc(DmsOssFileRecordsDO::getId);
            } else {
                queryWrapper.orderByDesc(DmsOssFileRecordsDO::getCreatedTime);
                queryWrapper.orderByDesc(DmsOssFileRecordsDO::getId);
            }
        }
        if(StringUtils.isNotBlank(logPageDTO.getOperateTimeSort())){
            if (SortTypeEnum.ASC.getSortType().equals(logPageDTO.getOperateTimeSort())) {
                queryWrapper.orderByAsc(DmsOssFileRecordsDO::getUpdatedTime);
                queryWrapper.orderByAsc(DmsOssFileRecordsDO::getId);
            } else {
                queryWrapper.orderByDesc(DmsOssFileRecordsDO::getUpdatedTime);
                queryWrapper.orderByDesc(DmsOssFileRecordsDO::getId);
            }
        }
        if (StringUtils.isNotBlank(logPageDTO.getStartTime())
                && StringUtils.isNotBlank(logPageDTO.getEndTime())) {
            queryWrapper.ge(DmsOssFileRecordsDO::getCreatedTime, logPageDTO.getStartTime())
                    .le(DmsOssFileRecordsDO::getCreatedTime, logPageDTO.getEndTime());
        }
    }

    @Override
    public CommonResult<Boolean> updateSyncResult(Set<Long> bauJobIdSet) {
        log.info("更新同步状态, bauJobIdSet={}", bauJobIdSet);
        if (CollUtil.isEmpty(bauJobIdSet)) {
            return CommonResult.success(true);
        }
        // 查询成功和失败的数量,vin_match_result=0为处理失败，vin_match_result=2为处理成功
        List<VinMatchCountDto> vinMatchCountDtos = dmsOssOriginalDataMapper.selectCountByJobId(bauJobIdSet);
        log.info("更新同步状态, 查询任务成功和失败的数量 result={}", vinMatchCountDtos);
        Map<Long, Map<Integer, Integer>> result = vinMatchCountDtos.stream()
                .collect(Collectors.groupingBy(
                        VinMatchCountDto::getBauJobId, // 外层Map的键
                        Collectors.toMap(
                                VinMatchCountDto::getVinMatchResult, // 内层Map的键
                                VinMatchCountDto::getVinCount, // 内层Map的值
                                (oldValue, newValue) -> oldValue // 解决键冲突，这里假设不会出现键冲突
                        )
                ));
        log.info("查询任务成功和失败的数量, result={}", result);
        // 查询任务原始数据
        List<DmsOssFileRecordsDO> recordsDOList = dmsOssFileRecordsMapper.selectList(new LambdaQueryWrapperX<DmsOssFileRecordsDO>()
                .in(DmsOssFileRecordsDO::getBauJobId, bauJobIdSet)
                .eq(DmsOssFileRecordsDO::getIsDeleted, false));
        Map<Long, DmsOssFileRecordsDO> jobToMap = recordsDOList.stream().collect(Collectors.toMap(DmsOssFileRecordsDO::getBauJobId, Function.identity(), (o, n) -> o));
        jobToMap.forEach((bauJobId, recordsDO) -> {
            Map<Integer, Integer> countMap = result.get(bauJobId);
            if (CollUtil.isEmpty(countMap)) {
                return;
            }
            recordsDO.setUpdatedTime(LocalDateTime.now());
            recordsDO.setSuccessVinNum(countMap.get(VinMatchResultEnum.SUCCESS.getCode()) == null ? 0 : countMap.get(VinMatchResultEnum.SUCCESS.getCode()));
            int notRequired = countMap.get(VinMatchResultEnum.NOT_REQUIRED.getCode()) == null ? 0 : countMap.get(VinMatchResultEnum.NOT_REQUIRED.getCode());
            int pending = countMap.get(VinMatchResultEnum.PENDING.getCode()) == null ? 0 : countMap.get(VinMatchResultEnum.PENDING.getCode());
            recordsDO.setFailedVinNum(notRequired + pending);
        });
        // 更新同步结果
        dmsOssFileRecordsMapper.updateBatch(jobToMap.values());
        return CommonResult.success(true);
    }

    @Override
    public void updateManualSyncResult(Long bauJobId) {
        log.info("更新手动补录同步状态, bauJobId={}", bauJobId);
        // 查询任务原始数据
        DmsOssFileRecordsDO recordsDO = dmsOssFileRecordsMapper.selectOne(new LambdaQueryWrapperX<DmsOssFileRecordsDO>()
                .eq(DmsOssFileRecordsDO::getBauJobId, bauJobId)
                .eq(DmsOssFileRecordsDO::getIsDeleted, false)
                .orderByDesc(DmsOssFileRecordsDO::getId)
                .last(LIMIT_ONE));
        if (Objects.isNull(recordsDO)) {
            log.info("更新手动补录同步状态, bauJobId={}在dmsOssFileRecords表中不存在", bauJobId);
            return;
        }
        recordsDO.setUpdatedTime(LocalDateTime.now());
        recordsDO.setSuccessVinNum(recordsDO.getSuccessVinNum() + 1);
        recordsDO.setFailedVinNum(recordsDO.getFailedVinNum() - 1);
        // 更新同步结果
        dmsOssFileRecordsMapper.updateById(recordsDO);
    }
}

