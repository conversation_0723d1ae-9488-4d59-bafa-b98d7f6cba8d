package com.jlr.ecp.subscription.excel.listener.appduc;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.appduc.AppDCuBatchRenewalExcel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AppDUcBatchRenewalReadListener implements ReadListener<AppDCuBatchRenewalExcel> {
    private static final int BATCH_COUNT = 500;

    private List<AppDCuBatchRenewalExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<AppDCuBatchRenewalExcel>> allList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    @Override
    public void invoke(AppDCuBatchRenewalExcel appDUcBatchRenewalExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(appDUcBatchRenewalExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allList.add(dataList);
                //重置空间
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("AppDUc批量续费读取异常:{}", e.getMessage());
            //重置空间
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("AppDUc批量续费读取最后剩余的手机号码");
        try {
            //调用批量处理逻辑
            allList.add(dataList);
        } catch (Exception e) {
            log.info("AppDUc批量续费读取最后剩余的手机号码异常:{}", e.getMessage());
        }  finally {
            dataList = null;
        }
        log.info("AppDUc批量续费读取最后剩余的手机号码，所有数据解析完成!");
    }
}
