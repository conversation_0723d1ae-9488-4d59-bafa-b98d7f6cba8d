package com.jlr.ecp.subscription.annotation;

import org.redisson.api.RateIntervalUnit;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiLimitation {

    /**
     *  最大限制访问次数
     * */
    long limitCount() default 10;

    /**
     *  限制访问的时间
     * */
    long time() default 1;

    /**
     * 时间单位
     * */
    RateIntervalUnit timeUnit() default RateIntervalUnit.SECONDS;

    /**
     *  令牌桶的名称，每个方法使用时最好不一样，比如用方法名 (不给默认值，必须使用者自己指定)
     * */
    String tokenBucketName();

}
