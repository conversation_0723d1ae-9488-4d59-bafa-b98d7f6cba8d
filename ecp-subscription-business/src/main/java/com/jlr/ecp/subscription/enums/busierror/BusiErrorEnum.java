package com.jlr.ecp.subscription.enums.busierror;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusiErrorEnum {

    ORD_CAR_NOT_FOUND("ORD_CAR_NOT_FOUND", "激活车辆未找到"),
    ORD_SERVICE_NOT_FOUND("ORD_SERVICE_NOT_FOUND", "激活车辆服务未找到"),
    ORD_TSDP_FAIL("ORD_TSDP_FAIL", "TSDP续费接口失败"),

    ORD_FUFILL_NOT_FOUND("ORD_FUFILL_NOT_FOUND", "履约信息未找到"),
    ;

    /**
     * 状态码
     * */
    public final String code;

    /**
     * 状态描述
     * */
    public final String desc;
}
