package com.jlr.ecp.subscription.service.model;

import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataCreateDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataUpdateDTO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataPageVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataVO;

import java.util.List;

/**
 * 品牌(ProductBrand)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-07 20:13:51
 */
public interface VehicleModelMasterDataService {

    /**
     * 创建车型年款配置
     * @param createDTO 创建入参
     * @return Boolean
     */
    Boolean createVehicleModelMasterData(VehicleModelMasterDataCreateDTO createDTO);

    /**
     * 编辑车型年款配置
     * @param updateDTO 创建入参
     * @return Boolean
     */
    Boolean editVehicleModelMasterData(VehicleModelMasterDataUpdateDTO updateDTO);

    /**
     * 车型年款详情
     * @param seriesCode 车型年款code
     * @return VehicleModelMasterDataVO
     */
    VehicleModelMasterDataVO getOneBySeriesCode(String seriesCode);


    /**
     * 分页列表
     * @param param 车型年款
     * @return List<VehicleModelMasterDataPageVO>
     */
    PageResult<VehicleModelMasterDataPageVO> getPage(PageParam param);

    /**
     * 删除
     * @param seriesCode 车型年款code
     * @return VehicleModelMasterDataVO
     */
    Boolean deleteBySeriesCode(String seriesCode);


    /**
     * 获取
     * @param vinList 车架号List
     * @return List<UserDPResultVO>
     */
    List<UserDPResultVO> findDpList(List<String> vinList);

    /**
     * 在vin初始化查询dp
     * @param vinList 车架号List
     * @return List<UserDPResultVO>
     */
    List<UserDPResultVO> findDpListByVinInit(List<String> vinList);

    /**
     * 获取
     * @param vin 车架号
     * @return UserDPResultVO
     */
    UserDPResultVO findDp(String vin);
}

