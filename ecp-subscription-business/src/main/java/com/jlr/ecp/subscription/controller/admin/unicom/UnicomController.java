package com.jlr.ecp.subscription.controller.admin.unicom;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.unicom.dto.UnicomBatchQueryPageDTO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomRnrBatchQueryListVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.*;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.unicom.UnicomRnrQueryStatusEnum;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.unicom.UnicomRnrBatchRecordsService;
import com.jlr.ecp.subscription.service.unicom.UnicomRnrQueryRecordsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "平台管理 - 批量查询ICCID")
@RestController
@RequestMapping("v1/unicom")
@Validated
@Slf4j
public class UnicomController {

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024 ;
    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private UnicomRnrBatchRecordsService unicomRnrBatchRecordsService;

    @Resource
    private UnicomRnrQueryRecordsService unicomRnrQueryRecordsService;



//
//    @PostMapping("/getEsimInfo")
//    @Operation(summary = "ESIM卡实名状态查询")
//    @PreAuthorize("@ss.hasPermission('query:iccid-single-query:forms')")
//    CommonResult<List<EsimInfoVO>> getEsimInfo(@Valid @RequestBody UnicomReqDTO unicomReqDTO) {
//
//        return CommonResult.success(piviUnicomService.getEsimInfo(unicomReqDTO));
//    }
    @GetMapping("/getEsimInfo")
    @Operation(summary = "ESIM卡实名状态查询")
    @PreAuthorize("@ss.hasPermission('query:iccid-single-query:forms')")
    CommonResult<UnicomRnrQueryVO> getEsimInfoV2(@RequestParam("carVin") String carVin) {

        return piviUnicomService.getRnrQuery(carVin);
    }



    @PostMapping("/addMockCarVin")
    @Deprecated
    @Operation(summary = "MOCK已实名车辆")
    CommonResult<List<String>> addMockCarVin(@Valid @RequestBody UnicomReqDTO unicomReqDTO) {

        return CommonResult.success(piviUnicomService.addRealNameList(unicomReqDTO));
    }


    @PostMapping("/callUnicom")
    @Operation(summary = "激活联通")
    CommonResult<Boolean> call(@RequestBody UnicomManualDTO unicomManualDTO) {
        FufilmentMessage fufilmentMessage = new FufilmentMessage();
        fufilmentMessage.setVin(unicomManualDTO.getVin());
        fufilmentMessage.setVcsOrderCode(unicomManualDTO.getVcsOrderCode());
        fufilmentMessage.setServiceBeginDate(unicomManualDTO.getServiceBeginDate());
        fufilmentMessage.setServiceEndDate(unicomManualDTO.getServiceEndDate());
        fufilmentMessage.setTenantId(1L);
        return CommonResult.success(piviUnicomService.callUnicomService(fufilmentMessage,unicomManualDTO.getFulfilmentId()));
    }

    @PostMapping("/cancelUnicom")
    @Operation(summary = "取消联通")
    CommonResult<Boolean> cancel(@RequestBody UnicomManualDTO unicomManualDTO) {
        CancelMessage cancelMessage = new CancelMessage();
        cancelMessage.setVin(unicomManualDTO.getVin());
        cancelMessage.setVcsOrderCode(unicomManualDTO.getVcsOrderCode());
        return CommonResult.success(piviUnicomService.cancelUnicomService(cancelMessage,unicomManualDTO.getFulfilmentId()));
    }


    @PostMapping("/download/template")
    @Operation(summary = "下载ICCID实名认证批量查询模板")
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query:forms')")
    public CommonResult<String> downloadUnicomRnrTemplate() {
        return unicomRnrBatchRecordsService.downloadUnicomRnrTemplate();
    }

    @PostMapping("/upload")
    @Operation(summary = "上传ICCID查询excel文件")
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query:forms')")
    public CommonResult<UnicomRnrBatchRecordsVO> batchUpload(@RequestBody MultipartFile file) {
        log.info("上传ICCID查询excel文件:{}, 文件大小:{}", file, file.getSize());
        long startTime = System.currentTimeMillis();
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.RNR_QUERY_FILE_SIZE_EXCEED_LIMIT);
        }
        CommonResult<UnicomRnrBatchRecordsVO> resp = unicomRnrBatchRecordsService.uploadRnrQueryExcel(file);
        long endTime = System.currentTimeMillis();
        log.info("上传ICCID查询excel文件解析完成，花费时间:{}毫秒，resp:{}", (endTime-startTime), resp);
        return resp;
    }


    @PostMapping("/file-pageList")
    @Operation(summary = "联通ICCID批量查询文件分页查询")
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query:forms')")
    public CommonResult<PageResult<UnicomRnrBatchRecordListVO>> getBatchFilePage(@RequestBody @Valid UnicomBatchFilePageDTO pageDTO) {
        return CommonResult.success(unicomRnrBatchRecordsService.getBatchFilePage(pageDTO));
    }



    @PostMapping("/pageList")
    @Operation(summary = "联通ICCID批量查询结果分页查询")
//    @PermitAll
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query:forms')")
    public CommonResult<PageResult<UnicomRnrBatchQueryListVO>> getBatchQueryPage(@RequestBody @Valid UnicomBatchQueryPageDTO pageDTO) {
        return CommonResult.success(unicomRnrQueryRecordsService.getBatchFilePage(pageDTO));
    }


    @GetMapping("/getQueryStatusList")
    @Operation(summary = "查询状态下拉列表接口")
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query-result:forms')")
    CommonResult<List<UnicomBatchQueryStatusVO>> getQueryStatusList() {
        List<UnicomBatchQueryStatusVO> orderStatusList = new ArrayList<>();
        for (UnicomRnrQueryStatusEnum value : UnicomRnrQueryStatusEnum.values()) {

            UnicomBatchQueryStatusVO vo = new UnicomBatchQueryStatusVO();
            vo.setStatus(value.getType());
            vo.setText(value.getDesc());
            orderStatusList.add(vo);
        }
        return CommonResult.success(orderStatusList);
    }



    @GetMapping("/queryBatchNo")
    @Operation(summary = "查询续费编号")
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query-result:forms')")
    public CommonResult<String> queryRenewalBatchNo(@RequestParam("batchNo") String batchNo) {
        String resp = unicomRnrBatchRecordsService.getUnicomQueryBatchNo(batchNo);
        return CommonResult.success(resp);
    }

    @GetMapping("/getInfo")
    @Operation(summary = "根据")
    CommonResult<UnicomRespVO> getInfo(@RequestParam("iccid") String iccid) {
        UnicomRespVO simCardInfo = piviUnicomService.getSimCardInfo(iccid);
        return CommonResult.success(simCardInfo);
    }


    @GetMapping("/checkVinRNRInfo")
    @Operation(summary = "重新查询RNR")
    @PreAuthorize("@ss.hasPermission('query:iccid-multiple-query-result:forms')")
    CommonResult<EsimInfoVO> checkVinRNRInfo(@RequestParam("carVin") String carVin) {
        EsimInfoVO esimInfoVO = piviUnicomService.checkVinRNRInfo(carVin);
        return CommonResult.success(esimInfoVO);
    }


}
