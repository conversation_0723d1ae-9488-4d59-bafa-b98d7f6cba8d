package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * dms发票状态
 */
@AllArgsConstructor
@Getter
public enum DMSStatusEnum {

    REGULAR("60721001", "正常发票"),

    INVALID("60721002", "作废发票");



    /**
     * 类型
     * */
    public final String code;

    /**
     * 描述
     * */
    public final String desc;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (DMSStatusEnum status : DMSStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
