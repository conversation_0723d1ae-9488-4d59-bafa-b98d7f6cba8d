package com.jlr.ecp.subscription.controller.app.unicom;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.bau.dto.HandleFileResultVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomReqDTO;
import com.jlr.ecp.subscription.service.oss.DmsOssFileRecordsService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "小程序端购物车 - 联通SIM卡相关")
@RestController
@RequestMapping("v1/unicom")
@Validated
@Slf4j
public class AppUnicomController {

    @Resource
    private PIVIUnicomService piviUnicomService;

    @PostMapping("/esimstate")
    @Operation(summary = "ESIM卡实名状态查询")
    CommonResult<List<EsimInfoVO>> esimstate(@Valid @RequestBody UnicomReqDTO unicomReqDTO) {

        return piviUnicomService.esimstate(unicomReqDTO);
    }

}
