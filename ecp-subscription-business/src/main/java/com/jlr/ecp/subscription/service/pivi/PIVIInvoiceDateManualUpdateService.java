package com.jlr.ecp.subscription.service.pivi;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualPageParam;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.vo.PIVIInvoiceDateUpdateVO;

import java.util.List;

public interface PIVIInvoiceDateManualUpdateService {

    /**
     * 该方法首先验证VIN码的合法性，然后检查对应的VIN码订单是否存在。
     *
     * @param carVin 车辆的VIN码。
     * @return 返回查询结果，包含VIN码的合法性验证结果和订单存在性验证结果。
     */
    CommonResult<String> queryCarVinFromECP(String carVin);

    /**
     * 根据车辆VIN码查询手动录入的发票信息页面参数。
     *
     * @param pageParam 页面请求参数，包含分页和查询条件信息。
     * @return 返回手动录入发票日期的查询结果，包含分页信息和转换后的业务对象列表。
     */
    CommonResult<PageResult<PIVIInvoiceDateUpdateVO>> queryManualInvoiceDateByCarVin(InvoiceDateManualPageParam pageParam);

    /**
     * 手动更新发票日期。
     *
     * @param manualUpdateDTO 手动更新发票日期的请求参数。
     * @return 返回更新结果。
     */
    CommonResult<String> manualUpdateInvoiceDate(InvoiceDateManualUpdateDTO manualUpdateDTO);
}
