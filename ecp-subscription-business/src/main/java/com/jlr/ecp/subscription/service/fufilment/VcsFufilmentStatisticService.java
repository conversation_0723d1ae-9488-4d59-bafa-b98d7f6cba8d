package com.jlr.ecp.subscription.service.fufilment;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsFufilmentStatisticDO;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_vcs_fufilment_statistic(t_vcs_fufilment_statistic)】的数据库操作Service
* @createDate 2024-01-19 14:15:09
*/
public interface VcsFufilmentStatisticService extends IService<VcsFufilmentStatisticDO> {

    /**
     * 获取顶部 tab list
     * 返回数据 Map<brand_name_view, Map<car_vin, series_code:series_name>>
     *
     * @param incontrolId icr
     * @param clientId
     * @return Map
     */
    Map<String, Map<String, String>> getTabs(String incontrolId, String clientId);
}
