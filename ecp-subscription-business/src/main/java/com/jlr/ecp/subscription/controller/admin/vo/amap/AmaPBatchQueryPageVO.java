package com.jlr.ecp.subscription.controller.admin.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP批量查询VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPBatchQueryPageVO {
    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "校验检验结果状态 0:不通过 1:通过")
    private Integer checkResultStatus;

    @Schema(description = "校验检验结果描述")
    private String checkResultDesc;

    @Schema(description = "续费编号")
    private String batchNo;

    @Schema(description = "错误详情")
    private String errorDetail;
}
