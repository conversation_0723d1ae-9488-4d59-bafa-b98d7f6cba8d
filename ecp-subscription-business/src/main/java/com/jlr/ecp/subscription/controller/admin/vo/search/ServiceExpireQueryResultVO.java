package com.jlr.ecp.subscription.controller.admin.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Schema(description = "服务到期日批量上传Excel校验VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ServiceExpireQueryResultVO implements Serializable {

    /**
     * 批次处理号；批次处理号，雪花算法ID
     */
    @Schema(description = "查询编号")
    private String batchNo;

    /**
     *  处理状态 0：查询中 1：查询完成 2：查询失败
     * */
    @Schema(description = "查询状态")
    private String queryStatus;

    @Schema(description = "VIN")
    private String carVin;

    /**
     *  品牌名称
     * */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     *  系列名称
     * */
    @Schema(description = "系列名称")
    private String seriesName;

    /**
     *  系列编码
     * */
    @Schema(description = "系列编码")
    private String seriesCode;

    /**
     *  PIVI车机属性 'PIVI'标识是，否则不是
     * */
    @Schema(description = "是否是PIVI车机")
    private String carSystemModel;

    /**
     * 是否具备信息娱乐服务相关配置 N 否 Y 是
     * */
    @Schema(description = "是否具备信息娱乐服务相关配置")
    private String hasInfoEntertain;

    /**
     *  型号年款
     * */
    @Schema(description = "型号年款")
    private String modelYear;

    /**
     *  配置编码
     * */
    @Schema(description = "配置编码")
    private String configCode;

    /**
     * 配置名称可能很长
     * */
    @Schema(description = "配置名称")
    private String configName;

    /**
     * 品牌编码
     * */
    @Schema(description = "品牌编码")
    private String brandCode;


    /**
     *  InControl远程车控服务
     * */
    @Schema(description = "InControl远程车控服务")
    private String remoteServiceDate;

    /**
     *  InControl在线服务
     * */
    @Schema(description = "InControl在线服务")
    private String piviServiceDate;

    /**
     *  信息娱乐服务
     * */
    @Schema(description = "信息娱乐服务")
    private String appDServiceDate;

    /**
     *  实时交通信息
     * */
    @Schema(description = "实时交通信息")
    private String amaPServiceDate;

    /**
     *  网络流量
     * */
    @Schema(description = "网络流量")
    private String unicomServiceDate;

    /**
     *  车辆发票日期
     * */
    @Schema(description = "车辆发票日期")
    private String invoiceServiceDate;


    /**
     *  操作时间
     * */
    @Schema(description = "操作时间")
    private String operateTime;

    /**
     * 操作人账号；操作人账号
     */
    @Schema(description = "操作人账号")
    private String operator;

    /**
     * 失败原因
     * */
    @Schema(description = "失败原因")
    private String errorReason;
}
