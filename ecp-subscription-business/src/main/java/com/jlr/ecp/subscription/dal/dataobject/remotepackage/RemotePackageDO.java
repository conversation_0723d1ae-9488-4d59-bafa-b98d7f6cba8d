package com.jlr.ecp.subscription.dal.dataobject.remotepackage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_remote_package
 * @TableName t_remote_package
 * <AUTHOR>
 */
@TableName(value ="t_remote_package")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RemotePackageDO extends BaseDO {
    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 车机;车机
     */
    @TableField(value = "car_system_model")
    private String carSystemModel;

    /**
     * 服务包编码;服务包编码
     */
    @TableField(value = "package_code")
    private String packageCode;

    /**
     * 上传用户;上传用户名
     */
    @TableField(value = "created_user")
    private String createdUser;



}