package com.jlr.ecp.subscription.model.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/** AppD续约服务参数实体
 */
@Data
public class AppDSubscriptionReq {

    /**
     * required
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "GMT+8")
    private LocalDateTime expiresDate;
    /**
     * required
     */
    private Long jlrSubscriptionId;
    /**
     * PROVISION ACTIVATE DEACTIVATE DEPROVISION
     * required
     */
    private String subscriptionEvent;
    /**
     * required
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "GMT+8")
    private LocalDateTime subscriptionEventDateTime;
}
