package com.jlr.ecp.subscription.dal.mysql.expiryvin;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.expiryvin.VinExpiryMonthlyRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description 针对表【t_vin_expiry_monthly_records】的数据库操作Mapper
 * @Entity com.jlr.ecp.order.dal.dataobject.report.VinExpiryMonthlyRecordDO
 */
@Mapper
@DS("ecp_subscription") // 若使用多数据源需保留，否则可删除
public interface VinExpiryMonthlyRecordDOMapper extends BaseMapperX<VinExpiryMonthlyRecordDO> {

    /**
     * 根据年月查询记录（唯一性校验）
     *
     * @param year  统计年份（如 "2025"）
     * @param month 统计月份（如 "4"）
     * @return 记录对象
     */
    default VinExpiryMonthlyRecordDO selectByYearMonth(Integer year, Integer month) {
        return selectOne(new QueryWrapper<VinExpiryMonthlyRecordDO>()
            .eq("job_year", year)
            .eq("job_month", month)
            .eq("is_deleted", 0));
    }

    /**
     * 查询最新生成的报表记录
     *
     * @return 最新记录
     */
    default VinExpiryMonthlyRecordDO selectLatestRecord() {
        return selectOne(new QueryWrapper<VinExpiryMonthlyRecordDO>()
            .orderByDesc("created_time")
            .last("LIMIT 1"));
    }
}