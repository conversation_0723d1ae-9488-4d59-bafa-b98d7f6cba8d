package com.jlr.ecp.subscription.dal.mysql.incontrol;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * t_incontrol_customer(t_incontrol_customer)数据Mapper
 *
 * <AUTHOR>
 * @since 2023-12-20 14:42:17
 * @description 由 Mybatisplus Code Generator 创建
*/
@Mapper
public interface IncontrolCustomerMapper extends BaseMapperX<IncontrolCustomerDO> {

}
