package com.jlr.ecp.subscription.service.fufilment;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsFufilmentStatisticDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsFufilmentStatisticMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_vcs_fufilment_statistic(t_vcs_fufilment_statistic)】的数据库操作Service实现
 * @createDate 2024-01-19 14:15:09
 */
@Service
@Slf4j
public class VcsFufilmentStatisticServiceImpl extends ServiceImpl<VcsFufilmentStatisticMapper, VcsFufilmentStatisticDO>
        implements VcsFufilmentStatisticService {


    @Resource
    private VcsFufilmentStatisticMapper vcsFufilmentStatisticMapper;
    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    RedisService redisService;

    /**
     * 获取顶部 tab list
     * 返回数据 Map<brand_name_view, Map<car_vin, series_code:series_name>>
     *
     * @param incontrolId
     * @param clientId
     * @return
     */
    @Override
    public Map<String, Map<String, String>> getTabs(String incontrolId, String clientId) {

        //1.查询t_vcs_fufilment_statistic 相关统计
        List<VcsFufilmentStatisticDO> statisticList = vcsFufilmentStatisticMapper
                .selectList(new LambdaQueryWrapperX<VcsFufilmentStatisticDO>()
                        .eq(VcsFufilmentStatisticDO::getIncontrolId, incontrolId)
                        .eq(VcsFufilmentStatisticDO::getBrandCode, clientId)
                        .eq(BaseDO::getIsDeleted, false));
        //没数据返回空
        if (CollectionUtils.isEmpty(statisticList)) {
            return null;
        }
        //2.拿到series_code 和 incontrolId 查询t_incontrol_vehicle 找到对应的car vin
        // 获取所有相关的 series_code
        Set<Object> seriesCodes = statisticList.stream()
                .map(VcsFufilmentStatisticDO::getSeriesCode).collect(Collectors.toSet());
        log.info("seriesCodes:{}", seriesCodes);
        List<String> multiCacheMapValue = redisService.getMultiCacheMapValue(Constants.REDIS_KEY.SERIES_CACHE_KEY, seriesCodes);
        log.info("multiCacheMapValue:{}", multiCacheMapValue);
        List<SeriesMappingVO> mappingVOList = multiCacheMapValue.stream()
                .map(s -> JSON.parseObject(s, SeriesMappingVO.class))
                .collect(Collectors.toList());
        log.info("mappingVOList:{}", mappingVOList);

        Map<String, SeriesMappingVO> seriesMapping = mappingVOList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SeriesMappingVO::getSeriesCode, Function.identity()));
        List<IncontrolVehicleDO> vehicles = incontrolVehicleDOMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getIncontrolId, incontrolId)
                .in(IncontrolVehicleDO::getSeriesCode, seriesCodes)
                .eq(BaseDO::getIsDeleted, false));
//        log.info("seriesMapping:{}", seriesMapping);
        //3.组装对应数据 并且返回  构建最终的映射
        Map<String, Map<String, String>> result = vehicles.stream()
                .filter(v -> Optional.ofNullable(seriesMapping.get(v.getSeriesCode()))
                        .map(SeriesMappingVO::getSeriesName)
                        .filter(seriesName -> !seriesName.equals("默认系列名称")) // 过滤掉默认系列名称
                        .isPresent()) // 仅包含有效系列名称的记录
                .collect(Collectors.groupingBy(
                        v -> Optional.ofNullable(seriesMapping.get(v.getSeriesCode()))
                                .map(SeriesMappingVO::getBrandNameView)
                                .orElse(null), // 使用默认品牌名称作为备选项
                        Collectors.toMap(
                                IncontrolVehicleDO::getCarVin,
                                v -> v.getSeriesCode() + ":" + Optional.ofNullable(seriesMapping.get(v.getSeriesCode()))
                                        .map(SeriesMappingVO::getSeriesName)
                                        .orElse(null) // 使用默认系列名称作为备选项
                        )
                ));

        return result;
    }
}




