package com.jlr.ecp.subscription.aop;

import cn.hutool.core.date.DateUtil;
import com.jlr.ecp.subscription.annotation.ApiLimitation;
import com.jlr.ecp.subscription.annotation.ApiRateLimitation;
import com.jlr.ecp.subscription.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.Redisson;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class ApiRateLimitationAspect {

    @Resource
    private Redisson redisson;

    @Pointcut("@annotation(com.jlr.ecp.subscription.annotation.ApiRateLimitation)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        ApiRateLimitation apiRateLimitation = method.getAnnotation(ApiRateLimitation.class);
        //没有使用限流，继续执行被代理方法的原始逻辑
        if (Objects.isNull(apiRateLimitation)) {
            return point.proceed();
        }

        long limitCount = apiRateLimitation.limitCount();
        long time = apiRateLimitation.time();
        RateIntervalUnit timeUnit = apiRateLimitation.timeUnit();
        String tokenBucketName = apiRateLimitation.tokenBucketName();

        // 初始化限流器配置
        String limiterKey = Constants.API_LIMITATION_KEY + tokenBucketName;
        RRateLimiter rateLimiter = redisson.getRateLimiter(limiterKey);
        if (!rateLimiter.isExists()) {
            rateLimiter.trySetRate(RateType.OVERALL, limitCount, time, timeUnit);
        }
        // 尝试获取令牌
        rateLimiter.acquire(1);
        log.info("限流执行中 ：当前时间：{}", DateUtil.now());
        // 获取到令牌后才执行
        return point.proceed();
    }
}
