package com.jlr.ecp.subscription.enums.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 联通方法枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomFunctionEnum {

    GET_CARD_INFO("getCardInfo", "查询卡状态"),
    DO_CARD_PRODUCT_ORDER("doCardProductOrder", "产品订购");;
    /**
     * 类型
     * */
    public final String code;

    /**
     * 描述
     * */
    public final String desc;

}
