package com.jlr.ecp.subscription.api.vininit.dto.parse;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 车辆订阅信息DTO
 */
@Data
public class VehicleSubscriptionDTO {
    
    /**
     * 订阅列表
     */
    private List<SubscriptionDTO> subscriptions;
    
    /**
     * 绑定的客户信息
     */
    private BoundToCustomerDTO boundToCustomer;
    

    /**
     * 最早过期时间（UTC）
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private LocalDateTime earliestExpiryDate;
    
    /**
     * 车辆信息
     */
    private VehicleInformationDTO vehicleInformation;
}
