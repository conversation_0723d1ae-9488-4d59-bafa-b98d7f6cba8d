package com.jlr.ecp.subscription.service.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalOperateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalYearVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPSingleRenewalPageVO;

import java.util.List;

public interface AmaPSingleManualRenewalService {
    /**
     *  AMAP单个续费分页查询列表
     *
     * @param amaPPageDto 续费页面的查询参数
     * @return 续费页面的数据列表和总记录数
     */
    PageResult<AmaPSingleRenewalPageVO> queryRenewalPageList(AmaPSingleRenewalPageDTO amaPPageDto);

    /**
     * 获取续费年份列表
     *
     * @return 一个包含所有续费年份信息的列表
     */
    List<AmaPRenewalYearVO> getAmaPRenewalYear();

    /**
     * 手动续费单个AMAP
     *
     * @param operateDTO 操作数据传输对象，包含续费所需的信息
     * @return CommonResult<String> 返回续费操作的结果
     */
    CommonResult<String> amaPSingleManualRenewal(AmaPSingleRenewalOperateDTO operateDTO);

    /**
     * 根据车架号检查是否存在续费中的高德手动续费记录
     * 此方法旨在确保车架号对应的车辆没有未完成的续费流程，以避免重复处理或系统错误
     *
     * @param carVin 车辆识别号（VIN），用于查询续费记录
     * @return Boolean 返回是否存在记录的布尔值
     */
    boolean checkProcessRecord(String carVin);
}
