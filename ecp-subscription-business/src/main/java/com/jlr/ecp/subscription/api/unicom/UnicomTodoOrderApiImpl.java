package com.jlr.ecp.subscription.api.unicom;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.unicom.dto.GenerateReportRequestV2;
import com.jlr.ecp.subscription.api.unicom.dto.MonthlyRnrBatchQueryDTO;
import com.jlr.ecp.subscription.api.unicom.dto.UnicomBatchQueryPageDTO;
import com.jlr.ecp.subscription.api.unicom.vo.InitResponse;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomRnrBatchQueryListVO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.unicom.UnicomRnrBatchRecordsService;
import com.jlr.ecp.subscription.service.unicom.UnicomRnrQueryRecordsService;
import com.jlr.ecp.subscription.service.unicom.UnicomTodoOrderService;
import com.jlr.ecp.subscription.service.vin.expiry.VinExpiryMonthlyDetailService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class UnicomTodoOrderApiImpl implements UnicomTodoOrderApi {


    @Resource
    private UnicomTodoOrderService unicomTodoOrderService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private UnicomRnrBatchRecordsService unicomRnrBatchRecordsService;
    @Resource
    private UnicomRnrQueryRecordsService unicomRnrQueryRecordsService;
    @Resource
    private VinExpiryMonthlyDetailService vinExpiryMonthlyDetailService;

    @Override
    public CommonResult<List<UnicomTodoOrderVO>> findUnicomTodoOrder(Integer total) {

        return unicomTodoOrderService.findUnicomTodoOrder(total);
    }

    @Override
    public CommonResult<Integer> executeTodoOrder(UnicomTodoOrderVO unicomTodoOrderVO) {
        return CommonResult.success(piviUnicomService.executeTodoOrder(unicomTodoOrderVO));
    }

    @Override
    public CommonResult<Integer> rnrBatchQueryParseJob(Long batchNo) throws Exception {
        return CommonResult.success(unicomRnrBatchRecordsService.rnrBatchQueryParseJob(batchNo));
    }

    @Override
    public CommonResult<Integer> rnrBatchQueryExecutor(List<Long> idList) {
        return CommonResult.success(unicomRnrBatchRecordsService.rnrBatchQueryExecutor(idList));
    }

    @Override
    public CommonResult<PageResult<UnicomRnrBatchQueryListVO>> getBatchQueryPage(UnicomBatchQueryPageDTO pageDTO) {
        return CommonResult.success(unicomRnrQueryRecordsService.getBatchFilePage(pageDTO));
    }


    @Override
    public CommonResult<List<Long>> selectQueryRecordsByBatchNo(Long batchNo) {
        return CommonResult.success(unicomRnrQueryRecordsService.selectQueryRecordsByBatchNo(batchNo));
    }


    @Override
    public CommonResult<List<Long>> selectQueryVinRecordsByJobId(Long jobId) {
        return  CommonResult.success(vinExpiryMonthlyDetailService.selectQueryVinRecordsByJobId(jobId));
    }

    @Override
    public CommonResult<Integer> monthlyRnrBatchQueryExecutor(MonthlyRnrBatchQueryDTO monthlyRnrBatchQueryDTO) {
        return CommonResult.success(vinExpiryMonthlyDetailService.rnrBatchQueryExecutor(monthlyRnrBatchQueryDTO));
    }

    @Override
    public CommonResult<InitResponse> initializeRecords(GenerateReportRequestV2 request) {
        return CommonResult.success(vinExpiryMonthlyDetailService.initializeRecords(request));
    }

    @Override
    public CommonResult<Integer> getInitializedCount(Long jobId) {
        return CommonResult.success(vinExpiryMonthlyDetailService.getInitializedCount(jobId));
    }

    @Override
    public CommonResult<Integer> insertAllReportDataByJobId(Long jobId) {
        return CommonResult.success(vinExpiryMonthlyDetailService.getInsertReportData(jobId));
    }
}
