package com.jlr.ecp.subscription.controller.admin.appd;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalUploadVO;
import com.jlr.ecp.subscription.service.appd.AppDCuBatchRenewalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "批量续费-APPD-联通")
@RequestMapping("/appd/uicom/batch/manual/renewal")
@Validated
public class AppDCuBatchRenewalController {

    @Resource
    private AppDCuBatchRenewalService appDCuBatchRenewalService;

    @GetMapping("/download/template")
    @Operation(summary = "下载AppDCu批量Excel发送模板")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-multiple-renewal:forms')")
    public CommonResult<String> downloadTemplateUrl() {
        // 获取模板文件的URL
        String templateUrl = appDCuBatchRenewalService.getAppDUcTemplateUrl();
        return CommonResult.success(templateUrl);
    }

    @PostMapping("/queryBatchPageList")
    @Operation(summary = "查询AppDUc续费页面列表")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-multiple-renewal:forms')")
    public CommonResult<PageResult<AppDUcBatchRenewalPageVO>> queryBatchRenewalPageList(@RequestBody @Valid AppDUcBatchRenewalPageDTO pageDto) {
        return CommonResult.success(appDCuBatchRenewalService.queryAppDUcBatchRenewalPageList(pageDto));
    }

    @PostMapping("/uploadExcel")
    @Operation(summary = "上传excel文件")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-multiple-renewal:forms')")
    public CommonResult<AppDUcBatchRenewalUploadVO> uploadExcelRenewal(@RequestBody MultipartFile multipartFile) {
        return appDCuBatchRenewalService.uploadAppDUcExcelRenewal(multipartFile);
    }

    @PostMapping("/batchSendAppDUc")
    @Operation(summary = "AppDCu批量续费发送")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-multiple-renewal:forms')")
    public CommonResult<String> batchSendRenewal(@RequestBody @Valid AppDUcBatchSendDTO appDUcBatchSendDTO) {
        return appDCuBatchRenewalService.batchSendAppDCuRenewal(appDUcBatchSendDTO);
    }
}
