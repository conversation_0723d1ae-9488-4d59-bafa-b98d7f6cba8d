package com.jlr.ecp.subscription.service.iccid.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuSingleRenewalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyPageV0;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyV0;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.iccid.ModifyDataSourceEnum;
import com.jlr.ecp.subscription.enums.iccid.ModifyStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.iccid.IccidSingleModifyService;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.IccidUtil;
import com.jlr.ecp.subscription.util.LoginUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.constant.Constants.SINFLE_MODIFY_SUCCESS_MESSAGE;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.VIN_NOT_FOUND;
import static com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum.SYSTEM_ERROR;

@Service
@Slf4j
public class IccidSingleModifyServiceImpl implements IccidSingleModifyService {
    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private PIVIUnicomService unicomService;

    @Resource
    private Environment environment;

    @Resource
    private Snowflake snowflake;

    @Resource
    private IccidModifyRecordsDOMapper iccidModifyRecordsDOMapper;

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Resource
    private AppDCuSingleRenewalService appDCuSingleRenewalService;

    @Resource
    private OrderCheckService orderCheckService;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    Snowflake ecpIdUtil;

    @Override
    public CommonResult<IccidSingleModifyV0> iccidSingleModifyPreCheck(IccidSingleModifyDTO iccidSingleModifyDTO) {
        log.info("iccidSingleModifyPreCheck, iccidSingleModifyDTO:{}", iccidSingleModifyDTO);

        // 清理 newIccid 字段 中的空格
        String sanitizedNewIccid = sanitizeNewIccid(iccidSingleModifyDTO.getNewIccid());
        iccidSingleModifyDTO.setNewIccid(sanitizedNewIccid);
        log.info("iccidSingleModifyPreCheck, sanitizedNewIccid:{}", sanitizedNewIccid);

        CommonResult<IccidSingleModifyV0> preCheckError = validateIccidSingleModifyDTO(iccidSingleModifyDTO);
        if (preCheckError != null) {
            return preCheckError;
        }

        // 返回预检查结果
        PIVIPackageDO piviPackageDOByCarVin = piviPackageDOMapper.findICCIDByCarVin(iccidSingleModifyDTO.getCarVin());

        IccidSingleModifyV0 iccidSingleModifyV0 = new IccidSingleModifyV0();
        iccidSingleModifyV0.setCarVin(CarVinUtil.carVinToUpperCase(iccidSingleModifyDTO.getCarVin()));
        iccidSingleModifyV0.setOldIccid(piviPackageDOByCarVin.getIccid());
        iccidSingleModifyV0.setNewIccid(iccidSingleModifyDTO.getNewIccid());
        iccidSingleModifyV0.setRenewalResult("前置校验通过");
        return CommonResult.success(iccidSingleModifyV0);
    }

    private CommonResult<IccidSingleModifyV0> validateIccidSingleModifyDTO(IccidSingleModifyDTO iccidSingleModifyDTO) {
        // 校验VIN格式
        if (!CarVinUtil.checkVinFormat(iccidSingleModifyDTO.getCarVin())) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }

        // 校验ICCID格式
        if (!IccidUtil.checkIccidFormat(iccidSingleModifyDTO.getNewIccid())) {
            return CommonResult.error(ErrorCodeConstants.ICCID_FORMAT_ERROR);
        }

        // 校验carVin是否在t_pivi_package 中
        PIVIPackageDO piviPackageDOByCarVin = piviPackageDOMapper.findICCIDByCarVin(iccidSingleModifyDTO.getCarVin());
        log.info("根据车架号查询出对应的 piviPackageDO:{}", piviPackageDOByCarVin);
        if (piviPackageDOByCarVin == null) {
            return CommonResult.error(VIN_NOT_FOUND);
        }
        return null;
    }

    /**
     * 清理 newIccid 字段，去除所有空格。
     *
     * @param newIccid 原始包含空格的 ICCID 字符串
     * @return 清理后的 ICCID 字符串
     */
    private String sanitizeNewIccid(String newIccid) {
        if (newIccid == null) {
            return null;
        }
        // 使用正则表达式去除所有空格
        return newIccid.replaceAll("\\s+", "");
    }

    /**
     * 查询 iccid在 CU中成功的情况
     * 0.查询联通结果不为空 1.getResponseDesc为success 2.getUnicomRespData.getSimCardInfo.getIccid不为null
     * 1. 更新 t_pivi_package 中的 iccid
     * 2. 插入 t_iccid_modify_records 记录
     * 3. update iccid 成功才落库 t_manual_modify_log
     *
     * @param iccidSingleModifyDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<IccidSingleModifyV0> iccidSingleOperateModify(IccidSingleModifyDTO iccidSingleModifyDTO) {
        log.info("iccidSingleOperateModify, iccidSingleModifyDTO:{}", iccidSingleModifyDTO);

        // 清理 newIccid 字段 中的空格
        String sanitizedNewIccid = sanitizeNewIccid(iccidSingleModifyDTO.getNewIccid());
        iccidSingleModifyDTO.setNewIccid(sanitizedNewIccid);
        log.info("iccidSingleOperateModify, sanitizedNewIccid:{}", sanitizedNewIccid);

        // 调用公共校验方法
        CommonResult<IccidSingleModifyV0> validationResult = validateIccidSingleModifyDTO(iccidSingleModifyDTO);
        if (validationResult != null) {
            return validationResult;
        }

        // init data
        PIVIPackageDO piviPackageDOByCarVin = piviPackageDOMapper.findICCIDByCarVin(iccidSingleModifyDTO.getCarVin());
        String newIccid = iccidSingleModifyDTO.getNewIccid();
        String oldIccid = piviPackageDOByCarVin.getIccid();
        String carVin = CarVinUtil.carVinToUpperCase(iccidSingleModifyDTO.getCarVin());
        IccidSingleModifyV0 iccidSingleModifyV0 = new IccidSingleModifyV0();
        IccidModifyRecordsDO iccidModifyRecordsDO = new IccidModifyRecordsDO();

        // 调用UNICOM接口 & 不同情况进行落库
        UnicomRespVO simCardInfo = unicomService.getSimCardInfo(newIccid);
        // dev \test \ uat env Assign a value to simCardInfo
        String env = environment.getProperty("spring.profiles.active");
        if ("dev".equals(env) || "test".equals(env) || "uat".equals(env)) {
            iccidSingleModifyV0.setUnicomRespVO(simCardInfo);
        }
        log.info("根据newIccid:{}, 查询联通结果:{}", newIccid, simCardInfo);


        // 异常是否需要一个原始cuResult 存原始信息 ErrorDesc只是为了ecp_display -暂不添加，可能原始cuResult返回的值很大
        // a. 查询时异常
        if (StrUtil.isNotBlank(simCardInfo.getQueryResult())) {
            // 插入 t_iccid_modify_records.error_desc 记录错误为系统异常
            log.info("a.根据newIccid:{}, 查询联通结果异常, getQueryResult():{}", newIccid, simCardInfo.getQueryResult());
            buildAndInsertFailureRecord(iccidModifyRecordsDO, carVin, oldIccid, newIccid, SYSTEM_ERROR.getDesc());

            iccidSingleModifyV0.setRenewalResult(simCardInfo.getQueryResult());
            return CommonResult.success(iccidSingleModifyV0);
        }

        // b.simCardInfo.getResponseDesc 不等于 UnicomResultEnum.SUCCESS.getDesc
        if (!UnicomResultEnum.SUCCESS.getDesc().equals(simCardInfo.getResponseDesc())) {
            // 插入 t_iccid_modify_records.error_desc 记录错误 responseDesc的值
            log.info("b.根据newIccid:{}, 查询联通结果为 不成功 , errorDesc:{}", newIccid, simCardInfo.getResponseDesc());
            buildAndInsertFailureRecord(iccidModifyRecordsDO, carVin, oldIccid, newIccid, simCardInfo.getResponseDesc());

            iccidSingleModifyV0.setRenewalResult(simCardInfo.getResponseDesc());
            return CommonResult.success(iccidSingleModifyV0);
        }

        // c.unicomRespData.getSimCardInfo().getIccid()查不到
        UnicomRespData unicomRespData = simCardInfo.getUnicomRespData();
        if (Objects.isNull(unicomRespData) || Objects.isNull(unicomRespData.getSimCardInfo())
                || StrUtil.isBlank(unicomRespData.getSimCardInfo().getIccid())) {
            log.info("c.根据newIccid:{}, 查询联通结果成功，但newIccid查不到", newIccid);
            buildAndInsertFailureRecord(iccidModifyRecordsDO, carVin, oldIccid, newIccid, SYSTEM_ERROR.getDesc());

            iccidSingleModifyV0.setRenewalResult("查询cu时iccid为空");
            return CommonResult.success(iccidSingleModifyV0);
        }

        // d.成功情况
        // 1.更新 t_pivi_package 中的 iccid
        piviPackageDOByCarVin.setIccid(newIccid);
        piviPackageDOByCarVin.setUpdatedBy(LoginUtil.getLoginUserName());
        piviPackageDOByCarVin.setUpdatedTime(LocalDateTime.now());
        // 更新cu到期日成功才视为成功
        CommonResult<UnicomRespVO> result = updateCuExpireDate(piviPackageDOByCarVin);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            buildAndInsertFailureRecord(iccidModifyRecordsDO, carVin, oldIccid, newIccid, result.getMsg());
            iccidSingleModifyV0.setRenewalResult(result.getMsg());
            return CommonResult.success(iccidSingleModifyV0);
        } else if (UnicomResultEnum.SUCCESS.getDesc().equals(result.getData().getResponseDesc())) {
            buildAndInsertSuccessRecord(iccidModifyRecordsDO, carVin, oldIccid, newIccid);
        } else {
            buildAndInsertFailureRecord(iccidModifyRecordsDO, carVin, oldIccid, newIccid, result.getData().getResponseDesc());
            iccidSingleModifyV0.setRenewalResult(result.getData().getResponseDesc());
            return CommonResult.success(iccidSingleModifyV0);
        }

        piviPackageDOMapper.updateById(piviPackageDOByCarVin);
        log.info("1.更新 t_pivi_package 中的 piviPackageDOByCarVin:{}", piviPackageDOByCarVin);

        //3. update iccid 成功才落库 t_manual_modify_log
        //修改日志记录插入
        manualModifyLogDOService.recordLog(iccidModifyRecordsDO);
        iccidSingleModifyV0.setRenewalResult(SINFLE_MODIFY_SUCCESS_MESSAGE);
        return CommonResult.success(iccidSingleModifyV0);
    }

    /**
     * 更新联通到期日
     * 当ICCID变更后，自动刷新联通到期日
     *
     * @param piviPackageDO 包含车辆VIN信息的PIVPackage对象
     */
    @Override
    public CommonResult<UnicomRespVO> updateCuExpireDate(PIVIPackageDO piviPackageDO) {
        // 查询要续费的日期
        List<SubscriptionServiceDO> list = subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .eq(SubscriptionServiceDO::getCarVin, piviPackageDO.getVin())
                        .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                        .ne(SubscriptionServiceDO::getServicePackage, ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName())
                        .eq(SubscriptionServiceDO::getIsDeleted, false));
        LocalDateTime expireDate;
        if (CollUtil.isEmpty(list)) {
            // 查询手动续费表到期日
            expireDate = getExpireDateFromAppDCuRenewRecords(piviPackageDO);
        } else {
            Map<String, LocalDateTime> piviExpireMap = list.stream().collect(Collectors.toMap(SubscriptionServiceDO::getServicePackage, SubscriptionServiceDO::getExpiryDate, (e1, e2) -> e1));
            LocalDateTime ecpAppdTime = piviExpireMap.get(ServicePackageEnum.ONLINE_PACK.getPackageName());
            LocalDateTime ecpUnicomTime = piviExpireMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());
            expireDate = ObjectUtil.defaultIfNull(ecpAppdTime, ecpUnicomTime);
        }
        AppDCuSingleRenewalDTO appDCuSingleRenewalDTO = new AppDCuSingleRenewalDTO();
        appDCuSingleRenewalDTO.setCarVin(piviPackageDO.getVin());
        appDCuSingleRenewalDTO.setCuExpireDate(expireDate.toString());
        // sprint47:校验是否存在续费中的记录
        if (appDCuSingleRenewalService.checkProcessRecord(appDCuSingleRenewalDTO)) {
            log.warn("修改ICCID后自动刷新联通到期日, 存在续费中的记录, vin={}", piviPackageDO.getVin());
            return CommonResult.error(ORDER_IN_TRANSIT_ERROR);
        }

        // sprint47:校验是否存在在途订单
        CommonResult<String> checkOrderInTransit = orderCheckService.checkOrderInTransit(appDCuSingleRenewalDTO.getCarVin(), ServiceTypeEnum.PIVI);
        if (!checkOrderInTransit.isSuccess()) {
            log.warn("修改ICCID后自动刷新联通到期日, 存在在途订单, vin={}", piviPackageDO.getVin());
            return CommonResult.error(ORDER_IN_TRANSIT_ERROR);
        }

        // 调用联通更新
        FufilmentMessage message = new FufilmentMessage();
        message.setVin(piviPackageDO.getVin());
        message.setServiceBeginDate(expireDate.minusDays(1));
        message.setServiceEndDate(expireDate);
        message.setVcsOrderCode(ecpIdUtil.nextIdStr());
        message.setTenantId(TenantContextHolder.getTenantId());
        CommonResult<UnicomRespVO> result = piviUnicomService.unicomRenewalByIccid(message, piviPackageDO.getIccid());
        log.info("修改ICCID后自动刷新联通到期日，续费结果:{}", result);
        return result;
    }

    /**
     * 从AppDCu续费记录中获取过期日期
     * 本方法旨在通过查询AppDCu续费记录表，找到指定车辆（通过vin识别）的最新续费记录，
     * 并从中获取续费后的过期日期如果找不到符合条件的续费记录，则返回原始的过期日期
     *
     * @param piviPackageDO 包含车辆信息和过期日期的对象
     * @return 续费后的过期日期或原始的过期日期
     */
    private LocalDateTime getExpireDateFromAppDCuRenewRecords(PIVIPackageDO piviPackageDO) {
        // 根据vin查询手动续费成功的记录
        List<AppDCuRenewRecords> appDCuRenewRecords = appDCuRenewRecordsMapper.getByVinAndStatus(piviPackageDO.getVin(), AppDRenewStatusEnum.RENEW_SUCCESS.getStatus());

        // 获取最新的续费记录
        AppDCuRenewRecords latestRecord = appDCuRenewRecords.stream()
                .filter(records -> RenewServiceTypeEnum.UNICOM.getServiceType().equals(records.getRenewServiceType()))
                .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);

        return Objects.nonNull(latestRecord) ? latestRecord.getRenewAfterExpiryDate() : piviPackageDO.getExpiryDate();
    }

    private void buildAndInsertFailureRecord(IccidModifyRecordsDO iccidModifyRecordsDO, String carVin, String oldIccid, String newIccid, String errorDesc) {
        buildBaseIccidModifyRecordsDO(iccidModifyRecordsDO, carVin, oldIccid, newIccid);

        iccidModifyRecordsDO.setErrorDesc(errorDesc);
        iccidModifyRecordsDO.setModifyStatus(ModifyStatusEnum.FAILURE.getStatus());
        iccidModifyRecordsDOMapper.insert(iccidModifyRecordsDO);
    }

    private void buildAndInsertSuccessRecord(IccidModifyRecordsDO iccidModifyRecordsDO, String carVin, String oldIccid, String newIccid) {
        buildBaseIccidModifyRecordsDO(iccidModifyRecordsDO, carVin, oldIccid, newIccid);
        iccidModifyRecordsDO.setCreatedBy(LoginUtil.getLoginUserName());
        iccidModifyRecordsDO.setModifyStatus(ModifyStatusEnum.SUCCESS.getStatus());
        iccidModifyRecordsDOMapper.insert(iccidModifyRecordsDO);
    }

    private void buildBaseIccidModifyRecordsDO(IccidModifyRecordsDO iccidModifyRecordsDO, String carVin, String oldIccid, String newIccid) {
        iccidModifyRecordsDO.setModifyNo(snowflake.nextId());
        iccidModifyRecordsDO.setCarVin(CarVinUtil.carVinToUpperCase(carVin));
        iccidModifyRecordsDO.setModifyBeforeIccid(oldIccid);
        iccidModifyRecordsDO.setModifyAfterIccid(newIccid);
        iccidModifyRecordsDO.setDataSource(ModifyDataSourceEnum.SINGLE.getDataSource());
    }

    @Override
    public PageResult<IccidSingleModifyPageV0> getIccidSinglePageList(IccidSingleModifyPageDTO pageDTO) {
        log.info("查询ICCID单个修改 操作记录分页, pageDTO:{}", pageDTO);
        Page<IccidModifyRecordsDO> pageResult = queryIccidSinglePageList(pageDTO);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageResult<>();
        }
        List<IccidModifyRecordsDO> records = pageResult.getRecords();
        List<IccidSingleModifyPageV0> pageListV0List = buildIccidSinglePageV0List(records);

        return new PageResult<>(pageListV0List, pageResult.getTotal());
    }

    private Page<IccidModifyRecordsDO> queryIccidSinglePageList(IccidSingleModifyPageDTO pageDTO) {
        if (Objects.isNull(pageDTO)) {
            return new Page<>();
        }

        Page<IccidModifyRecordsDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<IccidModifyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyRecordsDO::getIsDeleted, false)
                .eq(IccidModifyRecordsDO::getDataSource, ModifyDataSourceEnum.SINGLE.getDataSource());
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(IccidModifyRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(IccidModifyRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(IccidModifyRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(IccidModifyRecordsDO::getId);
        }

        return iccidModifyRecordsDOMapper.selectPage(pageParam, queryWrapper);
    }

    private List<IccidSingleModifyPageV0> buildIccidSinglePageV0List(List<IccidModifyRecordsDO> records) {
        List<IccidSingleModifyPageV0> resp = new ArrayList<>();
        if (CollUtil.isEmpty(records)) {
            return resp;
        }
        for (IccidModifyRecordsDO iccidModifyRecordsDO : records) {
            resp.add(buildIccidSinglePageVO(iccidModifyRecordsDO));
        }
        return resp;
    }

    private IccidSingleModifyPageV0 buildIccidSinglePageVO(IccidModifyRecordsDO iccidModifyRecordsDO) {
        if (Objects.isNull(iccidModifyRecordsDO)) {
            return new IccidSingleModifyPageV0();
        }

        return IccidSingleModifyPageV0.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(iccidModifyRecordsDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(iccidModifyRecordsDO.getCreatedBy())
                .carVin(iccidModifyRecordsDO.getCarVin())
                .modifyAfterIccid(iccidModifyRecordsDO.getModifyAfterIccid())
                .modifyNo(String.valueOf(iccidModifyRecordsDO.getModifyNo()))
                .build();
    }
}
