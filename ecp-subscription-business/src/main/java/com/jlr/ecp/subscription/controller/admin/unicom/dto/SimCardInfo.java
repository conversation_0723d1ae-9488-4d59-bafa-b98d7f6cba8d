package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * sim卡信息
 * <AUTHOR>
 */
@Data
public class SimCardInfo implements Serializable {

    /**
     * Iccid
     */
    private String iccid;
    /**
     * MSISDN 号
     */
    private String msisdn;

    /**
     * 卡状态：
     * open：开机
     * stop：停机
     * cancel：销户
     */
    @JsonProperty("card_state")
    private String cardState;

    /**
     * 开卡时间
     */
    @JsonProperty("open_time")
    private String openTime;
    /**
     * 最近一次变更时间
     */
    @JsonProperty("last_chg_time")
    private String lastChgTime;
    /**
     * 实名制状态
     * 1：已实名
     * 0：未实名
     */
    @JsonProperty("realname_flag")
    private String realnameFlag;

    /**
     * VIN号 车架号
     */
    private String vin;
}