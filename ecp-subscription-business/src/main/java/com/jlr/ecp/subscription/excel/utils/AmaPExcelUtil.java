package com.jlr.ecp.subscription.excel.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jlr.ecp.subscription.excel.pojo.amap.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AmaPExcelUtil {

    private static final String EXCEL_FORMATTER = ".xlsx";

    private static final int BATCH_COUNT = 500;

    /**
     * 将车辆VIN信息分组后批量写入下载文件。
     *
     * @param carVinDownloadList 车辆VIN信息列表，包含了需要写入下载文件的所有车辆信息。
     * @param count              需要写入下载文件的车辆VIN信息的序列。
     * @return 返回批量写入操作产生的文件。这个文件包含了所有输入车辆信息的分组写入结果。
     */
    public static File writeDownloadCarVinByGroup(List<CarVinDownload> carVinDownloadList, int count) {
        List<List<CarVinDownload>> carVinListByGroup = getCarVinListByGroup(carVinDownloadList);
        return batchWriteDownloadCarVin(carVinListByGroup, count);
    }

    /**
     * 分组获取车辆VIN列表。
     *
     * @param carVinList 订单履行信息列表，包含需要获取VIN的车辆信息。
     * @return 二维字符串列表，每个子列表是对应批次的车辆VIN列表。
     */
     public static List<List<CarVinDownload>> getCarVinListByGroup(List<CarVinDownload> carVinList) {
        List<List<CarVinDownload>> carVinListResp = new ArrayList<>();
        for (int i = 0; i < carVinList.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, carVinList.size());
            List<CarVinDownload> groupList = carVinList.subList(i, endIdx);
            carVinListResp.add(groupList);
        }
        return carVinListResp;
    }

    /**
     * 批量写入写入AMAP需要手动激活的carVin到Excel文件。
     *
     * @param carVinList 电话号码错误详情数据列表，不应为空。
     *                          列表中的每个元素都是一个包含电话错误详情的PhoneErrorDetail对象。
     * @param count              需要写入下载文件的车辆VIN信息的序列。
     * @return File
     */
    public static File batchWriteDownloadCarVin(List<List<CarVinDownload>> carVinList, int count) {
        log.info("批量写入写入AMAP需要手动激活的carVin到Excel文件, carVinList:{}, count:{}", carVinList, count);
        if (CollectionUtils.isEmpty(carVinList)) {
            return null;
        }
        String fileName = "ECP Export_Inactive AMAP VIN List(" + count + ")";
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, CarVinDownload.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("AmaPCarVinDownload").build();
                for (List<CarVinDownload> data : carVinList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入AMAP没有激活的carVin，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("写入AMAP没有激活的carVin，数据异常:", e);
        }
        return null;
    }

    /**
     * 将手动激活的车辆VIN码分组并写入到文件中。
     *
     * @param carVinList 包含所有手动激活车辆VIN码的结果列表。
     * @return 返回写入文件后的文件对象。
     */
    public static File writeResultManualActivationByGroup(List<ManualActivationResultExcel> carVinList) {
        List<List<ManualActivationResultExcel>> carVinListByGroup = getResultManualActivationByGroup(carVinList);
        return batchWriteResultManualActivation(carVinListByGroup);
    }

    /**
     * 根据分组大小将手动激活结果Excel文件列表分组。
     *
     * @param resultExcels 手动激活结果Excel文件的列表。
     * @return 返回一个列表的列表，其中每个内部列表都是从原始列表中分组得到的。
     */
    public static List<List<ManualActivationResultExcel>> getResultManualActivationByGroup(List<ManualActivationResultExcel> resultExcels) {
        List<List<ManualActivationResultExcel>> carVinListResp = new ArrayList<>();
        for (int i = 0; i < resultExcels.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, resultExcels.size());
            List<ManualActivationResultExcel> groupList = resultExcels.subList(i, endIdx);
            carVinListResp.add(groupList);
        }
        return carVinListResp;
    }

    /**
     * 批量写入手动激活结果到Excel文件。
     *
     * @param carVinList 包含手动激活结果的列表，每个元素是一个Excel表格的数据列表。
     * @return 返回生成的Excel文件，如果输入列表为空，则返回null。
     */
    public static File batchWriteResultManualActivation(List<List<ManualActivationResultExcel>> carVinList) {
        if (CollectionUtils.isEmpty(carVinList)) {
            return null;
        }
        String fileName = "ECP Result_AMAP Activation Success List-" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, ManualActivationResultExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("ResultManualActivation").build();
                for (List<ManualActivationResultExcel> data : carVinList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("批量写入手动激活结果到Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("批量写入手动激活结果到Excel文件，数据异常:", e);
        }
        return null;
    }

    /**
     * 将手动激活的车辆VIN码分组并写入到文件中。
     *
     * @param carVinList 包含所有手动激活车辆VIN码的结果列表。
     * @return 返回写入文件后的文件对象。
     */
    public static File writeManualActivationExcelByGroup(List<AmaPManualActivationExcel> carVinList) {
        List<List<AmaPManualActivationExcel>> activationExcelByGroup = getManualActivationExcelByGroup(carVinList);
        return batchWriteManualActivationCarVin(activationExcelByGroup);
    }

    /**
     * 根据分组大小将手动激活Excel文件列表分组。
     *
     * @param activationExcels 手动激活Excel文件的列表。
     * @return 返回一个列表的列表，其中每个内部列表都是从原始列表中分组得到的。
     */
    public static List<List<AmaPManualActivationExcel>> getManualActivationExcelByGroup(List<AmaPManualActivationExcel> activationExcels) {
        List<List<AmaPManualActivationExcel>> carVinListResp = new ArrayList<>();
        for (int i = 0; i < activationExcels.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, activationExcels.size());
            List<AmaPManualActivationExcel> groupList = activationExcels.subList(i, endIdx);
            carVinListResp.add(groupList);
        }
        return carVinListResp;
    }

    /**
     * 批量写入手动激活的车辆VIN信息到Excel文件。
     *
     * @param activationExcelList 包含手动激活车辆信息的Excel列表，每个列表代表一个Excel文件中的数据。
     * @return 返回生成的Excel文件，如果输入列表为空，则返回null。
     */
    public static File batchWriteManualActivationCarVin(List<List<AmaPManualActivationExcel>> activationExcelList) {
        if (CollectionUtils.isEmpty(activationExcelList)) {
            return null;
        }
        String fileName = "AmaPManualActivationCarVin" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, AmaPManualActivationExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("ManualActivationCarVin").build();
                for (List<AmaPManualActivationExcel> data : activationExcelList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("批量写入手动激活的车辆VIN信息到Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("批量写入手动激活的车辆VIN信息到Excel文件，数据异常:", e);
        }
        return null;
    }

    /**
     * 批量写入AMAP续费源文件
     *
     * @param batchRenewalExcelList 包含一批亚马逊批量续订信息的列表
     * @return 返回处理后的文件，如果输入列表为空，则返回null
     */
    public static File amaPBatchRenewalWriteSourceFile(List<AmaPBatchRenewalExcel> batchRenewalExcelList) {
        if (CollectionUtils.isEmpty(batchRenewalExcelList)) {
            log.info("批量写入AMAP续费源文件为空");
            return null;
        }
        List<List<AmaPBatchRenewalExcel>> groupList = new ArrayList<>();
        for (int i = 0; i < batchRenewalExcelList.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, batchRenewalExcelList.size());
            List<AmaPBatchRenewalExcel> group = batchRenewalExcelList.subList(i, endIdx);
            groupList.add(group);
        }
        return amaPBatchWriteSourceFileByGroup(groupList);
    }

    /**
     * 根据分组的高德批量续费Excel数据列表，批量写入原始Excel文件
     *
     * @param batchRenewalExcelList 分组的高德批量续费Excel数据列表
     * @return 生成的临时Excel文件，如果输入列表为空或写入过程中发生异常，则返回null
     */
    public static File amaPBatchWriteSourceFileByGroup(List<List<AmaPBatchRenewalExcel>> batchRenewalExcelList) {
        if (CollectionUtils.isEmpty(batchRenewalExcelList)) {
            return null;
        }
        String fileName = "AmaPBatchRenewalExcel" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, AmaPBatchRenewalExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("AmaPBatchRenewalExcel").build();
                for (List<AmaPBatchRenewalExcel> data : batchRenewalExcelList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入高德批量续费原始Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("写入高德批量续费原始Excel文件，数据异常:", e);
        }
        return null;
    }

    /**
     * 批量写入AMAP续费结果文件
     *
     * @param resultExcels AMAP续费结果的Excel列表，包含了所有待写入的数据
     * @return 如果结果列表为空，则返回null；否则，返回写入结果文件的File对象
     */
    public static File amaPBatchRenewalWriteResultFile(List<AmaPBatchRenewalResultExcel> resultExcels) {
        if (CollectionUtils.isEmpty(resultExcels)) {
            log.info("批量写入AMAP续费结果文件为空");
            return null;
        }
        List<List<AmaPBatchRenewalResultExcel>> groupList = new ArrayList<>();
        for (int i = 0; i < resultExcels.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, resultExcels.size());
            List<AmaPBatchRenewalResultExcel> group = resultExcels.subList(i, endIdx);
            groupList.add(group);
        }
        return amaPBatchWriteResultFileByGroup(groupList);
    }

    /**
     * 按组批量写入AMAP续费结果文件
     *
     * @param resultExcels 一个包含多组AmaPBatchRenewalResultExcel对象的列表
     * @return 返回写入数据后的临时File对象如果数据为空或者写入过程中发生异常，则返回null
     */
    public static File amaPBatchWriteResultFileByGroup(List<List<AmaPBatchRenewalResultExcel>> resultExcels) {
        if (CollectionUtils.isEmpty(resultExcels)) {
            return null;
        }
        String fileName = "AmaPBatchRenewalResultExcel" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, AmaPBatchRenewalResultExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("AmaPBatchRenewalResultExcel").build();
                for (List<AmaPBatchRenewalResultExcel> data : resultExcels) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("按组批量写入AMAP续费结果文件，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.info("按组批量写入AMAP续费结果文件，数据异常:{}", e.getMessage());
        }
        return null;
    }
}
