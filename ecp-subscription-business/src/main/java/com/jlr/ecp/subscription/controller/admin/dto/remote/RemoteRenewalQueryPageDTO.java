package com.jlr.ecp.subscription.controller.admin.dto.remote;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Remote续费记录查询DTO")
public class RemoteRenewalQueryPageDTO extends PageParam {
    @Schema(description = "车辆编号")
    private String carVin;

    @Schema(description = "续费批次号")
    private List<String> batchNoList;

    @Schema(description = "续费服务类型, 1:远程服务")
    private Integer renewalServiceType;

    @Schema(description = "操作开始时间")
    private String operateStartTime;

    @Schema(description = "操作结束时间")
    private String operateEndTime;

    @Schema(description = "续费状态")
    private Integer renewalStatus;

    @Schema(description = "操作人员")
    private String operator;

    @Schema(description = "操作时间排序 asc:正序 desc:倒叙")
    private String operateTimeSort;
}
