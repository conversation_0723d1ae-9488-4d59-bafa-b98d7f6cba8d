package com.jlr.ecp.subscription.model.dto;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/** AppD根据vin查询信息响应
 */
@Data
public class AppDSubscriptionData {

    private Long jlrSubscriptionId;

    private String subscriptionId;

    private String subscriberUniqueId;

    private List<AppDAttribute> attributes;

    private List<AppDServiceElement> serviceElements;

    private LocalDateTime commencesDate;

    private LocalDateTime expiresDate;

    private LocalDateTime createdDate;

    private LocalDateTime provisionedDate;

    private LocalDateTime activationDate;

    private LocalDateTime deprovisionedDate;

    private LocalDateTime deactivationDate;
}
