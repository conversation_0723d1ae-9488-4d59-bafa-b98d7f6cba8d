package com.jlr.ecp.subscription.dal.mysql.subscribeservice;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireVo;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;


/**
 * t_subscription_service(SubscriptionServiceDO)表数据库访问层
 * <AUTHOR>
 */
@Mapper
public interface SubscriptionServiceMapper extends BaseMapperX<SubscriptionServiceDO> {

    @Select("SELECT * FROM t_subscription_service" +
            " WHERE is_deleted = false" +
            " AND service_type = 1" +
            " AND (" +
            "   (expiry_date >= #{startDate} AND expiry_date <= #{endDate})" +
            ")")
    List<SubscriptionServiceDO> queryExpireService(@Param("startDate") LocalDateTime startDate,
                                                    @Param("endDate") LocalDateTime endDate
                                                   );

    List<SubscriptionServiceDO> queryByCarVinAndServiceType(@Param("carVinAndServiceTypeList") List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    List<SubscriptionServiceDO> getLatestExpireDate(@Param("carVinSet") Set<String> carVinSet);

    List<SubscriptionServiceDO> getLatestUnicomExpireDate(@Param("carVinSet") Set<String> carVinSet);
    default SubscriptionServiceDO findICCIDByCarVin(String carVin) {
        return selectOne(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .eq(BaseDO::getIsDeleted,false)
                .eq(SubscriptionServiceDO::getCarVin,carVin)
                .isNotNull(SubscriptionServiceDO::getIccid)
                .orderByDesc(BaseDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));
    }


    default List<SubscriptionServiceDO> selectListByCarVin(String carVin){
        return selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .eq(SubscriptionServiceDO::getCarVin,carVin)
                .in(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getIsDeleted, false)) ;
    }

    /**
     * 根据车辆识别号（VIN）查询订阅服务数据
     *
     * @param carVin 车辆识别号，用于唯一标识一辆车
     * @return 如果找到匹配的订阅服务数据对象，则返回该对象；否则返回null
     */
    default List<SubscriptionServiceDO> queryServiceDOByCarVinAndPackage(String carVin, String packageName) {
        if (StringUtils.isBlank(carVin)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServicePackage, packageName)
                .eq(SubscriptionServiceDO::getIsDeleted, false));
    }

    List<ServiceExpireVo> queryCombinedExpireList(@Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime,
                                                  @Param("serviceType")Integer serviceType,
                                                  @Param("servicePackage")String servicePackage,
                                                  @Param("pageSize") Integer pageSize, @Param("offset") Integer offset);


    Integer queryCombinedExpireCount(@Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime,
                                  @Param("serviceType")Integer serviceType,
                                  @Param("servicePackage")String servicePackage);
}
