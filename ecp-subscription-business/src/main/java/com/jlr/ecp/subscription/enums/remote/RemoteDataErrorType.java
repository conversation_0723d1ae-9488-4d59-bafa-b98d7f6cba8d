package com.jlr.ecp.subscription.enums.remote;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *   remote服务数据错误类型枚举
 */
@AllArgsConstructor
@Getter
public enum RemoteDataErrorType {

    RAW_ERROR("RAW_ERROR", "原始格式有误"),

    DP_FAIL("DP_FAIL", "DP查询失败"),

    SAVE_FAIL("SAVE_FAIL", "保存失败"),

    OTHER_FAIL("OTHER_FAIL", "未知失败"),

    NOT_REMOTE("NOT_REMOTE", "非remote数据");

    private final String type;

    private final String desc;
}
