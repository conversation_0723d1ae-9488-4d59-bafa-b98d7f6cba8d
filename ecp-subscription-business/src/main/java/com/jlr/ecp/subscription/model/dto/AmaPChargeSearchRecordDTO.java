package com.jlr.ecp.subscription.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class AmaPChargeSearchRecordDTO {
    @JsonProperty("charge_record_no")
    private String chargeRecordNo;

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("product_no")
    private String productNo;

    @JsonProperty("vid")
    private String vid;

    @JsonProperty("amount")
    private Integer amount;

    @JsonProperty("unified_amount")
    private Integer unifiedAmount;

    @JsonProperty("charge_unit")
    private Integer chargeUnit;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("fail_reason")
    private String failReason;

    @JsonProperty("diu")
    private String diu;

    @JsonProperty("sid")
    private String sid;

    @JsonProperty("start_time")
    private String startTime;

    @JsonProperty("end_time")
    private String endTime;
}
