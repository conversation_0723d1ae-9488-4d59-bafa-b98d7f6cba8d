package com.jlr.ecp.subscription.service.icrorder;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesBrandMappingDataPageVO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingDataUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingQueryPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_series_brand_mapping_data(t_series_brand_mapping_data)】的数据库操作Service
* @createDate 2024-01-16 09:42:36
*/
public interface SeriesBrandMappingDataDOService extends IService<SeriesBrandMappingDataDO> {

    /***
     * 初始化缓存
     */
   void initLocalCache();

   /***
     * 分页查询
     * @param pageDTO 查询条件
     * @return 分页结果集
     */
    PageResult<SeriesBrandMappingDataPageVO> queryPageList(SeriesMappingQueryPageDTO pageDTO);

    /***
     * 编辑
     * @param updateDTO 编辑条件
     * @return 是否成功
     */
    Boolean editSeriesBrandMappingData(SeriesMappingDataUpdateDTO updateDTO);

    /***
     * 删除
     * @param id 删除条件
     * @return 是否成功
     */
    Boolean delete(Long id,Integer revision);

    /***
     * 创建
     * @param dpResultVO 创建条件
     */
    void createSeriesBrandMappingData(UserDPResultVO dpResultVO);


    Long getNoCompleteCount();


    Map<String, SeriesMappingVO> getSeriesMapping();

}
