package com.jlr.ecp.subscription.dal.mysql.consumer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerIncontrolDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * t_consumer_incontrol(t_consumer_incontrol)数据Mapper
 *
 * <AUTHOR>
 * @since 2023-12-20 14:42:17
 * @description 由 Mybatisplus Code Generator 创建
*/
@Mapper
public interface ConsumerIncontrolMapper extends MPJBaseMapper<ConsumerIncontrolDO> {


    default ConsumerIncontrolDO selectByConsumerCode(String consumerCode,String clientId) {
        return selectOne(new LambdaQueryWrapper<ConsumerIncontrolDO>()
                .eq(ConsumerIncontrolDO::getConsumerCode, consumerCode)
                .eq(ConsumerIncontrolDO::getBindChannel, clientId)
                .eq(ConsumerIncontrolDO::getBindStatus, Constants.BindStatus.DO_BIND)
        );
    }

    default ConsumerIncontrolDO selectByIncontrolId(String incontrolId,String clientId) {
        return selectOne(new LambdaQueryWrapper<ConsumerIncontrolDO>()
                .eq(ConsumerIncontrolDO::getIncontrolId, incontrolId)
                .eq(ConsumerIncontrolDO::getBindChannel, clientId)
                .eq(ConsumerIncontrolDO::getBindStatus, Constants.BindStatus.DO_BIND)
        );
    }


    default ConsumerIncontrolDO selectNewByIncontrolId(String incontrolId) {
        return selectOne(new LambdaQueryWrapper<ConsumerIncontrolDO>()
                .eq(ConsumerIncontrolDO::getIncontrolId, incontrolId)
                .eq(BaseDO::getIsDeleted,false)
                .orderByDesc(BaseDO::getCreatedTime)
                .last(Constants.LIMIT_ONE)
        );
    }

     int unBindICR(@Param("inControlID") String inControlID,@Param("clientId") String clientId);
}
