package com.jlr.ecp.subscription.enums.vin.expriy;

public enum ServiceTypeEnum {
    BOTH("Both"),
    REMOTE("Remote"),
    SUBSCRIPTION("Subscription");

    private final String value;

    ServiceTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ServiceTypeEnum fromValue(String value) {
        for (ServiceTypeEnum type : ServiceTypeEnum.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown service type: " + value);
    }
}
