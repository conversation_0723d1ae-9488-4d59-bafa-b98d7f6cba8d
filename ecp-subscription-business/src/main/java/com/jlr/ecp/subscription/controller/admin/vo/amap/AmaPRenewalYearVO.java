package com.jlr.ecp.subscription.controller.admin.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP单个续费的年限")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPRenewalYearVO {
    @Schema(description = "年限Code")
    private Integer code;

    @Schema(description = "年限描述")
    private String desc;
}
