package com.jlr.ecp.subscription.aop;

import com.jlr.ecp.subscription.annotation.ApiLimitation;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.Redisson;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class ApiLimitationAspect {
    @Resource
    private Redisson redisson;

    @Pointcut("@annotation(com.jlr.ecp.subscription.annotation.ApiLimitation)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        ApiLimitation apiLimitation = method.getAnnotation(ApiLimitation.class);
        //没有使用限流，继续执行被代理方法的原始逻辑
        if (Objects.isNull(apiLimitation)) {
            return point.proceed();
        }

        long limitCount = apiLimitation.limitCount();
        long time = apiLimitation.time();
        RateIntervalUnit timeUnit = apiLimitation.timeUnit();
        String tokenBucketName = apiLimitation.tokenBucketName();

        // 初始化限流器配置
        String limiterKey = Constants.API_LIMITATION_KEY + tokenBucketName;
        RRateLimiter rateLimiter = redisson.getRateLimiter(limiterKey);
        if (!rateLimiter.isExists()) {
            rateLimiter.trySetRate(RateType.OVERALL, limitCount, time, timeUnit);
        }

        // 尝试获取令牌
        boolean acquired = rateLimiter.tryAcquire(1);
        if (acquired) {
            //获取成功，继续执行被代理方法的原始逻辑
            return point.proceed();
        } else {
            throw new TooManyRequestException(tokenBucketName + "请求过多，稍后再试");
        }
    }
}
