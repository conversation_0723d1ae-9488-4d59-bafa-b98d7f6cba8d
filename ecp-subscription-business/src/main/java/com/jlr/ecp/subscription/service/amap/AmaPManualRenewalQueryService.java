package com.jlr.ecp.subscription.service.amap;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalStatusVO;

import java.util.List;

public interface AmaPManualRenewalQueryService {

    /**
     * 根据批次号查询AMAP的批次号
     *
     * @param batchNo 批次号字符串
     * @return 如果找到对应的批次记录，则返回批次号字符串；否则返回null
     */
    String queryAmaPRenewalBatchNo(String batchNo);


    /**
     * 查询AMP续订状态
     *
     * @return 返回一个AmaPRenewalStatusVO对象的列表，每个对象代表一个AMP续订状态
     */
    List<AmaPRenewalStatusVO> queryAmaPRenewalStatus();


    /**
     * 查询续签记录的分页列表
     *
     * @param queryPageDTO 查询参数对象，用于分页和筛选查询结果
     * @return 返回续签记录的分页结果对象
     */
    PageResult<AmaPRenewalQueryPageVO> queryRenewalRecordPageList(AmaPRenewalQueryPageDTO queryPageDTO);
}
