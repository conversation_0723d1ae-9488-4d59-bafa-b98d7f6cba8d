package com.jlr.ecp.subscription.enums.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** 业务类编号枚举
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum BusiCodeEnum {

    /**
     * 车辆编码
     */
    ECP_CAR("ECP:CAR:","t_subscription_service"),
    /**
     * 司机编码
     */
    // DRIVER("DRIVER:","T_DRIVER"),
    ;

    private final String bussPrefix;
    private final String tableName;

}
