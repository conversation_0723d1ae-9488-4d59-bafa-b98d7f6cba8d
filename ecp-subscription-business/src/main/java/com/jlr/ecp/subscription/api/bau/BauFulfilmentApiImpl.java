package com.jlr.ecp.subscription.api.bau;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.api.bau.dto.*;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.enums.fufil.AppDServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.oss.*;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.oss.*;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIPackageService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Validated
@Slf4j
public class BauFulfilmentApiImpl implements BauFulfilmentApi {

    @Resource
    private PIVIUnicomService unicomService;

    @Resource
    private PIVIAppDService appDService;

    @Resource
    private PIVIAmaPService amaPService;

    @Resource
    private SOTAService sotaService;

    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;

    @Resource
    private PIVIPackageService piviPackageService;

    @Resource
    private DmsOssOriginalDataRecordsService dmsOssOriginalDataRecordsService;

    @Resource
    private VehicleConfigGroupService vehicleConfigGroupService;

    @Resource
    private DmsOssOriginalDataService dmsOssOriginalDataService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private DmsOssFileRecordsService dmsOssFileRecordsService;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private VinAdditionalRecordService additionalRecordService;

    @Override
    public CommonResult<List<Long>> getPreCheckList(PreCheckListRequest request) {
        // 查询待处理的idList
        return CommonResult.success(dmsOssOriginalDataService.getPreCheckIdList(request));
    }

    /**
     * 校验vin是否可以落库
     * 1.调用SOTA查询iccid by vin
     * 2.调用CU查询iccid、subscription history by iccid
     * 3.调用APPD查询vin、jlrSubscriptionId、expire date by vin
     * 4.调用AMAP查询vin、expiry date by vin
     * 5.调用DP查询vin、model、model year、配置编码 by vin
     * 6.查询车型年款配置表by model&model year，过滤出PIVI vin
     * 7.1 如果vin在SOTA, APPD, CU, AMAP存在, 且expiry date/invoice date不为空, 生成初始化PIVI记录
     * 7.2 如果vin在SOTA, CU, AMAP存在, 且expiry date/invoice date不为空, 但在APPD不存在，判断该车型在特殊履约方式的车型配置编码表，生成初始化PIVI记录
     * 7.3 否则不生成初始化记录
     *
     * @param idList id列表
     * @return Void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CheckedVinResp> check(List<Long> idList) {
        CheckedVinResp resp = new CheckedVinResp();
        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();
        // 根据idList查询详细数据
        List<DmsOssOriginalDataDO> doList = dmsOssOriginalDataService.getListByIdList(idList);
        if (CollUtil.isEmpty(doList)) {
            resp.setHandleSuccessCount(successCount.get());
            resp.setHandleFailCount(failCount.get());
            return CommonResult.success(resp);
        }
        // 查询PIVI_PACKAGE看vin是否已经存在，如果存在不做后续处理
        Set<String> existVinSet = new HashSet<>();
        Set<String> vinSet = doList.stream().map(DmsOssOriginalDataDO::getCarVin).collect(Collectors.toSet());
        List<PIVIPackageDO> piviPackageDOS = piviPackageService.queryByVinList(new ArrayList<>(vinSet));
        if(CollUtil.isNotEmpty(piviPackageDOS)){
            existVinSet = piviPackageDOS.stream().map(PIVIPackageDO::getVin).collect(Collectors.toSet());
            log.warn("BAU check时, PIVIPackage表已存在VIN：{}", existVinSet);
        }

        List<DmsOssOriginalDataRecordsDO> dataRecordsDOList = new ArrayList<>();
        for (DmsOssOriginalDataDO originalDataDO : doList) {
            // 该vin是否在ecp已经存在
            boolean vinExist = existVinSet.contains(originalDataDO.getCarVin());
            // 同步执行校验
            try{
                executeCheckVin(originalDataDO, dataRecordsDOList, successCount, failCount, vinExist);
            }catch (Exception e){
                log.warn("执行bau vin校验异常, vin={}, {}", originalDataDO.getCarVin(), e.getMessage());
            }
        }
        // 更新和新增记录
        dmsOssOriginalDataRecordsService.insertBatch(dataRecordsDOList);
        dmsOssOriginalDataService.updateBatch(doList);
        resp.setHandleSuccessCount(successCount.get());
        resp.setHandleFailCount(failCount.get());
        return CommonResult.success(resp);
    }

    /**
     * 执行VIN检查流程
     * 本方法实现了对车辆VIN码的一系列检查，包括查询不同系统中的车辆信息，处理结果，并更新数据对象
     *
     * @param originalDataDO 原始数据对象，包含车辆基础信息及处理结果
     * @param dataRecordsDOList 记录数据对象列表，用于存储车辆检查记录
     * @param successCount 成功计数器，用于记录处理成功的车辆数
     * @param failCount 失败计数器，用于记录处理失败的车辆数
     */
    private void executeCheckVin(DmsOssOriginalDataDO originalDataDO, List<DmsOssOriginalDataRecordsDO> dataRecordsDOList,
                                 AtomicInteger successCount, AtomicInteger failCount, boolean vinExist) {
        String vin = originalDataDO.getCarVin();
        // 初始化DmsOssOriginalDataRecordsDO
        DmsOssOriginalDataRecordsDO recordsDO = initRecordsDO(originalDataDO);
        // 默认数据处理结果为处理成功, 只要有一个系统失败，则整体处理失败
        originalDataDO.setStatus(HandleStatusEnum.SUCCESS.getCode());
        // 默认VIN无需入库ECP
        originalDataDO.setVinMatchResult(VinMatchResultEnum.NOT_REQUIRED.getCode());
        // 该vin在ecp已经存在，不用执行后续逻辑
        if(vinExist){
            successCount.getAndIncrement();
            return;
        }
        boolean checkCU = false;
        // 1.调用SOTA查询iccid
        boolean checkSOTA = additionalRecordService.checkSOTA(originalDataDO, recordsDO);
        if (checkSOTA) {
            // 2.调用CU查询iccid
            checkCU = additionalRecordService.checkCU(originalDataDO, recordsDO);
        } else {
            recordsDO.setCuResult(String.format(RecordResultEnum.NO_CALL.getDesc(), "调用SOTA未查询到ICCID"));
            originalDataDO.setCuResult(CuResultEnum.VIN_NOT_FOUND.getCode());
        }
        // 3.调用APPD查询vin、jlrSubscriptionId、expire date by vin
        boolean checkAPPD = additionalRecordService.checkAPPD(originalDataDO, recordsDO);
        // 4.调用AMAP查询vin、expiry date by vin
        boolean checkAMAP = additionalRecordService.checkAMAP(originalDataDO, recordsDO);
        // 5.调用DP查询vin、model、model year、配置编码 by vin
        String configCode = additionalRecordService.checkDP(originalDataDO, recordsDO);
        // 6.查询车型年款配置表by model&model year，过滤出PIVI vin
        boolean checkDP = DpResultEnum.SUCCESS.getCode().equals(originalDataDO.getDpResult());
        boolean checkPIVI = PIVIResultEnum.SUCCESS.getCode().equals(originalDataDO.getPiviConfigResult());
        dataRecordsDOList.add(recordsDO);
        // 记录数据处理成功/失败数量
        if (HandleStatusEnum.SUCCESS.getCode().equals(originalDataDO.getStatus())) {
            successCount.getAndIncrement();
        } else {
            failCount.getAndIncrement();
        }
        originalDataDO.setUpdatedTime(LocalDateTime.now());
        // 未查询到DP数据或不是PIVI车机不用落库
        if (!checkDP || !checkPIVI) {
            log.warn("BAU check时车辆未查询到DP数据或不是PIVI车机, vin:{}, DP:{}, PIVI:{}", vin, checkDP, checkPIVI);
            return;
        }
        // 7.1 如果vin在SOTA, APPD, CU, AMAP存在, 且expiry date/invoice date不为空, 需要生成初始化PIVI记录
        if (checkSOTA && checkAPPD && checkCU && checkAMAP) {
            // 设置VIN待入库ECP
            originalDataDO.setVinMatchResult(VinMatchResultEnum.PENDING.getCode());
            // 7.2 如果vin在SOTA, CU, AMAP存在, 且expiry date/invoice date不为空, 但在APPD不存在，判断该车型在特殊履约方式的车型配置编码表，需要生成初始化PIVI记录
        } else if (checkSOTA && checkCU && checkAMAP) {
            // 判断该车型是否在特殊履约方式的车型配置编码表
            additionalRecordService.judgeSpecialGroup(originalDataDO, configCode, recordsDO);
        }
    }

    @Override
    public CommonResult<TransferFileVO> transferFile(String dateStr) {
        return CommonResult.success(dmsOssFileRecordsService.transferFile(dateStr));
    }

    @Override
    public CommonResult<HandleFileResultVO> handleFile(String dateStr) {
        return CommonResult.success(dmsOssFileRecordsService.handleFile(dateStr));
    }

    @Override
    public CommonResult<Map<Long, List<Long>>> getPreSyncIdList(PreCheckListRequest request) {
        // 查询待初始化车辆, 根据bauJobId分组
        Map<Long, List<Long>> bauJobIdToMap = dmsOssOriginalDataService.getPreSyncIdList(request);
        return CommonResult.success(bauJobIdToMap);
    }

    @Override
    public CommonResult<SyncResultResp> initialize(List<Long> idList) {
        log.info("初始化VIN, idList={}", idList);
        SyncResultResp resp = new SyncResultResp();
        if (CollUtil.isEmpty(idList)) {
            return CommonResult.success(resp);
        }
        AtomicInteger appDNotNeedCount = new AtomicInteger();
        AtomicInteger appDSuccessCount = new AtomicInteger();
        AtomicInteger appDFailCount = new AtomicInteger();
        AtomicInteger cuSuccessCount = new AtomicInteger();
        AtomicInteger cuFailCount = new AtomicInteger();
        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();
        // 查询原始表
        List<DmsOssOriginalDataDO> dataDOList = dmsOssOriginalDataService.getListByIdList(idList);
        log.info("初始化VIN原始表数量, size={}", dataDOList.size());
        if (CollUtil.isEmpty(dataDOList)) {
            return CommonResult.success(resp);
        }

        Set<String> vinSet = dataDOList.stream().map(DmsOssOriginalDataDO::getCarVin).collect(Collectors.toSet());
        // 查询记录表
        List<DmsOssOriginalDataRecordsDO> dataRecordsDOS = dmsOssOriginalDataRecordsService.queryByVinSet(vinSet);
        log.info("初始化VIN记录表数量, size={}", dataRecordsDOS.size());
        if (CollUtil.isEmpty(dataRecordsDOS)) {
            return CommonResult.success(resp);
        }

        Map<String, DmsOssOriginalDataRecordsDO> vinToRecordMap = dataRecordsDOS.stream().collect(Collectors.toMap(DmsOssOriginalDataRecordsDO::getCarVin, Function.identity(), (o, n) -> o));

        List<PIVIPackageDO> packageDOList = new ArrayList<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (DmsOssOriginalDataDO originalDataDO : dataDOList) {
            DmsOssOriginalDataRecordsDO recordsDO = vinToRecordMap.get(originalDataDO.getCarVin());
            // 记录为空不需要同步
            if (Objects.isNull(recordsDO)) {
                log.warn("初始化VIN记录为空不需要同步, vin={}", originalDataDO.getCarVin());
                continue;
            }
            // 初始化PIVIPackageDO
            PIVIPackageDO piviPackageDO = initPIVIPackageDO(originalDataDO);
            // 同步服务到期时间
            log.info("初始化VIN同步服务到期时间, vin={}", originalDataDO.getCarVin());
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                log.info("开始执行异步同步服务到期时间, vin={}", originalDataDO.getCarVin());
                syncServiceExpiryDate(originalDataDO, recordsDO);
                log.info("同步服务到期时间结束, vin={}", originalDataDO.getCarVin());
                // 统计同步结果
                resultStatistic(originalDataDO, appDNotNeedCount, recordsDO, appDSuccessCount, appDFailCount, cuSuccessCount, cuFailCount);
                if (InitializeStatusEnum.SUCCESS.getCode().equals(originalDataDO.getSyncStatus())) {
                    successCount.getAndIncrement();
                    originalDataDO.setVinMatchResult(VinMatchResultEnum.SUCCESS.getCode());
                    packageDOList.add(piviPackageDO);
                } else {
                    failCount.getAndIncrement();
                }
                log.info("统计同步结果结束, vin={}", originalDataDO.getCarVin());
            }, subscribeAsyncThreadPool).exceptionally(ex -> {
                log.info("异步同步服务到期时间异常", ex);
                return null;
            });
            futures.add(future);
        }
        // 等待所有异步操作完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // 更新同步结果
        BauFulfilmentApiImpl bauFulfilmentApi = applicationContext.getBean(getClass());
        bauFulfilmentApi.updateSyncResult(dataDOList, dataRecordsDOS, packageDOList);
        // 设置同步结果返回值
        resp.setAppDNotNeedCount(appDNotNeedCount.get());
        resp.setAppDSuccessCount(appDSuccessCount.get());
        resp.setAppDFailCount(appDFailCount.get());
        resp.setCuSuccessCount(cuSuccessCount.get());
        resp.setCuFailCount(cuFailCount.get());
        resp.setSuccessCount(successCount.get());
        resp.setFailCount(failCount.get());
        return CommonResult.success(resp);
    }

    @Override
    public CommonResult<Boolean> updateSyncResult(Set<Long> bauJobIdSet) {
        return dmsOssFileRecordsService.updateSyncResult(bauJobIdSet);
    }

    /**
     * 根据同步结果统计APPD和CU的成败情况
     *
     * @param dataDO    dataDO
     * @param appDNotNeedCount 无需履约APPD的计数器
     * @param recordsDO        包含APPD和CU同步结果的记录实体对象
     * @param appDSuccessCount APPD同步成功的计数器
     * @param appDFailCount    APPD同步失败的计数器
     * @param cuSuccessCount   CU同步成功的计数器
     * @param cuFailCount      CU同步失败的计数器
     */
    private static void resultStatistic(DmsOssOriginalDataDO dataDO, AtomicInteger appDNotNeedCount, DmsOssOriginalDataRecordsDO recordsDO, AtomicInteger appDSuccessCount, AtomicInteger appDFailCount, AtomicInteger cuSuccessCount, AtomicInteger cuFailCount) {
        if (Objects.isNull(dataDO.getJlrSubscriptionId())) {
            log.info("无需履约APPD, vin={}", dataDO.getCarVin());
            appDNotNeedCount.getAndIncrement();
        } else {
            if (YesOrNoStatusEnum.SUCCESS.getCode().equals(recordsDO.getAppdSyncResult())) {
                appDSuccessCount.getAndIncrement();
            } else {
                appDFailCount.getAndIncrement();
            }
        }
        if (YesOrNoStatusEnum.SUCCESS.getCode().equals(recordsDO.getCuSyncResult())) {
            cuSuccessCount.getAndIncrement();
        } else {
            cuFailCount.getAndIncrement();
        }
    }

    /**
     * 使用事务处理机制更新同步结果
     * 该方法通过接收两个列表参数，分别更新不同的数据表中的同步状态
     * 在事务的包裹下，确保数据的一致性和完整性，如果操作过程中发生异常，将会回滚所有数据库操作
     *
     * @param dataDOS    需要更新的原始数据列表
     * @param recordsDOS 需要更新的数据记录列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSyncResult(List<DmsOssOriginalDataDO> dataDOS, List<DmsOssOriginalDataRecordsDO> recordsDOS,
                                 List<PIVIPackageDO> packageDOList) {
        if (CollUtil.isNotEmpty(packageDOList)) {
            // 先检查vin是否存在,存在则remove
            List<PIVIPackageDO> piviPackageDOS = piviPackageService.queryByVinList(packageDOList.stream().map(PIVIPackageDO::getVin).collect(Collectors.toList()));
            if(CollUtil.isNotEmpty(piviPackageDOS)){
                Set<String> existVinSet = piviPackageDOS.stream().map(PIVIPackageDO::getVin).collect(Collectors.toSet());
                packageDOList.removeIf(p -> existVinSet.contains(p.getVin()));
                log.warn("BAU更新同步结果, PIVIPackage表已存在VIN：{}", existVinSet);
            }
            piviPackageService.insertBatch(packageDOList);
        }
        // 批量更新同步状态
        if (CollUtil.isNotEmpty(dataDOS)) {
            dmsOssOriginalDataService.updateBatch(dataDOS);
        }
        if (CollUtil.isNotEmpty(recordsDOS)) {
            dmsOssOriginalDataRecordsService.updateBatch(recordsDOS);
        }
    }

    /**
     * 初始化PIVIPackageDO对象
     * 该方法用于根据车辆识别号（VIN）创建一个PIVIPackageDO实例，用于后续的业务处理
     *
     * @param originalDataDO originalDataDO
     * @return 初始化后的PIVIPackageDO对象，包含预定义的服务包码、服务名称和车辆识别号
     */
    private static PIVIPackageDO initPIVIPackageDO(DmsOssOriginalDataDO originalDataDO) {
        LocalDateTime dmsDate = TimeFormatUtil.stringToLocalDate(originalDataDO.getDmsInvoiceDate());
        LocalDateTime expireDate = dmsDate.plusYears(3).withHour(23).withMinute(59).withSecond(59);
        return PIVIPackageDO.builder()
                .packageCode(ServicePackageEnum.ONLINE_PACK.getPackageName())
                .serviceName(AppDServiceNameEnum.ONLINE_MEDIA.getServiceName())
                .vin(originalDataDO.getCarVin())
                .dmsInvoiceDate(dmsDate)
                .expiryDate(expireDate)
                .expireDateUtc0(expireDate.minusHours(8))
                .amaPExpireDate(originalDataDO.getAmaPExpireDate())
                .jlrSubscriptionId(originalDataDO.getJlrSubscriptionId())
                .iccid(originalDataDO.getIccid())
                .tenantId(TenantContextHolder.getTenantId().intValue())
                .build();
    }

    /**
     * 根据源数据DO对象初始化记录DO对象
     *
     * @param originalDataDO 源数据DO对象，包含bauJobId和dataId
     * @return 初始化的记录DO对象，包含bauJobId和dataId
     */
    private static DmsOssOriginalDataRecordsDO initRecordsDO(DmsOssOriginalDataDO originalDataDO) {
        return DmsOssOriginalDataRecordsDO.builder()
                .bauJobId(originalDataDO.getBauJobId())
                .dataId(originalDataDO.getDataId())
                .carVin(originalDataDO.getCarVin())
                .build();
    }

    /**
     * 同步服务到期时间
     * 该方法通过调用APPD和联通服务来激活服务，并根据激活结果更新同步状态
     *
     * @return 返回一个CompletableFuture，包含处理结果的DmsOssOriginalDataDO对象
     */
    private void syncServiceExpiryDate(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO) {
        LocalDateTime dmsDate = TimeFormatUtil.stringToLocalDate(originalDataDO.getDmsInvoiceDate());
        // 设置ecp到期时间
        LocalDateTime expireDate = dmsDate.plusYears(3).withHour(23).withMinute(59).withSecond(59);
        FufilmentMessage message = new FufilmentMessage();
        message.setVin(originalDataDO.getCarVin());
        message.setServiceBeginDate(expireDate.minusDays(1));
        message.setServiceEndDate(expireDate);
        message.setVcsOrderCode(ecpIdUtil.nextIdStr());
        message.setTenantId(TenantContextHolder.getTenantId());
        Integer syncStatus = originalDataDO.getSyncStatus();
        boolean appdSuccess = true;
        boolean cuSuccess = true;
        // 同步APPD和联通服务
        // 状态为1：同步成功，3：CU同步失败时不需要同步APPD
        if (InitializeStatusEnum.PENDING.getCode().equals(syncStatus) || InitializeStatusEnum.APPD_FAIL.getCode().equals(syncStatus)
                || InitializeStatusEnum.ALL_FAIL.getCode().equals(syncStatus)) {
            log.info("BAU同步APPD到期时间, message:{}, jlrSubscriptionId:{}", message, originalDataDO.getJlrSubscriptionId());
            appdSuccess = appDService.callAppDService(message, null, originalDataDO.getJlrSubscriptionId());
            log.info("同步APPD到期时间结束");
        }
        // 联通
        // 状态为1：同步成功，2：APPD同步失败时不需要同步CU
        if (InitializeStatusEnum.PENDING.getCode().equals(syncStatus) || InitializeStatusEnum.CU_FAIL.getCode().equals(syncStatus)
                || InitializeStatusEnum.ALL_FAIL.getCode().equals(syncStatus)) {
            log.info("BAU同步联通到期时间, message:{}, iccid:{}", message, originalDataDO.getIccid());
            CommonResult<UnicomRespVO> result = unicomService.unicomRenewalByIccid(message, originalDataDO.getIccid());
            cuSuccess = result.isSuccess() && UnicomResultEnum.SUCCESS.getDesc().equals(result.getData().getResponseDesc());
            log.info("同步联通到期时间结束");
        }
        recordsDO.setAppdSyncResult(appdSuccess ? YesOrNoStatusEnum.SUCCESS.getCode() : YesOrNoStatusEnum.FAIL.getCode());
        recordsDO.setCuSyncResult(cuSuccess ? YesOrNoStatusEnum.SUCCESS.getCode() : YesOrNoStatusEnum.FAIL.getCode());
        recordsDO.setUpdatedTime(LocalDateTime.now());
        // 两个系统都成功更新状态为同步成功，否则为失败
        originalDataDO.setUpdatedTime(LocalDateTime.now());
        originalDataDO.setSyncStatus(InitializeStatusEnum.getCodeByResult(appdSuccess, cuSuccess));
    }
}
