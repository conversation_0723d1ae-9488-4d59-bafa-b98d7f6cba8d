package com.jlr.ecp.subscription.file.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class S3FileUtil {

    /**
     * 获取上传文件路径的键
     *
     * @param uploadExcelPath 上传文件的完整路径
     * @return 提取出来的路径键，如果输入的路径无效或不合法，则返回空字符串
     */
    public static String getUploadPathKey(String uploadExcelPath) {
        log.info("获取上传文件路径的键, uploadExcelPath:{}", uploadExcelPath);
        if (StringUtils.isBlank(uploadExcelPath)) {
            return "";
        }
        // 找到 "file/" 子串在 URL 中的起始位置
        int startIndex = uploadExcelPath.indexOf("file/") + "file/".length();
        if (startIndex < 0) {
            return "";
        }
        // 从找到的起始位置截取至字符串末尾
        return uploadExcelPath.substring(startIndex);
    }
}
