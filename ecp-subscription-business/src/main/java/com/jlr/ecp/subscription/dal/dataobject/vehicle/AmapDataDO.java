package com.jlr.ecp.subscription.dal.dataobject.vehicle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * t_amap_initial_data表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_amap_initial_data")
public class AmapDataDO {


    /**
    * 主键
    */
    @TableId
    private Long id;

    /**
    * 车辆VIN码;车辆VIN码
    */
    @TableField(value = "car_vin")
    private String carVin;


    /**
     * 过期时间
     */
    @TableField(value = "expiry_date")
    private String expiryDate;

    @TableField(value = "error_msg")
    private String errorMsg;


    /**
     * 租户号
     */
    private Integer tenantId;
}

