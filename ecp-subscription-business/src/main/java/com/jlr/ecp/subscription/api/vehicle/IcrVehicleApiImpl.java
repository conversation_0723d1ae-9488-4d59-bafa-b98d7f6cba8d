package com.jlr.ecp.subscription.api.vehicle;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.service.icrorder.IncontrolVehicleDOService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * icr vehicle 信息
 *
 * <AUTHOR>
 */
@RestController
@Validated
public class IcrVehicleApiImpl implements IcrVehicleApi {
    @Resource
    private IncontrolVehicleDOService incontrolVehicleDOService;

    @Override
    public CommonResult<IcrVehicleRespVO> view(String carVin) {
        IcrVehicleRespVO vo = incontrolVehicleDOService.getOneByCarVin(carVin);
        return CommonResult.success(vo);
    }

    @Override
    public CommonResult<List<IcrVehicleListRespVO>> vinInfo(List<String> carVinList) {
        List<IcrVehicleListRespVO> list =incontrolVehicleDOService.getCarVinInfo(carVinList);
        return CommonResult.success(list);
    }
}
