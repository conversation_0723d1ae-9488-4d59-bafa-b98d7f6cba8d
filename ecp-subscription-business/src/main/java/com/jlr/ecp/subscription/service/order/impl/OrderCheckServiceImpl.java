package com.jlr.ecp.subscription.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.OrderAppApi;
import com.jlr.ecp.order.api.order.dto.OrderInTransitReqDTO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class OrderCheckServiceImpl implements OrderCheckService {

    @Resource
    private OrderAppApi orderAppApi;

    @Override
    public CommonResult<String> checkOrderInTransit(String vin, ServiceTypeEnum serviceTypeEnum) {
        CommonResult<Boolean> checkOrderInTransit = null;
        try {
            checkOrderInTransit = orderAppApi.checkOrderInTransit(vin, serviceTypeEnum.getCode());
        } catch (Exception e) {
            log.warn("调用orderAppApi.checkOrderInTransit校验是否存在在途订单异常,{}", e.getMessage());
        }
        if (checkOrderInTransit == null || checkOrderInTransit.getData() == null) {
            return CommonResult.error(ErrorCodeConstants.CALL_ORDER_SERVICE_ERROR);
        }
        if(Boolean.TRUE.equals(checkOrderInTransit.getData())){
            log.info("vin:{}, serviceType:{}存在在途订单", vin, serviceTypeEnum.getDescription());
            return CommonResult.error(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR);
        }
        return CommonResult.success("");
    }

    @Override
    public CommonResult<List<String>> checkOrderInTransitByVinList(List<String> vinList, ServiceTypeEnum serviceTypeEnum) {
        List<String> inTransitVinList = Collections.emptyList();
        if(CollUtil.isEmpty(vinList)){
            return CommonResult.success(inTransitVinList);
        }
        OrderInTransitReqDTO reqDTO = new OrderInTransitReqDTO();
        reqDTO.setCarVinList(vinList);
        reqDTO.setServiceType(serviceTypeEnum.getCode());
        CommonResult<List<String>> checkOrderInTransit = null;
        try {
            checkOrderInTransit = orderAppApi.checkOrderInTransitByVinList(reqDTO);
        } catch (Exception e) {
            log.warn("调用orderAppApi.checkOrderInTransitByVinList校验是否存在在途订单异常,{}", e.getMessage());
        }
        if (checkOrderInTransit == null || checkOrderInTransit.getData() == null) {
            return CommonResult.error(ErrorCodeConstants.CALL_ORDER_SERVICE_ERROR);
        }
        return CommonResult.success(checkOrderInTransit.getData());
    }
}
