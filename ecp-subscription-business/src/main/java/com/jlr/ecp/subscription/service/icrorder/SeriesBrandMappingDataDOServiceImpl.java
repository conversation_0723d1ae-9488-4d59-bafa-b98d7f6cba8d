package com.jlr.ecp.subscription.service.icrorder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesBrandMappingDataPageVO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingDataUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingQueryPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.jlr.ecp.subscription.dal.mysql.icroder.SeriesBrandMappingDataDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
* <AUTHOR>
* description 针对表【t_series_brand_mapping_data(t_series_brand_mapping_data)】的数据库操作Service实现
* createDate 2024-01-16 09:42:36
*/
@Service
@Slf4j
public class SeriesBrandMappingDataDOServiceImpl extends ServiceImpl<SeriesBrandMappingDataDOMapper, SeriesBrandMappingDataDO>
    implements SeriesBrandMappingDataDOService{

    @Resource
    RedisService redisService;

    @Resource
    private SeriesBrandMappingDataDOMapper seriesBrandMappingDataDOMapper;

    private volatile Map<String, SeriesBrandMappingDataDO> allDataMap = new ConcurrentHashMap<>();

    @PostConstruct
    @Override
    public void initLocalCache() {
        List<SeriesMappingVO> allMappings = getAllMappings();
        if (CollUtil.isNotEmpty(allMappings)) {
            Map<String,String> map = Maps.newHashMap();
            for (SeriesMappingVO seriesMappingVO : allMappings) {
                //判断seriesMappingVO的seriesName和brandNameView是否为null如果为null就不put进map
                if (Objects.isNull(seriesMappingVO.getSeriesName()) || Objects.isNull(seriesMappingVO.getBrandNameView())){
                    continue;
                }
                map.put(seriesMappingVO.getSeriesCode(), JSON.toJSONString(seriesMappingVO));
            }
            //先清除redis再set
            redisService.deleteObject(Constants.REDIS_KEY.SERIES_CACHE_KEY);
            redisService.setCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY, map);
        }
    }

    @Override
    public PageResult<SeriesBrandMappingDataPageVO> queryPageList(SeriesMappingQueryPageDTO pageDTO) {
        log.info("查询车型展示名称配置分页列表, pageDTO:{}", pageDTO);
        Page<SeriesBrandMappingDataDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<SeriesBrandMappingDataDO> queryWrapper = buildWrapper(pageDTO);
        Page<SeriesBrandMappingDataDO> seriesBrandMappingDataDOPage =seriesBrandMappingDataDOMapper.selectPage(pageParam, queryWrapper);
        if (ObjectUtils.isEmpty(seriesBrandMappingDataDOPage) || CollUtil.isEmpty(seriesBrandMappingDataDOPage.getRecords())) {
            log.info("查询车型展示名称配置分页列表结果为空, pageDTO:{}", pageDTO);
            return new PageResult<>();
        }
        List<SeriesBrandMappingDataPageVO> pageVOList = seriesBrandMappingDataDOPage.getRecords().stream()
                .map(seriesBrandMappingData->{
                    SeriesBrandMappingDataPageVO seriesBrandMappingDataPageVO = new SeriesBrandMappingDataPageVO();
                    BeanUtil.copyProperties(seriesBrandMappingData, seriesBrandMappingDataPageVO);
                    return seriesBrandMappingDataPageVO;
                }).collect(Collectors.toList());

        return new PageResult<>(pageVOList, seriesBrandMappingDataDOPage.getTotal());
    }

    @Override
    public Boolean editSeriesBrandMappingData(SeriesMappingDataUpdateDTO updateDTO) {
        SeriesBrandMappingDataDO seriesBrandMappingDataDO = seriesBrandMappingDataDOMapper.selectById(updateDTO.getId());
        if (Objects.isNull(seriesBrandMappingDataDO)){
            log.info("编辑的数据不存在");
            return false;
        }

        //乐观锁校验
        if (!seriesBrandMappingDataDO.getRevision().equals(updateDTO.getRevision())){
            throw exception(ErrorCodeConstants.SERIES_REVERSION_ERROR);
        }
        seriesBrandMappingDataDO.setSeriesName(updateDTO.getSeriesName());
        seriesBrandMappingDataDO.setBrandNameView(updateDTO.getBrandNameView());
        seriesBrandMappingDataDO.setUpdatedTime(LocalDateTime.now());
        boolean success = updateById(seriesBrandMappingDataDO);
        if(success){
            //同步redis缓存
            initLocalCache();
        }
        return success;
    }

    @Override
    public Boolean delete(Long id,Integer revision) {
        SeriesBrandMappingDataDO seriesBrandMappingDataDO = seriesBrandMappingDataDOMapper.selectById(id);
        if (Objects.isNull(seriesBrandMappingDataDO)){
            log.info("需要删除的数据不存在,id:{}",id);
            return false;
        }
        //乐观锁校验
        if (!seriesBrandMappingDataDO.getRevision().equals(revision)){
            throw exception(ErrorCodeConstants.SERIES_REVERSION_ERROR);
        }
        seriesBrandMappingDataDO.setSeriesName(null);
        seriesBrandMappingDataDO.setBrandNameView(null);
        seriesBrandMappingDataDO.setUpdatedTime(LocalDateTime.now());

        LambdaUpdateWrapper<SeriesBrandMappingDataDO> wrapper = new LambdaUpdateWrapper<>();

        wrapper.eq(SeriesBrandMappingDataDO::getId,seriesBrandMappingDataDO.getId());
        wrapper.set(SeriesBrandMappingDataDO::getBrandNameView,seriesBrandMappingDataDO.getBrandNameView());
        wrapper.set(SeriesBrandMappingDataDO::getSeriesName,seriesBrandMappingDataDO.getSeriesName());
        wrapper.set(SeriesBrandMappingDataDO::getUpdatedTime,seriesBrandMappingDataDO.getUpdatedTime());

        int i = seriesBrandMappingDataDOMapper.update(seriesBrandMappingDataDO,wrapper);
        //同步redis缓存
        if(i>0){
            initLocalCache();
        }
        return i>0;
    }

    @Override
    public void createSeriesBrandMappingData(UserDPResultVO dpResultVO) {
        //dp数据为空或者code为空就直接不处理
        if(Objects.isNull(dpResultVO) ||StringUtils.isBlank(dpResultVO.getSeriesCode())){
            return;
        }
        SeriesBrandMappingDataDO seriesBrandMappingDataDO = allDataMap.get(dpResultVO.getSeriesCode());
        //缓存表seriesBrandMappingDataDO不为空,并且seriesName不相等 更新DP的数据
        if (Objects.nonNull(seriesBrandMappingDataDO) && !Objects.equals(seriesBrandMappingDataDO.getSeriesName(), dpResultVO.getSeriesName())) {
            seriesBrandMappingDataDO.setDpSeriesName(dpResultVO.getSeriesName());
            seriesBrandMappingDataDO.setUpdatedTime(LocalDateTime.now());
            seriesBrandMappingDataDOMapper.updateById(seriesBrandMappingDataDO);
        }
        //缓存表seriesBrandMappingDataDO为空 并且是PIVI车机 新增一条
        if (Objects.isNull(seriesBrandMappingDataDO) && Objects.equals(dpResultVO.getCarSystemModel(), CarSystemModelEnum.PIVI.getCode())) {
            seriesBrandMappingDataDO = new SeriesBrandMappingDataDO();
            seriesBrandMappingDataDO.setSeriesCode(dpResultVO.getSeriesCode());
            seriesBrandMappingDataDO.setDpSeriesName(dpResultVO.getSeriesName());
            int insert = seriesBrandMappingDataDOMapper.insert(seriesBrandMappingDataDO);
            if(insert>0){
                initLocalCache();
            }
        }




    }

    @Override
    public Long getNoCompleteCount() {
        Long count = seriesBrandMappingDataDOMapper.selectCount(
                new LambdaQueryWrapperX<SeriesBrandMappingDataDO>()
                        .isNull(SeriesBrandMappingDataDO::getSeriesName)
                        .or()
                        .isNull(SeriesBrandMappingDataDO::getSeriesCode)
                        .eq(BaseDO::getIsDeleted,false));
        return count;
    }

    private LambdaQueryWrapperX<SeriesBrandMappingDataDO> buildWrapper(SeriesMappingQueryPageDTO pageDTO) {
        LambdaQueryWrapperX<SeriesBrandMappingDataDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(SeriesBrandMappingDataDO::getSeriesCode,pageDTO.getSeriesCode())
                .likeIfPresent(SeriesBrandMappingDataDO::getDpSeriesName,pageDTO.getDpSeriesName())
                .eq(BaseDO::getIsDeleted,false)
                .orderByAsc(SeriesBrandMappingDataDO::getSeriesName)
                .orderByDesc(SeriesBrandMappingDataDO::getUpdatedTime);
        return wrapper;
    }

    private List<SeriesMappingVO> getAllMappings() {
        List<SeriesBrandMappingDataDO> allDataList = seriesBrandMappingDataDOMapper.selectList(
                new LambdaQueryWrapperX<SeriesBrandMappingDataDO>()
                        .eq(BaseDO::getIsDeleted,false));
        //查询所有的映射数据组装并且返回
        if (CollUtil.isNotEmpty(allDataList)) {
            //把allDataList 转成Map<String,SeriesBrandMappingDataDO> SeriesCode为key 维护在内存里面
            allDataMap = allDataList.stream().collect(Collectors.toMap(SeriesBrandMappingDataDO::getSeriesCode, Function.identity()));

            return allDataList.stream().map(seriesBrandMappingDataDO -> {
                SeriesMappingVO seriesMappingVO = new SeriesMappingVO();
                seriesMappingVO.setSeriesCode(seriesBrandMappingDataDO.getSeriesCode());
                seriesMappingVO.setSeriesName(seriesBrandMappingDataDO.getSeriesName());
                seriesMappingVO.setBrandNameView(seriesBrandMappingDataDO.getBrandNameView());
                return seriesMappingVO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Map<String, SeriesMappingVO> getSeriesMapping(){
        //一次性拉去redis数据
        Map<String, String> map = redisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY);
        Map<String, SeriesMappingVO> seriesMapping = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            seriesMapping.put(entry.getKey(), JSON.parseObject(entry.getValue(), SeriesMappingVO.class));
        }

        log.info("过滤掉列表中null元素，过滤掉key为null。最终seriesMapping:{}", seriesMapping);
        return seriesMapping;
    }

}




