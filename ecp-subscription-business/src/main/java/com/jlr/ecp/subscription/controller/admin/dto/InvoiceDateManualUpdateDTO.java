package com.jlr.ecp.subscription.controller.admin.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "PIVI手动编辑发票时间")
public class InvoiceDateManualUpdateDTO {

    @Schema(description = "车架号")
    @NotBlank(message = "车架号不能为空")
    private String carVin;

    @Schema(description = "修改发票时间")
    @NotBlank(message = "修改发票时间不能为空")
    private String modifyInvoiceDate;
}
