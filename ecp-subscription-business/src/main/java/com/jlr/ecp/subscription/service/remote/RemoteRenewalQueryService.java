package com.jlr.ecp.subscription.service.remote;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteRenewalQueryPageVO;

import java.util.List;

public interface RemoteRenewalQueryService {
    /**
     * 查询Remote续期批次的批号
     *
     * @param batchNo 批次号，字符串类型
     * @return 如果找到对应的批次记录，返回该记录的批号；否则返回空字符串
     */
    String queryRemoteRenewalBatchNo(String batchNo);

    /**
     * 查询Remote续订状态
     *
     */
    List<RemoteEnumQueryVO> queryRemoteRenewalStatus();

    /**
     * 查询Remote续费服务类型
     *
     */
    List<RemoteEnumQueryVO> queryRemoteRenewalService();

    /**
     * 查询Remote续期的分页列表
     *
     * @param queryPageDTO 包含查询条件的DTO对象
     * @return 返回分页结果对象，包含数据列表和总记录数
     */
    PageResult<RemoteRenewalQueryPageVO> queryRemoteRenewalPageList(RemoteRenewalQueryPageDTO queryPageDTO);
}
