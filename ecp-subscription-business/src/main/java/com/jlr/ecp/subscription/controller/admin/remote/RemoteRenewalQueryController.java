package com.jlr.ecp.subscription.controller.admin.remote;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteRenewalQueryPageVO;
import com.jlr.ecp.subscription.service.remote.RemoteRenewalQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "批量查询-远程车控续费结果")
@RequestMapping("/remote/renewal/query")
@Validated
public class RemoteRenewalQueryController {
    @Resource
    private RemoteRenewalQueryService remoteRenewalQueryService;
    @GetMapping("/queryBatchNo")
    @Operation(summary = "查询续费编号")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-renewal-record:forms')")
    public CommonResult<String> queryRemoteRenewalBatchNo(@RequestParam("batchNo") String batchNo) {
        String resp = remoteRenewalQueryService.queryRemoteRenewalBatchNo(batchNo);
        return CommonResult.success(resp);
    }

    @GetMapping("/queryRenewalService")
    @Operation(summary = "查询续费服务")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-renewal-record:forms')")
    public CommonResult<List<RemoteEnumQueryVO>> queryRenewalService() {
        List<RemoteEnumQueryVO> resp = remoteRenewalQueryService.queryRemoteRenewalService();
        return CommonResult.success(resp);
    }

    @GetMapping("/queryRenewalStatus")
    @Operation(summary = "查询续费状态")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-renewal-record:forms')")
    public CommonResult<List<RemoteEnumQueryVO>> queryRenewalStatus() {
        List<RemoteEnumQueryVO> resp = remoteRenewalQueryService.queryRemoteRenewalStatus();
        return CommonResult.success(resp);
    }

    @PostMapping("/queryRenewalRecord/pageList")
    @Operation(summary = "分页查询续费记录")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-renewal-record:forms')")
    public CommonResult<PageResult<RemoteRenewalQueryPageVO>> query(@RequestBody RemoteRenewalQueryPageDTO queryPageDTO) {
        PageResult<RemoteRenewalQueryPageVO> resp = remoteRenewalQueryService.queryRemoteRenewalPageList(queryPageDTO);
        return CommonResult.success(resp);
    }
}
