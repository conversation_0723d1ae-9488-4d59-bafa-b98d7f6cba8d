package com.jlr.ecp.subscription.dal.mysql.iccid;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【t_iccid_modify_records(t_iccid_modify_records)】的数据库操作Mapper
 * @createDate 2024-11-19 19:44:40
 * @Entity generator.domain.IccidModifyRecordsDO
 */
@Mapper
public interface IccidModifyRecordsDOMapper extends BaseMapperX<IccidModifyRecordsDO> {

}




