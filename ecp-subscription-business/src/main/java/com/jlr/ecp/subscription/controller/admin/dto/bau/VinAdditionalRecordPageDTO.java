package com.jlr.ecp.subscription.controller.admin.dto.bau;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "VIN补录记录查询DTO")
public class VinAdditionalRecordPageDTO extends PageParam {

    @Schema(description = "操作时间排序, 正序:asc, 倒叙:desc")
    @NotEmpty
    private String operateTimeSort;
}
