package com.jlr.ecp.subscription.dal.dataobject.oss;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_dms_oss_file_records表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_dms_oss_file_records")
public class DmsOssFileRecordsDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;
    /**
    * 主键
    */
    @TableId
    private Long id;

    /**
     * 任务ID;雪花算法ID
     */
    @TableField(value = "bau_job_id")
    private Long bauJobId;

    /**
     * job日期;job日期YYYY-MM-DD
     */
    @TableField(value = "job_date")
    private LocalDateTime jobDate;

    /**
     * JOB指定日期;JOB指定日期
     */
    @TableField(value = "job_param")
    private String jobParam;

    /**
     * DMS OSS文件地址ARRAY JSON;DMS OSS文件地址
     */
    @TableField(value = "dms_oss_file")
    private String dmsOssFile;

    /**
     * ECP S3文件地址ARRAY JSON;ECP S3文件地址
     */
    @TableField(value = "ecp_s3_file")
    private String ecpS3File;

    /**
     * VIN总数量;VIN总数量
     */
    @TableField(value = "total_vin_num")
    private Integer totalVinNum;

    /**
     * 处理成功VIN数量;处理成功VIN数量
     */
    @TableField(value = "success_vin_num")
    private Integer successVinNum;

    /**
     * 处理失败VIN数量;处理失败VIN数量
     */
    @TableField(value = "failed_vin_num")
    private Integer failedVinNum;
}

