package com.jlr.ecp.subscription.service.unicom;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomTodoOrderDO;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomTodoOrderMapper;
import com.jlr.ecp.subscription.enums.unicom.UnicomTodoStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnicomTodoOrderServiceImpl implements UnicomTodoOrderService {

    @Resource
    private UnicomTodoOrderMapper unicomTodoOrderMapper;

    @Override
    public CommonResult<List<UnicomTodoOrderVO>> findUnicomTodoOrder(Integer total) {
        LocalDateTime fiveMinBefore = LocalDateTime.now().minusMinutes(5);
        List<UnicomTodoOrderDO> unicomTodoOrderList = unicomTodoOrderMapper.selectList(new LambdaQueryWrapperX<UnicomTodoOrderDO>()
                .in(UnicomTodoOrderDO::getStatus, Arrays.asList(UnicomTodoStatusEnum.FAIL.getCode(), UnicomTodoStatusEnum.WAITING.getCode()))
                .le(UnicomTodoOrderDO::getRequestCount, 3)
                .le(UnicomTodoOrderDO::getCreatedTime,fiveMinBefore)
                .last("limit " + total));
        List<UnicomTodoOrderVO> list = new ArrayList<>();
        if(CollUtil.isNotEmpty(unicomTodoOrderList)){
            list = unicomTodoOrderList.stream().map(unicomTodoOrderDO -> {
                UnicomTodoOrderVO unicomTodoOrderVO = new UnicomTodoOrderVO();
                BeanUtils.copyProperties(unicomTodoOrderDO,unicomTodoOrderVO);
                return unicomTodoOrderVO;
            }).collect(Collectors.toList());
        }
        return CommonResult.success(list);
    }


}