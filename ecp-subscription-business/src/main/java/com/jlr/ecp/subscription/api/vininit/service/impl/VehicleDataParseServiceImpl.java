package com.jlr.ecp.subscription.api.vininit.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.subscription.api.vininit.dto.parse.*;
import com.jlr.ecp.subscription.api.vininit.service.VehicleDataParseService;
import com.jlr.ecp.subscription.api.remoteservice.RemoteOriginalDataParseBO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 车辆数据解析服务实现
 */
@Service
@Slf4j
public class VehicleDataParseServiceImpl implements VehicleDataParseService {
    
    @Override
    public ParseResult parseRawData(List<RemoteOriginalDataDO> records) {
        if (CollUtil.isEmpty(records)) {
            log.info("解析原始数据为空");
            return new ParseResult(Collections.emptyList(), Collections.emptyList());
        }
        log.info("开始解析原始数据，记录数量: {}", records.size());
        List<RemoteOriginalDataParseBO> parsedDataList = Collections.synchronizedList(new ArrayList<>());
        List<VinInitParseErrorDTO> parseErrorDTOList = Collections.synchronizedList(new ArrayList<>());
        
        // 并行处理提高性能
        records.parallelStream().forEach(dataDO -> {
            try {
                parseSingleRecord(dataDO, parsedDataList, parseErrorDTOList);
            } catch (Exception e) {
                log.info("解析单条记录异常, id: {}", dataDO.getId(), e);
                parseErrorDTOList.add(new VinInitParseErrorDTO(dataDO.getId(), e.getMessage()));
            }
        });
        
        log.info("原始数据解析完成，成功解析: {} 条，失败: {} 条", parsedDataList.size(), parsedDataList.size());
        return new ParseResult(parsedDataList, parseErrorDTOList);
    }
    
    /**
     * 解析单条记录
     *
     * @param dataDO         原始数据
     * @param parsedDataList 解析后数据列表
     * @param parseErrorDTOList   错误数据ID集合
     */
    private void parseSingleRecord(RemoteOriginalDataDO dataDO,
                                  List<RemoteOriginalDataParseBO> parsedDataList,
                                   List<VinInitParseErrorDTO> parseErrorDTOList) {
        Long id = dataDO.getId();
        JSONObject rawJson = dataDO.getRawJson();
        if (Objects.isNull(rawJson)) {
            log.info("原始JSON数据为空, id: {}", id);
            parseErrorDTOList.add(new VinInitParseErrorDTO(id, "原始JSON数据为空"));
            return;
        }
        try {
            VehicleSubscriptionDTO vehicleSubscriptionDTO = rawJson.toJavaObject(VehicleSubscriptionDTO.class);
            if (Objects.isNull(vehicleSubscriptionDTO)) {
                return ;
            }
            List<SubscriptionDTO> subscriptions = vehicleSubscriptionDTO.getSubscriptions();
            BoundToCustomerDTO boundToCustomer = vehicleSubscriptionDTO.getBoundToCustomer();
            VehicleInformationDTO vehicleInformation = vehicleSubscriptionDTO.getVehicleInformation();
            String vin = "", email = "", phone = "", userid = "", firstName = "", surname = "";
            if (Objects.nonNull(vehicleInformation)) {
                vin = vehicleInformation.getVin();
            }
            if (Objects.nonNull(boundToCustomer)) {
                email = boundToCustomer.getEmail();
                phone = boundToCustomer.getPhone();
                userid = boundToCustomer.getUserid();
                firstName = boundToCustomer.getFirstName();
                surname = boundToCustomer.getSurname();
            }
            for (SubscriptionDTO subscription : subscriptions) {
                String servicePackage = subscription.getServicePackage();
                LocalDateTime expiryDate = subscription.getExpiryDate();
                for (String service : subscription.getServices()) {
                    RemoteOriginalDataParseBO parseBO = createParseBO(id, service, servicePackage,
                            expiryDate, vin, email, phone, userid, firstName, surname);
                    parsedDataList.add(parseBO);
                }
            }
        } catch (Exception e) {
            log.info("解析JSON数据异常, id: {}", id, e);
            parseErrorDTOList.add(new VinInitParseErrorDTO(id, e.getMessage()));
        }
    }

    
    /**
     * 创建解析后的数据对象
     */
    private RemoteOriginalDataParseBO createParseBO(Long id, String service, String servicePackage,
                                                   LocalDateTime expiryDate, String vin, String email,
                                                   String phone, String userid, String firstName,
                                                   String surname) {
        RemoteOriginalDataParseBO parseBO = new RemoteOriginalDataParseBO();
        parseBO.setId(id);
        parseBO.setServiceName(service);
        parseBO.setServicePackage(servicePackage);
        parseBO.setExpiryDateUTC0(expiryDate);
        parseBO.setCarVin(vin);
        parseBO.setIncontrolId(email);
        parseBO.setPhone(phone);
        parseBO.setUserid(userid);
        parseBO.setFirstName(firstName);
        parseBO.setSurname(surname);
        parseBO.setServiceType(Constants.SERVICE_TYPE.REMOTE);
        return parseBO;
    }
}
