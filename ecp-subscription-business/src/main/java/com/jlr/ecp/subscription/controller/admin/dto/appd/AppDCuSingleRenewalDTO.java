package com.jlr.ecp.subscription.controller.admin.dto.appd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "APPD和联通单个续费DTO")
public class AppDCuSingleRenewalDTO {
    @Schema(description = "车架号")
    @NotEmpty
    private String carVin;

    @Schema(description = "APPD到期日")
    private String appDExpireDate;

    @Schema(description = "联通到期日")
    private String cuExpireDate;
}
