package com.jlr.ecp.subscription.service.remote.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.*;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteSinglePageListV0;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.appd.AppDDataSourceEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyStatusEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteServiceTypeEnum;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.listener.dto.TSDPSubscriptionDTO;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remote.RemoteSingleRenewalService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.LoginUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RemoteSingleRenewalServiceImpl implements RemoteSingleRenewalService {

    @Resource
    private Snowflake snowflake;

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private SubscriptionService subscriptionService;

    private static final String TODAY_END = " 23:59:59";

    @Resource
    private RemoteRenewDetailRecordsMapper remoteRenewDetailRecordsMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Resource
    private OrderCheckService orderCheckService;

    @Override
    public CommonResult<String> remoteSingleOperateRenewal(RemoteSingleRenewalDTO remoteSingleRenewalDTO) {
        log.info("手动续费单个REMOTE, remoteSingleRenewalDTO:{}", remoteSingleRenewalDTO);
        CommonResult<RemoteSearchResultDTO> verify = verify(remoteSingleRenewalDTO);
        if(verify.isError()){
            return CommonResult.error(verify);
        }

        // sprint47:校验是否存在续费中的记录
        if (checkProcessRecord(remoteSingleRenewalDTO.getCarVin())) {
            return CommonResult.error(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR);
        }

        // sprint47:校验是否存在在途订单
        CommonResult<String> checkOrderInTransit = orderCheckService.checkOrderInTransit(remoteSingleRenewalDTO.getCarVin(), ServiceTypeEnum.REMOTE);
        if (checkOrderInTransit.isError()) {
            return checkOrderInTransit;
        }

        RemoteSearchResultDTO resultDTO = verify.getData();
        LocalDateTime endDate = SubscribeTimeFormatUtil.stringToTimeByFormat(remoteSingleRenewalDTO.getExpireDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2);
        // 初始化续费记录
        RemoteRenewDetailRecords renewRecords = insertRemoteRenewRecords(remoteSingleRenewalDTO, resultDTO.getBeforeExpiryDate(), endDate);
        // 调用TSDP执行续费
        OrdFufilmentBusiDTO busiDTO = buildCallApiParam(resultDTO, renewRecords);
        RemoteModifyRespDTO respDTO = remoteCallService.concurrentCallTSDPRenew(busiDTO);
        RemoteSingleRenewalServiceImpl bean = applicationContext.getBean(getClass());
        bean.updateRemoteRenewRecords(renewRecords, respDTO);
        return CommonResult.success("请求已发送，请前往续费记录查看结果");
    }

    @Override
    public PageResult<RemoteSinglePageListV0> getRemoteSinglePageList(RemoteSingleRenewalPageDTO pageDTO) {
        log.info("查询单个REMOTE的分页列表信息, pageDTO:{}", pageDTO);
        Page<RemoteRenewDetailRecords> pageResult = queryRemoteSinglePageList(pageDTO);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageResult<>();
        }
        List<RemoteRenewDetailRecords> records = pageResult.getRecords();
        List<RemoteSinglePageListV0> pageListV0List = buildRemoteSinglePageVOList(records);
        return new PageResult<>(pageListV0List, pageResult.getTotal());
    }

    /**
     * 构建REMOTE续费请求参数
     *
     * @param remoteSearchResultDTO remoteSearchResultDTO
     * @param renewRecords  renewRecords
     * @return 返回续费请求参数
     */
    @Override
    public OrdFufilmentBusiDTO buildCallApiParam(RemoteSearchResultDTO remoteSearchResultDTO, RemoteRenewDetailRecords renewRecords) {
        OrdFufilmentBusiDTO busiDTO = new OrdFufilmentBusiDTO();
        busiDTO.setUser(remoteSearchResultDTO.getIncontrolId());
        busiDTO.setVin(remoteSearchResultDTO.getCarVin());
        // 当user为空不传递transactionId
        busiDTO.setTransactionId(Objects.isNull(busiDTO.getUser()) ? null : snowflake.nextIdStr());

        ArrayList<TSDPSubscriptionDTO> list = new ArrayList<>();
        // 校验时间不能为过去
        for (SubscriptionServiceDO service : remoteSearchResultDTO.getServiceDOList()) {
            TSDPSubscriptionDTO subscriptionDTO = new TSDPSubscriptionDTO();
            subscriptionDTO.setServicePackage(service.getServicePackage());
            subscriptionDTO.setServiceName(service.getServiceName());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            String expiryDate = renewRecords.getModifyAfterDate().minusHours(8).format(formatter);
            subscriptionDTO.setExpiryDate(expiryDate);
            list.add(subscriptionDTO);
        }
        busiDTO.setNewOrModifiedSubscriptions(list);
        return busiDTO;
    }

    /**
     * 插入Remote续订记录
     *
     * @param renewalDTO       包含Remote产品单次续订信息的DTO对象
     * @param beforeExpiryDate AppD续订之前到期的日期时间对象
     * @return 返回插入的AppD续订记录对象
     */
    private RemoteRenewDetailRecords insertRemoteRenewRecords(RemoteSingleRenewalDTO renewalDTO,
                                                              LocalDateTime beforeExpiryDate, LocalDateTime renewDate) {
        RemoteRenewDetailRecords remoteRenewRecords = new RemoteRenewDetailRecords();
        remoteRenewRecords.setCarVin(CarVinUtil.carVinToUpperCase(renewalDTO.getCarVin()));
        remoteRenewRecords.setBatchNo(snowflake.nextId());
        remoteRenewRecords.setModifyBeforeDate(beforeExpiryDate);
        remoteRenewRecords.setModifyAfterDate(renewDate);
        remoteRenewRecords.setOperator(LoginUtil.getLoginUserName());
        remoteRenewRecords.setDataSource(AppDDataSourceEnum.SINGLE.getDateSource());
        remoteRenewRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus());
        remoteRenewDetailRecordsMapper.insert(remoteRenewRecords);
        return remoteRenewRecords;
    }

    /**
     * 更新Remote续订记录
     *
     * @param renewRecords 包含Remote产品单次续订信息的record对象
     * @param respDTO      respDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRemoteRenewRecords(RemoteRenewDetailRecords renewRecords,
                                         RemoteModifyRespDTO respDTO) {
        renewRecords.setUpdatedTime(LocalDateTime.now());
        if (respDTO.isSuccess()) {
            renewRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_SUCCESS.getStatus());
            remoteRenewDetailRecordsMapper.updateById(renewRecords);
            // 同步更新subscription_service表到期时间
            subscriptionService.updateRemoteExpireDate(renewRecords);
            //修改日志记录插入
            manualModifyLogDOService.recordLog(renewRecords);
        } else {
            // 报错不为请求过多, 才需要修改状态
            if (!respDTO.isTooManyRequests()) {
                renewRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_FAIL.getStatus());
            }
            renewRecords.setErrorDesc(respDTO.getErrorMsg());
            remoteRenewDetailRecordsMapper.updateById(renewRecords);
        }
    }

    @Override
    public CommonResult<RemoteVerifyDTO> verifyParameters(RemoteSingleRenewalDTO remoteSingleRenewalDTO) {
        CommonResult<RemoteSearchResultDTO> verify = verify(remoteSingleRenewalDTO);
        if(verify.isSuccess()){
            RemoteVerifyDTO verifyDTO = new RemoteVerifyDTO();
            verifyDTO.setCarVin(verify.getData().getCarVin());
            verifyDTO.setBeforeExpiryDate(TimeFormatUtil.localDateTimeToString(verify.getData().getBeforeExpiryDate(), TimeFormatUtil.formatter_7));
            verifyDTO.setAfterExpiryDate(TimeFormatUtil.localDateTimeToString(verify.getData().getAfterExpiryDate(), TimeFormatUtil.formatter_7));
            verifyDTO.setServiceName(RemoteServiceTypeEnum.REMOTE.getServiceName());
            return CommonResult.success(verifyDTO);
        }
        return CommonResult.error(verify);
    }

    /**
     * 查询REMOTE续费记录的分页列表
     *
     * @param pageDTO 分页和查询条件封装对象
     * @return 分页结果，包含查询到的AppDCu续费记录
     */
    private Page<RemoteRenewDetailRecords> queryRemoteSinglePageList(RemoteSingleRenewalPageDTO pageDTO) {
        if (Objects.isNull(pageDTO)) {
            return new Page<>();
        }
        Page<RemoteRenewDetailRecords> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewDetailRecords::getIsDeleted, false)
                .eq(RemoteRenewDetailRecords::getDataSource, AppDDataSourceEnum.SINGLE.getDateSource());
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(RemoteRenewDetailRecords::getCreatedTime);
            queryWrapper.orderByAsc(RemoteRenewDetailRecords::getId);
        } else {
            queryWrapper.orderByDesc(RemoteRenewDetailRecords::getCreatedTime);
            queryWrapper.orderByDesc(RemoteRenewDetailRecords::getId);
        }
        return remoteRenewDetailRecordsMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 将续订记录列表转换为单页视图对象列表
     *
     * @param records 续订记录列表，包含了多个续订记录
     * @return 返回一个应用DC单页视图对象列表，每个记录对应一个展示在单页上的数据对象
     */
    private List<RemoteSinglePageListV0> buildRemoteSinglePageVOList(List<RemoteRenewDetailRecords> records) {
        List<RemoteSinglePageListV0> resp = new ArrayList<>();
        if (CollUtil.isEmpty(records)) {
            return resp;
        }
        for (RemoteRenewDetailRecords remoteRenewRecords : records) {
            resp.add(buildRemoteSinglePageVO(remoteRenewRecords));
        }
        return resp;
    }

    /**
     * 将续订记录实体转换为单页面展示的VO对象
     *
     * @param remoteRenewRecords 续订记录实体，包含车辆续订的相关信息
     * @return 返回转换后的RemoteSinglePageListV0对象，包含前端展示所需的信息
     */
    private RemoteSinglePageListV0 buildRemoteSinglePageVO(RemoteRenewDetailRecords remoteRenewRecords) {
        if (Objects.isNull(remoteRenewRecords)) {
            return new RemoteSinglePageListV0();
        }
        return RemoteSinglePageListV0.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(remoteRenewRecords.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .carVin(remoteRenewRecords.getCarVin())
                .renewalServiceName(RemoteServiceTypeEnum.REMOTE.getServiceName())
                .operator(remoteRenewRecords.getOperator())
                .batchNo(String.valueOf(remoteRenewRecords.getBatchNo()))
                .build();
    }

    /**
     * 校验远程手动续费请求的合法性
     *
     * @param remoteSingleRenewalDTO 远程手动续费请求对象，包含车辆VIN、过期日期等信息
     * @return 如果校验通过，返回成功结果和查询到的车辆订阅信息；如果校验失败，返回相应的错误码
     */
    private CommonResult<RemoteSearchResultDTO> verify(RemoteSingleRenewalDTO remoteSingleRenewalDTO) {
        if (Objects.isNull(remoteSingleRenewalDTO)) {
            return CommonResult.error(ErrorCodeConstants.REMOTE_SINGLE_RENEWAL_REQUEST_EMPTY);
        }
        if (!CarVinUtil.checkVinFormat(remoteSingleRenewalDTO.getCarVin())) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        if (StringUtils.isBlank(remoteSingleRenewalDTO.getExpireDate())) {
            return CommonResult.error(ErrorCodeConstants.REMOTE_SINGLE_RENEWAL_EXPIRE_DATE_NULL);
        }
        // 校验在当前日期之后
        LocalDateTime endDate = SubscribeTimeFormatUtil.stringToTimeByFormat(remoteSingleRenewalDTO.getExpireDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2);
        if (Objects.isNull(endDate) || LocalDate.now().isAfter(endDate.toLocalDate())) {
            log.warn("手动续费单个REMOTE, 日期非法, {}", remoteSingleRenewalDTO.getExpireDate());
            return CommonResult.error(ErrorCodeConstants.REMOTE_SINGLE_RENEWAL_EXPIRE_DATE_INVALID);
        }
        // 查询ecp库里是否存在该vin的记录
        RemoteSearchResultDTO resultDTO = subscriptionService.getRemoteExpireDateByVin(remoteSingleRenewalDTO.getCarVin());
        resultDTO.setAfterExpiryDate(endDate);
        if (!resultDTO.isExistInEcp()) {
            log.warn("手动续费单个REMOTE, vin={}不存在", remoteSingleRenewalDTO.getCarVin());
            return CommonResult.error(ErrorCodeConstants.REMOTE_RENEWAL_VIN_NOT_FOUND);
        }
        if (!resultDTO.isPiviModel()) {
            log.warn("手动续费单个REMOTE, vin={}不是PIVI车机", remoteSingleRenewalDTO.getCarVin());
            return CommonResult.error(ErrorCodeConstants.REMOTE_RENEWAL_VIN_NOT_PIVI);
        }
        if (CollUtil.isEmpty(resultDTO.getServiceDOList())) {
            log.warn("手动续费单个REMOTE, vin={}没有remote服务相关数据", remoteSingleRenewalDTO.getCarVin());
            return CommonResult.error(ErrorCodeConstants.REMOTE_RENEWAL_SERVICE_DATA_EMPTY);
        }
        return CommonResult.success(resultDTO);
    }

    @Override
    public boolean checkProcessRecord(String carVin) {
        List<RemoteRenewDetailRecords> processRecordList = remoteRenewDetailRecordsMapper.getProcessRecordsByVin(carVin);
        if (CollUtil.isNotEmpty(processRecordList)) {
            log.info("vin:{}存在续费中的remote手动续费记录", carVin);
        }
        return CollUtil.isNotEmpty(processRecordList);
    }
}
