package com.jlr.ecp.subscription.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "查询中心，在线服务初始化日志VO")
public class OnlineServiceInitializeLogVO {

    @Schema(description = "任务ID")
    private Long bauJobId;

    @Schema(description = "处理时间")
    private String processDate;

    @Schema(description = "生成时间")
    private String createdTime;

    @Schema(description = "从DMS获取的总数据量")
    private Integer totalVinNum;

    @Schema(description = "已进行在线服务初始化数据量")
    private Integer successVinNum;

    @Schema(description = "未进行在线服务初始化数据量")
    private Integer failedVinNum;

    @Schema(description = "是否展示下载按钮")
    private boolean showButton;
}
