package com.jlr.ecp.subscription.controller.app.vcsorderfufilment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResultVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.VcsOrderFulfilmentApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.SubBrandListDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.OrderSubscriptionRespVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.subscription.config.CdpService;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.fufilment.VcsFufilmentStatisticService;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 */
@Tag(name = "小程序端 - vcs order 履约相应接口")
@RestController
@RequestMapping("v1/vcsorder/fufilment")
@Validated
@Slf4j
public class VCSOrderFulfilmentController {
    @Resource
    private VcsOrderFufilmentDOService vcsOrderFufilmentDOService;

    @Resource
    private VcsOrderFulfilmentApi vcsOrderFulfilmentApi;

    @Resource
    private VcsFufilmentStatisticService vcsFufilmentStatisticService;

    @Resource
    private CdpService cdpService;

    @Resource
    private SubscriptionService subscriptionService;

    @PostMapping("/tab/subscription/list")
    @Operation(summary = "我的订阅下 已经履约的订单列表 ")
//    @PermitAll
    CommonResult<List<OrderSubscriptionRespVO>> getSubscriptionList(@Valid @RequestBody @NotNull(message = "tab数据不能为空") SubBrandListDTO subBrandListDTO, @RequestHeader(value = "client-id", required = false) String clientId) {
        log.info("请求头clientId:{}", clientId);
        if (StrUtil.isBlank(clientId)) {
            throw exception(ErrorCodeConstants.CLIENT_ID_HEADER_NEEDED);
        }
        subBrandListDTO.setBrandCode(clientId);
        // 校验数据有效
        if (CollUtil.isEmpty(subBrandListDTO.getBrandVehicles())) {
            return CommonResult.success(Collections.emptyList());
        }
        List<String> vinList = subBrandListDTO.getBrandVehicles().values().stream()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toList());
        // tab下没有车 -》没有car_vin
        if (CollUtil.isEmpty(vinList)) {
            return CommonResult.success(Collections.emptyList());
        }
        return CommonResult.success(vcsOrderFufilmentDOService.getSubscriptionList(subBrandListDTO));
    }

    @Deprecated
    @PostMapping("/view/list")
    @Operation(summary = "批量获取最新履约订单详情 废弃")
    @PermitAll
    public CommonResult<List<VcsOrderFulfilmentRespVO>> fulfilmentViewList(@RequestBody List<String> orderItemCodeList) {
        return vcsOrderFulfilmentApi.fulfilmentViewList(orderItemCodeList);
    }

    /**
     * 订阅列表
     * @return
     */
    @GetMapping( "/getTabs")
    @Operation(summary = "查询履约Tabs")
    @Parameter(name = "incontrolId", description = "客户编码")
//    @PermitAll
    CommonResult<List<OrderSubscriptionRespVO>> getTabs(@RequestHeader(value = "jlrId") String jlrId,
                                                           @RequestHeader(value = "client-id") String clientId){
        log.info("请求头clientId:{}, jlrId:{}", clientId, jlrId);
        //Map<String, Map<String, String>> map = vcsFufilmentStatisticService.getTabs(incontrolId, clientId);
        return CommonResult.success(vcsOrderFufilmentDOService.getSubscriptionList(jlrId, clientId));
    }


    /**
     * CDP请求
     * @return
     */
    @Deprecated
    @PostMapping( "/test")
    @Operation(summary = "CDP请求 废弃")
    @PermitAll
    CommonResult<String> test(String vin){
        String[] split = vin.split(",");
        List<String> vinList = Arrays.asList(split);
        List<CdpResultVO> cdpResultVOS = cdpService.callApi(vinList);

        return CommonResult.success(JSON.toJSONString(cdpResultVOS));
    }

    @GetMapping("/getExpireDateByVin")
    @Operation(summary = "到期服务日期查询")
    @Parameter(name = "carVin", description = "vin号")
    public CommonResult<SubscriptionSearchResultDTO> getExpireDateByVin(@RequestParam(value = "carVin") String carVin) {
        log.info("getExpireDateByVin request incoming ~~~~~");
        return CommonResult.success(subscriptionService.getExpireDateByVinDeeply(carVin));
    }

}
