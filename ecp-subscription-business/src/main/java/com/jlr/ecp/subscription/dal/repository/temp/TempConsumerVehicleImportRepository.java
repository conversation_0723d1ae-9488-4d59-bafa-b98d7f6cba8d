package com.jlr.ecp.subscription.dal.repository.temp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;

import java.util.List;

public interface TempConsumerVehicleImportRepository extends IService<TempConsumerVehicleImportDO> {

    /**
     * 根据状态获取vin绑定临时表的ID列表
     *
     * @param status 状态值，如果为null则返回空列表, VinBindImportTempStatusEnum
     * @return 符合条件的记录ID列表
     */
    List<TempConsumerVehicleImportDO> getImportListByStatus(Integer status);

    /**
     * 根据ID列表查询临时消费者车辆导入数据
     *
     * @param idList ID列表
     * @return 临时消费者车辆导入数据列表，如果查询结果为空则返回空列表
     */
    List<TempConsumerVehicleImportDO> selectByIdList(List<Long> idList);
}
