package com.jlr.ecp.subscription.dal.mysql.appd;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface AppDCuRenewRecordsMapper extends BaseMapperX<AppDCuRenewRecords> {
    /**
     * 根据条件获取续费记录
     *
     * @param vinList vin列表，用于筛选特定的记录
     * @return 符合条件的AppDCuRenewRecords记录列表
     */
    default List<AppDCuRenewRecords> getByVinListAndStatus(List<String> vinList, Integer status, Long renewNo, Integer renewServiceType) {
        if (CollUtil.isEmpty(vinList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(AppDCuRenewRecords::getCarVin, vinList)
                .neIfPresent(AppDCuRenewRecords::getRenewNo, renewNo)
                .eqIfPresent(AppDCuRenewRecords::getRenewServiceType, renewServiceType)
                .eq(AppDCuRenewRecords::getRenewStatus, status)
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        return selectList(queryWrapper);
    }

    /**
     * 根据vin获取续费记录
     *
     * @param vin vin，用于筛选特定的记录
     * @return 符合条件的AppDCuRenewRecords记录列表
     */
    default List<AppDCuRenewRecords> getByVinAndStatus(String vin, Integer status) {
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewRecords::getCarVin, vin)
                .eq(AppDCuRenewRecords::getRenewStatus, status)
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        return selectList(queryWrapper);
    }
}
