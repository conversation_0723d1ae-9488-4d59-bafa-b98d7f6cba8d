package com.jlr.ecp.subscription.dal.dataobject.icrorder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_incontrol_vehicle
 *
 * <AUTHOR>
 * @TableName t_incontrol_vehicle
 */
@TableName(value = "t_incontrol_vehicle")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class IncontrolVehicleDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车辆VIN;车辆VIN，唯一索引
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * incontrol账号;incontrol账号
     */
    @TableField(value = "incontrol_id")
    private String incontrolId;

    /**
     * 车辆型号;车辆型号
     */
    @TableField(value = "series_code")
    private String seriesCode;

    /**
     * 车辆名称;车辆名称
     */
    @TableField(value = "series_name")
    private String seriesName;

    /**
     * 品牌CODE;品牌CODE; LALANDJAG
     */
    @TableField(value = "brand_code")
    private String brandCode;

    /**
     * 品牌名;车型品牌
     */
    @TableField(value = "brand_name")
    private String brandName;

    /**
     * House of Brand 英文描述描述;House of Brand 英文描述描述
     */
    @TableField(value = "hob_en")
    private String hobEn;

    /**
     * 产品类型EN;产品类型EN
     */
    @TableField(value = "production_en")
    private String productionEn;

    /**
     * 配置编码;配置编码
     */
    @TableField(value = "config_code")
    private String configCode;

    /**
     * 配置名称;配置名称
     */
    @TableField(value = "config_name")
    private String configName;

    /**
     * 车辆年款;车辆年款
     */
    @TableField(value = "model_year")
    private String modelYear;

    /**
     * 车机型号;车机型号，PIVI，通过计算获得
     */
    @TableField(value = "car_system_model")
    private String carSystemModel;

    /**
     * 车主手机号
     */
    @TableField(value = "incontrol_phone")
    private String incontrolPhone;

    /**
     *  发票时间
     * */
    @TableField(value = "dms_invoice_date")
    private LocalDateTime dmsInvoiceDate;

    /**
     * 绑定时间;绑定时间
     */
    @TableField(value = "bind_time")
    private LocalDateTime bindTime;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}