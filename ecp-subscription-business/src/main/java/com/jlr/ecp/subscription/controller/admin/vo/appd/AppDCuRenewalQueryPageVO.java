package com.jlr.ecp.subscription.controller.admin.vo.appd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "查询AppDCu续费记录VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AppDCuRenewalQueryPageVO {

    @Schema(description = "续费批次号")
    private String batchNo;

    @Schema(description = "车辆编号")
    private String carVin;

    @Schema(description = "续费服务类型, 1:信息娱乐服务  2:网络流量")
    private Integer renewalServiceType;

    @Schema(description = "续费服务描述")
    private String renewalServiceDesc;

    @Schema(description = "续费状态")
    private Integer renewalStatus;

    @Schema(description = "续费状态描述")
    private String renewalStatusDesc;

    @Schema(description = "续费前到期时间")
    private String renewalBeforeExpiryDate;

    @Schema(description = "续费后到期时间")
    private String renewalAfterExpiryDate;

    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "错误信息描述")
    private String errorDesc;
}
