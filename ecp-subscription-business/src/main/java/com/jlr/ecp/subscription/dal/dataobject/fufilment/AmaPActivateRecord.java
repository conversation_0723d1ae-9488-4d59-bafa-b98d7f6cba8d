package com.jlr.ecp.subscription.dal.dataobject.fufilment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "t_amap_activate_record")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AmaPActivateRecord extends BaseDO {

    // 主键
    @TableId
    private Long id;

    // 上传文件名
    @TableField("upload_file_name")
    private String uploadFileName;

    // 上传文件地址
    @TableField("upload_s3_file")
    private String uploadS3File;

    // 处理结果文件地址
    @TableField("result_s3_file")
    private String resultS3File;

    // 上传用户名
    @TableField("upload_user_name")
    private String uploadUserName;

    // 上传时间
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    // 租户号
    @TableField("tenant_id")
    private Integer tenantId;

}
