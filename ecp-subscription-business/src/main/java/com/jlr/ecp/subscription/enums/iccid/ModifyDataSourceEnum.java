package com.jlr.ecp.subscription.enums.iccid;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ModifyDataSourceEnum {
    SINGLE(1, "单次修改"),
    BATCH(2, "批量修改");

    /**
     * 数据来源code
     */
    private final Integer dataSource;

    /**
     * 数据来源描述
     */
    private final String desc;

    /**
     * 根据数据来源码获取描述
     *
     * @param dataSource 数据来源码，类型为Integer
     * @return 返回与数据来源码对应的数据来源描述，如果找不到匹配的数据来源码，则返回空字符串
     */
    public static String getDescByDataSource(Integer dataSource) {
        for (ModifyDataSourceEnum dataSourceEnum : values()) {
            if (dataSourceEnum.getDataSource().equals(dataSource)) {
                return dataSourceEnum.getDesc();
            }
        }
        return "";
    }
}