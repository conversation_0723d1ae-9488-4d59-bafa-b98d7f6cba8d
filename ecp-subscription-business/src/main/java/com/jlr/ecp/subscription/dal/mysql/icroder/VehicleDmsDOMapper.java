package com.jlr.ecp.subscription.dal.mysql.icroder;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.tenant.core.aop.TenantIgnore;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface VehicleDmsDOMapper extends BaseMapperX<VehicleDmsDO> {

    @TenantIgnore
    @Select("select * from t_vehicle_dms where car_vin = #{carVin}")
    VehicleDmsDO selectVehicleDmsDOByCarVin(@Param("carVin") String carVin);

    @TenantIgnore
    List<VehicleDmsDO> selectVehicleDmsDOByCarVinList(@Param("carVinList") List<String> carVinList);
}
