package com.jlr.ecp.subscription.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 基础通知消息
 *
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseMessage {
    /**
     * 消息id，具有唯一性
     * */
    private String messageId;

    /**
     * 租户号
     * */
    private Long tenantId;

    /**
     * 订单号
     * */
    private String orderCode;

    /**
     *   VCS订单编码
     * */
    private String vcsOrderCode;

    /**
     *  车辆VIN
     * */
    private String vin;

    /**
     * 服务结束时间;服务结束时间
     */
    private LocalDateTime serviceEndDate;

    /**
     * 创建人
     * */
    private String createBy;
}
