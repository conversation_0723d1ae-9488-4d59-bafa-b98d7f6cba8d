package com.jlr.ecp.subscription.service.sota;

import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.enums.oss.RecordResultEnum;
import com.jlr.ecp.subscription.util.HttpUtils;
import com.jlr.ecp.subscription.util.RLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SOTAServiceImpl implements SOTAService {

    @Value("${apimService.prefixUrl}")
    private String prefixUrl;


    @Value("${apimService.sotaPrefixUrl}")
    private String sotaPrefixUrl;

    @Value("${apimService.sotaSuffixUrl}")
    private String sotaSuffixUrl;
    @Resource
    private HttpUtils httpUtils;
    @Resource
    private Redisson redisson;

    @Override
    public SOTAResultVO getSOTAInfoByVin(String vin) {
        SOTAResultVO sotaResultVO = new SOTAResultVO();
        Instant start = Instant.now();
        RLock lock = redisson.getLock(Constants.REDIS_KEY.SOTA_QUERY_KEY);
        try {
            if (RLockUtil.tryLock(lock, 1, 5, TimeUnit.SECONDS)) {
                Map<String, String> headerMap = new HashMap<>();
                // 构建GET请求URL
                StringBuffer urlString = new StringBuffer();
                urlString.append(prefixUrl).append(sotaPrefixUrl).append(vin).append(sotaSuffixUrl);

                log.info("调用apim转发请求开始,url = {},subffixUrl={}", urlString, vin);
                sotaResultVO = httpUtils.callApimServiceForGet(urlString.toString(), headerMap, vin);
                log.info("调用apim转发请求结束,resultJson={}", sotaResultVO);
            } else {
                sotaResultVO.setQueryResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), "调用sota获取限流锁失败"));
            }
        } catch (Exception e) {
            log.warn("call sota error：{}", e.getMessage());
            sotaResultVO.setQueryResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), e.getMessage()));
        } finally {
            // 计算并打印处理耗时
            Duration elapsed = Duration.between(start, Instant.now());
            log.info("call sota duration: " + elapsed.toMillis() + " ms");
            if (elapsed.toMillis() < 1000L) {
                try {
                    // sleep 1 second - elapsed.toMillis()
                    Thread.sleep(1000L - elapsed.toMillis());
                } catch (InterruptedException e) {
                    log.info("call sota sleep error：{}", e.getMessage());
                    Thread.currentThread().interrupt();
                }
            }
            RLockUtil.unlock(lock, 3);
        }
        return sotaResultVO;
    }


}