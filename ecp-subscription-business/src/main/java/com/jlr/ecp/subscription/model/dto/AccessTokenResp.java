package com.jlr.ecp.subscription.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * getAccessTokenResp 返回值
 */
@Data
@Schema(description = "APPD - getAccessTokenResp返回值")
public class AccessTokenResp {

    @JSONField(name = "access_token")
    private String accessToken;
    @JSONField(name = "expires_in")
    private Integer expiresIn;
    @JSONField(name = "refresh_expires_in")
    private Integer refreshExpiresIn;
    @JSONField(name = "token_type")
    private String tokenType;
    @JSONField(name = "not-before-policy")
    private Integer notBeforePolicy;
    @JSONField(name = "scope")
    private String scope;

}
