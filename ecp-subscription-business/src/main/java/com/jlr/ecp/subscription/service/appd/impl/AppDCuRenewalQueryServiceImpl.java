package com.jlr.ecp.subscription.service.appd.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuRenewalQueryPageVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.service.appd.AppDCuRenewalQueryService;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class AppDCuRenewalQueryServiceImpl implements AppDCuRenewalQueryService {
    @Resource
    private AppDCuRenewBatchRecordsMapper appDCuRenewBatchRecordsMapper;

    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    private static final String LOG_BY_BATCH_NO = "根据批号获取APPD续费记录，batchNo:{}";

    /**
     * 查询AppDCU续期批次的批号
     *
     * @param batchNo 批次号，字符串类型
     * @return 如果找到对应的批次记录，返回该记录的批号；否则返回空字符串
     */
    @Override
    public String queryAppDCuRenewalBatchNo(String batchNo) {
        log.info("查询AppDCU续期批次的批号, batchNo:{}", batchNo);
        AppDCuRenewBatchRecords appDCuRenewBatchRecords;
        try {
            Long batchNoLong = Long.parseLong(batchNo);
            appDCuRenewBatchRecords = getAppDCuBatchByBatchNo(batchNoLong);
            if (Objects.isNull(appDCuRenewBatchRecords)) {
                AppDCuRenewRecords appDCuRenewRecords = getAppDCuRecordByBatchNo(batchNoLong);
                if (Objects.isNull(appDCuRenewRecords)) {
                    return "";
                }
                return String.valueOf(appDCuRenewRecords.getRenewNo());
            }
        } catch (Exception e) {
            log.info("查询AppDCU续期批次的批号异常:{}, batchNo:{}", e.getMessage(), batchNo);
            return "";
        }
        return String.valueOf(appDCuRenewBatchRecords.getBatchNo());
    }

    /**
     * 查询AppDCu续费服务类型
     *
     * @return 包含所有续费服务类型的列表，每个服务类型都以AppDCuEnumQueryVO形式呈现
     */
    @Override
    public List<AppDCuEnumQueryVO> queryAppDCuRenewalService() {
        List<AppDCuEnumQueryVO> resp = new ArrayList<>();
        RenewServiceTypeEnum[] values = RenewServiceTypeEnum.values();
        for (RenewServiceTypeEnum renewServiceTypeEnum : values) {
            AppDCuEnumQueryVO appDCuEnumQueryVO = AppDCuEnumQueryVO.builder()
                    .code(renewServiceTypeEnum.getServiceType())
                    .desc(renewServiceTypeEnum.getServiceName())
                    .build();
            resp.add(appDCuEnumQueryVO);
        }
        return resp;
    }

    /**
     * 查询AppPCU续费状态枚举
     *
     */
    @Override
    public List<AppDCuEnumQueryVO> queryAppDCuRenewalStatus() {
        List<AppDCuEnumQueryVO> resp = new ArrayList<>();
        Set<String> displaySet = new HashSet<>();
        AppDRenewStatusEnum[] values = AppDRenewStatusEnum.values();
        for (AppDRenewStatusEnum appDRenewStatusEnum : values) {
            if (displaySet.contains(appDRenewStatusEnum.getDisplay())) {
                continue;
            }
            displaySet.add(appDRenewStatusEnum.getDisplay());
            AppDCuEnumQueryVO appDCuEnumQueryVO = AppDCuEnumQueryVO.builder()
                    .code(appDRenewStatusEnum.getStatus())
                    .desc(appDRenewStatusEnum.getDisplay())
                    .build();
            resp.add(appDCuEnumQueryVO);
        }
        return resp;
    }

    /**
     * 查询AppDCU续期的分页列表
     *
     * @param queryPageDTO 包含查询条件的DTO对象
     * @return 返回分页结果对象，包含数据列表和总记录数
     */
    @Override
    public PageResult<AppDCuRenewalQueryPageVO> queryAppDCuRenewalPageList(AppDCuRenewalQueryPageDTO queryPageDTO) {
        log.info("查询AppDCU续期的分页列表, queryPageDTO:{}", queryPageDTO);
        Page<AppDCuRenewRecords> pageParam = new Page<>(queryPageDTO.getPageNo(), queryPageDTO.getPageSize());
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = buildAppDCuQueryWrapper(queryPageDTO);
        Page<AppDCuRenewRecords> appDCuRenewRecordsPage = appDCuRenewRecordsMapper.selectPage(pageParam, queryWrapper);
        if (Objects.isNull(appDCuRenewRecordsPage) || CollUtil.isEmpty(appDCuRenewRecordsPage.getRecords())) {
            log.info("查询AppDCU续期的分页列表结果为空, queryPageDTO:{}", queryPageDTO);
            return new PageResult<>();
        }
        List<AppDCuRenewalQueryPageVO> pageVOList = buildAppDCuRenewalQueryPageVOList(appDCuRenewRecordsPage.getRecords());
        return new PageResult<>(pageVOList, appDCuRenewRecordsPage.getTotal());
    }

    /**
     * 构建应用DCU续期查询页面VO列表
     *
     * @param recordList 记录列表，包含所有的续期记录
     * @return 返回一个列表，包含转换后的VO对象
     */
    private List<AppDCuRenewalQueryPageVO> buildAppDCuRenewalQueryPageVOList(List<AppDCuRenewRecords> recordList) {
        List<AppDCuRenewalQueryPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(recordList)) {
            return resp;
        }
        for (AppDCuRenewRecords appDCuRenewRecords : recordList) {
            resp.add(buildAppDCuRenewalQueryPageVO(appDCuRenewRecords));
        }
        return resp;
    }

    /**
     * 构建AppDCu续订查询页面VO对象
     *
     * @param appDCuRenewRecords AppDCu的续订记录实体，包含车辆VIN码、续订服务类型等信息
     * @return AppDCuRenewalQueryPageVO 续订查询页面VO对象，包含车辆VIN码、续订批次号、续订服务类型等信息
     */
    private AppDCuRenewalQueryPageVO buildAppDCuRenewalQueryPageVO(AppDCuRenewRecords appDCuRenewRecords) {
        if (Objects.isNull(appDCuRenewRecords)) {
            return null;
        }
        return AppDCuRenewalQueryPageVO.builder()
                .batchNo(String.valueOf(appDCuRenewRecords.getRenewNo()))
                .carVin(appDCuRenewRecords.getCarVin())
                .renewalServiceType(appDCuRenewRecords.getRenewServiceType())
                .renewalServiceDesc(RenewServiceTypeEnum.getServiceName(appDCuRenewRecords.getRenewServiceType()))
                .renewalStatus(appDCuRenewRecords.getRenewStatus())
                .renewalStatusDesc(AppDRenewStatusEnum.getDisplayByStatus(appDCuRenewRecords.getRenewStatus()))
                .renewalBeforeExpiryDate(SubscribeTimeFormatUtil.timeToStringByFormat(appDCuRenewRecords.getRenewBeforeExpiryDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .renewalAfterExpiryDate(SubscribeTimeFormatUtil.timeToStringByFormat(appDCuRenewRecords.getRenewDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(appDCuRenewRecords.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(appDCuRenewRecords.getOperator())
                .errorDesc(getAppDCuErrorDesc(appDCuRenewRecords))
                .build();
    }

    /**
     * 获取对应的错误信息
     *
     * @param appDCuRenewRecords 续费记录实体
     * @return 用户友好的错误信息如果原始错误描述为空或不是以"400"或"500"开头，则返回原始错误描述
     */
    private String getAppDCuErrorDesc(AppDCuRenewRecords appDCuRenewRecords) {
        if (Objects.isNull(appDCuRenewRecords) || StringUtils.isBlank(appDCuRenewRecords.getErrorDesc())) {
            return "";
        }
        if (RenewServiceTypeEnum.APPD.getServiceType().equals(appDCuRenewRecords.getRenewServiceType())) {
            return getAppDErrorDesc(appDCuRenewRecords);
        } else if (RenewServiceTypeEnum.UNICOM.getServiceType().equals(appDCuRenewRecords.getRenewServiceType())) {
            if (UnicomResultEnum.REQUEST_ID_MISSING.code.equals(appDCuRenewRecords.getOrderResultCode())) {
                if (appDCuRenewRecords.getErrorDesc().contains("missing")) {
                    return UnicomResultEnum.REQUEST_ID_MISSING.getEcpDisplay();
                } else {
                    return UnicomResultEnum.USER_PROCESSING_ORDERS.getEcpDisplay();
                }
            } else {
                String unicomErrorDesc = UnicomResultEnum.getEcpDisplayByCode(appDCuRenewRecords.getOrderResultCode());
                if (StringUtils.isBlank(unicomErrorDesc)) {
                    return appDCuRenewRecords.getErrorDesc();
                }
                return unicomErrorDesc;
            }
        }
        return "";
    }

    /**
     * 根据错误描述获取应用的错误信息
     * 此方法旨在根据错误描述的前缀判断请求或系统级别的错误，并返回相应的错误信息
     * 如果错误描述不以400或500开头，则返回原始错误描述
     *
     * @param appDCuRenewRecords 包含错误描述的AppDCuRenewRecords对象
     * @return 根据错误代码返回错误信息，如果错误代码不是400或500开头，则返回原始错误描述
     */
    private static String getAppDErrorDesc(AppDCuRenewRecords appDCuRenewRecords) {
        if (appDCuRenewRecords.getErrorDesc().startsWith("400")) {
            return "请求异常";
        }
        if (appDCuRenewRecords.getErrorDesc().startsWith("500")) {
            return "系统异常";
        }
        return appDCuRenewRecords.getErrorDesc();
    }

    /**
     * 构建AppDCuRenewRecords的查询参数
     *
     * @param queryPageDTO 包含查询参数的DTO对象
     * @return 返回构建的查询包装器对象，如果输入参数为空则返回null
     */
    private LambdaQueryWrapper<AppDCuRenewRecords> buildAppDCuQueryWrapper(AppDCuRenewalQueryPageDTO queryPageDTO) {
        log.info("构建AppDCuRenewRecords的查询参数, queryPageDTO:{}", queryPageDTO);
        if (Objects.isNull(queryPageDTO)) {
            return null;
        }
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(queryPageDTO.getCarVin()), AppDCuRenewRecords::getCarVin,
                queryPageDTO.getCarVin());
        if (CollUtil.isNotEmpty(queryPageDTO.getBatchNoList())) {
            List<Long> renewNoList = new ArrayList<>();
            for (String batchNo : queryPageDTO.getBatchNoList()) {
                Long renewNo = Long.valueOf(batchNo);
                renewNoList.add(renewNo);
            }
            queryWrapper.in(AppDCuRenewRecords::getRenewNo, renewNoList);
        }
        queryWrapper.eq(Objects.nonNull(queryPageDTO.getRenewalServiceType()), AppDCuRenewRecords::getRenewServiceType,
                queryPageDTO.getRenewalServiceType());
        if (StringUtils.isNotBlank(queryPageDTO.getOperateStartTime()) && StringUtils.isNotBlank(queryPageDTO.getOperateEndTime())) {
            LocalDateTime startTime = SubscribeTimeFormatUtil.stringToTimeByFormat(queryPageDTO.getOperateStartTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            LocalDateTime endTime = SubscribeTimeFormatUtil.stringToTimeByFormat(queryPageDTO.getOperateEndTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            queryWrapper.ge(AppDCuRenewRecords::getCreatedTime, startTime)
                    .le(AppDCuRenewRecords::getCreatedTime, endTime);
        }
        if (Objects.nonNull(queryPageDTO.getRenewalStatus())) {
            if(AppDRenewStatusEnum.WAIT_RENEW.getStatus().equals(queryPageDTO.getRenewalStatus())
                    || AppDRenewStatusEnum.RENEW_PROGRESS.getStatus().equals(queryPageDTO.getRenewalStatus())) {
                queryWrapper.in(AppDCuRenewRecords::getRenewStatus,
                        List.of(AppDRenewStatusEnum.WAIT_RENEW.getStatus(), AppDRenewStatusEnum.RENEW_PROGRESS.getStatus()));
            } else {
                queryWrapper.eq(AppDCuRenewRecords::getRenewStatus, queryPageDTO.getRenewalStatus());
            }
        }
        queryWrapper.eq(StringUtils.isNotBlank(queryPageDTO.getOperator()), AppDCuRenewRecords::getOperator, queryPageDTO.getOperator());
        if (StringUtils.isNotBlank(queryPageDTO.getOperateTimeSort())) {
            if (SortTypeEnum.ASC.getSortType().equals(queryPageDTO.getOperateTimeSort())) {
                queryWrapper.orderByAsc(AppDCuRenewRecords::getCreatedTime);
                queryWrapper.orderByAsc(AppDCuRenewRecords::getId);
            } else {
                queryWrapper.orderByDesc(AppDCuRenewRecords::getCreatedTime);
                queryWrapper.orderByDesc(AppDCuRenewRecords::getId);
            }
        }
        return queryWrapper;
    }

    /**
     * 根据批次号获取AppDCu续费批次记录
     *
     * @param batchNo 批次号，用于识别特定的续费批次
     * @return AppDCuRenewBatchRecords对象，表示找到的批次记录如果找不到匹配的记录，则返回null
     */
    private AppDCuRenewBatchRecords getAppDCuBatchByBatchNo(Long batchNo) {
        log.info("根据批次号获取AppDCu续费批次记录，batchNo:{}", batchNo);
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapper<AppDCuRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewBatchRecords::getBatchNo, batchNo)
                .eq(AppDCuRenewBatchRecords::getIsDeleted, false);
        List<AppDCuRenewBatchRecords> respList = appDCuRenewBatchRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(respList)) {
            log.info("根据批次号获取AppDCu续费批次记录为空，batchNo:{}", batchNo);
            return null;
        }
        if (respList.size() > 1) {
            log.info("根据批次号获取AppDCu续费批次记录数量大于1，batchNo:{}", batchNo);
        }
        return respList.get(0);
    }

    /**
     * 根据批号获取APPD续费记录
     *
     * @param batchNo 续费操作的批号，用于查询特定的续费记录
     * @return 如果存在符合条件的续费记录，则返回该记录；否则返回null
     */
    private AppDCuRenewRecords getAppDCuRecordByBatchNo(Long batchNo) {
        log.info(LOG_BY_BATCH_NO, batchNo);
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewRecords::getRenewNo, batchNo)
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        List<AppDCuRenewRecords> respList = appDCuRenewRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(respList)) {
            log.info(LOG_BY_BATCH_NO, batchNo);
            return null;
        }
        if (respList.size() > 1) {
            log.info(LOG_BY_BATCH_NO, batchNo);
        }
        return respList.get(0);
    }
}
