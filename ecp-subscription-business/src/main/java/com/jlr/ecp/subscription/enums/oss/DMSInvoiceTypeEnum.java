package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * dms发票类型
 */
@AllArgsConstructor
@Getter
public enum DMSInvoiceTypeEnum {

    REGULAR("60711001", "正常红冲票"),

    INVALID("60711002", "作废红冲票");



    /**
     * 类型
     * */
    public final String code;

    /**
     * 描述
     * */
    public final String desc;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (DMSInvoiceTypeEnum status : DMSInvoiceTypeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
