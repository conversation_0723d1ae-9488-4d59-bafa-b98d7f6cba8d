package com.jlr.ecp.subscription.api.appdcu;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.pivi.appdcu.AppDCuRenewalServiceApi;
import com.jlr.ecp.subscription.api.pivi.dto.appdcu.AppDCuRenewJobDTO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.service.appd.AppDCuBatchRenewalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class AppDCuRenewalServiceApiImpl implements AppDCuRenewalServiceApi {
    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    @Resource
    private AppDCuBatchRenewalService appDCuBatchRenewalService;

    @Resource
    private AppDCuRenewBatchRecordsMapper appDCuRenewBatchRecordsMapper;

    /**
     * 获取待续费AppDID的列表
     *
     * @return 返回一个包含待续费应用ID列表的CommonResult对象
     */
    @Override
    public CommonResult<List<List<Long>>> getAppDCuRenewalIdList(Integer renewStatus) {
        List<List<Long>> appWaitedRenewalIdList = getWaitedAppDCuRecordsByStatus(renewStatus);
        log.info("获取待续费AppDID的列表, status:{}, 待续费的总数量：{}", renewStatus, appWaitedRenewalIdList.size());
        return CommonResult.success(appWaitedRenewalIdList);
    }

    /**
     * job批量发送AppDCu续期并更新记录
     *
     * @param appDCuRenewJobDTO appDCu续期任务对象，包含待续费的AppDID列表
     * @return 返回一个CommonResult对象，包含处理结果
     *         注意：当前实现直接返回null，表示无需处理结果或处理结果不重要
     */
    @Override
    public CommonResult<Integer> batchSendAppDRenewal(AppDCuRenewJobDTO appDCuRenewJobDTO) {
        if (Objects.isNull(appDCuRenewJobDTO) || CollUtil.isEmpty(appDCuRenewJobDTO.getAppDCuIdList()) ||
            Objects.isNull(appDCuRenewJobDTO.getRenewStatus())) {
            log.info("job批量发送AppDCu续期并更新记录, 输入参数为空, appDCuRenewJobDTO:{}", appDCuRenewJobDTO);
            return CommonResult.success(0);
        }
        List<Long> appdIdList = appDCuRenewJobDTO.getAppDCuIdList();
        Integer renewStatus = appDCuRenewJobDTO.getRenewStatus();
        log.info("job批量发送AppDCu续期并更新记录, appDIdList的数量:{}, renewStatus:{}, endFlag:{}",
                appdIdList.size(), renewStatus, appDCuRenewJobDTO.getEndFlag());
        List<AppDCuRenewRecords> appDCuRenewRecordsList = getAppDRecordsByIdList(appdIdList, renewStatus);
        if (CollUtil.isEmpty(appDCuRenewRecordsList)) {
            log.info("job批量发送AppDCu续期并更新记录, 查询的appDCu总数量：{}, 无需处理", appDCuRenewRecordsList.size());
            return CommonResult.success(0);
        }
        List<AppDCuRenewRecords> appDList = getAppDRecordsByServiceType(appDCuRenewRecordsList,
                RenewServiceTypeEnum.APPD.getServiceType());
        List<AppDCuRenewRecords> unicomList = getAppDRecordsByServiceType(appDCuRenewRecordsList,
                RenewServiceTypeEnum.UNICOM.getServiceType());
        appDCuBatchRenewalService.appDBatchSendRenewalAndUpdate(appDList, renewStatus);
        appDCuBatchRenewalService.unicomBatchSendRenewalAndUpdate(unicomList, renewStatus);
        log.info("job批量发送AppDCu续期并更新记录, 查询的appDCu总数量：{}, appDList的数量：{}， unicomList的数量：{}",
                appDCuRenewRecordsList.size(), appDList.size(), unicomList.size());
        if (Boolean.TRUE.equals(appDCuRenewJobDTO.getEndFlag())) {
            if (CollUtil.isNotEmpty(appDList) && Objects.nonNull(appDList.get(0).getRenewNo())) {
                updateBatchRecordStatus(appDList.get(0).getRenewNo());
            } else if (CollUtil.isNotEmpty(unicomList) && Objects.nonNull(unicomList.get(0).getRenewNo())) {
                updateBatchRecordStatus(unicomList.get(0).getRenewNo());
            }
        }
        return CommonResult.success(appDCuRenewRecordsList.size());
    }

    /**
     *  修改AppDCu批量续费记录的状态
     *
     * @param batchNo 批次号
     */
    private void updateBatchRecordStatus(Long batchNo){
        //更新批量记录的状态
        AppDCuRenewBatchRecords batchRecordsDO = queryAppDCuRenewalByBatchNo(batchNo);
        log.info("修改AppDCu批量续费记录的状态, batchNo:{}, batchRecordsDO:{}", batchNo, batchRecordsDO);
        if (Objects.isNull(batchRecordsDO)) {
            log.info("修改AppDCu批量续费记录的状态, 查询结果为空");
            return ;
        }
        batchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        appDCuRenewBatchRecordsMapper.updateById(batchRecordsDO);
    }


    /**
     * 根据服务类型过滤AppD续费记录
     * 该方法从一个AppDCuRenewRecords对象列表中，筛选出符合指定服务类型的记录
     * 主要用于在批量操作或查询时，根据服务类型对记录进行快速过滤
     *
     * @param appDCuList AppDCuRenewRecords对象列表，包含所有待过滤的记录
     * @param renewServiceType 续费服务类型，用于过滤记录的依据
     * @return 返回一个AppDCuRenewRecords对象列表，仅包含符合指定服务类型的记录
     */
    private List<AppDCuRenewRecords> getAppDRecordsByServiceType(List<AppDCuRenewRecords> appDCuList,
                                                                 Integer renewServiceType) {
        if (CollUtil.isEmpty(appDCuList)) {
            return new ArrayList<>();
        }
        return appDCuList.stream()
                .filter(appDCu -> appDCu.getRenewServiceType().equals(renewServiceType))
                .collect(Collectors.toList());
    }

    /**
     * 获取AppDCu中未处理完成的记录数
     *
     * @param batchNo 批次号，用于查询对应的续费记录
     * @return 续费中的记录数
     */
    private Long getAppDCuRecordProgressNumber(Long batchNo) {
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewRecords::getRenewNo, batchNo)
                .in(AppDCuRenewRecords::getRenewStatus,
                        List.of(AppDRenewStatusEnum.RENEW_PROGRESS.getStatus(), AppDRenewStatusEnum.WAIT_RENEW.getStatus()))
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        return appDCuRenewRecordsMapper.selectCount(queryWrapper);
    }

    /**
     * 根据应用ID列表获取应用D的续费记录
     *
     * @param appDCuIdList appD的ID列表，用于查询特定的应用D的续费记录
     * @param renewStatus 续费状态，用于筛选特定状态的记录，默认为等待续费
     * @return 返回一个AppDCuRenewRecords对象列表，包含所有对应ID且未被逻辑删除的应用D的续费记录
     */
    private List<AppDCuRenewRecords> getAppDRecordsByIdList(List<Long> appDCuIdList, Integer renewStatus) {
        if (CollUtil.isEmpty(appDCuIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppDCuRenewRecords::getId, appDCuIdList)
                .eq(AppDCuRenewRecords::getRenewStatus, renewStatus)
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        return appDCuRenewRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 根据服务类型和续费状态获取AppDCu续费记录
     *
     * @param renewStatus 续费状态，用于筛选特定状态的记录
     * @return 返回符合指定条件的续费记录列表
     */
    private List<List<Long>> getWaitedAppDCuRecordsByStatus(Integer renewStatus) {
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewRecords::getRenewStatus, renewStatus)
                .eq(AppDCuRenewRecords::getIsDeleted, false)
                .select(AppDCuRenewRecords::getId, AppDCuRenewRecords::getRenewNo);
        List<AppDCuRenewRecords> records = appDCuRenewRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>();
        }
        // 按照renewNo分组，并提取每个分组中的id列表
        Map<Long, List<AppDCuRenewRecords>> groupedById = records.stream()
                .collect(Collectors.groupingBy(AppDCuRenewRecords::getRenewNo));
        return groupedById.values().stream()
                .map(group -> group.stream().map(AppDCuRenewRecords::getId).collect(Collectors.toList()))
                .collect(Collectors.toList());
    }

    /**
     * 根据批次编号查询AppDCuRenewBatchRecords对象
     *
     * @param batchNo 批次编号，用于查询特定批次的记录
     * @return 如果找到对应的记录，则返回AppDCuRenewBatchRecords对象；如果未找到或有多条记录，则返回null
     */
    private AppDCuRenewBatchRecords queryAppDCuRenewalByBatchNo(Long batchNo) {
        LambdaQueryWrapper<AppDCuRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewBatchRecords::getBatchNo, batchNo)
                .eq(AppDCuRenewBatchRecords::getIsDeleted, false);
        List<AppDCuRenewBatchRecords> resp = appDCuRenewBatchRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询AppDCuRenewBatchRecords对象, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }
}
