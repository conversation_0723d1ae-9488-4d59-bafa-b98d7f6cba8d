package com.jlr.ecp.subscription.dal.dataobject.fufilment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_vcs_order_rollback
 *
 * @TableName t_vcs_order_rollback
 */
@TableName(value = "t_vcs_order_rollback")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsOrderRollbackDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 履约号ID;履约号ID
     */
    @TableField(value = "fufilment_id")
    private String fufilmentId;

    /**
     * 回滚履约ID;回滚履约ID，雪花算法
     */
    @TableField(value = "rollback_fufilment_id")
    private String rollbackFufilmentId;

    /**
     * VCS订单编码
     * */
    @TableField(value = "vcs_order_code")
    private String vcsOrderCode;

    /**
     * 退单号
     */
    @TableField(value = "refund_order_code")
    private String refundOrderCode;


    /**
     * 服务结束时间;服务结束时间
     */
    @TableField(value = "service_end_date")
    private LocalDateTime serviceEndDate;

    /**
     * 服务状态;服务状态， 1:激活关闭中 2：激活关闭
     */
    @TableField(value = "service_status")
    private Integer serviceStatus;

}