package com.jlr.ecp.subscription.util.token;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.forgeRock.core.util.JWTPS256TokenUtils;
import okhttp3.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class AccessTokenUtil {
    private static final Logger log = LogManager.getLogger(AccessTokenUtil.class);

    private static final OkHttpClient CLIENT = OKHttpClientBuilder.buildOKHttpClient()
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    @Value("${access.token.url}")
    private String url;

    @Value("${access.token.grantType}")
    private String grantType;

    @Value("${access.token.scope}")
    private String scope;

    @Value("${access.token.clientAssertionType}")
    private String clientAssertionType;

    @Value("${access.token.clientId}")
    private String clientId;

    @Autowired
    private JWTPS256TokenUtils jwtps256TokenUtils;

    public AccessTokenResponse fetchAccessToken() throws Exception {
        RequestBody formBody = new FormBody.Builder()
                .addEncoded("grant_type", grantType)
                .addEncoded("scope", scope)
                .addEncoded("client_id", clientId)
                .addEncoded("client_assertion", getJwtToken())
                .addEncoded("client_assertion_type", clientAssertionType)
                .build();

        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        log.info("获取accesstoken, url:{}, head:{}", url, request.headers());
        log.info("body, grant_type:{}", grantType);
        log.info("body, scope:{}", scope);
        log.info("body, client_id:{}", clientId);
        log.info("body, client_assertion:{}", getJwtToken());
        log.info("body, client_assertion_type:{}", clientAssertionType);
        try (Response response = CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to fetch access token: HTTP error code " + response.code());
            }
            String responseBody = response.body().string();
            return JSON.parseObject(responseBody, AccessTokenResponse.class);
        }
    }

    /**
     *  jwt token
     * @return String
     * */
    public String getJwtToken() {
        String token = "";
        try {
            token = jwtps256TokenUtils.createJwtToken();
        } catch (Exception e) {
            log.error("create jwt token exception: ", e);
        }
        log.info("create jwt token: {}", token);
        return token;
    }
}
