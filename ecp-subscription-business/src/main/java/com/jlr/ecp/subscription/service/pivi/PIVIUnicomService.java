package com.jlr.ecp.subscription.service.pivi;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomReqDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRnrQueryVO;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.SimCardInfoDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface PIVIUnicomService {

    /**
     *  callUnicom
     * @param message
     * @param fulfilmentId
     * @return
     */
    Boolean callUnicomService(FufilmentMessage message,String fulfilmentId);

    /**
     * 手动续费联通服务
     *
     * @param carVin 车辆识别号（Vehicle Identification Number），用于唯一标识车辆
     * @param endDate 续费结束日期，指定续费服务到何时结束
     * @return 返回联通API的响应对象，包含续费结果信息如果遇到异常或无效数据，返回null
     */
    CommonResult<UnicomRespVO> unicomManualRenewal(String carVin, LocalDateTime endDate);

    /**
     * 手动续费联通服务(job补偿)
     *
     * @param carVin 车辆识别号（Vehicle Identification Number），用于唯一标识车辆
     * @param endDate 续费结束日期，指定续费服务到何时结束
     * @param cusOrderId 订单号，用于标识本次续费操作，方便后续查询续费状态
     * @return 返回联通API的响应对象，包含续费结果信息如果遇到异常或无效数据，返回null
     */
    CommonResult<UnicomRespVO> unicomManualRenewalJob(String carVin, LocalDateTime endDate, String cusOrderId);

    /**
     * 根据CarVin 和ICCID 查询联通SIM卡信息
     * @return UnicomRespVO
     */
    UnicomRespVO getSimCardInfo(String iccid);

    /**
     * 批量+限流 根据CarVin 和ICCID 查询联通SIM卡信息
     * @param simCardInfoDTOList simCar查询的dto
     * @return List<UnicomRespVO>
     */
    List<UnicomRespVO> getSimCardInfoList(List<SimCardInfoDTO> simCardInfoDTOList);

    /**
     * 同步获取联通SIM卡信息
     *
     * @param simCardInfoDTOList 包含SIM卡信息的数据传输对象列表，需要包含iccid和vin字段
     * @return 包含联通SIM卡查询结果的响应对象列表
     */
    List<UnicomRespVO> getSimCardInfoSync(List<SimCardInfoDTO> simCardInfoDTOList);

    /**
     * 根据CarVin查询联通SIM卡信息
     * @param carVin 车架号
     * @return UnicomRespVO
     */
    UnicomRespVO getSimCarInfoByCarVin(String carVin);

    List<String> addRealNameList(UnicomReqDTO unicomReqDTO);




    CommonResult<List<EsimInfoVO>> esimstate(UnicomReqDTO unicomReqDTO);


    /**
     *  取消联调调用
     * @param message
     * @param
     * @return
     */
    Boolean cancelUnicomService(CancelMessage message,String fulfilmentId);

    Boolean newCancelUnicomService(CancelMessage message,String fulfilmentId);

    /**
     * 保存取消订单
     * @param cancelMessage
     * @return
     */
    Boolean saveCancelTodo(CancelMessage cancelMessage);

    Integer executeTodoOrder(UnicomTodoOrderVO unicomTodoOrderVO);

    /**
     * ICCID查询
     * @param carVin
     * @return
     */
    CommonResult<UnicomRnrQueryVO>  getRnrQuery(String carVin);

    /**
     *  callUnicom
     * @param message
     * @param iccid
     * @return
     */
    CommonResult<UnicomRespVO> unicomRenewalByIccid(FufilmentMessage message, String iccid);


    /**
     * 代客下单校验RNR信息
     * @param carVin
     * @return
     */
    public EsimInfoVO checkVinRNRInfo(String carVin);
}
