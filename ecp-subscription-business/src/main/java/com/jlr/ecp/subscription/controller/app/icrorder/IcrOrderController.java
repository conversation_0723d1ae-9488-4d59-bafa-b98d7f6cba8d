package com.jlr.ecp.subscription.controller.app.icrorder;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.service.icrorder.IncontrolVehicleDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 与订单相关的接口
 *
 * <AUTHOR>
 */
@Tag(name = "app端 - 与订单相关的接口")
@RestController
@RequestMapping("v1/app/subscription/icr/order")
@Validated
public class IcrOrderController {



}
