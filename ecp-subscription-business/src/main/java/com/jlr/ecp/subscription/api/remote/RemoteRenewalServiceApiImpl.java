package com.jlr.ecp.subscription.api.remote;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.remote.dto.RemoteRenewJobDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyStatusEnum;
import com.jlr.ecp.subscription.service.remote.RemoteBatchRenewalService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class RemoteRenewalServiceApiImpl implements RemoteRenewalServiceApi {
    @Resource
    private RemoteRenewDetailRecordsMapper remoteRenewDetailRecordsMapper;

    @Resource
    private RemoteBatchRenewalService remoteBatchRenewalService;

    @Resource
    private RemoteRenewBatchRecordsMapper remoteRenewBatchRecordsMapper;

    @Resource
    private SubscriptionService subscriptionService;

    /**
     * 修改Remote批量续费记录的状态
     *
     * @param batchNo 批次号
     */
    private void updateBatchRecordStatus(Long batchNo) {
        //更新批量记录的状态
        RemoteRenewBatchRecords batchRecordsDO = queryRenewalByBatchNo(batchNo);
        log.info("修改Remote批量续费记录的状态, batchNo:{}, batchRecordsDO:{}", batchNo, batchRecordsDO);
        if (Objects.isNull(batchRecordsDO)) {
            log.info("修改Remote批量续费记录的状态, 查询结果为空");
            return;
        }
        batchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        remoteRenewBatchRecordsMapper.updateById(batchRecordsDO);
    }

    /**
     * 根据ID列表获取续费记录
     *
     * @param remoteIdList remote的ID列表，用于查询特定的应用D的续费记录
     * @return 返回一个RemoteRenewDetailRecords对象列表，包含所有对应ID且未被逻辑删除的应用D的续费记录
     */
    private List<RemoteRenewDetailRecords> getRecordsByIdList(List<Long> remoteIdList) {
        if (CollUtil.isEmpty(remoteIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RemoteRenewDetailRecords::getId, remoteIdList).eq(RemoteRenewDetailRecords::getModifyStatus, RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus()).eq(RemoteRenewDetailRecords::getIsDeleted, false);
        return remoteRenewDetailRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 根据服务类型和续费状态获取remote续费记录
     *
     * @return 返回符合指定条件的续费记录列表
     */
    private List<Long> getWaitedRecordsByStatus() {
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewDetailRecords::getModifyStatus, RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus()).eq(RemoteRenewDetailRecords::getIsDeleted, false).select(RemoteRenewDetailRecords::getId);
        List<RemoteRenewDetailRecords> records = remoteRenewDetailRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>();
        }
        return records.stream().map(RemoteRenewDetailRecords::getId).collect(Collectors.toList());
    }

    /**
     * 根据批次编号查询RemoteRenewBatchRecords对象
     *
     * @param batchNo 批次编号，用于查询特定批次的记录
     * @return 如果找到对应的记录，则返回RemoteRenewBatchRecords对象；如果未找到或有多条记录，则返回null
     */
    private RemoteRenewBatchRecords queryRenewalByBatchNo(Long batchNo) {
        LambdaQueryWrapper<RemoteRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewBatchRecords::getBatchNo, batchNo).eq(RemoteRenewBatchRecords::getIsDeleted, false);
        List<RemoteRenewBatchRecords> resp = remoteRenewBatchRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询RemoteRenewBatchRecords对象, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    @Override
    public CommonResult<List<Long>> getRenewalIdList() {
        List<Long> waitedRenewalIdList = getWaitedRecordsByStatus();
        log.info("获取待续费ID的列表, 待续费的总数量：{}", waitedRenewalIdList.size());
        return CommonResult.success(waitedRenewalIdList);
    }

    @Override
    public CommonResult<Integer> batchSendRenewal(RemoteRenewJobDTO remoteRenewJobDTO) {
        if (Objects.isNull(remoteRenewJobDTO) || CollUtil.isEmpty(remoteRenewJobDTO.getRemoteIdList())) {
            log.info("job批量发送Remote续期并更新记录, 输入参数为空, remoteRenewJobDTO:{}", remoteRenewJobDTO);
            return CommonResult.success(0);
        }
        List<Long> remoteIdList = remoteRenewJobDTO.getRemoteIdList();
        log.info("job批量发送Remote续期并更新记录, remoteIdList的数量:{}, endFlag:{}", remoteIdList.size(), remoteRenewJobDTO.getEndFlag());
        List<RemoteRenewDetailRecords> recordsList = getRecordsByIdList(remoteIdList);
        if (CollUtil.isEmpty(recordsList)) {
            log.info("job批量发送Remote续期并更新记录, 查询的Remote总数量：{}, 无需处理", recordsList.size());
            return CommonResult.success(0);
        }
        // 获取服务包信息
        Map<String, RemoteSearchResultDTO> allMap = getRemoteSearchResultDTOMap(recordsList);
        log.info("job批量发送Remote续期并更新记录, 存在服务包的vin数量：{}", allMap.keySet().size());
        remoteBatchRenewalService.batchSendRenewalAndUpdate(recordsList, allMap);
        log.info("job批量发送Remote续期并更新记录, 查询的Remote总数量：{}", recordsList.size());
        if (Boolean.TRUE.equals(remoteRenewJobDTO.getEndFlag())) {
            updateBatchRecordStatus(recordsList.get(0).getBatchNo());
        }
        return CommonResult.success(recordsList.size());
    }

    /**
     * 获取服务包信息
     *
     * @param recordsList 包含应用数据库续费信息的列表
     * @return 返回一个映射，键为数据库标识，值为续费日期之前的时间
     */
    private Map<String, RemoteSearchResultDTO> getRemoteSearchResultDTOMap(List<RemoteRenewDetailRecords> recordsList) {
        List<List<RemoteRenewDetailRecords>> partition = Lists.partition(recordsList, 10);
        List<RemoteSearchResultDTO> allResult = new ArrayList<>(recordsList.size());
        for (List<RemoteRenewDetailRecords> records : partition) {
            List<String> vinList = records.stream().map(RemoteRenewDetailRecords::getCarVin).collect(Collectors.toList());
            List<RemoteSearchResultDTO> resultDTOList = subscriptionService.getRemoteExpireDateByVinList(vinList);
            resultDTOList = resultDTOList.stream()
                    .filter(resultDTO -> resultDTO.isExistInEcp()
                            && resultDTO.isPiviModel() && CollUtil.isNotEmpty(resultDTO.getServiceDOList()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(resultDTOList)) {
                allResult.addAll(resultDTOList);
            }
        }
        return allResult.stream().collect(Collectors.toMap(RemoteSearchResultDTO::getCarVin, Function.identity(), (o, n) -> o));
    }
}
