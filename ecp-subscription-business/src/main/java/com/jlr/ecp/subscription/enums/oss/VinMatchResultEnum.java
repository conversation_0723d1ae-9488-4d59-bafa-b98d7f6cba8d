package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 	VIN处理结果枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum VinMatchResultEnum {

    NOT_REQUIRED(0, "无需入库", "否"),

    PENDING(1, "待入库", "否"),

    SUCCESS(2, "成功入库", "是");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 展示描述
     * */
    public final String displayDesc;

    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (VinMatchResultEnum status : VinMatchResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDisplayDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
