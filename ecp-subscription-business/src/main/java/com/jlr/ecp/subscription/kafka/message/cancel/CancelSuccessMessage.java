package com.jlr.ecp.subscription.kafka.message.cancel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 服务关闭成功通知order
 *
 * <AUTHOR>
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CancelSuccessMessage {
    /**
     * 消息id，具有唯一性
     * */
    private String messageId;

    /**
     * 租户号
     * */
    private Long tenantId;



    /**
     * 退单订单号
     */
    private String refundOrderCode;

    /**
     * 履约ID
     */
    private String fufilmentId;


    /**
     * 是否需要修改订单状态
     */
    private Boolean updateStatus;


}
