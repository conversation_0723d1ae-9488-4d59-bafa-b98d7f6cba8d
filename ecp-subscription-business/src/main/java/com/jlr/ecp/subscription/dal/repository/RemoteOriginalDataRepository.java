package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;

import java.util.List;

/**
 * Remote原始数据Repository接口
 *
 */
public interface RemoteOriginalDataRepository extends IService<RemoteOriginalDataDO> {

    /**
     * 分页查询Remote原始数据
     *
     * @param page 分页对象
     * @param dataNo 数据批次号
     * @param status 状态
     * @param missCountMax 最大失败次数
     * @return 分页结果
     */
    Page<RemoteOriginalDataDO> selectPage(Page<RemoteOriginalDataDO> page, String dataNo, Integer status, Integer missCountMax);

    /**
     * 批量插入Remote原始数据
     *
     * @param insertList 数据列表
     * @return 插入数量
     */
    boolean insertBatch(List<RemoteOriginalDataDO> insertList);

    /**
     * 根据ID查询Remote原始数据
     *
     * @param id 主键ID
     * @return Remote原始数据
     */
    RemoteOriginalDataDO selectById(Long id);

    /**
     * 根据ID列表查询Remote原始数据
     *
     * @param idList ID列表
     * @return Remote原始数据列表
     */
    List<RemoteOriginalDataDO> selectByIdList(List<Long> idList);

    /**
     * 更新Remote原始数据
     *
     * @param remoteOriginalDataDO Remote原始数据
     * @return 更新数量
     */
    boolean updateById(RemoteOriginalDataDO remoteOriginalDataDO);

    /**
     * 批量更新Remote原始数据
     *
     * @param dataList Remote原始数据列表
     * @return 更新数量
     */
     boolean updateBatch(List<RemoteOriginalDataDO> dataList);
}
