package com.jlr.ecp.subscription.dal.dataobject.vehicle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_vehicle_esim(VehicleEsim)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_vehicle_esim")
public class VehicleEsimDO {


    /**
    * 主键
    */
    @TableId
    private Long id;

    /**
    * 车辆VIN码;车辆VIN码
    */
    @TableField(value = "car_vin")
    private String carVin;


    /**
     * 车机SIM卡ID;车机SIM卡ID
     */
    @TableField(value = "esimccid")
    private String esimccid;



}

