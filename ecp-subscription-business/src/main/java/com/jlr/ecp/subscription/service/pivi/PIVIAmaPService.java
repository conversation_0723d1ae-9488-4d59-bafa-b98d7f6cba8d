package com.jlr.ecp.subscription.service.pivi;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchResponseDTO;
import com.jlr.ecp.subscription.model.dto.AmaPOrderChargeResponseDTO;
import com.jlr.ecp.subscription.model.dto.AmaPOrderChargerRequestDTO;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;

public interface PIVIAmaPService {

    /**
     * 调用高德地图充电服务接口。
     *
     * @param fufilmentMessage 履行消息，包含调用高德地图充电服务所需的信息。
     * @param fulfilmentId      履行ID，用于标识当前调用的履行。
     * @return boolean 调用成功的标志，true表示成功，false表示失败。
     */
    boolean callAmaPService(FufilmentMessage fufilmentMessage, String fulfilmentId);

    /**
     * 对高德订单进行充值。
     *
     * @param requestDTO 充电请求的数据载体，包含订单充电的相关信息。
     * @return 返回充电操作的响应结果，如果请求失败或解析异常，返回null。
     */
    AmaPOrderChargeResponseDTO chargeAmaPOrder(AmaPOrderChargerRequestDTO requestDTO);

    /**
     * 获取高德地图车辆信息
     * @param carVin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    AmaPSearchCenterVO queryAmaPInfo(String carVin);

    /**
     * 获取汽车amap信息
     * @param pid  租户pid
     * @param vin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    AmaPCarInfoResponse getCarAmaPInfo(String pid, String vin);

    /**
     * 批量获取汽车amap信息
     * @param pid  租户pid
     * @param vin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    AmaPCarInfoResponse getCarAmaPInfoBatch(String pid, String vin);

    /**
     * 查询AMAP充值信息
     *
     * @param cusOrderId 客户订单号，实际就是VcsOrderCode
     * @return 返回支付搜索响应DTO
     */
    AmaPChargeSearchResponseDTO queryAmaPChargeInfo(String cusOrderId);

    /**
     * 获取高德地图车辆信息
     * @param carVin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    AmaPCarInfoResponse queryAmaPExpireDate(String carVin);

    /**
     * 并发对高德订单进行充值。
     *
     * @param requestDTO 充电请求的数据载体，包含订单充电的相关信息。
     * @return 返回充电操作的响应结果，如果请求失败或解析异常，返回null。
     */
    CommonResult<AmaPOrderChargeResponseDTO> concurrentCallAMapRenew(AmaPOrderChargerRequestDTO requestDTO);
}
