package com.jlr.ecp.subscription.api.vininit.dto.parse;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 订阅信息DTO
 */
@Data
public class SubscriptionDTO {

    /**
     * 服务列表
     */
    private List<String> services;


    /**
     * 过期时间（UTC）
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private LocalDateTime expiryDate;

    /**
     * 服务状态
     */
    private String serviceState;

    /**
     * 服务包
     */
    private String servicePackage;

    /**
     * 手动设置过期时间（从ISO 8601字符串）
     */
    public void setExpiryDateFromIso(String isoDateString) {
        if (isoDateString != null && isoDateString.endsWith("Z")) {
            // 解析UTC时间并转换为LocalDateTime
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(isoDateString);
            this.expiryDate = zonedDateTime.toLocalDateTime();
        }
    }
}
