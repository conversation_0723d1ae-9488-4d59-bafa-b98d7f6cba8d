package com.jlr.ecp.subscription.dal.dataobject.expiryvin;

import com.baomidou.mybatisplus.annotation.*;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * VIN到期月度记录表
 */
@TableName(value = "t_vin_expiry_monthly_records")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VinExpiryMonthlyRecordDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID（雪花算法生成）
     */
    @TableField("job_id")
    private Long jobId;

    /**
     * 任务自动触发日期（格式：yyyy-MM-dd）
     */
    @TableField("job_date")
    private LocalDateTime jobDate;

    /**
     * 手动触发参数（格式：yyyy-MM）
     */
    @TableField(value = "job_param", updateStrategy = FieldStrategy.IGNORED)
    private String jobParam;

    /**
     * 统计开始时间（包含）
     */
    @TableField("begin_date")
    private LocalDateTime beginDate;

    /**
     * 统计结束时间（包含）
     */
    @TableField("end_date")
    private LocalDateTime endDate;

    /**
     * 统计月份（数字，如4）
     */
    @TableField("job_month")
    private String jobMonth;

    /**
     * 统计年份（如2025）
     */
    @TableField("job_year")
    private String jobYear;

    /**
     * 报告S3文件地址
     */
    @TableField(value = "report_s3_file", updateStrategy = FieldStrategy.IGNORED)
    private String reportS3File;
}