package com.jlr.ecp.subscription.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPOrderChargerRequestDTO {
    /**
     *  设备id，为数字或大写字母，不能包含特殊字符。 位数在8-32位之间，需要保证唯一，不能重复。 实际就是carVin
     * */
    private String vid;

    /**
     * 商品id（同commodity_no），用于标识商品。 由高德提供。需在对接时向高德项目接口人申请。
     * */
    private String cid;

    /**
     *  客户订单id，该参数必须唯一，与历史重复会导致下单失败。 长度最大为48位。就是orderCode
     * */
    private String cusOrderId;

    /**
     *   充值数额
     * 1、为此设备充值的时间长度，单位支持年、月等。
     * 2、一个项目产品只支持一种单位，需要在项目对接初期与项目接口人拟定好，一旦约定，则项目过程中不允许修改。
     * 3、单位拟定后，此处的充值时长则默认以此单位进行充值。
     *      例如：此项目商定单位为年，amount=6则表示本次为此设备充值6年；此项目商定单位为月，amount=6则表示本次为此设备充值6月。
     * */
    private Integer amount;
}
