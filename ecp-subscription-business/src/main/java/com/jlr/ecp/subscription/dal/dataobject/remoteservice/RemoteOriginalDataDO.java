package com.jlr.ecp.subscription.dal.dataobject.remoteservice;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * TSDP remote服务拉取原始数据记录表
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "t_remote_original_data", autoResultMap = true)
public class RemoteOriginalDataDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 数据批次号，以时间存储，20240812
     */
    @TableField(value = "data_no")
    private String dataNo;
    /**
     * TSDP原始数据json
     */
    @TableField(value = "raw_json", typeHandler = FastjsonTypeHandler.class)
    private JSONObject rawJson;
    /**
     * 数据状态:1待处理 2处理成功 3处理失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 处理失败类型:
     * RAW_ERROR：数据解析错误
     * DP_FAIL：DP数据不匹配
     * SAVE_FAIL：系统保存异常
     * NOT_REMOTE：非remote数据
     */
    @TableField(value = "fail_type")
    private String failType;
    /**
     * 处理失败的描述
     */
    @TableField(value = "fail_desc")
    private String failDesc;
    /**
     * 处理失败次数默认0，超过5次不再处理
     */
    @TableField(value = "miss_count")
    private Integer missCount;
}
