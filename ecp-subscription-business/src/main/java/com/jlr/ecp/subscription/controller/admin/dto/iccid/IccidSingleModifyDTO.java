package com.jlr.ecp.subscription.controller.admin.dto.iccid;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "ICCID单个修改 DTO")
public class IccidSingleModifyDTO {
    @Schema(description = "车架号 vin")
    @NotEmpty
    private String carVin;

    @Schema(description = "新ICCID（20位）")
    @NotEmpty
    private String newIccid;
}
