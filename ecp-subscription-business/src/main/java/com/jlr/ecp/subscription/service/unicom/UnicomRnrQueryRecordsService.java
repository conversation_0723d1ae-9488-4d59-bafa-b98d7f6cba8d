package com.jlr.ecp.subscription.service.unicom;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.unicom.dto.UnicomBatchQueryPageDTO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomRnrBatchQueryListVO;

import java.util.List;

public interface UnicomRnrQueryRecordsService {


    PageResult<UnicomRnrBatchQueryListVO> getBatchFilePage(UnicomBatchQueryPageDTO pageDTO);

    List<Long> selectQueryRecordsByBatchNo(Long batchNo);
}
