package com.jlr.ecp.subscription.dal.dataobject.subscribeservice;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_subscription_service_log表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_subscription_service_log")
public class SubscriptionServiceLogDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;

     /**
     * 订阅服务ID;订阅服务ID
     */    
    private String subscriptionId;

     /**
     * 履约ID;履约ID，为哪一次履约刷新服务
     */    
    private String fufilmentId;

    /**
     * 回滚履约ID;回滚履约ID
     */
    @TableField(value = "rollback_fufilment_id")
    private String rollbackFufilmentId;

    /**
     * 服务更新前时间;服务更新前时间
     */
    private LocalDateTime refreshBeforeDate;

    /**
     * 服务更新后时间;服务更新后时间
     */
    private LocalDateTime refreshAfterDate;

    /**
     *  服务名称
     * */
    @TableField(value = "service_name")
    private String serviceName;

    /**
     *  更新类型 1：订单履约更新 2：手动续费更新
     * */
    @TableField(value = "modify_type")
    private Integer modifyType;
}

