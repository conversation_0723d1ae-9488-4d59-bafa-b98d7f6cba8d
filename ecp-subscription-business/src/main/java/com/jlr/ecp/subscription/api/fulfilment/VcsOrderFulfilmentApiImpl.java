package com.jlr.ecp.subscription.api.fulfilment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.api.order.OrderApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.api.incontrol.IncontrolVehicleImpl;
import com.jlr.ecp.subscription.api.subscripiton.dto.FulfilmentPIVICompensationDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.VcsOrderFulfilmentApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.*;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.*;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.*;
import com.jlr.ecp.subscription.enums.subscribeservice.notify.ExpirationServiceEnum;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderRollbackDOService;
import com.jlr.ecp.subscription.service.icrorder.IncontrolVehicleDOService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIFulfilmentService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.util.SnsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * VCS履约订单API  提供 RESTful API 接口，给 Feign 调用
 *
 * <AUTHOR>
 */
@RestController
@Validated
@Slf4j
public class VcsOrderFulfilmentApiImpl implements VcsOrderFulfilmentApi {
    @Resource
    private VcsOrderFufilmentDOService vcsOrderFufilmentDOService;

    @Resource
    private VcsOrderFufilmentRecordsMapper vcsOrderFufilmentRecordsMapper;

    @Resource
    private VcsOrderRollbackRecordsMapper vcsOrderRollbackRecordsMapper;

    @Resource
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Resource
    private VcsOrderRollbackDOMapper vcsOrderRollbackDOMapper;

    @Resource
    private PIVIAppDService piviAppDService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper subscriptionServiceLogMapper;

    @Resource
    private PIVIFulfilmentService piviFulfilmentService;

    @Resource(name = "subscribeScheduledThreadPool")
    private ThreadPoolTaskScheduler subscribeScheduledThreadPool;

    @Resource
    private RemoteCallService remoteCallService;

    // 补偿最大重试次数
    private static final Integer MAX_RETRY_TIMES = 3;

    @Resource
    private OrderApi orderApi;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private VcsOrderRollbackDOService vcsOrderRollbackDOService;

    @Resource
    private SnsUtil snsUtil;

    @Resource
    private Snowflake ecpIdUtil;

    @Resource
    private VcsOrderAmapRecordsMapper vcsOrderAmapRecordsMapper;

    @Resource
    private IncontrolVehicleDOService incontrolVehicleDOService;

    @Override
    public CommonResult<VcsOrderFulfilmentRespVO> view(String orderItemCode) {
        VcsOrderFulfilmentRespVO vo = vcsOrderFufilmentDOService.getOneByOrderItemCode(orderItemCode);
        return CommonResult.success(vo);
    }

    /**
     * 履约订单详情API 获取每个orderItemCode根据MaxId对应的最新履约记录
     *
     * @param orderItemCodeList 订单item编码
     * @return CommonResult<List < VcsOrderFulfilmentRespVO>>
     */
    @Override
    public CommonResult<List<VcsOrderFulfilmentRespVO>> fulfilmentViewList(List<String> orderItemCodeList) {
        List<VcsOrderFulfilmentRespVO> list = vcsOrderFufilmentDOService.getLatestByOrderItemCodeList(orderItemCodeList);
        return CommonResult.success(list);
    }

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    @Override
    public CommonResult<Boolean> checkCanBuyService(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList) {
        Boolean result = vcsOrderFufilmentDOService.checkCanBuyService(carVinAndServiceTypeList);
        return CommonResult.success(result);
    }

    @Override
    public CommonResult<Boolean> checkCanBuyServiceV2(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList) {
        Boolean result = vcsOrderFufilmentDOService.checkCanBuyServiceV2(carVinAndServiceTypeList);
        return CommonResult.success(result);
    }

    @Override
    public CommonResult<List<String>> getFulfilmentListByCarVinList(List<String> carvinList) {
        return CommonResult.success(vcsOrderFufilmentDOService.getFulfilmentListByCarVinList(carvinList));
    }

    @Override
    public CommonResult<List<VinsAndServiceDateDTO>> findVinAndServiceDate(List<String> carvinList) {
        return CommonResult.success(vcsOrderFufilmentDOService.findVinAndServiceDate(carvinList));
    }

    @Override
    public CommonResult<Integer>  processTsdpFailedTasks(Integer upcastDays) {
        return CommonResult.success(vcsOrderFufilmentDOService.processTsdpFailedTasks(upcastDays));
    }

    @Override
    public CommonResult<Boolean> checkCanBuyServiceForPC(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList){
        return vcsOrderFufilmentDOService.checkCanBuyServiceForPC(carVinAndServiceTypeList);
    }

    /**
     *  PIVI履约失败的补偿
     *  @param compensationDTO 履约补偿的dto
     * */
    @Override
    public CommonResult<Integer> fulfilmentPIVICompensation(FulfilmentPIVICompensationDTO compensationDTO) {
        log.info("PIVI履约失败补偿, compensationDTO:{}", compensationDTO);
        List<VcsOrderFufilmentRecords> fulfilmentRecords = getFulfilmentRecordsByFailed(compensationDTO);
        if (CollUtil.isEmpty(fulfilmentRecords)) {
            log.info("PIVI履约失败补偿, 待补偿记录为空");
            return CommonResult.success(0);
        }
        log.info("PIVI履约失败补偿, 待补偿记录:{}", fulfilmentRecords);
        Set<String> fulfilmentIdSet = fulfilmentRecords.stream().map(VcsOrderFufilmentRecords::getFufilmentId).collect(Collectors.toSet());
        // 查询待补偿的履约记录
        List<VcsOrderFufilmentDO> fulfilmentDOList = getFulfilmentListByIdSet(fulfilmentIdSet);
        if (CollUtil.isEmpty(fulfilmentDOList)) {
            log.info("PIVI履约失败补偿, 查询到的fulfilment为空");
            return CommonResult.success(fulfilmentRecords.size());
        }
        // 转为key为fulfilmentId, value为fulfilmentDO的map
        Map<String, VcsOrderFufilmentDO> fulfilmentDOMap = fulfilmentDOList.stream().collect(Collectors.toMap(VcsOrderFufilmentDO::getFufilmentId, Function.identity()));
        // 获取appd的fulfilmentId集合
        Set<String> appdFufilmentIdSet = fulfilmentRecords.stream().filter(records -> ServicePackageEnum.ONLINE_PACK.getPackageName().equals(records.getServicePackage()))
                .map(VcsOrderFufilmentRecords::getFufilmentId)
                .collect(Collectors.toSet());
        // carVin和jlrSubscriptionId的映射关系
        Map<String, Long> jlrSubscriptionIdMap = getJlrSubscriptionIdMap(appdFufilmentIdSet, fulfilmentDOMap);
        log.info("PIVI履约失败补偿, carVin和jlrSubscriptionId的映射关系, {}", jlrSubscriptionIdMap);

        // 获取remote的fulfilmentId集合
        Set<String> remoteFufilmentIdSet = fulfilmentRecords.stream().filter(records -> ExpirationServiceEnum.REMOTE.getType().equals(records.getServicePackage()))
                .map(VcsOrderFufilmentRecords::getFufilmentId)
                .collect(Collectors.toSet());
        // 获取remote的履约服务
        Map<String, List<SubscriptionServiceDO>> remoteServiceMap = getRemoteServiceMap(remoteFufilmentIdSet, fulfilmentDOMap);

        // 异步执行履约
        CompletableFuture.runAsync(()->{
            log.info("开始执行异步执行履约");
            // 遍历激活失败的记录
            for (VcsOrderFufilmentRecords records : fulfilmentRecords) {
                String fulfilmentId = records.getFufilmentId();
                VcsOrderFufilmentDO fulfilmentDO = fulfilmentDOMap.get(fulfilmentId);
                if (Objects.isNull(fulfilmentDO)) {
                    continue;
                }
                executeCallService(records, fulfilmentDO, jlrSubscriptionIdMap, remoteServiceMap);
            }
        }).exceptionally(ex -> {
            log.info("PIVI异步执行履约异常", ex);
            return null;
        });
        log.info("PIVI履约失败补偿完成, record数量={}", fulfilmentRecords.size());
        return CommonResult.success(fulfilmentRecords.size());
    }

    private FufilmentMessage buildFulfilmentMessage(VcsOrderFufilmentDO fulfilmentDO) {
        FufilmentMessage message = new FufilmentMessage();
        message.setTenantId(TenantContextHolder.getTenantId());
        message.setVcsOrderCode(fulfilmentDO.getVcsOrderCode());
        message.setVin(fulfilmentDO.getCarVin());
        message.setServiceBeginDate(fulfilmentDO.getServiceBeginDate());
        message.setServiceEndDate(fulfilmentDO.getServiceEndDate());
        message.setMessageId(ecpIdUtil.nextIdStr());
        return message;
    }

    /**
     * 异步调用激活服务
     *
     * @param records 订单履行记录
     * @param fulfilmentDO 订单履行详情
     * @param jlrSubscriptionIdMap 车架号与订阅ID的映射
     */
    public void executeCallService(VcsOrderFufilmentRecords records, VcsOrderFufilmentDO fulfilmentDO,
                                   Map<String, Long> jlrSubscriptionIdMap, Map<String, List<SubscriptionServiceDO>> remoteServiceMap) {
        FufilmentMessage message = buildFulfilmentMessage(fulfilmentDO);
        String fulfilmentId = records.getFufilmentId();
        // 重试次数为null，修改重试次数为1，否则修改为重试次数+1
        Integer retryTimes = Objects.isNull(records.getRetryTimes()) ? 1 : records.getRetryTimes() + 1;
        VcsOrderFulfilmentApiImpl bean = applicationContext.getBean(getClass());
        if (ServicePackageEnum.ONLINE_PACK.getPackageName().equals(records.getServicePackage())) {
            if (CollUtil.isNotEmpty(jlrSubscriptionIdMap) && Objects.nonNull(jlrSubscriptionIdMap.get(fulfilmentDO.getCarVin()))) {
                Long jlrSubscriptionId = jlrSubscriptionIdMap.get(fulfilmentDO.getCarVin());
                boolean result = piviAppDService.callAppDService(message, fulfilmentId, jlrSubscriptionId);
                if (!result) {
                    log.info("APPD激活补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
                    updateRetryFailRecord(fulfilmentId, records.getServicePackage(), retryTimes, fulfilmentDO.getOrderCode());
                    return;
                }
                log.info("APPD激活补偿成功, fulfilmentId={}", fulfilmentId);
                // 更新状态并同步订单服务
                bean.updateRecordAndSyncOrder(records.getServicePackage(), fulfilmentDO, fulfilmentId);
            }
        } else if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(records.getServicePackage())) {
            boolean result = piviAmaPService.callAmaPService(message, fulfilmentId);
            // 根据高德续费结果处理
            handleAmaPResult(result, message, records, retryTimes, fulfilmentDO.getOrderCode());
        } else if (ServicePackageEnum.DATA_PLAN.getPackageName().equals(records.getServicePackage())) {
            boolean result = piviUnicomService.callUnicomService(message, fulfilmentId);
            if (!result) {
                log.info("联通激活补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
                updateRetryFailRecord(fulfilmentId, records.getServicePackage(), retryTimes, fulfilmentDO.getOrderCode());
                return;
            }
            log.info("联通激活补偿成功, fulfilmentId={}", fulfilmentId);
            // 更新状态并同步订单服务
            bean.updateRecordAndSyncOrder(records.getServicePackage(), fulfilmentDO, fulfilmentId);
        } else if (ExpirationServiceEnum.REMOTE.getType().equals(records.getServicePackage())) {
            callRemoteService(records, fulfilmentDO, remoteServiceMap, fulfilmentId, message, retryTimes);
        }
    }

    /**
     * 处理AMAP结果
     * 根据AMAP返回的结果决定PIVI履约的更新方式
     *
     * @param isSuccess 是否成功
     * @param fufilmentMessage 履约消息
     * @param records 履约
     */
    private void handleAmaPResult(boolean isSuccess, FufilmentMessage fufilmentMessage,
                                  VcsOrderFufilmentRecords records, Integer retryTimes, String orderCode) {
        String fulfilmentId = records.getFufilmentId();
        // 续费时长
        int year = Math.abs(
                fufilmentMessage.getServiceEndDate().getYear() -
                        fufilmentMessage.getServiceBeginDate().getYear()
        );
        if (isSuccess) {
            log.info("高德激活补偿调用成功, fulfilmentId={}", fulfilmentId);
            if (year == Constants.AMAP_RENEWAL_YEAR.SIX_YEAR) {
                updatePIVIOrderFulfilmentBySixYear(fufilmentMessage.getVcsOrderCode(), fulfilmentId);
            } else {
                updatePIVIFulfilmentByAmaP(fufilmentMessage.getVcsOrderCode(), fulfilmentId);
            }
        } else {
            log.info("高德激活补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
            updateRetryFailRecord(records.getFufilmentId(), records.getServicePackage(), retryTimes, orderCode);
            if (year == Constants.AMAP_RENEWAL_YEAR.SIX_YEAR) {
                // 查询是否存在一个订单续费成功，待查询状态
                LambdaQueryWrapper<VcsOrderAmapRecords> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VcsOrderAmapRecords::getFufilmentId, fulfilmentId)
                        .eq(VcsOrderAmapRecords::getChargeOrderStatus, AMapChargeOrderStatusEnum.SUCCESS.getStatus())
                        .eq(VcsOrderAmapRecords::getQueryOrderStatus, AMapQueryOrderStatusEnum.PRE_QUERY.getStatus())
                        .eq(VcsOrderAmapRecords::getIsDeleted, false);
                List<VcsOrderAmapRecords> vcsOrderAmapRecords = vcsOrderAmapRecordsMapper.selectList(queryWrapper);
                if (CollUtil.isNotEmpty(vcsOrderAmapRecords)) {
                    updateOrderAmapRecords(vcsOrderAmapRecords.get(0).getVcsOrderCode(), fulfilmentId);
                }
            }
        }
    }

    /**
     * 调用远程服务进行订单履行补偿
     *
     * @param records 订单履行记录
     * @param fulfilmentDO 订单履行数据对象
     * @param remoteServiceMap 远程服务映射，键为车架号，值为订阅服务列表
     * @param fulfilmentId 履行单ID
     * @param message 履行消息
     * @param retryTimes 重试次数
     */
    private void callRemoteService(VcsOrderFufilmentRecords records, VcsOrderFufilmentDO fulfilmentDO, Map<String, List<SubscriptionServiceDO>> remoteServiceMap, String fulfilmentId, FufilmentMessage message, Integer retryTimes) {
        if (CollUtil.isNotEmpty(remoteServiceMap) && CollUtil.isNotEmpty(remoteServiceMap.get(fulfilmentDO.getCarVin()))) {
            List<SubscriptionServiceDO> serviceDOS = remoteServiceMap.get(fulfilmentDO.getCarVin());
            message.setMessageId(fulfilmentId);
            // 查询incontrolId
            IcrVehicleRespVO vehicleRespVO = incontrolVehicleDOService.getOneByCarVin(fulfilmentDO.getCarVin());
            if (Objects.isNull(vehicleRespVO)) {
                log.info("REMOTE激活补偿失败, incontrolVehicle为空, fulfilmentDO={}", fulfilmentDO);
                return;
            }
            RemoteModifyRespDTO result = remoteCallService.listenerCallTSDPRenew(serviceDOS, fulfilmentId, message, vehicleRespVO.getIncontrolId());
            if (!result.isSuccess()) {
                log.info("REMOTE激活补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
                updateRetryFailRecord(fulfilmentId, records.getServicePackage(), retryTimes, fulfilmentDO.getOrderCode());
                return;
            }
            log.info("REMOTE激活补偿成功, fulfilmentId={}", fulfilmentId);
            // 更新履约记录表
            updateRecord(fulfilmentId, ExpirationServiceEnum.REMOTE.getType());
        }
    }

    /**
     * 更新履约记录并同步订单状态
     * 本方法首先更新履约记录表，然后调用激活成功后的状态更新方法如果所有激活成功，则同步订单状态
     *
     * @param servicePackage 服务包信息，用于更新履约记录
     * @param fulfilmentDO 履约信息对象，包含需要更新的状态信息
     * @param fulfilmentId 履约记录ID，用于定位履约记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRecordAndSyncOrder(String servicePackage, VcsOrderFufilmentDO fulfilmentDO, String fulfilmentId) {
        // 更新履约记录表
        updateRecord(fulfilmentId, servicePackage);
        // 更新激活成功后的状态
        boolean allSuccess = activationSuccessUpdateStatus(fulfilmentDO);
        if(allSuccess){
            //发送PIVI激活通知
            vcsOrderFufilmentDOService.executeOrderStatusSync(fulfilmentDO.getOrderCode(), fulfilmentDO.getVcsOrderCode());
        }
    }

    /**
     * 通过AMAP更新PIVI订单履行状态
     *
     * @param vcsOrderCode 订单代码，用于识别特定的订单
     * @param fulfilmentId 履行ID，与订单履行状态相关联
     */
    private void updatePIVIFulfilmentByAmaP(String vcsOrderCode, String fulfilmentId) {
        Date twoMinutesLater = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
        Runnable task = () -> piviFulfilmentService.updatePIVIOrderFulfilment(vcsOrderCode, fulfilmentId);
        subscribeScheduledThreadPool.schedule(task, twoMinutesLater);
    }

    /**
     * 通过AMAP更新PIVI订单履行状态(续费六年)
     *
     * @param vcsOrderCode 订单代码，用于识别特定的订单
     * @param fulfilmentId 履行ID，与订单履行状态相关联
     */
    private void updatePIVIOrderFulfilmentBySixYear(String vcsOrderCode, String fulfilmentId) {
        Date twoMinutesLater = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
        Runnable task = () -> piviFulfilmentService.updatePIVIOrderFulfilmentBySixYear(vcsOrderCode, fulfilmentId);
        subscribeScheduledThreadPool.schedule(task, twoMinutesLater);
    }

    /**
     * 更新续费过程记录(续费六年，一次成功一次失败)
     *
     * @param vcsOrderCode 订单代码，用于识别特定的订单
     * @param fulfilmentId 履行ID，与订单履行状态相关联
     */
    private void updateOrderAmapRecords(String vcsOrderCode, String fulfilmentId) {
        Date twoMinutesLater = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
        Runnable task = () -> piviFulfilmentService.updateOrderAmapRecords(vcsOrderCode, fulfilmentId);
        subscribeScheduledThreadPool.schedule(task, twoMinutesLater);
    }

    /**
     * 异步调用激活关闭服务
     *
     * @param records 回滚履行记录
     * @param fulfilmentDO 订单履行详情
     * @param jlrSubscriptionIdMap 车架号与订阅ID的映射
     */
    private void executeCallCancelService(VcsOrderRollbackRecords records, VcsOrderFufilmentDO fulfilmentDO,
                                          Map<String, Long> jlrSubscriptionIdMap, Map<String, List<SubscriptionServiceDO>> remoteServiceMap) {
        CancelMessage message = buildCancelMessage(fulfilmentDO, records);
        String fulfilmentId = records.getFufilmentId();
        // 重试次数为null，修改重试次数为1，否则修改为重试次数+1
        Integer retryTimes = Objects.isNull(records.getRetryTimes()) ? 1 : records.getRetryTimes() + 1;
        VcsOrderFulfilmentApiImpl bean = applicationContext.getBean(getClass());
        if (ServicePackageEnum.ONLINE_PACK.getPackageName().equals(records.getServicePackage())) {
            if (CollUtil.isNotEmpty(jlrSubscriptionIdMap) && Objects.nonNull(jlrSubscriptionIdMap.get(fulfilmentDO.getCarVin()))) {
                Long jlrSubscriptionId = jlrSubscriptionIdMap.get(fulfilmentDO.getCarVin());
                boolean result = piviAppDService.callAppDService(message, fulfilmentId, jlrSubscriptionId);
                if (!result) {
                    log.info("APPD激活关闭补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
                    updateRollbackRetryFailRecord(records.getRollbackFufilmentId(), records.getServicePackage(), retryTimes);
                    return;
                }
                log.info("APPD激活关闭补偿成功, fulfilmentId={}", fulfilmentId);
                bean.updateRollbackRecordAndSyncOrder(records, fulfilmentDO, fulfilmentId);
            }
        } else if (ServicePackageEnum.DATA_PLAN.getPackageName().equals(records.getServicePackage())) {
            boolean result = piviUnicomService.newCancelUnicomService(message, fulfilmentId);
            if (!result) {
                log.info("联通激活关闭补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
                updateRollbackRetryFailRecord(records.getRollbackFufilmentId(), records.getServicePackage(), retryTimes);
                return;
            }
            log.info("联通激活关闭补偿成功, fulfilmentId={}", fulfilmentId);
            bean.updateRollbackRecordAndSyncOrder(records, fulfilmentDO, fulfilmentId);
        } else if (ExpirationServiceEnum.REMOTE.getType().equals(records.getServicePackage())) {
            callRollbackRemoteService(records, fulfilmentDO, remoteServiceMap, fulfilmentId, message, retryTimes);
        }
    }

    /**
     * 更新回滚记录并同步订单状态
     * 当回滚操作成功时，此方法用于更新回滚记录，并通过调用API同步订单状态
     *
     * @param records 回滚记录对象，包含回滚所需的信息
     * @param fulfilmentDO 订单履行数据对象，用于更新状态
     * @param fulfilmentId 订单履行ID，用于同步订单状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRollbackRecordAndSyncOrder(VcsOrderRollbackRecords records, VcsOrderFufilmentDO fulfilmentDO, String fulfilmentId) {
        updateRollbackRecord(records.getRollbackFufilmentId(), records.getServicePackage());
        // 更新回滚成功后的状态
        VcsOrderRollbackDO rollbackDO = activationOffUpdateStatus(records.getRollbackFufilmentId(), fulfilmentDO);
        if(Objects.nonNull(rollbackDO)){
            //发送PIVI关闭激活通知
            vcsOrderFufilmentDOService.executeRefundOrderStatusSync(fulfilmentDO.getOrderCode(), rollbackDO.getRefundOrderCode(), fulfilmentId);
        }
    }

    /**
     * 构建取消消息对象
     * 本方法用于创建一个CancelMessage对象，用于后续的取消操作
     * 主要负责填充消息对象的各个字段，以确保消息对象能够正确地传达取消请求的信息
     *
     * @param fulfilmentDO 履约信息对象，包含订单的履约相关信息
     * @param records 回滚记录对象，包含订单回滚的特定信息
     * @return 返回构建好的CancelMessage对象
     */
    private CancelMessage buildCancelMessage(VcsOrderFufilmentDO fulfilmentDO, VcsOrderRollbackRecords records) {
        CancelMessage message = new CancelMessage();
        message.setTenantId(TenantContextHolder.getTenantId());
        message.setVcsOrderCode(fulfilmentDO.getVcsOrderCode());
        message.setVin(fulfilmentDO.getCarVin());
        message.setServiceEndDate(records.getExpireDate());
        message.setFufilmentId(records.getFufilmentId());
        message.setMessageId(ecpIdUtil.nextIdStr());
        return message;
    }

    @Override
    public CommonResult<Integer> fulfilmentPIVICancelCompensation(FulfilmentPIVICompensationDTO compensationDTO) {
        log.info("PIVI履约关闭失败的补偿, compensationDTO:{}", compensationDTO);
        List<VcsOrderRollbackRecords> rollbackRecords = getRollbackFulfilmentRecordsByFailed(compensationDTO);
        log.info("PIVI履约关闭失败的补偿, rollbackRecords:{}", rollbackRecords);
        if (CollUtil.isEmpty(rollbackRecords)) {
            log.info("PIVI履约关闭失败的补偿, 待补偿记录为空");
            return CommonResult.success(0);
        }
        Set<String> fulfilmentIdSet = rollbackRecords.stream().map(VcsOrderRollbackRecords::getFufilmentId).collect(Collectors.toSet());
        List<VcsOrderFufilmentDO> fulfilmentDOList = getFulfilmentListByIdSet(fulfilmentIdSet);
        if (CollUtil.isEmpty(fulfilmentDOList)) {
            log.info("PIVI履约关闭失败的补偿, 查询到的fulfilment为空");
            return CommonResult.success(rollbackRecords.size());
        }
        // 转为key为fulfilmentId, value为fulfilmentDO的map
        Map<String, VcsOrderFufilmentDO> fulfilmentDOMap = fulfilmentDOList.stream().collect(Collectors.toMap(VcsOrderFufilmentDO::getFufilmentId, Function.identity()));
        // fulfilmentRecords中存在appd的失败记录，需要查询subscription_service表获取jlr_subscription_id
        Set<String> appdFufilmentIdSet = rollbackRecords.stream().filter(records -> ServicePackageEnum.ONLINE_PACK.getPackageName().equals(records.getServicePackage()))
                .map(VcsOrderRollbackRecords::getFufilmentId)
                .collect(Collectors.toSet());
        Map<String, Long> jlrSubscriptionIdMap = getJlrSubscriptionIdMap(appdFufilmentIdSet, fulfilmentDOMap);

        // 获取remote的fulfilmentId集合
        Set<String> remoteFufilmentIdSet = rollbackRecords.stream().filter(records -> ExpirationServiceEnum.REMOTE.getType().equals(records.getServicePackage()))
                .map(VcsOrderRollbackRecords::getFufilmentId)
                .collect(Collectors.toSet());
        // 获取remote的履约服务
        Map<String, List<SubscriptionServiceDO>> remoteServiceMap = getRemoteServiceMap(remoteFufilmentIdSet, fulfilmentDOMap);

        // 异步执行关闭履约
        CompletableFuture.runAsync(()->{
            log.info("开始异步执行关闭履约");
            // 遍历激活关闭失败的记录
            for (VcsOrderRollbackRecords records : rollbackRecords) {
                String fulfilmentId = records.getFufilmentId();
                VcsOrderFufilmentDO fulfilmentDO = fulfilmentDOMap.get(fulfilmentId);
                if(Objects.isNull(fulfilmentDO)){
                    log.info("PIVI履约关闭失败的补偿, 查询到的fulfilmentDO为空, fulfilmentId={}", fulfilmentId);
                    continue;
                }
                executeCallCancelService(records, fulfilmentDO, jlrSubscriptionIdMap, remoteServiceMap);
            }
        }).exceptionally(ex -> {
            log.info("PIVI异步执行关闭履约异常", ex);
            return null;
        });
        log.info("PIVI履约关闭失败的补偿, record数量={}", rollbackRecords.size());
        return CommonResult.success(rollbackRecords.size());
    }

    /**
     * job更新PIVI履行信息
     *
     * @param days 查找履行记录的天数范围如果为null，则使用默认值30天
     * @return 返回更新成功的履行记录数量
     */
    @Override
    public CommonResult<Integer> amaPUpdateFulfilmentPIVI(Integer days) {
        log.info("更新PIVI履行信息, days:{}", days);
        Integer searchDay = days;
        if (Objects.isNull(days)) {
            searchDay = 30;
        }
        List<VcsOrderFufilmentRecords> needUpdateRecordList = getFulfilmentRecordsByDays(searchDay);
        if (CollUtil.isEmpty(needUpdateRecordList)) {
            return CommonResult.success(0);
        }
        Set<String> needUpdteFulfilmentIdSet = needUpdateRecordList.stream()
                .map(VcsOrderFufilmentRecords::getFufilmentId).collect(Collectors.toSet());
        Map<String, List<VcsOrderFufilmentRecords>> fulfilmentRecordMap = getFulfilmentRecordMap(needUpdteFulfilmentIdSet);
        Set<String> fulfilmentIdSet = getUpdateAmaPFulfilmentIdList(fulfilmentRecordMap);
        List<VcsOrderFufilmentDO> fulfilmentDOList = getFulfilmentListByIdSet(fulfilmentIdSet);
        piviFulfilmentService.updatePIVIFulfilmentByVcsOrder(fulfilmentDOList);
        log.info("job更新PIVI履行信息, 更新成功数量:{}", fulfilmentDOList.size());
        return CommonResult.success(fulfilmentDOList.size());
    }

    @Override
    public CommonResult<Boolean> checkStatus(String orderCode) {
        return CommonResult.success(vcsOrderFufilmentDOService.checkStatus(orderCode));
    }

    @Override
    public CommonResult<Set<String>> getFailOrders() {
        return CommonResult.success(vcsOrderFufilmentDOService.getFailOrders());
    }

    /**
     * 根据履行单记录Map获取需要激活的履行单ID列表
     *
     * @param fulfilmentRecordMap 履行单记录的Map，键为履行单ID，值为该履行单的记录列表
     * @return 需要激活的履行单ID列表
     */
    private Set<String> getUpdateAmaPFulfilmentIdList(Map<String, List<VcsOrderFufilmentRecords>>fulfilmentRecordMap) {
        log.info("根据履行单记录Map获取需要激活的履行单ID列表, 待处理数量:{}", fulfilmentRecordMap.size());
        Set<String> fulfilmentIdSet = new HashSet<>();
        for (Map.Entry<String, List<VcsOrderFufilmentRecords>> entry : fulfilmentRecordMap.entrySet()) {
            if (!checkNeedActivationFulfilmentRecord(entry.getValue())) {
                log.info("根据履行单记录Map获取需要激活的履行单ID列表, 当前不符合, fulfilmentRecords:{}", entry.getValue());
                continue;
            }
            fulfilmentIdSet.add(entry.getKey());
        }
        log.info("根据履行单记录Map获取需要激活的履行单ID列表,需要激活总数量:{}", fulfilmentIdSet.size());
        return fulfilmentIdSet;
    }

    /**
     * 检查是否需要满足激活条件。
     *
     * @param fulfilmentRecords 订单履行记录列表，包含每个服务包的激活状态。
     * @return 如果所有服务包都满足激活条件，则返回true；否则返回false。
     */
    private boolean checkNeedActivationFulfilmentRecord(List<VcsOrderFufilmentRecords> fulfilmentRecords) {
        if (CollUtil.isEmpty(fulfilmentRecords)) {
            log.info("检查是否需要满足激活条件,fulfilmentRecords为空");
            return false;
        }
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            if (ServicePackageEnum.ONLINE_PACK.getPackageName().equals(fulfilmentRecord.getServicePackage())) {
                if (ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus().equals(fulfilmentRecord.getActivationStatus()) ||
                        ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                    return false;
                }
            } else if (ServicePackageEnum.DATA_PLAN.getPackageName().equals(fulfilmentRecord.getServicePackage()) &&
                    !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            } else if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(fulfilmentRecord.getServicePackage())
                    && !ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            }
        }
        return true;
    }



    /**
     * 根据履行单ID集合获取履行单记录的映射
     *
     * @param fulfilmentIdSet 履行单ID的集合，指定了需要获取履行单记录的履行单ID集合
     * @return 返回一个映射，其中每个键是一个履行单ID，对应的值是该履行单的所有履行单记录列表
     */
    private Map<String, List<VcsOrderFufilmentRecords>> getFulfilmentRecordMap(Set<String> fulfilmentIdSet) {
        List<VcsOrderFufilmentRecords> fulfilmentRecordList = getFulfilmentRecordByIdSet(fulfilmentIdSet);
        log.info("根据履行单ID集合获取履行单记录的映射, record的数量为:{}", fulfilmentRecordList.size());
        Map<String, List<VcsOrderFufilmentRecords>> fufilmentRecordsMap = new HashMap<>();
        for (VcsOrderFufilmentRecords records : fulfilmentRecordList) {
            List<VcsOrderFufilmentRecords> recordList = fufilmentRecordsMap.getOrDefault(records.getFufilmentId(), new ArrayList<>());
            recordList.add(records);
            fufilmentRecordsMap.put(records.getFufilmentId(), recordList);
        }
        log.info("根据履行单ID集合获取履行单记录的映射, map的数量:{}", fufilmentRecordsMap.size());
        return fufilmentRecordsMap;
    }


    /**
     * 根据履行单ID集合获取履行单记录
     *
     * @param fulfilmentIdSet 履行单ID的集合，用于批量查询
     * @return 返回未被删除的履行单记录列表
     */
    private List<VcsOrderFufilmentRecords> getFulfilmentRecordByIdSet(Set<String> fulfilmentIdSet) {
        LambdaQueryWrapperX<VcsOrderFufilmentRecords> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentIdSet)
                .eq(VcsOrderFufilmentRecords::getIsDeleted,false);
        return vcsOrderFufilmentRecordsMapper.selectList(queryWrapper);
    }



    /**
     * 根据天数获取激活中的AMAP履约记录
     *
     * @param days 查询的天数，用于限定查询范围内的时间段
     * @return 返回符合条件的履行记录列表如果days为null，返回空列表
     */
    private List<VcsOrderFufilmentRecords> getFulfilmentRecordsByDays(Integer days) {
        if (Objects.isNull(days)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VcsOrderFufilmentRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(VcsOrderFufilmentRecords::getCreatedTime, LocalDateTime.now().minusDays(days))
                .le(VcsOrderFufilmentRecords::getCreatedTime, LocalDateTime.now())
                .eq(VcsOrderFufilmentRecords::getServicePackage, ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName())
                .in(VcsOrderFufilmentRecords::getActivationStatus,
                        List.of(ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus(), ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus()))
                .eq(VcsOrderFufilmentRecords::getIsDeleted, false);
        return vcsOrderFufilmentRecordsMapper.selectList(queryWrapper);
    }

    private List<VcsOrderFufilmentDO> getFulfilmentListByIdSet(Set<String> fulfilmentIdSet) {
        LambdaQueryWrapper<VcsOrderFufilmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VcsOrderFufilmentDO::getFufilmentId, fulfilmentIdSet)
                .eq(VcsOrderFufilmentDO::getIsDeleted, false);
        return vcsOrderFufilmentDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据车辆识别号（VIN）获取JLR订阅ID
     * 此方法旨在通过车辆识别号获取相应的JLR订阅ID，用于处理PIVI履约失败的补偿逻辑
     *
     * @param appdFufilmentIdSet appd履约id集合
     * @param fulfilmentDOMap 履约详细信息的映射表，通过履约ID映射到具体的履约详情
     * @return 返回一个映射，其中包含车辆识别号和对应的JLR订阅ID
     */
    private Map<String, Long> getJlrSubscriptionIdMap(Set<String> appdFufilmentIdSet, Map<String, VcsOrderFufilmentDO> fulfilmentDOMap) {
        if(CollUtil.isEmpty(appdFufilmentIdSet)){
            return Collections.emptyMap();
        }
        Set<String> vinSet = new HashSet<>();
        for(String fulfilmentId : appdFufilmentIdSet){
            VcsOrderFufilmentDO fufilmentDO = fulfilmentDOMap.get(fulfilmentId);
            if(Objects.nonNull(fufilmentDO)){
                vinSet.add(fufilmentDO.getCarVin());
            }
        }
        if(CollUtil.isEmpty(vinSet)){
            log.info("PIVI履约失败补偿, 查询到ONLINE-PACK的vinSet为空, appdFufilmentIdSet={}", appdFufilmentIdSet);
            return Collections.emptyMap();
        }
        List<SubscriptionServiceDO> serviceDOList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, vinSet)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getServicePackage, ServicePackageEnum.ONLINE_PACK.getPackageName())
                .eq(SubscriptionServiceDO::getIsDeleted, false)
                .groupBy(SubscriptionServiceDO::getCarVin)
                .select(SubscriptionServiceDO::getCarVin, SubscriptionServiceDO::getJlrSubscriptionId));
        return serviceDOList.stream().collect(Collectors.toMap(SubscriptionServiceDO::getCarVin, SubscriptionServiceDO::getJlrSubscriptionId));
    }

    /**
     * 根据车辆识别号（VIN）获取remote订阅
     *
     * @param remoteFufilmentIdSet remote履约id集合
     * @param fulfilmentDOMap 履约详细信息的映射表，通过履约ID映射到具体的履约详情
     * @return 返回一个映射，其中包含车辆识别号和对应的JLR订阅ID
     */
    private Map<String, List<SubscriptionServiceDO>> getRemoteServiceMap(Set<String> remoteFufilmentIdSet, Map<String, VcsOrderFufilmentDO> fulfilmentDOMap) {
        if(CollUtil.isEmpty(remoteFufilmentIdSet)){
            return Collections.emptyMap();
        }
        Set<String> vinSet = new HashSet<>();
        for(String fulfilmentId : remoteFufilmentIdSet){
            VcsOrderFufilmentDO fufilmentDO = fulfilmentDOMap.get(fulfilmentId);
            if(Objects.nonNull(fufilmentDO)){
                vinSet.add(fufilmentDO.getCarVin());
            }
        }
        if(CollUtil.isEmpty(vinSet)){
            log.info("PIVI履约失败补偿, 查询到REMOTE的vinSet为空, remoteFufilmentIdSet={}", remoteFufilmentIdSet);
            return Collections.emptyMap();
        }
        List<SubscriptionServiceDO> serviceDOList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, vinSet)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
                .eq(SubscriptionServiceDO::getIsDeleted, false));
        return serviceDOList.stream().collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin));
    }

    /**
     * 更新激活成功订单的状态
     * 本方法旨在处理订单履约记录的激活状态更新，包括更新记录、筛选完全激活的订单、
     * 更新履约表状态、处理订阅服务等
     *
     * @param fulfilmentDO 订单履行详情
     */
    private boolean activationSuccessUpdateStatus(VcsOrderFufilmentDO fulfilmentDO) {
        log.info("PIVI履约失败补偿的fulfilmentId={}", fulfilmentDO.getFufilmentId());
        // 获取PIVI履约记录列表
        List<VcsOrderFufilmentRecords> recordsList = selectFulfilmentRecordsByFulfilmentId(fulfilmentDO.getFufilmentId());
        if (!checkNeedActivationFulfilment(recordsList)) {
            log.info("PIVI履约失败补偿不是所有记录都为已激活, fulfilmentDO:{}", fulfilmentDO);
            return false;
        }
        log.info("PIVI履约失败补偿开始更新激活成功订单的状态, fulfilmentDO={}", fulfilmentDO);
        // 更新履约表状态
        updateFulfilmentDO(fulfilmentDO);
        List<SubscriptionServiceDO> subscriptionServiceDOList = getSubscriptionServiceDO(fulfilmentDO.getCarVin());
        if (CollUtil.isEmpty(subscriptionServiceDOList)) {
            log.info("PIVI履约失败补偿根据car_vin查询SubscriptionServiceDO为空, car_vin:{}", fulfilmentDO.getCarVin());
            return true;
        }
        // 过滤出高德的履约记录，取记录中的过期时间
        LocalDateTime aMapExpireDate = recordsList.stream()
                .filter(records -> {
                    boolean isAMap = ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(records.getServicePackage());
                    return isAMap && Objects.nonNull(records.getExpireDate());
                }).findFirst().map(VcsOrderFufilmentRecords::getExpireDate).orElse(null);
        insertSubscriptionServiceAndLog(fulfilmentDO, subscriptionServiceDOList, aMapExpireDate);
        return true;
    }

    private void updateRollbackFulfilmentDO(VcsOrderRollbackDO vcsOrderRollbackDO) {
        vcsOrderRollbackDO.setServiceStatus(CancelServiceStatusEnum.ACTIVATE_OFF.getStatus());
        vcsOrderRollbackDO.setUpdatedTime(LocalDateTime.now());
        // 更新回滚履约表状态
        vcsOrderRollbackDOMapper.updateById(vcsOrderRollbackDO);

    }

    /**
     * 批量更新回滚订单履约记录的激活状态
     * 该方法使用了事务，并在发生异常时回滚，确保数据的一致性
     * 主要针对三种不同的服务包进行更新操作
     *
     * @param rollbackFulfilmentId rollbackFulfilmentId
     * @param fulfilmentDO fulfilmentDO
     */
    private VcsOrderRollbackDO activationOffUpdateStatus(String rollbackFulfilmentId, VcsOrderFufilmentDO fulfilmentDO) {
        log.info("PIVI履约关闭失败补偿的rollbackFulfilmentId={}", rollbackFulfilmentId);
        // 获取PIVI回滚履约记录列表
        List<VcsOrderRollbackRecords> records = selectRollbackRecords(rollbackFulfilmentId);
        if (!checkNeedActivationOffFulfilment(records)) {
            log.info("PIVI履约关闭失败补偿不全是激活关闭状态, fulfilmentDO:{}", fulfilmentDO);
            return null;
        }
        VcsOrderRollbackDO vcsOrderRollbackDO = vcsOrderRollbackDOMapper.getOneByRollBackId(rollbackFulfilmentId);
        log.info("PIVI履约关闭失败补偿开始更新激活关闭成功订单的状态, vcsOrderRollbackDO={}", vcsOrderRollbackDO);
        if(Objects.isNull(vcsOrderRollbackDO)){
            return null;
        }
        // 更新回滚履约表状态
        updateRollbackFulfilmentDO(vcsOrderRollbackDO);
        List<SubscriptionServiceDO> subscriptionServiceDOList = getSubscriptionServiceDO(fulfilmentDO.getCarVin());
        if (CollUtil.isEmpty(subscriptionServiceDOList)) {
            log.info("PIVI履约关闭失败补偿根据car_vin查询SubscriptionServiceDO为空, car_vin:{}", fulfilmentDO.getCarVin());
            return vcsOrderRollbackDO;
        }
        insertRollbackSubscriptionServiceAndLog(fulfilmentDO, vcsOrderRollbackDO, subscriptionServiceDOList);
        return vcsOrderRollbackDO;
    }


    /**
     * 根据激活失败状态获取履行记录
     *
     * @param compensationDTO 履行补偿DTO，包含页码和页面大小信息
     * @return 返回激活失败的履行记录列表
     */
    private List<VcsOrderFufilmentRecords> getFulfilmentRecordsByFailed(FulfilmentPIVICompensationDTO compensationDTO) {
        Page<VcsOrderFufilmentRecords> page = new Page<>(compensationDTO.getPageNo(), compensationDTO.getPageSize());
        LambdaQueryWrapper<VcsOrderFufilmentRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VcsOrderFufilmentRecords::getActivationStatus, ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus())
                .eq(VcsOrderFufilmentRecords::getIsDeleted, false)
                .groupBy(List.of(VcsOrderFufilmentRecords::getFufilmentId, VcsOrderFufilmentRecords::getServicePackage))
                .orderByAsc(VcsOrderFufilmentRecords::getId);
        Page<VcsOrderFufilmentRecords> recordsPage = null;
        try {
            recordsPage = vcsOrderFufilmentRecordsMapper.selectPage(page, queryWrapper);
        } catch (Exception e) {
            log.info("根据激活失败状态获取履行记录分页查询异常:", e);
        }
        if (Objects.isNull(recordsPage)) {
            return new ArrayList<>();
        }
        return recordsPage.getRecords();
    }

    /**
     * 根据激活关闭失败状态获取履行记录
     *
     * @param compensationDTO 履行补偿DTO，包含页码和页面大小信息
     * @return 返回激活失败的履行记录列表
     */
    private List<VcsOrderRollbackRecords> getRollbackFulfilmentRecordsByFailed(FulfilmentPIVICompensationDTO compensationDTO) {
        Page<VcsOrderRollbackRecords> page = new Page<>(compensationDTO.getPageNo(), compensationDTO.getPageSize());
        LambdaQueryWrapper<VcsOrderRollbackRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VcsOrderRollbackRecords::getActivationStatus, CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus())
                .eq(VcsOrderRollbackRecords::getIsDeleted, false)
                .groupBy(List.of(VcsOrderRollbackRecords::getRollbackFufilmentId, VcsOrderRollbackRecords::getServicePackage))
                .orderByAsc(VcsOrderRollbackRecords::getId);
        Page<VcsOrderRollbackRecords> recordsPage = null;
        try {
            recordsPage = vcsOrderRollbackRecordsMapper.selectPage(page, queryWrapper);
        } catch (Exception e) {
            log.info("根据激活关闭失败状态获取履行记录分页查询异常:", e);
        }
        if (Objects.isNull(recordsPage)) {
            return new ArrayList<>();
        }
        return recordsPage.getRecords();
    }

    /**
     * 更新VCS订单履行信息。
     *
     */
    private void updateFulfilmentDO(VcsOrderFufilmentDO fulfilmentDO) {
        fulfilmentDO.setServiceStatus(ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus());
        fulfilmentDO.setUpdatedTime(LocalDateTime.now());
        vcsOrderFufilmentDOMapper.updateById(fulfilmentDO);
    }

    /**
     * 根据车辆识别码和服务套餐获取订阅服务信息。
     *
     * @param carVin 车辆识别码，用于精确查询特定车辆的订阅服务信息。
     * @return 返回匹配条件的订阅服务数据对象列表。
     */
    private List<SubscriptionServiceDO> getSubscriptionServiceDO(String carVin) {
        LambdaQueryWrapperX<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        return subscriptionServiceDOMapper.selectList(queryWrapper);
    }

    /**
     * 插入订阅服务和日志。
     *
     */
    private void insertSubscriptionServiceAndLog(VcsOrderFufilmentDO fulfilmentDO, List<SubscriptionServiceDO> serviceDOList,
                                                 LocalDateTime aMapExpireDate) {
        List<SubscriptionServiceLogDO> logDOList = new ArrayList<>();
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            SubscriptionServiceLogDO logDO = SubscriptionServiceLogDO.builder()
                    .subscriptionId(serviceDO.getSubscriptionId())
                    .fufilmentId(fulfilmentDO.getFufilmentId())
                    .refreshBeforeDate(serviceDO.getExpiryDate())
                    .refreshAfterDate(fulfilmentDO.getServiceEndDate())
                    .build();
            // 如果aMapExpireDate不为空，且服务包名为高德，则更新高德导航的过期时间
            if (Objects.nonNull(aMapExpireDate) &&
                    ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                logDO.setRefreshAfterDate(aMapExpireDate);
            }
            logDOList.add(logDO);
            serviceDO.setExpiryDate(logDO.getRefreshAfterDate());
            serviceDO.setExpireDateUtc0(logDO.getRefreshAfterDate().minusHours(8));
            serviceDO.setUpdatedTime(LocalDateTime.now());
        }
        if(CollUtil.isNotEmpty(logDOList)){
            subscriptionServiceLogMapper.insertBatch(logDOList);
            log.info("插入SubscriptionServiceLogDO表成功, size={}", logDOList.size());
        }
        subscriptionServiceDOMapper.updateBatch(serviceDOList);
    }

    /**
     * 插入回滚订阅服务和日志。
     *
     */
    private void insertRollbackSubscriptionServiceAndLog(VcsOrderFufilmentDO fulfilmentDO, VcsOrderRollbackDO rollbackDO,
                                                         List<SubscriptionServiceDO> serviceDOList) {
        List<SubscriptionServiceLogDO> logDOList = new ArrayList<>();
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            // 高德的时间不做回退
            if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                continue;
            }
            LocalDateTime refreshBeforeDate = serviceDO.getExpiryDate();
            LocalDateTime refreshAfterDate = rollbackDO.getServiceEndDate();
            SubscriptionServiceLogDO logDO = SubscriptionServiceLogDO.builder()
                    .subscriptionId(serviceDO.getSubscriptionId())
                    .fufilmentId(fulfilmentDO.getFufilmentId())
                    .refreshBeforeDate(refreshBeforeDate)
                    .refreshAfterDate(refreshAfterDate)
                    .build();
            logDOList.add(logDO);
            serviceDO.setExpiryDate(refreshAfterDate);
            serviceDO.setExpireDateUtc0(refreshAfterDate.minusHours(8));
            serviceDO.setUpdatedTime(LocalDateTime.now());
        }
        if(CollUtil.isNotEmpty(logDOList)){
            subscriptionServiceLogMapper.insertBatch(logDOList);
            log.info("插入SubscriptionServiceLogDO表成功, size={}", logDOList.size());
        }
        subscriptionServiceDOMapper.updateBatch(serviceDOList);
    }

    /**
     * 根据履行记录ID列表查询服务履行记录。
     *
     * @param fulfilmentId 履行记录ID列表。
     * @return 未激活的服务履行记录列表。
     */
    private List<VcsOrderFufilmentRecords> selectFulfilmentRecordsByFulfilmentId (String fulfilmentId) {
        LambdaQueryWrapperX<VcsOrderFufilmentRecords> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getIsDeleted, false);
        return vcsOrderFufilmentRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 根据回滚履行记录ID列表查询服务回滚履行记录。
     *
     * @param rollbackFulfilmentId 回滚履行记录ID。
     */
    private List<VcsOrderRollbackRecords> selectRollbackRecords(String rollbackFulfilmentId) {
        LambdaQueryWrapperX<VcsOrderRollbackRecords> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackFulfilmentId)
                .eq(VcsOrderRollbackRecords::getIsDeleted, false);
        return vcsOrderRollbackRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 更新履约记录的激活状态
     *
     */
    private void updateRecord(String fulfilmentId, String packageName) {
        VcsOrderFufilmentRecords record = new VcsOrderFufilmentRecords();
        record.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus());
        vcsOrderFufilmentRecordsMapper.update(record, new LambdaUpdateWrapper<VcsOrderFufilmentRecords>()
                .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getServicePackage, packageName)
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 更新重试失败履约记录
     *
     */
    private void updateRetryFailRecord(String fulfilmentId, String packageName, Integer retryTimes, String orderCode) {
        VcsOrderFufilmentRecords records = new VcsOrderFufilmentRecords();
        records.setRetryTimes(retryTimes);
        // 重试次数最多3次，达到阈值后activation_status改为4
        if (retryTimes >= MAX_RETRY_TIMES) {
            records.setActivationStatus(ServiceActivationStatusEnum.BUSINESS_ACTIVATION_FAILED.getStatus());
            snsUtil.publishTopic(String.format("订单号: %s, %s履约服务激活失败", orderCode, packageName));
        }
        vcsOrderFufilmentRecordsMapper.update(records, new LambdaUpdateWrapper<VcsOrderFufilmentRecords>()
                .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getServicePackage, packageName)
                .eq(BaseDO::getIsDeleted, false));
        if (retryTimes < MAX_RETRY_TIMES) {
            return;
        }
        // 如果是remote服务，直接更新履约单状态为"激活失败"
        if (VinExpiryServiceTypeEnum.REMOTE.getType().equals(packageName)) {
            vcsOrderFufilmentDOService.updateServiceStatusFail(fulfilmentId);
            return;
        }
        // 查询是否存在状态为"激活中"或"激活失败"的记录
        boolean hasActivationPending = vcsOrderFufilmentRecordsMapper.selectList(new LambdaQueryWrapper<VcsOrderFufilmentRecords>()
                        .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                        .eq(BaseDO::getIsDeleted, false))
                .stream()
                .anyMatch(fufilmentRecords ->
                        ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus().equals(fufilmentRecords.getActivationStatus()) ||
                                ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus().equals(fufilmentRecords.getActivationStatus()));

        // 如果不存在，则更新履约单状态为"激活失败"
        if (!hasActivationPending) {
            vcsOrderFufilmentDOService.updateServiceStatusFail(fulfilmentId);
        }
    }

    /**
     * 更新回滚履约记录的激活状态
     *
     */
    private void updateRollbackRecord(String rollbackFulfilmentId, String packageName) {
        VcsOrderRollbackRecords records = new VcsOrderRollbackRecords();
        records.setActivationStatus(CancelServiceStatusEnum.ACTIVATE_OFF.getStatus());
        vcsOrderRollbackRecordsMapper.update(records, new LambdaUpdateWrapper<VcsOrderRollbackRecords>()
                .eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackFulfilmentId)
                .eq(VcsOrderRollbackRecords::getServicePackage, packageName)
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 更新重试失败回滚履约记录
     *
     */
    private void updateRollbackRetryFailRecord(String rollbackFulfilmentId, String packageName, Integer retryTimes) {
        VcsOrderRollbackRecords records = new VcsOrderRollbackRecords();
        records.setRetryTimes(retryTimes);
        // 重试次数最多3次，达到阈值后activation_status改为4
        if(retryTimes >= MAX_RETRY_TIMES){
            records.setActivationStatus(CancelServiceStatusEnum.BUSINESS_ACTIVATE_OFF_FAILURE.getStatus());
        }
        vcsOrderRollbackRecordsMapper.update(records, new LambdaUpdateWrapper<VcsOrderRollbackRecords>()
                .eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackFulfilmentId)
                .eq(VcsOrderRollbackRecords::getServicePackage, packageName)
                .eq(BaseDO::getIsDeleted, false));
        if (retryTimes < MAX_RETRY_TIMES) {
            return;
        }
        // 如果是remote服务，直接更新履约单状态为"关闭激活失败"
        if (VinExpiryServiceTypeEnum.REMOTE.getType().equals(packageName)) {
            vcsOrderRollbackDOService.updateServiceStatusFail(rollbackFulfilmentId);
            return;
        }
        // 查询是否存在状态为"激活关闭中"或"激活关闭失败"的记录
        boolean hasActivationPending = vcsOrderRollbackRecordsMapper.selectList(new LambdaQueryWrapper<VcsOrderRollbackRecords>()
                        .eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackFulfilmentId)
                        .eq(BaseDO::getIsDeleted, false))
                .stream()
                .anyMatch(rollbackRecords ->
                        CancelServiceStatusEnum.UNACTIVATED.getStatus().equals(rollbackRecords.getActivationStatus()) ||
                                CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus().equals(rollbackRecords.getActivationStatus()));

        // 如果不存在，则更新履约单状态为"业务激活关闭失败"
        if (!hasActivationPending) {
            vcsOrderRollbackDOService.updateServiceStatusFail(rollbackFulfilmentId);
        }
    }

    /**
     * 检查是否需要满足激活条件。
     *
     * @param fulfilmentRecords 订单履行记录列表，包含每个服务包的激活状态。
     * @return 如果所有服务包都满足激活条件，则返回true；否则返回false。
     */
    private boolean checkNeedActivationFulfilment(List<VcsOrderFufilmentRecords> fulfilmentRecords) {
        if (CollUtil.isEmpty(fulfilmentRecords)) {
            log.info("检查是否需要满足激活条件,fulfilmentRecords为空");
            return false;
        }
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            if (ServicePackageEnum.ONLINE_PACK.getPackageName().equals(fulfilmentRecord.getServicePackage())){
                if (ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus().equals(fulfilmentRecord.getActivationStatus())
                        || ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus().equals(fulfilmentRecord.getActivationStatus())){
                    return false;
                }
            } else if (ServicePackageEnum.DATA_PLAN.getPackageName().equals(fulfilmentRecord.getServicePackage()) &&
                    !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            } else if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(fulfilmentRecord.getServicePackage())
                    && !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查是否满足回滚条件。
     *
     * @param rollbackRecords 订单履行记录列表，包含每个服务包的激活状态。
     * @return 如果所有服务包都满足激活条件，则返回true；否则返回false。
     */
    private boolean checkNeedActivationOffFulfilment(List<VcsOrderRollbackRecords> rollbackRecords) {
        if (CollUtil.isEmpty(rollbackRecords)) {
            log.info("检查是否满足回滚条件,rollbackRecords为空");
            return false;
        }
        for (VcsOrderRollbackRecords records : rollbackRecords) {
            if (!CancelServiceStatusEnum.ACTIVATE_OFF.getStatus().equals(records.getActivationStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 调用远程服务进行订单履行激活关闭补偿
     *
     * @param records 订单履行记录
     * @param fulfilmentDO 订单履行数据对象
     * @param remoteServiceMap 远程服务映射，键为车架号，值为订阅服务列表
     * @param fulfilmentId 履行单ID
     * @param message 履行消息
     * @param retryTimes 重试次数
     */
    private void callRollbackRemoteService(VcsOrderRollbackRecords records, VcsOrderFufilmentDO fulfilmentDO, Map<String, List<SubscriptionServiceDO>> remoteServiceMap, String fulfilmentId, CancelMessage message, Integer retryTimes) {
        if (CollUtil.isNotEmpty(remoteServiceMap) && CollUtil.isNotEmpty(remoteServiceMap.get(fulfilmentDO.getCarVin()))) {
            List<SubscriptionServiceDO> serviceDOS = remoteServiceMap.get(fulfilmentDO.getCarVin());
            // 查询incontrolId
            IcrVehicleRespVO vehicleRespVO = incontrolVehicleDOService.getOneByCarVin(fulfilmentDO.getCarVin());
            if (Objects.isNull(vehicleRespVO)) {
                log.info("REMOTE激活关闭补偿失败, incontrolVehicle为空, fulfilmentDO={}", fulfilmentDO);
                return;
            }
            RemoteModifyRespDTO result = remoteCallService.listenerCallTSDPRenew(serviceDOS, fulfilmentId, message, vehicleRespVO.getIncontrolId());
            if (!result.isSuccess()) {
                log.info("REMOTE激活关闭补偿失败, fulfilmentId={}, 当前重试次数={}", fulfilmentId, retryTimes);
                updateRollbackRetryFailRecord(records.getRollbackFufilmentId(), records.getServicePackage(), retryTimes);
                return;
            }
            log.info("REMOTE激活关闭补偿成功, fulfilmentId={}", fulfilmentId);
            // 更新回滚履约记录表
            updateRollbackRecord(records.getRollbackFufilmentId(), ExpirationServiceEnum.REMOTE.getType());
        }
    }
}
