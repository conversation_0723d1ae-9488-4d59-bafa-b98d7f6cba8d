package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.jlr.ecp.subscription.dal.mysql.icroder.SeriesBrandMappingDataDOMapper;
import com.jlr.ecp.subscription.dal.repository.SeriesBrandMappingDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 车系品牌映射数据Repository实现类
 *
 */
@Component
@Slf4j
public class SeriesBrandMappingDataRepositoryImpl extends ServiceImpl<SeriesBrandMappingDataDOMapper, SeriesBrandMappingDataDO> implements SeriesBrandMappingDataRepository {

    @Override
    public SeriesBrandMappingDataDO findBySeriesCode(String seriesCode) {
        log.info("根据系列代码查询映射数据, seriesCode: {}", seriesCode);
        return getOne(new LambdaQueryWrapperX<SeriesBrandMappingDataDO>()
                .eq(SeriesBrandMappingDataDO::getSeriesCode, seriesCode)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(BaseDO::getCreatedTime)
                .last("LIMIT 1"));
    }

    @Override
    public List<SeriesBrandMappingDataDO> findBySeriesCodeList(List<String> seriesCodeList) {
        log.info("根据系列代码列表查询映射数据, seriesCodeList size: {}", seriesCodeList.size());
        return list(new LambdaQueryWrapperX<SeriesBrandMappingDataDO>()
                .in(SeriesBrandMappingDataDO::getSeriesCode, seriesCodeList)
                .eq(BaseDO::getIsDeleted, false));
    }

    @Override
    public int insert(SeriesBrandMappingDataDO mappingDataDO) {
        log.info("插入单个映射数据, seriesCode: {}", mappingDataDO.getSeriesCode());
        return save(mappingDataDO) ? 1 : 0;
    }

    @Override
    public boolean updateById(SeriesBrandMappingDataDO mappingDataDO) {
        log.info("更新映射数据, id: {}, seriesCode: {}", mappingDataDO.getId(), mappingDataDO.getSeriesCode());
        return super.updateById(mappingDataDO);
    }

    @Override
    public boolean insertBatch(List<SeriesBrandMappingDataDO> mappingDataList) {
        log.info("批量插入映射数据, size: {}", mappingDataList.size());
        try {
            baseMapper.insertBatch(mappingDataList);
        } catch (Exception e) {
            log.info("批量插入车系品牌映射异常：", e);
            return false;
        }
        return true;
    }

    @Override
    public List<SeriesBrandMappingDataDO> findAllValid() {
        log.info("查询所有有效的映射数据");
        return list(new LambdaQueryWrapperX<SeriesBrandMappingDataDO>()
                .eq(BaseDO::getIsDeleted, false));
    }

    @Override
    public SeriesBrandMappingDataDO findById(Long id) {
        log.info("根据ID查询映射数据, id: {}", id);
        return getById(id);
    }
}
