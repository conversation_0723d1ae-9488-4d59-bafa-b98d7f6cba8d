package com.jlr.ecp.subscription.controller.admin.dto.manuallog;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "manuallog - 分页 Response VO")
@Data
public class ManualLogPageRespVO {

    @Schema(description = "VIN")
    private String carVin;

    @Schema(description = "修改日志类型")
    private String modifyType;

    @Schema(description = "修改日志类型-描述")
    private String modifyTypeText;

    /**
     * 修改前值;修改前值
     */
    @Schema(description = "修改前值")
    private String modifyBeforeValue;

    /**
     * 修改后值;修改后值
     */
    @Schema(description = "修改后值")
    private String modifyAfterValue;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "通过创建时间排序方式（asc：升序，desc：降序）")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

}
