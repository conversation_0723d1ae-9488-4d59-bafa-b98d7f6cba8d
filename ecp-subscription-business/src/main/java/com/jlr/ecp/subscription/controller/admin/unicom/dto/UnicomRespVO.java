package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 联通返回公共参数
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnicomRespVO implements Serializable {


    /**
     * 应答编码
     * (0000成功，非0000为失败)
     */
    @JsonProperty("resp_code")
    private String responseCode;


    /**
     * 应答描述
     */
    @JsonProperty("resp_desc")
    private String responseDesc;


    /**
     * 返回数据包
     */
    @JsonProperty("response_data")
    private UnicomRespData unicomRespData;

    /**
     * oss查询结果
     */
    private String queryResult;
}