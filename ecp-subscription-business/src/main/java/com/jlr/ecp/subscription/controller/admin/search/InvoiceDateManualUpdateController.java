package com.jlr.ecp.subscription.controller.admin.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualPageParam;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.vo.PIVIInvoiceDateUpdateVO;
import com.jlr.ecp.subscription.service.pivi.PIVIInvoiceDateManualUpdateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


@RestController
@RequestMapping("/pivi/invoiceDate")
@Tag(name = "pivi-手动修改发票")
public class InvoiceDateManualUpdateController {
    @Resource
    private PIVIInvoiceDateManualUpdateService piviInvoiceDateManualUpdateService;
    @GetMapping("/query/carVin")
    @Operation(summary = "查询carVin是否存在")
    @PreAuthorize("@ss.hasPermission('subscribe:vehicle-invoice:forms')")
    public CommonResult<String> queryCarVin(@RequestParam String carVin) {
        return piviInvoiceDateManualUpdateService.queryCarVinFromECP(carVin);
    }

    @PostMapping("/get/update/record")
    @Operation(summary = "查询发票编辑记录")
    @PreAuthorize("@ss.hasPermission('subscribe:vehicle-invoice:forms')")
    public CommonResult<PageResult<PIVIInvoiceDateUpdateVO>> queryUpdateRecord(@RequestBody InvoiceDateManualPageParam param){
        return piviInvoiceDateManualUpdateService.queryManualInvoiceDateByCarVin(param);
    }

    @PostMapping("/manual/update")
    @Operation(summary = "pivi手动修改发票时间")
    @PreAuthorize("@ss.hasPermission('subscribe:vehicle-invoice:forms')")
    public CommonResult<String> manualUpdate(@Valid @RequestBody InvoiceDateManualUpdateDTO manualUpdateDTO) {
        return piviInvoiceDateManualUpdateService.manualUpdateInvoiceDate(manualUpdateDTO);
    }
}
