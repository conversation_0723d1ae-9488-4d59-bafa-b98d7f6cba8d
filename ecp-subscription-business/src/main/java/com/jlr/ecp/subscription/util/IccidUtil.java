package com.jlr.ecp.subscription.util;

import cn.smallbun.screw.core.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IccidUtil {

    // 静态成员变量存储预先编译的正则表达式模式
    private static final Pattern ICCID_PATTERN = Pattern.compile("^\\d{20}$");

    /**
     * 检查ICCID是否有效。
     *
     * @param iccid ICCID
     * @return 如果ICCID有效返回true，否则返回false
     */
    public static boolean checkIccidFormat(String iccid) {
        if (StringUtils.isBlank(iccid)) {
            return false;
        }
        Matcher matcher = ICCID_PATTERN.matcher(iccid);
        return matcher.matches();
    }

    /**
     * 校验ICCID格式并返回提示信息。
     *
     * @param iccid ICCID
     * @return 如果ICCID有效返回空字符串，否则返回"ICCID格式错误"
     */
    public static String validateIccidFormat(String iccid) {
        if (!checkIccidFormat(iccid)) {
            return "ICCID格式错误";
        }
        return "";
    }

    public static void main(String[] args) {
        // 测试用例
        String iccid1 = "12345678901234567890"; // 有效ICCID
        String iccid2 = "1234567890123456789";  // 无效ICCID（长度不足20位）

        String iccid3 = "  1234567890  1234567890  "; //测试空格

        System.out.println(checkIccidFormat(iccid1)); // 输出：空字符串
        System.out.println(checkIccidFormat(iccid2)); // 输出：ICCID格式错误

        System.out.println(iccid3.replaceAll("\\s+", ""));
    }
}