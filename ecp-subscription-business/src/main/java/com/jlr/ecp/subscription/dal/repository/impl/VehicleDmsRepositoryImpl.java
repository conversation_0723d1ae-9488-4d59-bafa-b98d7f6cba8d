package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper;
import com.jlr.ecp.subscription.dal.repository.VehicleDmsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 车辆DMS信息Repository实现类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class VehicleDmsRepositoryImpl extends ServiceImpl<VehicleDmsDOMapper, VehicleDmsDO> implements VehicleDmsRepository {

    @Override
    public VehicleDmsDO findByCarVin(String carVin) {
        log.info("根据车辆VIN查询DMS信息, carVin: {}", carVin);
        return baseMapper.selectVehicleDmsDOByCarVin(carVin);
    }

    @Override
    public List<VehicleDmsDO> findByCarVinList(List<String> carVinList) {
        log.info("根据车辆VIN列表查询DMS信息, carVinList size: {}", carVinList.size());
        return baseMapper.selectVehicleDmsDOByCarVinList(carVinList);
    }

    @Override
    public boolean batchInsert(List<VehicleDmsDO> vehicleDmsList) {
        log.info("批量插入DMS信息, size: {}", vehicleDmsList.size());
        try {
            baseMapper.insertBatch(vehicleDmsList);
        } catch (Exception e) {
            log.info("批量插入DMS信息，异常： ", e);
            return false;
        }
        return true;
    }
}
