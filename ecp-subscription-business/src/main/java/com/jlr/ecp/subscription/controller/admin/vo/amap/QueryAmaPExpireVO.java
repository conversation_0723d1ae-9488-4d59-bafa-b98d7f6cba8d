package com.jlr.ecp.subscription.controller.admin.vo.amap;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP批量查询结果VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class QueryAmaPExpireVO {
    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "车架号")
    private String carVin;

    @Schema(description = "查询状态")
    private Integer queryStatus;

    @Schema(description = "查询状态描述")
    private String queryStatusDesc;


    @Schema(description = "在线导航到期日 年/月/日")
    private String amaPExpireDate;

    @Schema(description = "incontrol在线服务到期日 年/月/日")
    private String ecpExpireDate;

    @Schema(description = "在线导航服务状态")
    private Integer serviceStatus;

    @Schema(description = "在线导航服务状态描述")
    private String serviceStatusDesc;

    @Schema(description = "操作时间 年/月/日 时/分/秒")
    private String operateTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "失败信息")
    private String errorInfo;
}
