package com.jlr.ecp.subscription.controller.admin.dto.remote;

import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "过期服务查询出参")
public class RemoteSearchResultDTO {

    @Schema(description = "vin")
    private String carVin;

    @Schema(description = "incontrolId")
    private String incontrolId;

    @Schema(description = "是否在ecp存在")
    private boolean existInEcp;

    @Schema(description = "是否为PIVI车机")
    private boolean piviModel;

    @Schema(description = "续费前到期时间")
    private LocalDateTime beforeExpiryDate;

    @Schema(description = "服务指定到期日")
    private LocalDateTime afterExpiryDate;

    @Schema(description = "服务数据集合")
    private List<SubscriptionServiceDO> serviceDOList;
}
