package com.jlr.ecp.subscription.service.pivi;


import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;

import java.util.List;

public interface PIVIFulfilmentService {
    void updatePIVIOrderFulfilment(String vcsOrderCode, String fulfilmentId);
    void updatePIVIOrderFulfilmentBySixYear(String vcsOrderCode, String fulfilmentId);
    void updateOrderAmapRecords(String vcsOrderCode, String fulfilmentId);

    void updatePIVIFulfilmentByVcsOrder(List<VcsOrderFufilmentDO> vcsOrderFulfilmentDOList);
}
