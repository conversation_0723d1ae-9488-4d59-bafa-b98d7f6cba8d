package com.jlr.ecp.subscription.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;
import software.amazon.awssdk.services.sns.model.SnsException;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;
import software.amazon.awssdk.services.sts.model.Credentials;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SnsUtil {

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${sns.accessKeyId}")
    private String accessKeyId;

    @Value("${sns.secretAccessKey}")
    private String secretAccessKey;

    @Value("${sns.roleArn}")
    private String roleArn;

    @Value("${sns.topicArn}")
    private String topicArn;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    public static final String ROLE_SESSION_NAME = "ExecutorSession";

    /**
     * 获取 SnsClient 实例
     * 通过STS（Security Token Service）假设角色获取临时安全凭证
     * 以获得 SnsClient 的访问权限
     *
     * @return SnsClient 实例，用于后续的SNS服务调用
     */
    public SnsClient getSnsClient() {
        JSONObject credentialsJson = new JSONObject();
        // 判断redis是否存在临时凭证
        String tempCredentialsVal = redisTemplate.opsForValue().get(ROLE_SESSION_NAME);
        if(StringUtils.isBlank(tempCredentialsVal)){
            Credentials tempCredentials = getCredentials();
            if(tempCredentials == null){
                return null;
            }
            credentialsJson.put("accessKeyId", tempCredentials.accessKeyId());
            credentialsJson.put("secretAccessKey", tempCredentials.secretAccessKey());
            credentialsJson.put("sessionToken", tempCredentials.sessionToken());
            redisTemplate.opsForValue().set(ROLE_SESSION_NAME, credentialsJson.toJSONString(), 50, TimeUnit.MINUTES);
        }else {
            credentialsJson = JSON.parseObject(tempCredentialsVal);
        }

        // 创建临时凭证
        AwsSessionCredentials sessionCredentials = AwsSessionCredentials.create(
                credentialsJson.getString("accessKeyId"),
                credentialsJson.getString("secretAccessKey"),
                credentialsJson.getString("sessionToken")
        );

        // 创建 SnsClient
        return SnsClient.builder()
                .region(Region.CN_NORTHWEST_1) // AWS China (Ningxia) region
                .credentialsProvider(StaticCredentialsProvider.create(sessionCredentials))
                .build();
    }

    /**
     * 获取临时凭证
     *
     * @return 返回临时凭证对象，如果获取过程中发生异常，则返回null
     */
    private Credentials getCredentials() {
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);

        // Create AssumeRoleRequest
        try (StsClient stsClient = StsClient.builder()
                .region(Region.CN_NORTHWEST_1) // AWS China (Ningxia) region
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .build()) {
            AssumeRoleRequest assumeRoleRequest = AssumeRoleRequest.builder()
                    .roleArn(roleArn)
                    .roleSessionName(ROLE_SESSION_NAME)
                    .build();
            // Call AssumeRole API
            AssumeRoleResponse assumeRoleResponse = stsClient.assumeRole(assumeRoleRequest);
            // Get the temporary credentials
            Credentials tempCredentials = assumeRoleResponse.credentials();

            // Print the temporary credentials
            log.info("AccessKeyId:{}", tempCredentials.accessKeyId());
            log.info("SecretAccessKey:{}", tempCredentials.secretAccessKey());
            log.info("SessionToken:{}", tempCredentials.sessionToken());
            return tempCredentials;
        } catch (Exception e) {
            log.info("AssumeRole Failed, Message:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 发布消息到指定的SNS主题
     *
     * @param message 要发布的消息内容
     */
    public void publishTopic(String message) {
        if(StringUtils.isBlank(message)){
            return;
        }
        StringBuilder sb = new StringBuilder("环境:")
                .append(env)
                .append(">>>>>")
                .append(message);
        try (SnsClient snsClient = getSnsClient()) {
            if (snsClient == null) {
                return;
            }
            PublishRequest request = PublishRequest.builder()
                    .message(sb.toString())
                    .topicArn(topicArn)
                    .build();

            PublishResponse result = snsClient.publish(request);
            log.info("Publish SNS Success, Message ID:{}, Status:{}", result.messageId(), result.sdkHttpResponse().statusCode());
        } catch (SnsException e) {
            log.info("Publish SNS Failed, Message:{}", e.awsErrorDetails().errorMessage());
        }
    }
}
