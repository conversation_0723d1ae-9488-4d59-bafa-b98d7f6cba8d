package com.jlr.ecp.subscription.dal.mysql.remote;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

@Mapper
public interface RemoteRenewDetailRecordsMapper extends BaseMapperX<RemoteRenewDetailRecords> {

    /**
     * 根据vin获取进行中的续费记录
     *
     * @param vin vin，用于筛选特定的记录
     * @return 符合条件的RemoteRenewDetailRecords记录列表
     */
    default List<RemoteRenewDetailRecords> getProcessRecordsByVin(String vin) {
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewDetailRecords::getCarVin, vin)
                .eq(RemoteRenewDetailRecords::getModifyStatus, RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus())
                .eq(RemoteRenewDetailRecords::getIsDeleted, false);
        return selectList(queryWrapper);
    }

    /**
     * 根据vinSet和batchNo获取进行中的续费记录
     *
     * @param vinSet vinSet，用于筛选特定的记录
     * @return 符合条件的RemoteRenewDetailRecords记录列表
     */
    default List<RemoteRenewDetailRecords> getProcessRecordsByVinSetAndBatchNo(Set<String> vinSet, Long batchNo) {
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RemoteRenewDetailRecords::getCarVin, vinSet)
                .ne(RemoteRenewDetailRecords::getBatchNo, batchNo)
                .eq(RemoteRenewDetailRecords::getModifyStatus, RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus())
                .eq(RemoteRenewDetailRecords::getIsDeleted, false);
        return selectList(queryWrapper);
    }
}
