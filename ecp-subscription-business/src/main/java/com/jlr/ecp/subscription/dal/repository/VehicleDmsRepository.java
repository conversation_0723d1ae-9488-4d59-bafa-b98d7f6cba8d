package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;

import java.util.List;

/**
 * 车辆DMS信息Repository接口
 *
 */
public interface VehicleDmsRepository extends IService<VehicleDmsDO> {

    /**
     * 根据车辆VIN查询DMS信息
     *
     * @param carVin 车辆VIN
     * @return DMS信息
     */
    VehicleDmsDO findByCarVin(String carVin);

    /**
     * 根据车辆VIN列表查询DMS信息
     *
     * @param carVinList 车辆VIN列表
     * @return DMS信息列表
     */
    List<VehicleDmsDO> findByCarVinList(List<String> carVinList);

    /**
     * 批量插入DMS信息
     *
     * @param vehicleDmsList DMS信息列表
     * @return 插入数量
     */
    boolean batchInsert(List<VehicleDmsDO> vehicleDmsList);
}
