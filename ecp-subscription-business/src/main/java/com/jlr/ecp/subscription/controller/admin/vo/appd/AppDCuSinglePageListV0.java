package com.jlr.ecp.subscription.controller.admin.vo.appd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "APPD和联通单个续费分页VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AppDCuSinglePageListV0 {

    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "VIN")
    private String carVin;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "续费服务名称")
    private String renewalServiceName;

    @Schema(description = "续费编号")
    private String batchNo;
}
