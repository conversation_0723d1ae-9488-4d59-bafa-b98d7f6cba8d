package com.jlr.ecp.subscription.service.manuallog.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageReqDTO;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageRespVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.manuallog.ManualModifyLogDO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;
import com.jlr.ecp.subscription.dal.mysql.manuallog.ManualModifyLogDOMapper;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.manuallog.ManualModifyLogTypeEnum;
import com.jlr.ecp.subscription.enums.remotepackage.SortOrder;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.constant.Constants.DEFAULT_CONCAT_STR;
import static com.jlr.ecp.subscription.util.TimeFormatUtil.formatter_7;

/**
* <AUTHOR>
* @description 针对表【t_manual_modify_log(t_manual_modify_log)】的数据库操作Service实现
* @createDate 2024-11-19 19:44:40
*/
@Slf4j
@Service
public class ManualModifyLogDOServiceImpl extends ServiceImpl<ManualModifyLogDOMapper, ManualModifyLogDO>
    implements ManualModifyLogDOService{

    @Resource
    private ManualModifyLogDOMapper manualModifyLogDOMapper;
    @Override
    public PageResult<ManualLogPageRespVO> getLogPage(ManualLogPageReqDTO dto) {
        // 构建分页
        Page<ManualModifyLogDO> page = new Page<>(dto.getPageNo(), dto.getPageSize());

        // 构建查询条件
        LambdaQueryWrapperX<ManualModifyLogDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(ManualModifyLogDO::getIsDeleted, 0)
                .eqIfPresent(ManualModifyLogDO::getCarVin,dto.getVin())
                .eqIfPresent(ManualModifyLogDO::getModifyType,dto.getModifyType())
                .geIfPresent(ManualModifyLogDO::getOperateTime,dto.getStartTime())
                .leIfPresent(ManualModifyLogDO::getOperateTime,dto.getEndTime())
                .eqIfPresent(ManualModifyLogDO::getOperator, dto.getOperator());

        if (Objects.nonNull(dto.getOperateTimeSort())) {
            boolean isAscending = SortOrder.ASCENDING.getCode().equals(dto.getOperateTimeSort());
            queryWrapper.orderBy(true, isAscending, ManualModifyLogDO::getOperateTime);
            queryWrapper.orderBy(true, isAscending, ManualModifyLogDO::getId);
        }

        // 执行查询
        page = manualModifyLogDOMapper.selectPage(page, queryWrapper);


        // 转换为VO
        List<ManualLogPageRespVO> respVOList = page.getRecords().stream()
                .map(manual -> {
                    ManualLogPageRespVO manualLogPageRespVO = new ManualLogPageRespVO();
                    BeanUtil.copyProperties(manual,manualLogPageRespVO);
                    manualLogPageRespVO.setModifyTypeText(ManualModifyLogTypeEnum.getDescriptionByType(Integer.valueOf(manual.getModifyType())));
                    return manualLogPageRespVO;
                })
                .collect(Collectors.toList());

        // 构建返回结果
        return new PageResult<>(respVOList, page.getTotal());
    }

    /**
     * 记录单个实体的日志。
     *
     * @param entity 实体对象
     * @param converter 转换函数，将实体对象转换为ManualModifyLogDO
     * @param <T> 实体类型
     */
    public <T> void recordLog(T entity, Function<T, ManualModifyLogDO> converter) {
        ManualModifyLogDO logDO = converter.apply(entity);
        manualModifyLogDOMapper.insert(logDO);
    }

    /**
     * 记录多个实体的日志。
     *
     * @param entities 实体列表
     * @param converter 转换函数，将实体对象转换为ManualModifyLogDO
     * @param <T> 实体类型
     */
    public <T> void recordLogList(List<T> entities, Function<T, ManualModifyLogDO> converter) {
        List<ManualModifyLogDO> logDOs = entities.stream()
                .map(converter)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(logDOs)) {
            manualModifyLogDOMapper.insertBatch(logDOs);
        }
    }

    /**
     * 记录AmaPRenewRecordsDO的日志。
     *
     * @param amaPRenewRecordsDO AmaPRenewRecordsDO对象
     */
    @Override
    public void recordLog(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        recordLog(amaPRenewRecordsDO, this::convertAmaPRenewRecordsDO);
    }

    /**
     * 记录AmaPRenewRecordsDO列表的日志。
     *
     * @param amaPRenewRecordsDOList AmaPRenewRecordsDO对象列表
     */
    @Override
    public void recordLogAmapList(List<AmaPRenewRecordsDO> amaPRenewRecordsDOList) {
        recordLogList(amaPRenewRecordsDOList, this::convertAmaPRenewRecordsDO);
    }

    /**
     * 记录IccidModifyRecordsDO的日志。
     *
     * @param iccidModifyRecordsDO IccidModifyRecordsDO对象
     */
    @Override
    public void recordLog(IccidModifyRecordsDO iccidModifyRecordsDO) {
        recordLog(iccidModifyRecordsDO, this::convertIccidModifyRecordsDO);
    }

    /**
     * 记录IccidModifyRecordsDO列表的日志。
     *
     * @param iccidModifyRecordsDOList IccidModifyRecordsDO对象列表
     */
    @Override
    public void recordLogIccIdModifyList(List<IccidModifyRecordsDO> iccidModifyRecordsDOList) {
        recordLogList(iccidModifyRecordsDOList, this::convertIccidModifyRecordsDO);
    }

    /**
     * 记录AppDCuRenewRecords的日志。
     *
     * @param appDCuRenewRecords AppDCuRenewRecords对象
     */
    @Override
    public void recordLog(AppDCuRenewRecords appDCuRenewRecords) {
        recordLog(appDCuRenewRecords, this::convertAppDCuRenewRecords);
    }

    /**
     * 记录RemoteRenewDetailRecords的日志。
     *
     * @param remoteRenewDetailRecords RemoteRenewDetailRecords对象
     */
    @Override
    public void recordLog(RemoteRenewDetailRecords remoteRenewDetailRecords) {
        recordLog(remoteRenewDetailRecords, this::convertRemoteRenewDetailRecords);
    }

    /**
     * 记录PIVIPackageLogDO的日志。
     *
     * @param piviPackageLogDO PIVIPackageLogDO对象
     */
    @Override
    public void recordLog(PIVIPackageLogDO piviPackageLogDO) {
        recordLog(piviPackageLogDO, this::convertPIVIPackageLogDO);
    }

    @Override
    public void recordManualLog(PIVIPackageLogDO piviPackageLogDO) {
        recordLog(piviPackageLogDO, this::convertManualPIVIPackageLogDO);
    }

    /**
     * 将AmaPRenewRecordsDO转换为ManualModifyLogDO。
     *
     * @param amaPRenewRecordsDO AmaPRenewRecordsDO对象
     * @return ManualModifyLogDO对象
     */
    private ManualModifyLogDO convertAmaPRenewRecordsDO(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        ManualModifyLogDO logDO = new ManualModifyLogDO();
        logDO.setCarVin(amaPRenewRecordsDO.getCarVin());
        logDO.setModifyBeforeValue(TimeFormatUtil.timeToStringByFormat(amaPRenewRecordsDO.getRenewBeforeExpiryDate(), formatter_7));
        logDO.setModifyAfterValue(TimeFormatUtil.timeToStringByFormat(amaPRenewRecordsDO.getRenewAfterExpiryDate(), formatter_7));
        logDO.setOperator(amaPRenewRecordsDO.getOperator());
        logDO.setModifyType(ManualModifyLogTypeEnum.AMAP_RENEW.getType());
        logDO.setOperateTime(amaPRenewRecordsDO.getUpdatedTime());
        return logDO;
    }

    /**
     * 将IccidModifyRecordsDO转换为ManualModifyLogDO。
     *
     * @param iccidModifyRecordsDO IccidModifyRecordsDO对象
     * @return ManualModifyLogDO对象
     */
    private ManualModifyLogDO convertIccidModifyRecordsDO(IccidModifyRecordsDO iccidModifyRecordsDO) {
        ManualModifyLogDO logDO = new ManualModifyLogDO();
        logDO.setCarVin(iccidModifyRecordsDO.getCarVin());
        logDO.setModifyBeforeValue(iccidModifyRecordsDO.getModifyBeforeIccid());
        logDO.setModifyAfterValue(iccidModifyRecordsDO.getModifyAfterIccid());
        logDO.setModifyType(ManualModifyLogTypeEnum.ICCID_UPDATED.getType());
        logDO.setOperator(iccidModifyRecordsDO.getCreatedBy());
        logDO.setOperateTime(LocalDateTime.now());
        return logDO;
    }

    /**
     * 将AppDCuRenewRecords转换为ManualModifyLogDO。
     *
     * @param appDCuRenewRecords AppDCuRenewRecords对象
     * @return ManualModifyLogDO对象
     */
    private ManualModifyLogDO convertAppDCuRenewRecords(AppDCuRenewRecords appDCuRenewRecords) {
        ManualModifyLogDO logDO = new ManualModifyLogDO();
        logDO.setCarVin(appDCuRenewRecords.getCarVin());
        logDO.setModifyBeforeValue(TimeFormatUtil.timeToStringByFormat(appDCuRenewRecords.getRenewBeforeExpiryDate(), formatter_7));
        logDO.setModifyAfterValue(TimeFormatUtil.timeToStringByFormat(appDCuRenewRecords.getRenewAfterExpiryDate(), formatter_7));
        logDO.setOperator(appDCuRenewRecords.getOperator());
        logDO.setModifyType(RenewServiceTypeEnum.APPD.getServiceType().equals(appDCuRenewRecords.getRenewServiceType())
                ? ManualModifyLogTypeEnum.APPD_RENEW.getType() : ManualModifyLogTypeEnum.UNICOM_RENEW.getType());
        logDO.setOperateTime(appDCuRenewRecords.getUpdatedTime());
        return logDO;
    }

    /**
     * 将RemoteRenewDetailRecords转换为ManualModifyLogDO。
     *
     * @param remoteRenewDetailRecords RemoteRenewDetailRecords对象
     * @return ManualModifyLogDO对象
     */
    private ManualModifyLogDO convertRemoteRenewDetailRecords(RemoteRenewDetailRecords remoteRenewDetailRecords) {
        ManualModifyLogDO logDO = new ManualModifyLogDO();
        logDO.setCarVin(remoteRenewDetailRecords.getCarVin());
        logDO.setModifyBeforeValue(TimeFormatUtil.timeToStringByFormat(remoteRenewDetailRecords.getModifyBeforeDate(), formatter_7));
        logDO.setModifyAfterValue(TimeFormatUtil.timeToStringByFormat(remoteRenewDetailRecords.getModifyAfterDate(), formatter_7));
        logDO.setOperator(remoteRenewDetailRecords.getOperator());
        logDO.setModifyType(ManualModifyLogTypeEnum.REMOTE_RENEW.getType());
        logDO.setOperateTime(remoteRenewDetailRecords.getUpdatedTime());
        return logDO;
    }

    /**
     * 将PIVIPackageLogDO转换为ManualModifyLogDO。
     *
     * @param piviPackageLogDO PIVIPackageLogDO对象
     * @return ManualModifyLogDO对象
     */
    private ManualModifyLogDO convertPIVIPackageLogDO(PIVIPackageLogDO piviPackageLogDO) {
        ManualModifyLogDO logDO = new ManualModifyLogDO();
        logDO.setCarVin(piviPackageLogDO.getCarVin());
        logDO.setModifyBeforeValue(TimeFormatUtil.timeToStringByFormat(piviPackageLogDO.getModifyInvoiceDateBefore(), formatter_7));
        logDO.setModifyAfterValue(TimeFormatUtil.timeToStringByFormat(piviPackageLogDO.getModifyInvoiceDateAfter(), formatter_7));
        logDO.setOperator(piviPackageLogDO.getModifyUser());
        logDO.setModifyType(ManualModifyLogTypeEnum.INVOICE_DATE_UPDATED.getType());
        logDO.setOperateTime(LocalDateTime.now());
        return logDO;
    }

    /**
     * 将PIVIPackageLogDO转换为ManualModifyLogDO。
     *
     * @param piviPackageLogDO piviPackageLogDO
     * @return ManualModifyLogDO对象
     */
    private ManualModifyLogDO convertManualPIVIPackageLogDO(PIVIPackageLogDO piviPackageLogDO) {
        ManualModifyLogDO logDO = new ManualModifyLogDO();
        logDO.setCarVin(piviPackageLogDO.getCarVin());
        logDO.setModifyBeforeValue(DEFAULT_CONCAT_STR);
        logDO.setModifyAfterValue(DEFAULT_CONCAT_STR);
        logDO.setOperator(piviPackageLogDO.getModifyUser());
        logDO.setModifyType(ManualModifyLogTypeEnum.MANUAL_VIN_ADDED.getType());
        logDO.setOperateTime(LocalDateTime.now());
        return logDO;
    }
}




