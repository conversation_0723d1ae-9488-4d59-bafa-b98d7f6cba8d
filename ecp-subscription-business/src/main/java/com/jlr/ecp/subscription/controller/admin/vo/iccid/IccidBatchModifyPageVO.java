package com.jlr.ecp.subscription.controller.admin.vo.iccid;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "ICCID批量修改分页查询的VO")
public class IccidBatchModifyPageVO {

    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "校验结果状态 0:不通过 1:通过")
    private Integer checkResultStatus;

    @Schema(description = "校验结果描述")
    private String checkResultDesc;

    @Schema(description = "批量修改状态 0:待处理 1:处理中 2:已处理")
    private Integer dealStatus;

    @Schema(description = "批量修改编号")
    private Long batchNo;

    @Schema(description = "错误详情地址")
    private String errorDetailPath;
}