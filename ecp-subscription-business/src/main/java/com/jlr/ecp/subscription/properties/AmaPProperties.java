package com.jlr.ecp.subscription.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "amap")
@Configuration
@Data
public class AmaPProperties {

    /**
     * url
     */
    private String url;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     *  一年商品id（同commodity_no），用于标识商品。 由高德提供。需在对接时向高德项目接口人申请
     * */
    private String oneYearCid;

    /**
     *  三年商品id（同commodity_no），用于标识商品。 由高德提供。需在对接时向高德项目接口人申请
     * */
    private String threeYearCid;

    /**
     *  租户pid (查询过期vin使用)
     * */
    private String pid;

    /**
     *  AMAP批量续费的的Excel的模板地址
     * */
    private String renewalExcelUrl;

    /**
     *  AMAP批量查询的的Excel的模板地址
     * */
    private String queryExcelUrl;
}

