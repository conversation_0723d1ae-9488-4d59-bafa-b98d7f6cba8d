package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalOperateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalYearVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPSingleRenewalPageVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPDataSourceEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPErrorCode;
import com.jlr.ecp.subscription.enums.amap.AmaPRenewStatusEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPRenewalYearEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceModifyTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchResponseDTO;
import com.jlr.ecp.subscription.model.dto.AmaPOrderChargeResponseDTO;
import com.jlr.ecp.subscription.model.dto.AmaPOrderChargerRequestDTO;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.amap.AmaPSingleManualRenewalService;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.LoginUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AmaPSingleManualRenewalServiceImpl implements AmaPSingleManualRenewalService {
    @Resource
    private AmaPRenewRecordsDOMapper amapRenewRecordsDOMapper;

    @Resource
    private AmaPProperties amaPProperties;

    @Resource
    private Snowflake snowflake;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource(name = "subscribeScheduledThreadPool")
    private ThreadPoolTaskScheduler subscribeScheduledThreadPool;

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private SubscriptionServiceMapper serviceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper serviceLogMapper;

    @Resource
    private PIVIPackageDOMapper pivIPackageMapper;

    @Resource
    private OrderCheckService orderCheckService;


    /**
     *  AMAP单个续费分页查询列表
     *
     * @param amaPPageDto 续费页面的查询参数
     * @return 续费页面的数据列表和总记录数
     */
    @Override
    public PageResult<AmaPSingleRenewalPageVO> queryRenewalPageList(AmaPSingleRenewalPageDTO amaPPageDto) {
        log.info("AMAP单个续费分页查询列表, amaPPageDto:{}", amaPPageDto);
        Page<AmaPRenewRecordsDO> amaPPage = queryRenewalRecord(AmaPDataSourceEnum.SINGLE.getDateSource(), amaPPageDto);
        if (Objects.isNull(amaPPage) || CollUtil.isEmpty(amaPPage.getRecords())) {
            return new PageResult<>();
        }
        List<AmaPSingleRenewalPageVO> amaPPageVOList = buildAmaPSingleRenewalPageVOList(amaPPage.getRecords());
        return new PageResult<>(amaPPageVOList, amaPPage.getTotal());
    }

    /**
     * 获取续费年份列表
     *
     * @return 一个包含所有续费年份信息的列表
     */
    @Override
    public List<AmaPRenewalYearVO> getAmaPRenewalYear() {
        List<AmaPRenewalYearVO> resp = new ArrayList<>();
        AmaPRenewalYearEnum[] values = AmaPRenewalYearEnum.values();
        for (AmaPRenewalYearEnum yearEnum : values) {
            AmaPRenewalYearVO amaPRenewalYearVO = AmaPRenewalYearVO.builder()
                    .code(yearEnum.getCode())
                    .desc(yearEnum.getDesc())
                    .build();
            resp.add(amaPRenewalYearVO);
        }
        return resp;
    }

    /**
     * 手动续费单个AMAP
     *
     * @param operateDTO 操作数据传输对象，包含续费所需的信息
     * @return CommonResult<String> 返回续费操作的结果
     */
    @Override
    public CommonResult<String> amaPSingleManualRenewal(AmaPSingleRenewalOperateDTO operateDTO) {
        log.info("手动续费单个AMAP, operateDTO:{}", operateDTO);
        CommonResult<String> checkResult = checkSingleRenewalArgs(operateDTO);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        // sprint47:校验是否存在续费中的记录
        if (checkProcessRecord(operateDTO.getCarVin())) {
            return CommonResult.error(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR);
        }

        // sprint47:校验是否存在在途订单
        CommonResult<String> checkOrderInTransit = orderCheckService.checkOrderInTransit(operateDTO.getCarVin(), ServiceTypeEnum.PIVI);
        if (!checkOrderInTransit.isSuccess()) {
            return checkOrderInTransit;
        }
        String cusOrderId = snowflake.nextIdStr();
        AmaPOrderChargerRequestDTO requestDTO = buildAmaPOrderChargerRequestDTO(operateDTO, cusOrderId);
        log.info("手动续费单个AMAP, 获取高德请求入参requestDTO:{}", requestDTO);
        AmaPRenewRecordsDO amaPRenewRecordsDO = addSingleAmaPRenewRecordsDO(operateDTO, cusOrderId);
        AmaPOrderChargeResponseDTO chargeResponseDTO = new AmaPOrderChargeResponseDTO();
        // sprint47:并发控制
        CommonResult<AmaPOrderChargeResponseDTO> result = piviAmaPService.concurrentCallAMapRenew(requestDTO);
        if (!result.isSuccess()) {
            chargeResponseDTO.setCode(String.valueOf(result.getCode()));
            chargeResponseDTO.setMessage(result.getMsg());
        } else {
            chargeResponseDTO = result.getData();
        }
        updateSingleAmaPRenewResult(amaPRenewRecordsDO, chargeResponseDTO);
        if (Objects.nonNull(chargeResponseDTO) && AmaPErrorCode.SUCCESSFUL.getCode().equals(chargeResponseDTO.getCode())) {
            queryAmaPChargeAndUpdateRenewResult(amaPRenewRecordsDO);
        }
        return CommonResult.success("请求已发送，请前往续费记录查看结果");
    }

    /**
     * 根据车架号检查是否存在续费中的高德手动续费记录
     * 此方法旨在确保车架号对应的车辆没有未完成的续费流程，以避免重复处理或系统错误
     *
     * @param carVin 车辆识别号（VIN），用于查询续费记录
     * @return Boolean 返回是否存在记录的布尔值
     */
    @Override
    public boolean checkProcessRecord(String carVin) {
        List<AmaPRenewRecordsDO> processRecordList = amapRenewRecordsDOMapper.getProcessRecordByVin(carVin);
        if (CollUtil.isNotEmpty(processRecordList)) {
            log.info("vin:{}存在续费中的高德手动续费记录", carVin);
        }
        return CollUtil.isNotEmpty(processRecordList);
    }

    /**
     * 更新单个AMAP续费结果
     *
     * @param amaPRenewRecordsDO 续费记录实体对象，包含续费的相关信息和状态
     * @param chargeResponseDTO 充值接口响应对象，包含充值操作的结果代码和描述信息
     */
    private void updateSingleAmaPRenewResult(AmaPRenewRecordsDO amaPRenewRecordsDO,
                                             AmaPOrderChargeResponseDTO chargeResponseDTO) {
        if (Objects.isNull(amaPRenewRecordsDO) || Objects.isNull(chargeResponseDTO)) {
            return ;
        }
        amaPRenewRecordsDO.setOrderResultCode(chargeResponseDTO.getCode());
        if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(chargeResponseDTO.getCode())) {
            amaPRenewRecordsDO.setErrorDesc(chargeResponseDTO.getMessage());
            amaPRenewRecordsDO.setRenewStatus(AmaPRenewStatusEnum.RENEW_FAIL.getStatus());
        }
        amaPRenewRecordsDO.setUpdatedTime(LocalDateTime.now());
        amapRenewRecordsDOMapper.updateById(amaPRenewRecordsDO);

    }

    /**
     * 查询AmaP充电信息并更新续订结果
     *
     * @param amaPRenewRecordsDO 包含需要查询的客户订单ID
     */
    public void queryAmaPChargeAndUpdateRenewResult(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        try {
            AmaPSingleManualRenewalServiceImpl self = applicationContext.getBean(getClass());
            Date oneMinutesLater = new Date(System.currentTimeMillis() + 60 * 1000);
            subscribeScheduledThreadPool.schedule(() ->
                            self.searchAmaPChargeInfoAndUpdateRenewResult(amaPRenewRecordsDO),
                    oneMinutesLater);
        } catch (Exception e) {
            log.info("查询AmaP充电信息并更新续订结果, 异常信息:{}", e.getMessage());
        }
    }

    /**
     * 查询AMAP信息并更新续订结果
     *
     * @param amaPRenewRecordsDO 续订记录对象，包含查询所需的客户订单ID等信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void searchAmaPChargeInfoAndUpdateRenewResult(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        AmaPChargeSearchResponseDTO response = piviAmaPService.queryAmaPChargeInfo(amaPRenewRecordsDO.getCusOrderId());
        if (Objects.isNull(response)) {
            return;
        }
        amaPRenewRecordsDO.setQueryResultCode(response.getCode());
        if (AmaPErrorCode.SUCCESSFUL.getCode().equals(response.getCode())) {
            amaPRenewRecordsDO.setRenewStatus(AmaPRenewStatusEnum.RENEW_SUCCESS.getStatus());
        } else {
            amaPRenewRecordsDO.setErrorDesc(response.getMessage());
        }
        if (Objects.nonNull(response.getData()) && CollUtil.isNotEmpty(response.getData().getChargeRecords())) {
            String endTime = response.getData().getChargeRecords().get(0).getEndTime();
            amaPRenewRecordsDO.setRenewAfterExpiryDate(SubscribeTimeFormatUtil.stringToTimeByISOZonedDateTimeFormat(endTime));
            // 更新AMAP的ServiceDO的续期时间
            updateServiceOrPIVIPackage(amaPRenewRecordsDO);
        }
        LambdaUpdateWrapper<AmaPRenewRecordsDO> updateWrapper = new LambdaUpdateWrapper<AmaPRenewRecordsDO>()
                .eq(AmaPRenewRecordsDO::getCusOrderId, amaPRenewRecordsDO.getCusOrderId())
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        amaPRenewRecordsDO.setUpdatedTime(LocalDateTime.now());
        amapRenewRecordsDOMapper.update(amaPRenewRecordsDO, updateWrapper);
        //操作日志记录 --  续费成功的才会去做一个操作记录
        if(AmaPRenewStatusEnum.RENEW_SUCCESS.getStatus().equals(amaPRenewRecordsDO.getRenewStatus())){
            manualModifyLogDOService.recordLog(amaPRenewRecordsDO);
        }
    }

    /**
     * 更新AmaP服务DO信息
     *
     * @param amaPRenewRecordsDO 续费记录对象，包含续费相关信息如果为null，则不执行任何操作
     */
    public void updateServiceOrPIVIPackage(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        log.info("更新AmaP服务DO信息, amaPRenewRecordsDO:{}", amaPRenewRecordsDO);
        if (Objects.isNull(amaPRenewRecordsDO)) {
            return ;
        }
        //1.AmaP查询ServiceDO
        SubscriptionServiceDO amaPServiceDO = queryServiceDOByCarVinAndPackage(amaPRenewRecordsDO.getCarVin(),
                ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName());
        //2.ServiceDO不为空，表示用户已经登录了，直接修改ServiceDO和添加日志
        if (Objects.nonNull(amaPServiceDO)) {
            insertAmaPServiceDOLog(amaPRenewRecordsDO, amaPServiceDO);
            updateAmaPServiceDO(amaPRenewRecordsDO, amaPServiceDO);
        } else {
            //3.ServiceDO为空，表示用户未登录了，修改PIVIPack中的Amap的到期时间
            PIVIPackageDO piviPackageDO = queryPIVIPackageDOByCarVin(amaPRenewRecordsDO.getCarVin());
            if (Objects.isNull(piviPackageDO)) {
                log.info("更新AmaP服务DO信息, SubscriptionServiceDO和PIVIPackageDO都为空, carVin:{}", amaPRenewRecordsDO.getCarVin());
                return ;
            }
            updatePIVIPackageDO(piviPackageDO, amaPRenewRecordsDO);
        }
    }

    /**
     * 插入AmaP服务日志记录
     *
     * @param amaPRenewRecordsDO 包含续订记录信息的对象，用于获取续订相关的字段值
     * @param amaPServiceDO 包含订阅服务信息的对象，用于获取订阅服务相关的字段值
     */
    public void insertAmaPServiceDOLog(AmaPRenewRecordsDO amaPRenewRecordsDO, SubscriptionServiceDO amaPServiceDO) {
        SubscriptionServiceLogDO serviceLogDO = SubscriptionServiceLogDO.builder()
                .subscriptionId(amaPServiceDO.getSubscriptionId())
                .fufilmentId(String.valueOf(amaPRenewRecordsDO.getRenewNo()))
                .refreshBeforeDate(amaPServiceDO.getExpiryDate())
                .refreshAfterDate(amaPRenewRecordsDO.getRenewAfterExpiryDate())
                .serviceName(ServicePackageEnum.CONNECTED_NAVIGATION.getDescCN())
                .modifyType(ServiceModifyTypeEnum.MANUAL_RENEWAL.getType())
                .build();
        serviceLogMapper.insert(serviceLogDO);
    }

    /**
     * 更新服务信息
     *
     * @param amaPRenewRecordsDO 续费记录对象，包含续费后的过期日期等信息
     * @param amaPServiceDO 服务对象，需要更新的信息
     */
    public void updateAmaPServiceDO(AmaPRenewRecordsDO amaPRenewRecordsDO, SubscriptionServiceDO amaPServiceDO) {
        if (Objects.isNull(amaPServiceDO) || Objects.isNull(amaPRenewRecordsDO)
                || Objects.isNull(amaPRenewRecordsDO.getRenewAfterExpiryDate())) {
            log.info("更新服务信息, 数据异常，不予更新, amaPRenewRecordsDO:{}, amaPServiceDO:{}", amaPRenewRecordsDO, amaPServiceDO);
            return ;
        }
        amaPServiceDO.setExpiryDate(amaPRenewRecordsDO.getRenewAfterExpiryDate());
        amaPServiceDO.setExpireDateUtc0(amaPRenewRecordsDO.getRenewAfterExpiryDate().minusHours(8));
        amaPServiceDO.setUpdatedBy(LoginUtil.getLoginUserName());
        amaPServiceDO.setUpdatedTime(LocalDateTime.now());
        serviceDOMapper.updateById(amaPServiceDO);
    }

    /**
     * 更新PIVIPackageDO对象的信息
     *
     * @param piviPackageDO 待更新的PIVIPackageDO对象，不应为null
     * @param amaPRenewRecordsDO 包含续费后过期日期的AmaPRenewRecordsDO对象，不应为null
     */
    public void updatePIVIPackageDO(PIVIPackageDO piviPackageDO, AmaPRenewRecordsDO amaPRenewRecordsDO) {
        if (Objects.isNull(piviPackageDO) || Objects.isNull(amaPRenewRecordsDO) ||
                Objects.isNull(amaPRenewRecordsDO.getRenewAfterExpiryDate())) {
            return ;
        }
        piviPackageDO.setAmaPExpireDate(amaPRenewRecordsDO.getRenewAfterExpiryDate());
        piviPackageDO.setUpdatedBy(LoginUtil.getLoginUserName());
        piviPackageDO.setUpdatedTime(LocalDateTime.now());
        pivIPackageMapper.updateById(piviPackageDO);
    }

    /**
     * 根据车辆识别号（VIN）查询订阅服务数据
     *
     * @param carVin 车辆识别号，用于唯一标识一辆车
     * @return 如果找到匹配的订阅服务数据对象，则返回该对象；否则返回null
     */
    public SubscriptionServiceDO queryServiceDOByCarVinAndPackage(String carVin, String packageName) {
        if (StringUtils.isBlank(carVin)) {
            return null;
        }
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServicePackage, packageName)
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        try {
            return serviceDOMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.info("根据车辆识别号（VIN）查询订阅服务数据异常：{}, carVin:{}", e.getMessage(), carVin);
        }
        return null;
    }

    /**
     * 根据车辆识别号查询PIVIPackage
     *
     * @param carVin 车辆识别号，用于唯一标识一辆汽车
     * @return 返回查询到的PIVIPackage数据对象，如果没有找到则返回null
     */
    public PIVIPackageDO queryPIVIPackageDOByCarVin(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return null;
        }
        LambdaQueryWrapper<PIVIPackageDO>  queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PIVIPackageDO::getVin, carVin)
                .eq(PIVIPackageDO::getIsDeleted, false);
        try {
            return pivIPackageMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.info("根据车辆识别号查询PIVIPackage异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 添加单次AMAP续费记录
     *
     * @param operateDTO 续费操作的数据传输对象，包含车辆识别码（carVin）和续费年份代码（renewYearCode）等信息
     * @param cusOrderId 客户订单ID
     * @return 返回添加的AMAP续费记录对象，如果添加失败返回null
     */
    public AmaPRenewRecordsDO addSingleAmaPRenewRecordsDO(AmaPSingleRenewalOperateDTO operateDTO, String cusOrderId) {
        if (Objects.isNull(operateDTO)) {
            return null;
        }
        //续费前amap到期时间查询
        AmaPCarInfoResponse amaPSearchCenterVO = piviAmaPService.getCarAmaPInfo(amaPProperties.getPid(),
                operateDTO.getCarVin());
        AmaPRenewRecordsDO amaPRenewRecordsDO = AmaPRenewRecordsDO.builder()
                .carVin(CarVinUtil.carVinToUpperCase(operateDTO.getCarVin()))
                .cusOrderId(cusOrderId)
                .renewYear(operateDTO.getRenewYearCode())
                .renewNo(snowflake.nextId())
                .operator(getOperatorUser())
                .dataSource(AmaPDataSourceEnum.SINGLE.getDateSource())
                .renewStatus(AmaPRenewStatusEnum.WAIT_RENEW.getStatus())
                .build();
        if (Objects.nonNull(amaPSearchCenterVO) && Objects.nonNull(amaPSearchCenterVO.getData())
                && CollUtil.isNotEmpty(amaPSearchCenterVO.getData().getPermissions())) {
            amaPRenewRecordsDO.setRenewBeforeExpiryDate(amaPSearchCenterVO.getData().getPermissions().get(0).getEndTime());
        }
        amapRenewRecordsDOMapper.insert(amaPRenewRecordsDO);
        return amaPRenewRecordsDO;
    }


    /**
     * 获取当前操作用户
     *
     * @return 当前操作用户的名称
     */
    private String getOperatorUser() {
        if (StringUtils.isBlank(WebFrameworkUtils.getLoginUserName())) {
            return "system";
        }
        return WebFrameworkUtils.getLoginUserName();
    }

    /**
     * 构建AMAP订单充电请求DTO
     *
     * @param operateDTO AMAP单次续费操作DTO对象，包含车辆VIN码、续费年份代码等信息
     * @param cusOrderId  AMAP订单ID
     * @return AmaPOrderChargerRequestDTO对象，包含车辆VIN码、订单ID、商品数量和产品ID
     */
    private AmaPOrderChargerRequestDTO buildAmaPOrderChargerRequestDTO(AmaPSingleRenewalOperateDTO operateDTO,
                                                                       String cusOrderId) {
        AmaPOrderChargerRequestDTO requestDTO = AmaPOrderChargerRequestDTO.builder()
                .vid(operateDTO.getCarVin())
                .cusOrderId(cusOrderId)
                .amount(1)
                .build();
        if (AmaPRenewalYearEnum.ONE.getCode().equals(operateDTO.getRenewYearCode())) {
            requestDTO.setCid(amaPProperties.getOneYearCid());
        }
        if (AmaPRenewalYearEnum.THREE.getCode().equals(operateDTO.getRenewYearCode())) {
            requestDTO.setCid(amaPProperties.getThreeYearCid());
        }
        return requestDTO;
    }

    /**
     * 检查AMAP单个续费操作的参数是否有效
     *
     * @param operateDTO 续费操作的数据传输对象，包含续费所需的所有信息
     * @return CommonResult<Boolean> 返回一个表示验证结果的对象
     *         如果验证成功，返回错误结果；如果验证失败，返回成功结果并携带true标志
     */
    private CommonResult<String> checkSingleRenewalArgs(AmaPSingleRenewalOperateDTO operateDTO) {
        if (Objects.isNull(operateDTO)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SINGLE_RENEWAL_REQUEST_EMPTY);
        }
        if (!CarVinUtil.checkVinFormat(operateDTO.getCarVin())) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        if (!AmaPRenewalYearEnum.containsRenewalYearCode(operateDTO.getRenewYearCode())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SINGLE_RENEWAL_CODE_ERROR);
        }
        return CommonResult.success("参数校验成功");
    }

    /**
     * 构建单个续费页面的VO列表
     * 该方法主要用于将数据库中的续费记录转换为前端展示的格式
     *
     * @param recordsDOList 续费记录的DO列表，包含了所有的续费记录信息
     * @return 返回一个列表，其中包含了转换后的单个续费页面的VO对象
     */
    private List<AmaPSingleRenewalPageVO> buildAmaPSingleRenewalPageVOList(List<AmaPRenewRecordsDO> recordsDOList) {
        List<AmaPSingleRenewalPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(recordsDOList)) {
            return resp;
        }
        for (AmaPRenewRecordsDO recordsDO : recordsDOList) {
            resp.add(buildAmaPSingleRenewalPageVO(recordsDO));
        }
        return resp;
    }

    /**
     * 构建单个续费页面的视图对象
     *
     * @param amaPRenewRecordsDO 续费记录实体，包含从数据库中查询到的单个续费记录的详细信息
     * @return AmaPSingleRenewalPageVO 返回一个包含续费信息的视图对象，如果输入实体为空，则返回一个空的视图对象
     */
    private AmaPSingleRenewalPageVO buildAmaPSingleRenewalPageVO(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        if (Objects.isNull(amaPRenewRecordsDO)) {
            return new AmaPSingleRenewalPageVO();
        }
        return AmaPSingleRenewalPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(amaPRenewRecordsDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .vin(amaPRenewRecordsDO.getCarVin())
                .operateUser(amaPRenewRecordsDO.getOperator())
                .renewNo(amaPRenewRecordsDO.getRenewNo())
                .build();
    }


    /**
     * 查询续费记录
     *
     * @param dateSource 数据来源标识，用于区分不同的数据源
     * @param pageDTO 分页查询的DTO对象，包含分页信息和排序方式
     * @return 返回分页的续费记录列表
     */
    private Page<AmaPRenewRecordsDO> queryRenewalRecord(Integer dateSource, AmaPSingleRenewalPageDTO pageDTO) {
        Page<AmaPRenewRecordsDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewRecordsDO::getDataSource, dateSource)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        if (SortTypeEnum.DESC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByDesc(AmaPRenewRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(AmaPRenewRecordsDO::getId);
        } else {
            queryWrapper.orderByAsc(AmaPRenewRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(AmaPRenewRecordsDO::getId);
        }
        return amapRenewRecordsDOMapper.selectPage(pageParam, queryWrapper);
    }
}
