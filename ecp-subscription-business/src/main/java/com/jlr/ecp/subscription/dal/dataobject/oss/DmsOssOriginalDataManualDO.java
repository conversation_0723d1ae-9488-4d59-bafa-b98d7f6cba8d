package com.jlr.ecp.subscription.dal.dataobject.oss;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_dms_oss_original_data_manual表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_dms_oss_original_data_manual")
public class DmsOssOriginalDataManualDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 数据id;雪花算法ID
     */
    @TableField(value = "data_id")
    private Long dataId;

    /**
     * VIN;VIN
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 发票日期;发票日期
     */
    @TableField(value = "dms_invoice_date")
    private String dmsInvoiceDate;

    /**
     * iccid
     */
    @TableField(value = "iccid")
    private String iccid;

    /**
     * 处理状态;数据处理状态 0：初始化中 1：初始化成功 2:初始化失败
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 同步状态;同步过期日期状态 0：未同步 1：同步成功 2：APPD同步失败 3：CU同步失败 4：均同步失败
     */
    @TableField(value = "sync_status")
    private Integer syncStatus;

    /**
     * sota查询结果;sota查询结果 0：失败 1：成功
     */
    @TableField(value = "sota_result")
    private Integer sotaResult;

    /**
     * appd查询结果;appd查询结果 0：失败 1：成功
     */
    @TableField(value = "appd_result")
    private Integer appdResult;

    /**
     * 联通查询结果;cu查询结果 0：失败 1：成功
     */
    @TableField(value = "cu_result")
    private Integer cuResult;

    /**
     * amap查询结果;amap查询结果 0：失败 1：成功
     */
    @TableField(value = "amap_result")
    private Integer amapResult;

    /**
     * DP查询结果;dp查询结果 0：失败 1：成功
     */
    @TableField(value = "dp_result")
    private Integer dpResult;

    /**
     * PIVI车机匹配结果;PIVI车机匹配结果 0：非PIVI 1：PIVI
     */
    @TableField(value = "pivi_config_result")
    private Integer piviConfigResult;

    /**
     * 特殊车型
     */
    @TableField(value = "special_vin_config")
    private String specialVinConfig;

    /**
     * 失败原因;失败原因，各个系统查不到或者同步失败的原因拼接
     */
    @TableField(value = "error_message")
    private String errorMessage;
}

