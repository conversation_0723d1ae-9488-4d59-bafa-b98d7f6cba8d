package com.jlr.ecp.subscription.controller.admin.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.excel.service.amap.AmaPManualActivationService;
import com.jlr.ecp.subscription.excel.vo.amap.AmaPManualActivationImportVO;
import com.jlr.ecp.subscription.excel.vo.amap.AmaPManualUploadResultVO;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "高德-手动激活")
@RequestMapping("/amap/manual/activation")
@Slf4j
public class AmaPManualActivationController {
    @Resource
    private AmaPManualActivationService amapManualActivationService;

    /**
     * 2MB
     */
    private static final long MAX_FILE_SIZE = 2L * 1024 * 1024 ;

    @PostMapping("/download/carVin")
    @Operation(summary = "下载高德需要手动激活的excel文件")
    @PreAuthorize("@ss.hasPermission('query:manualactivation:forms')")
    public CommonResult<String> downloadAmaPNotActivationCarVin() {
        return amapManualActivationService.downloadCarVin();
    }

    @PostMapping("/upload")
    @Operation(summary = "上传高德手动激活的excel文件")
    @PreAuthorize("@ss.hasPermission('query:manualactivation:forms')")
    public CommonResult<AmaPManualUploadResultVO> batchUpload(@RequestBody MultipartFile file) {
        log.info("上传高德手动激活的excel文件:{}, 文件大小:{}", file, file.getSize());
        long startTime = System.currentTimeMillis();
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.FILE_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(file)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        CommonResult<AmaPManualUploadResultVO> resp = amapManualActivationService.uploadAmaPActivationExcel(file);
        long endTime = System.currentTimeMillis();
        log.info("上传高德手动激活excel文件解析完成，花费时间:{}毫秒，resp:{}", (endTime-startTime), resp);
        return resp;
    }

    @GetMapping("/query/importVo")
    @Operation(summary = "查询高德手动激活，当前用户的excel文件导入历史记录")
    @PreAuthorize("@ss.hasPermission('query:manualactivation:forms')")
    public CommonResult<List<AmaPManualActivationImportVO>> queryImportVo() {
        log.info("查询高德手动激活，当前用户的excel文件导入历史记录，userName:{}", WebFrameworkUtils.getLoginUserName());
        return CommonResult.success(amapManualActivationService.getAmaPManualActivationImportVO());
    }
}
