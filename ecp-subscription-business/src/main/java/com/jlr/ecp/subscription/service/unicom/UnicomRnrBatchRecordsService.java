package com.jlr.ecp.subscription.service.unicom;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomBatchFilePageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRnrBatchRecordListVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRnrBatchRecordsVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface UnicomRnrBatchRecordsService {

    /**
     * 下载ICCID查询模板
     * @return String
     */
    CommonResult<String> downloadUnicomRnrTemplate();


    /**
     * 上传ICCID批量查询模板
     * @return UnicomRnrBatchRecordsVO
     */
    CommonResult<UnicomRnrBatchRecordsVO> uploadRnrQueryExcel(MultipartFile file);


    /**
     * 执行解析job
     * @param batchNo 执行编码
     * @return Integer成功入库数量
     */
    Integer rnrBatchQueryParseJob(Long batchNo);

    /**
     * 批量查询ICCID实名状态
     * @param ids 数据id集合
     * @return Integer成功处理数量
     */
    Integer rnrBatchQueryExecutor(List<Long> ids) ;


    /**
     * 批量查询文件列表
     * @param pageDTO 分页
     * @return ageResult<UnicomRnrBatchRecordListVO>
     */
    PageResult<UnicomRnrBatchRecordListVO> getBatchFilePage(UnicomBatchFilePageDTO pageDTO);

    /**
     * 根据批次号查询ICCID的批次号
     *
     * @param batchNo 批次号字符串
     * @return 如果找到对应的批次记录，则返回批次号字符串；否则返回null
     */
    String getUnicomQueryBatchNo(String batchNo);
}
