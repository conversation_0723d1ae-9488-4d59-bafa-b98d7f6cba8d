package com.jlr.ecp.subscription.enums.remote;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RemoteModifyErrorEnum {

    VIN_NOT_FOUND(1, "在ECP数据库中未找到该VIN"),
    VIN_NOT_PIVI(2, "该VIN是非PIVI车机"),
    SERVICE_DATA_EMPTY(3, "该VIN无remote服务相关数据"),
    REQUEST_ERROR(4, "请求异常"),
    SYSTEM_ERROR(5, "系统异常");

    private final Integer code;

    private final String desc;
}
