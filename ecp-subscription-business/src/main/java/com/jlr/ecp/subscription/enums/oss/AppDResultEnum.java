package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.jlr.ecp.subscription.constant.Constants.APPD_SYSTEM_ERROR;

/**
 * APPD查询结果枚举
 */
@AllArgsConstructor
@Getter
public enum AppDResultEnum {

    SUCCESS(1, "是", null),

    SYSTEM_ERROR(2, "否，系统异常", APPD_SYSTEM_ERROR),

    REQUEST_ERROR(3, "否，请求异常", APPD_SYSTEM_ERROR),

    VIN_NOT_FOUND(4, "否，未找到该VIN", "APPD未找到该VIN"),

    EXPIRY_DATE_NOT_FOUND(5, "否，到期日不存在", APPD_SYSTEM_ERROR),

    JLR_SUBSCRIPTION_ID_NOT_FOUND(6, "否，JLR Subscription ID不存在", APPD_SYSTEM_ERROR);

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 错误信息
     * */
    public final String errorMessage;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (AppDResultEnum status : AppDResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    public static String getErrorMessage(Integer code){
        if (code == null) {
            return null;
        }
        for (AppDResultEnum status : AppDResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getErrorMessage();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
