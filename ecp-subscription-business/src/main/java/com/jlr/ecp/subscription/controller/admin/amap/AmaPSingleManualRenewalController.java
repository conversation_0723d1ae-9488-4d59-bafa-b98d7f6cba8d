package com.jlr.ecp.subscription.controller.admin.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalOperateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalYearVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPSingleRenewalPageVO;
import com.jlr.ecp.subscription.service.amap.AmaPSingleManualRenewalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/amap/single/manual/renewal")
@Validated
@Tag(name = "AMAP手动续费- 单个续费")
public class AmaPSingleManualRenewalController {
    @Resource
    private AmaPSingleManualRenewalService amaPSingleManualRenewalService;

    @PostMapping("/pageList")
    @Operation(summary = "AMAP单个续费分页查询")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-single-renewal:forms')")
    public CommonResult<PageResult<AmaPSingleRenewalPageVO>> queryRenewalPageList(@RequestBody @Valid AmaPSingleRenewalPageDTO dto) {
        PageResult<AmaPSingleRenewalPageVO> resp = amaPSingleManualRenewalService.queryRenewalPageList(dto);
        return CommonResult.success(resp);
    }

    @GetMapping("/getYear")
    @Operation(summary = "获取AMAP续费年限")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-single-renewal:forms')")
    public CommonResult<List<AmaPRenewalYearVO>> getRenewalYear() {
        return CommonResult.success(amaPSingleManualRenewalService.getAmaPRenewalYear());
    }

    @PostMapping("/operate")
    @Operation(summary = "AMAP单个续费")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-single-renewal:forms')")
    public CommonResult<String> operateRenewal(@RequestBody @Valid AmaPSingleRenewalOperateDTO operateDTO) {
        return amaPSingleManualRenewalService.amaPSingleManualRenewal(operateDTO);
    }
}
