package com.jlr.ecp.subscription.controller.admin.dto.bau;


import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "VIN初始化日志分页查询DTO")
@Validated
public class VinInitialLogPageDTO extends PageParam {

    @Schema(description = "处理时间排序, 正序:asc, 倒叙:desc")
    private String operateTimeSort;

    @Schema(description = "生成时间排序, 正序:asc, 倒叙:desc")
    private String createdTimeSort;

    @Schema(description = "任务id")
    private Long bauJobId;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
}
