package com.jlr.ecp.subscription.dal.mysql.oss;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataManualDO;
import com.jlr.ecp.subscription.enums.oss.InitialStateEnum;
import org.apache.ibatis.annotations.Mapper;


/**
 * t_dms_oss_original_data表数据库访问层
 * <AUTHOR>
 */
@Mapper
public interface DmsOssOriginalDataManualMapper extends BaseMapperX<DmsOssOriginalDataManualDO> {
    default DmsOssOriginalDataManualDO selectOneByCarVin(String carVin){
        return selectOne(new LambdaQueryWrapperX<DmsOssOriginalDataManualDO>()
                .eq(DmsOssOriginalDataManualDO::getCarVin, carVin)
                .eq(DmsOssOriginalDataManualDO::getStatus, InitialStateEnum.PENDING.getCode())
                .eq(DmsOssOriginalDataManualDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));
    }
}
