package com.jlr.ecp.subscription.controller.admin.model;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataCreateDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataUpdateDTO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataPageVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Tag(name = "平台管理 - 适用车机年款配置")
@RestController
@RequestMapping("v1/subscription/vehicleModel")
@Validated
public class VehicleModelMasterDataController {


    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;
    /**
     * 创建车型年款API接口
     * @param createDTO 入参
     * @return CommonResult<String>
     */
    @PostMapping( "/create")
    @Operation(summary = "添加车型年款")
    @PreAuthorize("@ss.hasPermission('platform:applicable:create')")
    CommonResult<String> createVehicleModelMasterData(@Validated @RequestBody VehicleModelMasterDataCreateDTO createDTO){
        Boolean success = vehicleModelMasterDataService.createVehicleModelMasterData(createDTO);
        if(success){
            return CommonResult.success(Constants.SERIES_CREATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_CREATE_FAIL);
    }

    /**
     * 编辑车型年款API接口
     * @param updateDTO 编辑入参
     * @return String
     */
    @PutMapping( "/edit")
    @Operation(summary = "编辑车型年款")
    @PreAuthorize("@ss.hasPermission('platform:applicable:edit')")
    CommonResult<String> editVehicleModelMasterData(@Validated @RequestBody VehicleModelMasterDataUpdateDTO updateDTO){
        Boolean success = vehicleModelMasterDataService.editVehicleModelMasterData(updateDTO);
        if(success){
            return CommonResult.success(Constants.SERIES_UPDATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_UPDATE_FAIL);
    }



    /**
     * 车型年款详情API
     * @param seriesCode 父级品牌 非必传
     * @return CommonResult<List<VehicleModelMasterDataListRespVO>>
     */
    @GetMapping( "/view")
    @Operation(summary = "车型年款查询接口")
    @Parameter(name = "seriesCode", description = "车型编码")
    @PreAuthorize("@ss.hasPermission('platform:applicable:edit')") //和前端约定的特殊配置
    CommonResult<VehicleModelMasterDataVO> view(@RequestParam(value = "seriesCode") String seriesCode){
        VehicleModelMasterDataVO vo =vehicleModelMasterDataService.getOneBySeriesCode(seriesCode);
        return CommonResult.success(vo);
    }



    /**
     * 车型年款列表API
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return CommonResult<List<VehicleModelMasterDataListVO>>
     */
    @GetMapping( "/page")
    @Operation(summary = "车型年款列表查询接口")
    @Parameters({
            @Parameter(name = "pageNo", description = "页码", required = true, example = "0"),
            @Parameter(name = "pageSize", description = "页大小", required = true, example = "10")
    })
    @PreAuthorize("@ss.hasPermission('platform:applicable:list')")
    CommonResult<PageResult<VehicleModelMasterDataPageVO>> page(@RequestParam(name = "pageNo", defaultValue = "10") Integer pageNo,
                                                                @RequestParam(name = "pageSize",defaultValue = "1") Integer pageSize){
        PageParam param = new PageParam();
        param.setPageNo(pageNo);
        param.setPageSize(pageSize);
        return CommonResult.success(vehicleModelMasterDataService.getPage(param));
    }


    /**
     * 删除车型年款
     * @param seriesCode 车型编码
     * @return CommonResult<String>
     */
    @DeleteMapping( "/delete")
    @Operation(summary = "删除车型年款")
    @Parameter(name = "seriesCode", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('platform:applicable:delete')")
    CommonResult<String> deleteBySeriesCode(@RequestParam("seriesCode") String seriesCode){
        Boolean success = vehicleModelMasterDataService.deleteBySeriesCode(seriesCode);
        if(success){
            return CommonResult.success(Constants.SERIES_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_DELETE_FAIL);
    }




}
