package com.jlr.ecp.subscription.service.appd;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuRenewalQueryPageVO;

import java.util.List;

public interface AppDCuRenewalQueryService {
    /**
     * 查询AppDCU续期批次的批号
     *
     * @param batchNo 批次号，字符串类型
     * @return 如果找到对应的批次记录，返回该记录的批号；否则返回空字符串
     */
    String queryAppDCuRenewalBatchNo(String batchNo);

    /**
     * 查询AppDCu续费服务类型
     *
     * @return 包含所有续费服务类型的列表，每个服务类型都以AppDCuEnumQueryVO形式呈现
     */
    List<AppDCuEnumQueryVO> queryAppDCuRenewalService();

    /**
     * 查询AppPCU续费状态枚举
     *
     */
    List<AppDCuEnumQueryVO> queryAppDCuRenewalStatus();

    /**
     * 查询AppDCU续期的分页列表
     *
     * @param queryPageDTO 包含查询条件的DTO对象
     * @return 返回分页结果对象，包含数据列表和总记录数
     */
    PageResult<AppDCuRenewalQueryPageVO> queryAppDCuRenewalPageList(AppDCuRenewalQueryPageDTO queryPageDTO);
}
