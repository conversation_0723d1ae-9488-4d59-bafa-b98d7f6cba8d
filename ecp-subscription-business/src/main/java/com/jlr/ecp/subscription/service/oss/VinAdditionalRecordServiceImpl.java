package com.jlr.ecp.subscription.service.oss;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataManualDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleConfigGroupDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataManualMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPErrorCode;
import com.jlr.ecp.subscription.enums.fufil.AppDServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.oss.*;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
import com.jlr.ecp.subscription.model.dto.AmaPPermissionInfo;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionData;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIPackageService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import com.jlr.ecp.subscription.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * vin补录服务接口
 *
 * <AUTHOR>
 */
@Service("vinAdditionalRecordService")
@Validated
@Slf4j
public class VinAdditionalRecordServiceImpl implements VinAdditionalRecordService {

    @Resource
    private PIVIPackageService piviPackageService;

    @Resource
    private SOTAService sotaService;

    @Resource
    private PIVIUnicomService unicomService;

    @Resource
    private PIVIAppDService appDService;

    @Resource
    private PIVIAmaPService amaPService;

    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;

    @Resource
    private VehicleConfigGroupService vehicleConfigGroupService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private DmsOssOriginalDataService dmsOssOriginalDataService;

    @Resource
    private DmsOssOriginalDataRecordsService dmsOssOriginalDataRecordsService;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private DmsOssOriginalDataManualMapper dmsOssOriginalDataManualMapper;

    private static final String TODAY_END = " 23:59:59";

    @Resource
    private DmsOssFileRecordsService dmsOssFileRecordsService;

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Override
    public CommonResult<String> additionalRecord(VinAdditionalRecordDTO recordDTO) {
        // 校验车架号格式
        if (!CarVinUtil.checkVinFormat(recordDTO.getVin())) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        recordDTO.setVin(CarVinUtil.carVinToUpperCase(recordDTO.getVin()).replace(" ",""));
        recordDTO.setIccid(recordDTO.getIccid().replace(" ",""));
        // 校验ICCID格式
        if (!IccidUtil.checkIccidFormat(recordDTO.getIccid())) {
            return CommonResult.error(ErrorCodeConstants.ICCID_LENGTH_ERROR);
        }
        recordDTO.setVin(CarVinUtil.carVinToUpperCase(recordDTO.getVin()));
        recordDTO.setInvoiceDate(recordDTO.getInvoiceDate() + TODAY_END);
        if (!checkExpireDate(recordDTO.getInvoiceDate())) {
            return CommonResult.error(ErrorCodeConstants.INVOICE_DATE_FORMAT_ERROR);
        }
        PIVIPackageDO piviPackageDO = piviPackageService.queryByVin(recordDTO.getVin());
        // 该vin是否在ecp已经存在
        if (Objects.nonNull(piviPackageDO)) {
            return CommonResult.error(ErrorCodeConstants.VIN_INITIALIZED_SUCCESS);
        }
        // 该vin是否在正在补录
        DmsOssOriginalDataManualDO manualDOPending = dmsOssOriginalDataManualMapper.selectOneByCarVin(recordDTO.getVin());
        if (Objects.nonNull(manualDOPending)) {
            return CommonResult.error(ErrorCodeConstants.VIN_MANUAL_PENDING);
        }
        // 查询车辆数据是否已经存在
        DmsOssOriginalDataDO originalDataDO = dmsOssOriginalDataService.getByVin(recordDTO.getVin());
        DmsOssOriginalDataManualDO manualDO = buildDmsOssOriginalDataManualDO(recordDTO);
        if (Objects.nonNull(originalDataDO)) {
            // 只有入库状态为无需入库才能补录
            if (!VinMatchResultEnum.NOT_REQUIRED.getCode().equals(originalDataDO.getVinMatchResult())) {
                return CommonResult.error(ErrorCodeConstants.VIN_INITIALIZED_PENDING);
            }
            insertAndUpdateOriginalData(recordDTO, originalDataDO, manualDO);
        } else {
            originalDataDO = insertOriginalData(recordDTO, manualDO);
        }
        String modifyUser = LoginUtil.getLoginUserName();
        // 异步执行校验和续费
        DmsOssOriginalDataDO finalOriginalDataDO = originalDataDO;
        CompletableFuture.runAsync(() -> executeCheckVin(finalOriginalDataDO, manualDO, modifyUser), subscribeAsyncThreadPool).exceptionally(ex -> {
            log.info("异步执行校验和续费异常", ex);
            return null;
        });
        return CommonResult.success("数据补录中，请刷新查看结果");
    }

    /**
     * 插入手动补录数据并更新原始数据
     * 它首先更新原始数据对象的信息，然后在事务中插入手动补录数据
     * 并更新原始数据对象，以确保数据一致性和完整性
     *
     * @param recordDTO 手动补录记录DTO，包含需要更新和插入的数据信息
     * @param originalDataDO 原始数据对象，代表需要更新的原始数据记录
     * @param manualDO 手动补录数据对象，代表需要插入的新手动补录记录
     */
    private void insertAndUpdateOriginalData(VinAdditionalRecordDTO recordDTO, DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataManualDO manualDO) {
        originalDataDO.setIccid(recordDTO.getIccid());
        originalDataDO.setSotaResult(SotaResultEnum.SUCCESS.getCode());
        originalDataDO.setDmsInvoiceDate(recordDTO.getInvoiceDate());
        originalDataDO.setUpdatedTime(LocalDateTime.now());
        transactionTemplate.executeWithoutResult(status -> {
            // 新增手动补录数据
            dmsOssOriginalDataManualMapper.insert(manualDO);
            // 更新BAU数据
            dmsOssOriginalDataService.update(originalDataDO);
        });
    }

    /**
     * 插入原始数据记录
     * 此方法负责将手动补录的车辆附加信息记录到系统中它创建一个DmsOssOriginalDataDO对象来存储原始数据信息，
     * 并使用事务来确保手动补录数据和BAU数据的一致性
     *
     * @param recordDTO 包含车辆附加记录信息的DTO
     * @param manualDO 包含手动补录数据的DO对象
     * @return 返回创建的DmsOssOriginalDataDO对象
     */
    private DmsOssOriginalDataDO insertOriginalData(VinAdditionalRecordDTO recordDTO, DmsOssOriginalDataManualDO manualDO) {
        DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .bauJobId(ecpIdUtil.nextId())
                .dataId(ecpIdUtil.nextId())
                .jobDate(LocalDateTime.now())
                .carVin(recordDTO.getVin())
                .dmsInvoiceDate(recordDTO.getInvoiceDate())
                .sotaResult(SotaResultEnum.SUCCESS.getCode())
                .status(HandleStatusEnum.PENDING.getCode())
                .syncStatus(InitializeStatusEnum.PENDING.getCode())
                .iccid(recordDTO.getIccid())
                .sourceType(SourceTypeEnum.MANUAL.getCode())
                .tenantId(TenantContextHolder.getTenantId().intValue())
                .build();
        transactionTemplate.executeWithoutResult(status -> {
            // 新增手动补录数据
            dmsOssOriginalDataManualMapper.insert(manualDO);
            // 新增BAU数据
            dmsOssOriginalDataService.insert(originalDataDO);
        });
        return originalDataDO;
    }

    /**
     * 构建DmsOssOriginalDataManualDO对象
     * 该方法根据VinAdditionalRecordDTO记录DTO中的信息，构建一个DmsOssOriginalDataManualDO对象
     * 主要用于将额外的VIN记录信息转换为DMS OSS原始数据手动录入系统中的实体对象
     *
     * @param recordDTO VinAdditionalRecordDTO对象，包含额外记录的信息，如VIN号、发票日期等
     * @return DmsOssOriginalDataManualDO对象，用于在DMS OSS系统中记录原始数据
     */
    private DmsOssOriginalDataManualDO buildDmsOssOriginalDataManualDO(VinAdditionalRecordDTO recordDTO) {
        return DmsOssOriginalDataManualDO.builder()
                .dataId(ecpIdUtil.nextId())
                .sotaResult(SotaResultEnum.SUCCESS.getCode())
                .carVin(recordDTO.getVin())
                .dmsInvoiceDate(recordDTO.getInvoiceDate())
                .iccid(recordDTO.getIccid())
                .status(InitialStateEnum.PENDING.getCode())
                .syncStatus(InitializeStatusEnum.PENDING.getCode())
                .tenantId(TenantContextHolder.getTenantId().intValue())
                .build();
    }

    @Override
    public PageResult<VinAdditionalRecordVO> getAdditionalRecordPage(VinAdditionalRecordPageDTO pageDTO) {
        Page<DmsOssOriginalDataManualDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<DmsOssOriginalDataManualDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmsOssOriginalDataManualDO::getIsDeleted, false);
        if (SortTypeEnum.DESC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByDesc(DmsOssOriginalDataManualDO::getCreatedTime);
            queryWrapper.orderByDesc(DmsOssOriginalDataManualDO::getId);
        } else {
            queryWrapper.orderByAsc(DmsOssOriginalDataManualDO::getCreatedTime);
            queryWrapper.orderByAsc(DmsOssOriginalDataManualDO::getId);
        }
        Page<DmsOssOriginalDataManualDO> page = dmsOssOriginalDataManualMapper.selectPage(pageParam, queryWrapper);
        List<VinAdditionalRecordVO> recordVOList = buildVinAdditionalRecordVOPageList(page.getRecords());
        return new PageResult<>(recordVOList, page.getTotal());
    }

    /**
     * 构建Vin附加记录VO页面列表
     * 该方法用于将一组原始数据手动记录（DmsOssOriginalDataManualDO）转换为Vin附加记录视图对象（VinAdditionalRecordVO）列表
     * 主要用于数据展示或进一步的数据处理
     *
     * @param manualDOS 原始数据手动记录列表如果列表为空或null，则返回一个空的视图对象列表
     * @return Vin附加记录视图对象列表
     */
    private List<VinAdditionalRecordVO> buildVinAdditionalRecordVOPageList(List<DmsOssOriginalDataManualDO> manualDOS) {
        List<VinAdditionalRecordVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(manualDOS)) {
            return resp;
        }
        for (DmsOssOriginalDataManualDO manualDO : manualDOS) {
            resp.add(buildVinAdditionalRecordVO(manualDO));
        }
        return resp;
    }

    /**
     * 构建Vin附加记录视图对象
     * 该方法用于将原始数据手动DO对象转换为Vin附加记录视图对象
     * 主要用于数据结构的转换和格式化，以便于后续的处理和展示
     *
     * @param manualDO 原始数据手动DO对象，包含车辆识别号、创建者、错误信息等数据
     * @return VinAdditionalRecordVO视图对象，包含了转换和格式化后的数据
     */
    private VinAdditionalRecordVO buildVinAdditionalRecordVO(DmsOssOriginalDataManualDO manualDO) {
        return VinAdditionalRecordVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(manualDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .vin(manualDO.getCarVin())
                .operator(manualDO.getCreatedBy())
                .initialResult(InitialStateEnum.getDescByCode(manualDO.getStatus()))
                .failReason(manualDO.getErrorMessage())
                .build();
    }


    /**
     * 执行VIN检查流程
     * 本方法实现了对车辆VIN码的一系列检查，包括查询不同系统中的车辆信息，处理结果，并更新数据对象
     *
     * @param originalDataDO 原始数据对象，包含车辆基础信息及处理结果
     */
    private void executeCheckVin(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataManualDO manualDO, String modifyUser) {
        DmsOssOriginalDataRecordsDO recordsDO = DmsOssOriginalDataRecordsDO.builder()
                .bauJobId(originalDataDO.getBauJobId())
                .dataId(originalDataDO.getDataId())
                .carVin(originalDataDO.getCarVin())
                .tenantId(TenantContextHolder.getTenantId().intValue())
                .build();
        String vin = originalDataDO.getCarVin();
        // 初始化DmsOssOriginalDataRecordsDO
        // 默认初始化结果为初始化成功, 只要有一个环节失败，则为初始化失败
        originalDataDO.setStatus(HandleStatusEnum.SUCCESS.getCode());
        // 默认VIN无需入库ECP
        originalDataDO.setVinMatchResult(VinMatchResultEnum.NOT_REQUIRED.getCode());
        // 2.调用CU查询iccid
        boolean checkCU = checkCU(originalDataDO, recordsDO);
        // 3.调用APPD查询vin、jlrSubscriptionId、expire date by vin
        boolean checkAPPD = checkAPPD(originalDataDO, recordsDO);
        // 4.调用AMAP查询vin、expiry date by vin
        boolean checkAMAP = checkAMAP(originalDataDO, recordsDO);
        // 5.调用DP查询vin、model、model year、配置编码 by vin
        String configCode = checkDP(originalDataDO, recordsDO);
        // 6.查询车型年款配置表by model&model year，过滤出PIVI vin
        boolean checkDP = DpResultEnum.SUCCESS.getCode().equals(originalDataDO.getDpResult());
        boolean checkPIVI = PIVIResultEnum.SUCCESS.getCode().equals(originalDataDO.getPiviConfigResult());
        // 7.1未查询到DP数据或不是PIVI车机不用落库
        if (!checkDP || !checkPIVI) {
            log.warn("VIN补录时时车辆未查询到DP数据或不是PIVI车机, vin:{}, DP:{}, PIVI:{}", vin, checkDP, checkPIVI);
            // 7.2 如果vin在APPD, CU, AMAP存在, 且expiry date/invoice date不为空, 需要生成初始化PIVI记录
        } else if (checkAPPD && checkCU && checkAMAP) {
            // 设置VIN待入库ECP
            originalDataDO.setVinMatchResult(VinMatchResultEnum.PENDING.getCode());
            // 7.3 如果vin在SOTA, CU, AMAP存在, 且expiry date/invoice date不为空, 但在APPD不存在，判断该车型在特殊履约方式的车型配置编码表，需要生成初始化PIVI记录
        } else if (checkCU && checkAMAP) {
            // 判断该车型是否在特殊履约方式的车型配置编码表
            judgeSpecialGroup(originalDataDO, configCode, recordsDO);
        }
        updateManualDOCheckStatus(originalDataDO, manualDO);
        originalDataDO.setUpdatedTime(LocalDateTime.now());
        manualDO.setUpdatedTime(LocalDateTime.now());
        // 更新检查记录
        transactionTemplate.executeWithoutResult(status -> {
            dmsOssOriginalDataRecordsService.insert(recordsDO);
            dmsOssOriginalDataService.update(originalDataDO);
            dmsOssOriginalDataManualMapper.updateById(manualDO);

            // 注册事务同步器，确保事务提交后执行
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 同步到期日
                    syncExpiryDate(originalDataDO, recordsDO, manualDO, modifyUser);
                }
            });
        });
    }

    /**
     * 同步过保时间
     * 本方法用于同步车辆的过保时间，只在车辆识别号（VIN）匹配结果为待入库时执行同步操作
     * 如果VIN匹配结果不是待入库状态，则记录警告日志并返回，不执行同步
     * 同步过程包括更新服务到期时间，并根据同步结果更新相关状态
     *
     * @param originalDataDO 原始数据对象，包含车辆的基本信息和VIN匹配结果
     * @param recordsDO 记录数据对象，用于记录同步过程中的数据变化
     * @param manualDO 手动录入数据对象，用于处理手动录入的数据
     */
    private void syncExpiryDate(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO,
                                DmsOssOriginalDataManualDO manualDO, String modifyUser) {
        // 不是待入库不需要同步
        if (!VinMatchResultEnum.PENDING.getCode().equals(originalDataDO.getVinMatchResult())) {
            log.warn("VIN不是待入库状态不需要同步, vin={}", originalDataDO.getCarVin());
            return;
        }
        // 同步服务到期时间
        log.info("VIN补录同步服务到期时间, vin={}", originalDataDO.getCarVin());
        syncServiceExpiryDate(originalDataDO, recordsDO, manualDO);
        log.info("VIN补录同步服务到期时间结束, vin={}", originalDataDO.getCarVin());
        // 更新同步结果
        transactionTemplate.executeWithoutResult(status -> {
            if (InitializeStatusEnum.SUCCESS.getCode().equals(originalDataDO.getSyncStatus())) {
                originalDataDO.setVinMatchResult(VinMatchResultEnum.SUCCESS.getCode());
                manualDO.setStatus(InitialStateEnum.SUCCESS.getCode());
                // 初始化PIVIPackageDO
                PIVIPackageDO piviPackageDO = initPIVIPackageDO(originalDataDO);
                piviPackageService.insert(piviPackageDO);
                // 记录操作日志
                manualModifyLogDOService.recordManualLog(PIVIPackageLogDO.builder()
                        .carVin(originalDataDO.getCarVin())
                        .modifyUser(modifyUser)
                        .build());
                // 更新任务成功的数量
                dmsOssFileRecordsService.updateManualSyncResult(originalDataDO.getBauJobId());
            } else {
                originalDataDO.setVinMatchResult(VinMatchResultEnum.NOT_REQUIRED.getCode());
                manualDO.setStatus(InitialStateEnum.FAIL.getCode());
            }
            // 更新同步状态
            dmsOssOriginalDataService.update(originalDataDO);
            dmsOssOriginalDataRecordsService.update(recordsDO);
            dmsOssOriginalDataManualMapper.updateById(manualDO);
        });
    }

    /**
     * 初始化PIVIPackageDO对象
     * 该方法用于根据车辆识别号（VIN）创建一个PIVIPackageDO实例，用于后续的业务处理
     *
     * @param originalDataDO originalDataDO
     * @return 初始化后的PIVIPackageDO对象，包含预定义的服务包码、服务名称和车辆识别号
     */
    private static PIVIPackageDO initPIVIPackageDO(DmsOssOriginalDataDO originalDataDO) {
        LocalDateTime dmsDate = TimeFormatUtil.stringToLocalDate(originalDataDO.getDmsInvoiceDate());
        LocalDateTime expireDate = dmsDate.plusYears(3).withHour(23).withMinute(59).withSecond(59);
        return PIVIPackageDO.builder()
                .packageCode(ServicePackageEnum.ONLINE_PACK.getPackageName())
                .serviceName(AppDServiceNameEnum.ONLINE_MEDIA.getServiceName())
                .vin(originalDataDO.getCarVin())
                .dmsInvoiceDate(dmsDate)
                .expiryDate(expireDate)
                .expireDateUtc0(expireDate.minusHours(8))
                .amaPExpireDate(originalDataDO.getAmaPExpireDate())
                .jlrSubscriptionId(originalDataDO.getJlrSubscriptionId())
                .iccid(originalDataDO.getIccid())
                .tenantId(TenantContextHolder.getTenantId().intValue())
                .build();
    }

    /**
     * 同步服务到期时间
     * 该方法通过调用APPD和联通服务来激活服务，并根据激活结果更新同步状态
     *
     */
    private void syncServiceExpiryDate(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO,
                                       DmsOssOriginalDataManualDO manualDO) {
        LocalDateTime dmsDate = TimeFormatUtil.stringToLocalDate(originalDataDO.getDmsInvoiceDate());
        // 设置ecp到期时间
        LocalDateTime expireDate = dmsDate.plusYears(3).withHour(23).withMinute(59).withSecond(59);
        FufilmentMessage message = new FufilmentMessage();
        message.setVin(originalDataDO.getCarVin());
        message.setServiceBeginDate(expireDate.minusDays(1));
        message.setServiceEndDate(expireDate);
        message.setVcsOrderCode(ecpIdUtil.nextIdStr());
        message.setTenantId(TenantContextHolder.getTenantId());
        // 同步APPD和联通服务
        log.info("VIN补录同步APPD到期时间, message:{}, jlrSubscriptionId:{}", message, originalDataDO.getJlrSubscriptionId());
        boolean appdSuccess = appDService.callAppDService(message, null, originalDataDO.getJlrSubscriptionId());
        log.info("VIN补录同步联通到期时间, message:{}, iccid:{}", message, originalDataDO.getIccid());
        CommonResult<UnicomRespVO> result = unicomService.unicomRenewalByIccid(message, originalDataDO.getIccid());
        boolean cuSuccess = result.isSuccess() && UnicomResultEnum.SUCCESS.getDesc().equals(result.getData().getResponseDesc());
        recordsDO.setAppdSyncResult(appdSuccess ? YesOrNoStatusEnum.SUCCESS.getCode() : YesOrNoStatusEnum.FAIL.getCode());
        recordsDO.setCuSyncResult(cuSuccess ? YesOrNoStatusEnum.SUCCESS.getCode() : YesOrNoStatusEnum.FAIL.getCode());
        recordsDO.setUpdatedTime(LocalDateTime.now());
        // 两个系统都成功更新状态为同步成功，否则为失败
        originalDataDO.setUpdatedTime(LocalDateTime.now());
        originalDataDO.setSyncStatus(InitializeStatusEnum.getCodeByResult(appdSuccess, cuSuccess));

        manualDO.setSyncStatus(originalDataDO.getSyncStatus());
        String errorMessage = InitializeStatusEnum.getErrorMessage(originalDataDO.getSyncStatus());
        manualDO.setErrorMessage(errorMessage);
        manualDO.setUpdatedTime(LocalDateTime.now());
    }

    /**
     * 更新手动数据校验状态
     * 根据原始数据DO更新手动DO的校验结果和状态
     *
     * @param originalDataDO 原始数据DO，包含各种校验结果
     * @param manualDO 手动数据DO，需要更新的DO对象
     */
    private static void updateManualDOCheckStatus(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataManualDO manualDO) {
        manualDO.setAmapResult(originalDataDO.getAmapResult());
        manualDO.setAppdResult(originalDataDO.getAppdResult());
        manualDO.setSpecialVinConfig(originalDataDO.getSpecialVinConfig());
        manualDO.setCuResult(originalDataDO.getCuResult());
        manualDO.setDpResult(originalDataDO.getDpResult());
        manualDO.setPiviConfigResult(originalDataDO.getPiviConfigResult());
        // 待入库则不需要错误信息
        if (VinMatchResultEnum.PENDING.getCode().equals(originalDataDO.getVinMatchResult())) {
            return;
        }
        // 无需入库则为初始化失败
        manualDO.setStatus(InitialStateEnum.FAIL.getCode());
        List<String> errorList = Stream.of(
                        AMapResultEnum.getErrorMessage(manualDO.getAmapResult()),
                        AppDResultEnum.getErrorMessage(manualDO.getAppdResult()),
                        CuResultEnum.getErrorMessage(manualDO.getCuResult()),
                        DpResultEnum.getErrorMessage(manualDO.getDpResult()),
                        PIVIResultEnum.getErrorMessage(manualDO.getPiviConfigResult())
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errorList)) {
            manualDO.setErrorMessage(String.join("；", errorList));
        }
    }

    @Override
    public boolean checkSOTA(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO) {
        String carVin = originalDataDO.getCarVin();
        SOTAResultVO response = sotaService.getSOTAInfoByVin(carVin);
        log.info("BAU获取SOTA信息, carVin:{}, response:{}", carVin, response);
        if (CharSequenceUtil.isNotBlank(response.getQueryResult())) {
            log.warn("BAU check时查询SOTA失败异常, vin:{}", carVin);
            recordsDO.setSotaResult(response.getQueryResult());
            originalDataDO.setStatus(HandleStatusEnum.FAIL.getCode());
            originalDataDO.setSotaResult(SotaResultEnum.EMPTY_DATA.getCode());
            return false;
        }
        // 只要有响应, 皆为请求成功
        recordsDO.setSotaResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), JSON.toJSONString(response)));
        if (CharSequenceUtil.isBlank(response.getEsimiccid())) {
            log.warn("BAU check时查询SOTA失败, esmiccid为空, vin:{}", carVin);
            originalDataDO.setSotaResult(SotaResultEnum.EMPTY_ICCID.getCode());
            return false;
        }
        log.info("BAU check时查询SOTA成功, vin:{}", carVin);
        originalDataDO.setIccid(response.getEsimiccid());
        originalDataDO.setSotaResult(SotaResultEnum.SUCCESS.getCode());
        return true;
    }

    @Override
    public boolean checkCU(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO) {
        String iccid = originalDataDO.getIccid();
        UnicomRespVO simCardInfo = unicomService.getSimCardInfo(iccid);
        log.info("BAU获取CU信息, iccid:{}, response:{}", iccid, simCardInfo);
        if (CharSequenceUtil.isNotBlank(simCardInfo.getQueryResult())) {
            log.warn("BAU check时查询联通异常, iccid:{}", iccid);
            recordsDO.setCuResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), simCardInfo.getQueryResult()));
            originalDataDO.setStatus(HandleStatusEnum.FAIL.getCode());
            originalDataDO.setCuResult(CuResultEnum.SYSTEM_ERROR.getCode());
            return false;
        }
        // 只要有响应, 皆为请求成功
        recordsDO.setCuResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), JSON.toJSONString(simCardInfo)));
        if (!UnicomResultEnum.SUCCESS.getDesc().equals(simCardInfo.getResponseDesc())) {
            log.warn("BAU check时查询联通失败, iccid:{}", iccid);
            Integer code = UnicomResultEnum.getCuResultByCode(simCardInfo.getResponseCode());
            if (Objects.isNull(code)) {
                log.warn("BAU check时联通响应编码非法:{}", simCardInfo.getResponseCode());
                originalDataDO.setCuResult(CuResultEnum.SYSTEM_ERROR.getCode());
                return false;
            }
            originalDataDO.setCuResult(code);
            return false;
        }
        UnicomRespData unicomRespData = simCardInfo.getUnicomRespData();
        if (Objects.isNull(unicomRespData) || Objects.isNull(unicomRespData.getSimCardInfo())
                || CharSequenceUtil.isBlank(unicomRespData.getSimCardInfo().getIccid())) {
            log.warn("BAU check时未查询到iccid数据, iccid:{}", iccid);
            originalDataDO.setCuResult(CuResultEnum.VIN_NOT_FOUND.getCode());
            return false;
        }
        log.info("BAU check时查询联通成功, vin:{}", originalDataDO.getCarVin());
        originalDataDO.setCuResult(CuResultEnum.SUCCESS.getCode());
        return true;
    }

    @Override
    public boolean checkAPPD(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO) {
        String vin = originalDataDO.getCarVin();
        AppDSubscriptionResp response = appDService.getVinSubscriptions(vin);
        log.info("BAU获取APPD车辆信息, carVin:{}, response:{}", vin, response);
        if (CharSequenceUtil.isNotBlank(response.getQueryResult())) {
            log.warn("BAU check时查询APPD信息异常, vin:{}", vin);
            recordsDO.setAppdResult(response.getQueryResult());
            originalDataDO.setStatus(HandleStatusEnum.FAIL.getCode());
            originalDataDO.setAppdResult(response.getResultCode());
            return false;
        }
        // 只要有响应, 皆为请求成功
        recordsDO.setAppdResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), JSON.toJSONString(response)));
        List<AppDSubscriptionData> respList = response.getResult();
        if (CollUtil.isEmpty(respList)) {
            log.warn("BAU check时查询APPD成功, 但响应数据为空, vin={}", vin);
            originalDataDO.setAppdResult(AppDResultEnum.VIN_NOT_FOUND.getCode());
            return false;
        }
        AppDSubscriptionData subscriptionData = respList.get(0);
        log.info("BAU check时调用APPD查询jlrSubscriptionId成功, data={}", subscriptionData);
        if (Objects.isNull(subscriptionData.getExpiresDate())) {
            log.warn("BAU check时expiresDate为空, vin:{}", vin);
            originalDataDO.setAppdResult(AppDResultEnum.EXPIRY_DATE_NOT_FOUND.getCode());
            return false;
        }
        if (Objects.isNull(subscriptionData.getJlrSubscriptionId())) {
            log.warn("BAU check时jlrSubscriptionId为空, vin:{}", vin);
            originalDataDO.setAppdResult(AppDResultEnum.JLR_SUBSCRIPTION_ID_NOT_FOUND.getCode());
            return false;
        }
        log.info("BAU check时查询APPD成功, vin:{}", vin);
        originalDataDO.setJlrSubscriptionId(subscriptionData.getJlrSubscriptionId());
        originalDataDO.setAppdResult(AppDResultEnum.SUCCESS.getCode());
        return true;
    }

    @Override
    public boolean checkAMAP(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO) {
        String carVin = originalDataDO.getCarVin();
        AmaPCarInfoResponse response = amaPService.queryAmaPExpireDate(carVin);
        log.info("BAU获取AMAP车辆信息, vin:{}, response:{}", carVin, response);
        if (CharSequenceUtil.isNotBlank(response.getQueryResult())) {
            log.warn("BAU check时查询到AMAP异常, vin:{}", carVin);
            recordsDO.setAmapResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), response.getQueryResult()));
            originalDataDO.setStatus(HandleStatusEnum.FAIL.getCode());
            originalDataDO.setAmapResult(AMapResultEnum.SYSTEM_ERROR.getCode());
            return false;
        }
        // 只要有响应, 皆为请求成功
        recordsDO.setAmapResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), JSON.toJSONString(response)));
        if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(response.getCode())) {
            log.warn("BAU check时查询AMAP失败, vin:{}", carVin);
            Integer code = AmaPErrorCode.getAMapResultByCode(response.getCode());
            if (Objects.isNull(code)) {
                log.warn("BAU check时AMAP响应编码非法:{}", response.getCode());
                originalDataDO.setAmapResult(AMapResultEnum.SYSTEM_ERROR.getCode());
                return false;
            }
            originalDataDO.setAmapResult(code);
            return false;
        }
        if (Objects.isNull(response.getData()) || CollUtil.isEmpty(response.getData().getPermissions())) {
            log.warn("BAU check时未查询到AMAP数据, vin:{}", carVin);
            originalDataDO.setAmapResult(AMapResultEnum.VIN_NOT_FOUND.getCode());
            return false;
        }
        List<AmaPPermissionInfo> pPermissionInfoList = response.getData().getPermissions();
        LocalDateTime amaPExpireDate = pPermissionInfoList.get(0).getEndTime();
        if (Objects.isNull(amaPExpireDate)) {
            log.warn("BAU check时查询到AMap过期时间为空, vin:{}", carVin);
            originalDataDO.setAmapResult(AMapResultEnum.EXPIRY_DATE_NOT_FOUND.getCode());
            return false;
        }
        log.info("BAU check时查询AMAP成功, vin:{}", carVin);
        originalDataDO.setAmaPExpireDate(amaPExpireDate);
        originalDataDO.setAmapResult(AMapResultEnum.SUCCESS.getCode());
        return true;
    }

    @Override
    public String checkDP(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO) {
        String carVin = originalDataDO.getCarVin();
        UserDPResultVO resultVO = vehicleModelMasterDataService.findDp(carVin);
        log.info("BAU获取DP信息, vin:{}, response:{}", carVin, resultVO);
        if (CharSequenceUtil.isNotBlank(resultVO.getQueryResult())) {
            log.warn("BAU check时查询到DP异常, vin:{}", carVin);
            recordsDO.setDpResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), resultVO.getQueryResult()));
            originalDataDO.setStatus(HandleStatusEnum.FAIL.getCode());
            originalDataDO.setDpResult(DpResultEnum.SYSTEM_ERROR.getCode());
            originalDataDO.setPiviConfigResult(PIVIResultEnum.INTERFACE_ERROR.getCode());
            return null;
        }
        // 只要有响应, 皆为请求成功
        recordsDO.setDpResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), JSON.toJSONString(resultVO)));
        if (CharSequenceUtil.isBlank(resultVO.getConfigCode())) {
            log.warn("BAU check时查询DP数据为空, vin:{}", carVin);
            originalDataDO.setDpResult(DpResultEnum.EMPTY_DATA.getCode());
            originalDataDO.setPiviConfigResult(PIVIResultEnum.INTERFACE_ERROR.getCode());
            return null;
        }
        log.info("BAU check时查询DP成功, vin:{}", carVin);
        originalDataDO.setDpResult(DpResultEnum.SUCCESS.getCode());
        boolean checkPIVI = CarSystemModelEnum.PIVI.getCode().equals(resultVO.getCarSystemModel());
        originalDataDO.setPiviConfigResult(checkPIVI ? PIVIResultEnum.SUCCESS.getCode() : PIVIResultEnum.NOT_PIVI_CAR.getCode());
        return resultVO.getConfigCode();
    }

    @Override
    public void judgeSpecialGroup(DmsOssOriginalDataDO originalDataDO, String configCode,
                                  DmsOssOriginalDataRecordsDO recordsDO) {
        VehicleConfigGroupDO configGroupDO = getByConfigCode(configCode);
        if (Objects.isNull(configGroupDO)) {
            String format = String.format("vin:%s的车型%s不在特殊履约方式的车型配置编码表", originalDataDO.getCarVin(), configCode);
            recordsDO.setAppdResult(recordsDO.getAppdResult() + ", " + format);
            log.info("BAU check时该车型不在特殊履约方式的车型配置编码表, vin={}, configCode={}", originalDataDO.getCarVin(), configCode);
        } else {
            // 设置VIN待入库ECP
            originalDataDO.setVinMatchResult(VinMatchResultEnum.PENDING.getCode());
            originalDataDO.setSpecialVinConfig(configGroupDO.getGroupId());
        }
    }

    /**
     * 根据配置编码查找配置组
     * <p>
     * 本方法旨在通过配置编码（configCode）获取相应的配置组信息
     * 它通过查询数据库中配置编码匹配且未被逻辑删除的配置组记录来实现
     * 由于可能只需要一个匹配项，因此查询结果被限制为一条记录
     *
     * @param configCode 配置编码，用于标识特定的配置组
     * @return 返回匹配的配置组记录（VehicleConfigGroupDO实例）
     * 如果没有找到匹配项，则返回null
     */
    private VehicleConfigGroupDO getByConfigCode(String configCode) {
        if (CharSequenceUtil.isBlank(configCode)) {
            return null;
        }
        return vehicleConfigGroupService.getByConfigCode(configCode);
    }

    /**
     * 校验发票到期日期。
     *
     * @param invoiceDateStr 发票日期，字符串格式。
     * @return 返回计算得到的发票到期日期，如果输入日期格式不正确，则返回null。
     */
    private boolean checkExpireDate(String invoiceDateStr) {
        try {
            TimeFormatUtil.stringToLocalDate(invoiceDateStr);
            return true;
        } catch (Exception e) {
            log.error("将符合指定格式的字符串转换为LocalDateTime对象解析时间异常，time:{}", invoiceDateStr);
            return false;
        }
    }
}

