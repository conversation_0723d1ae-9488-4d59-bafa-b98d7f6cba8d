package com.jlr.ecp.subscription.enums.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum QueryUploadResultEnum {
    FAIL(0, "上传失败"),
    SUCCESS(1, "上传成功");

    private final Integer code;

    private final String desc;

    public static String getDescByCode(Integer code) {
        for (QueryUploadResultEnum queryUploadResultEnum : QueryUploadResultEnum.values()) {
            if (queryUploadResultEnum.getCode().equals(code)) {
                return queryUploadResultEnum.getDesc();
            }
        }
        return null;
    }
}
