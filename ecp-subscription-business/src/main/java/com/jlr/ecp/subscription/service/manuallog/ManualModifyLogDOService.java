package com.jlr.ecp.subscription.service.manuallog;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageReqDTO;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageRespVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.manuallog.ManualModifyLogDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_manual_modify_log(t_manual_modify_log)】的数据库操作Service
* @createDate 2024-11-19 19:44:40
*/
public interface ManualModifyLogDOService extends IService<ManualModifyLogDO> {


    /**
     * 查询中心分页列表
     * @param dto 入参
     * @return ManualLogPageRespVO
     */
    PageResult<ManualLogPageRespVO> getLogPage(ManualLogPageReqDTO dto);


    void recordLog(AmaPRenewRecordsDO amaPRenewRecordsDO);
    void recordLogAmapList(List<AmaPRenewRecordsDO> amaPRenewRecordsDOList);
    void recordLog(IccidModifyRecordsDO iccidModifyRecordsDO);
    void recordLogIccIdModifyList(List<IccidModifyRecordsDO> iccidModifyRecordsDOList);
    void recordLog(AppDCuRenewRecords appDCuRenewRecords);
    void recordLog(RemoteRenewDetailRecords remoteRenewDetailRecords);


    void recordLog(PIVIPackageLogDO piviPackageLogDO);

    void recordManualLog(PIVIPackageLogDO piviPackageLogDO);
}
