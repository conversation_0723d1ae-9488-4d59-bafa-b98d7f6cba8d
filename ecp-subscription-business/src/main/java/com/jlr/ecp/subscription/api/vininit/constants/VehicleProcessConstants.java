package com.jlr.ecp.subscription.api.vininit.constants;

/**
 * 车辆处理相关常量
 *
 * <AUTHOR>
 */
public class VehicleProcessConstants {
    
    /**
     * 默认DP批量查询大小
     */
    public static final int DEFAULT_DP_BATCH_SIZE = 100;
    
    /**
     * 默认分批处理大小
     */
    public static final int DEFAULT_SPLIT_SIZE = 1000;
    
    /**
     * 默认最大失败次数
     */
    public static final int MISS_COUNT_MAX = 5;
    
    /**
     * 错误描述最大长度
     */
    public static final int ERROR_DESC_MAX_LENGTH = 250;
    
    /**
     * 处理状态
     */
    public static class ProcessStatus {
        public static final int PENDING = 1;    // 待处理
        public static final int SUCCESS = 2;    // 处理成功
        public static final int FAILED = 3;     // 处理失败
    }
    
    /**
     * 线程池配置
     */
    public static class ThreadPoolConfig {
        /**
         * CPU核心数
         */
        private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();

        /**
         * 核心线程数 = CPU数量
         */
        public static final int CORE_POOL_SIZE = CPU_COUNT;

        /**
         * 最大线程数 = CPU数量 * 2 + 1
         */
        public static final int MAX_POOL_SIZE = CPU_COUNT * 2 + 1;

        /**
         * 队列容量
         */
        public static final int QUEUE_CAPACITY = 10000;

        /**
         * 线程名前缀
         */
        public static final String THREAD_NAME_PREFIX = "vehicle-process-";

        /**
         * 线程空闲时间（秒）
         */
        public static final int KEEP_ALIVE_SECONDS = 60;

        /**
         * 获取CPU核心数
         */
        public static int getCpuCount() {
            return CPU_COUNT;
        }
    }
    
    /**
     * 批量处理配置
     */
    public static class BatchConfig {
        public static final int DEFAULT_BATCH_SIZE = 1000;
        public static final int DP_QUERY_BATCH_SIZE = 100;

        /**
         * VIN数据持久化批次大小
         * 设置为连接池最大连接数的一半，避免连接池耗尽
         */
        public static final int VIN_PERSIST_BATCH_SIZE = 10;
    }
}
