package com.jlr.ecp.subscription.dal.dataobject.oss;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_dms_oss_original_data表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_dms_oss_original_data")
public class DmsOssOriginalDataDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * BAU任务ID;BAU任务ID
     */
    @TableField(value = "bau_job_id")
    private Long bauJobId;

    /**
     * 数据id;雪花算法ID
     */
    @TableField(value = "data_id")
    private Long dataId;

    /**
     * 任务日期;任务日期
     */
    @TableField(value = "job_date")
    private LocalDateTime jobDate;

    /**
     * VIN;VIN
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 发票日期;发票日期
     */
    @TableField(value = "dms_invoice_date")
    private String dmsInvoiceDate;

    /**
     * 处理状态;数据处理状态 0：待处理 1：处理成功 2:处理失败
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 同步状态;同步过期日期状态 0：未同步 1：同步成功 2：APPD同步失败 3：CU同步失败 4：均同步失败
     */
    @TableField(value = "sync_status")
    private Integer syncStatus;

    /**
     * sota查询结果;sota查询结果 0：失败 1：成功
     */
    @TableField(value = "sota_result")
    private Integer sotaResult;

    /**
     * appd查询结果;appd查询结果 0：失败 1：成功
     */
    @TableField(value = "appd_result")
    private Integer appdResult;

    /**
     * 联通查询结果;cu查询结果 0：失败 1：成功
     */
    @TableField(value = "cu_result")
    private Integer cuResult;

    /**
     * amap查询结果;amap查询结果 0：失败 1：成功
     */
    @TableField(value = "amap_result")
    private Integer amapResult;

    /**
     * DP查询结果;dp查询结果 0：失败 1：成功
     */
    @TableField(value = "dp_result")
    private Integer dpResult;

    /**
     * PIVI车机匹配结果;PIVI车机匹配结果 0：非PIVI 1：PIVI
     */
    @TableField(value = "pivi_config_result")
    private Integer piviConfigResult;

    /**
     * VIN处理结果;VIN处理结果是否入库ECP 0：无需入库 1：待入库 2：成功入库
     */
    @TableField(value = "vin_match_result")
    private Integer vinMatchResult;

    /**
     * 特殊车型
     */
    @TableField(value = "special_vin_config")
    private String specialVinConfig;

    /**
     * iccid
     */
    @TableField(value = "iccid")
    private String iccid;

    /**
     * APPD jlr_subscription_id
     */
    @TableField(value = "jlr_subscription_id")
    private Long jlrSubscriptionId;

    /**
     * AMAP到期时间
     * */
    @TableField(value = "amap_expire_date")
    private LocalDateTime amaPExpireDate;

    /**
     * 数据来源 1：BAU JOB 2：手动补录
     * */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * APPD刷数标识，1：是 0：否
     * */
    @TableField(value = "appd_job_flag")
    private Integer appdJobFlag;
}

