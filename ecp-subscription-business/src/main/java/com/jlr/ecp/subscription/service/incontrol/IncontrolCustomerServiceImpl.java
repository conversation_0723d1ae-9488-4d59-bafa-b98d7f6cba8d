package com.jlr.ecp.subscription.service.incontrol;

import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.mysql.incontrol.IncontrolCustomerMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IncontrolCustomerServiceImpl implements IncontrolCustomerService{

    @Resource
    IncontrolCustomerMapper incontrolCustomerMapper;


    @Override
    public void saveOrUpdateIncontrolCustomerWithProactive(String inControlId) {
        // inControlId为空不做处理
        if (CharSequenceUtil.isBlank(inControlId)) {
            return;
        }
        IncontrolCustomerDO existConsumer = incontrolCustomerMapper.selectOne(IncontrolCustomerDO::getIncontrolId, inControlId);
        log.info("existConsumer信息 :{}",existConsumer);
        if (existConsumer == null){
            IncontrolCustomerDO insertConsumer = new IncontrolCustomerDO();
            insertConsumer.setIncontrolId(inControlId.toLowerCase());
            insertConsumer.setSource(Constants.PROACTIVE);
            insertConsumer.setLastLoginTime(LocalDateTime.now());
            incontrolCustomerMapper.insert(insertConsumer);
        }else {
            IncontrolCustomerDO updateConsumer = new IncontrolCustomerDO();
            updateConsumer.setId(existConsumer.getId());
            updateConsumer.setLastLoginTime(LocalDateTime.now());
            updateConsumer.setRevision(existConsumer.getRevision());
            incontrolCustomerMapper.updateById(updateConsumer);
        }
    }

    @Override
    public void saveOrUpdateIncontrolCustomerWithTSDP(Set<IncontrolCustomerDO> customerSet) {
        //处理TSDP返回的IncontrolCustomer集合为map，并同时处理incontrolId为小写
        Map<String, IncontrolCustomerDO> customerMap = customerSet.stream()
                .peek(customer -> customer.setIncontrolId(customer.getIncontrolId().toLowerCase())) // 将 incontrolId 设置为小写
                .collect(Collectors.toMap(k -> k.getIncontrolId(), v -> v, (v1, v2) -> v1));
        List<IncontrolCustomerDO> existCustomer = incontrolCustomerMapper.selectList(IncontrolCustomerDO::getIncontrolId, customerMap.keySet());
        //将existCustomer里的IncontrolCustomerDO的incontrolId字段都转换为小写
        existCustomer.forEach(customer -> customer.setIncontrolId(customer.getIncontrolId().toLowerCase()));
        ArrayList<IncontrolCustomerDO> updateList = Lists.newArrayListWithExpectedSize(existCustomer.size());
        for (IncontrolCustomerDO customerDO : existCustomer) {
            IncontrolCustomerDO updateCustomer = customerMap.get(customerDO.getIncontrolId());
            if (updateCustomer != null){
                updateCustomer.setId(customerDO.getId());
                updateCustomer.setRevision(customerDO.getRevision());
                updateCustomer.setLastLoginTime(LocalDateTime.now());
                updateCustomer.setFirstName(updateCustomer.getFirstName());
                updateCustomer.setSurname(updateCustomer.getSurname());
                updateCustomer.setPhoneEncrypt(updateCustomer.getPhoneEncrypt());
                updateList.add(updateCustomer);
                customerMap.remove(customerDO.getIncontrolId());
            }
        }
        log.info("本次定时任务会更新t_incontrol_customer表:{}条数据",updateList.size());
        ArrayList<IncontrolCustomerDO> insertList = Lists.newArrayListWithExpectedSize(customerMap.size());
        for (IncontrolCustomerDO insertDO : customerMap.values()) {
            insertDO.setSource(Constants.EXPIRE_QRY);
            insertDO.setLastLoginTime(LocalDateTime.now());
            insertList.add(insertDO);
        }
        log.info("本次定时任务会插入t_incontrol_customer表:{}条数据",insertList.size());
        updateList.addAll(insertList);
        incontrolCustomerMapper.saveOrUpdateBatch(updateList);
    }

    @Override
    public void saveOrUpdateIncontrolCustomerByQueryVin(String inControlId, String firstName, String surName, String phone) {
        // inControlId为空不做处理
        if (CharSequenceUtil.isBlank(inControlId)) {
            return;
        }
        IncontrolCustomerDO existConsumer = incontrolCustomerMapper.selectOne(IncontrolCustomerDO::getIncontrolId, inControlId);
        log.info("existConsumer信息 :{}",existConsumer);
        if (existConsumer == null){
            IncontrolCustomerDO insertConsumer = new IncontrolCustomerDO();
            insertConsumer.setIncontrolId(inControlId.toLowerCase());
            insertConsumer.setSource(Constants.PROACTIVE);
            insertConsumer.setFirstName(firstName);
            insertConsumer.setSurname(surName);
            insertConsumer.setPhoneEncrypt(phone);
            insertConsumer.setLastLoginTime(LocalDateTime.now());
            incontrolCustomerMapper.insert(insertConsumer);
        }else {
            IncontrolCustomerDO updateConsumer = new IncontrolCustomerDO();
            updateConsumer.setId(existConsumer.getId());
            updateConsumer.setLastLoginTime(LocalDateTime.now());
            updateConsumer.setRevision(existConsumer.getRevision());
            updateConsumer.setFirstName(firstName);
            updateConsumer.setSurname(surName);
            updateConsumer.setPhoneEncrypt(phone);
            incontrolCustomerMapper.updateById(updateConsumer);
        }
    }
}
