package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;

import java.util.List;

/**
 * 车系品牌映射数据Repository接口
 *
 */
public interface SeriesBrandMappingDataRepository extends IService<SeriesBrandMappingDataDO> {

    /**
     * 根据系列代码查询映射数据
     *
     * @param seriesCode 系列代码
     * @return 映射数据
     */
    SeriesBrandMappingDataDO findBySeriesCode(String seriesCode);

    /**
     * 根据系列代码列表查询映射数据
     *
     * @param seriesCodeList 系列代码列表
     * @return 映射数据列表
     */
    List<SeriesBrandMappingDataDO> findBySeriesCodeList(List<String> seriesCodeList);

    /**
     * 插入单个映射数据
     *
     * @param mappingDataDO 映射数据
     * @return 插入数量
     */
    int insert(SeriesBrandMappingDataDO mappingDataDO);

    /**
     * 更新映射数据
     *
     * @param mappingDataDO 映射数据
     * @return 更新数量
     */
    boolean updateById(SeriesBrandMappingDataDO mappingDataDO);

    /**
     * 批量插入映射数据
     *
     * @param mappingDataList 映射数据列表
     * @return 插入数量
     */
    boolean insertBatch(List<SeriesBrandMappingDataDO> mappingDataList);

    /**
     * 查询所有有效的映射数据
     *
     * @return 映射数据列表
     */
    List<SeriesBrandMappingDataDO> findAllValid();

    /**
     * 根据ID查询映射数据
     *
     * @param id 主键ID
     * @return 映射数据
     */
    SeriesBrandMappingDataDO findById(Long id);
}
