package com.jlr.ecp.subscription.service.pivi.impl;

import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.service.pivi.PIVIPackageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * PIVIPackageService接口实现
 */
@Service
@Slf4j
public class PIVIPackageServiceImpl implements PIVIPackageService {

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Override
    public void insertBatch(List<PIVIPackageDO> packageDOList) {
        piviPackageDOMapper.insertBatch(packageDOList);
    }

    @Override
    public List<PIVIPackageDO> queryByVinList(List<String> vinList) {
        return piviPackageDOMapper.selectList(new LambdaQueryWrapperX<PIVIPackageDO>()
                .in(PIVIPackageDO::getVin, vinList)
                .eq(PIVIPackageDO::getIsDeleted, false));
    }

    @Override
    public PIVIPackageDO queryByVin(String vin) {
        return piviPackageDOMapper.selectOne(new LambdaQueryWrapperX<PIVIPackageDO>()
                .in(PIVIPackageDO::getVin, vin)
                .eq(PIVIPackageDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));
    }

    @Override
    public void insert(PIVIPackageDO piviPackageDO) {
        piviPackageDOMapper.insert(piviPackageDO);
    }
}
