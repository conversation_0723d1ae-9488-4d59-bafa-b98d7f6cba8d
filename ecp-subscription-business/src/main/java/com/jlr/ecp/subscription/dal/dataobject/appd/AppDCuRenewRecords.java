package com.jlr.ecp.subscription.dal.dataobject.appd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_appdcu_renew_records")
public class AppDCuRenewRecords extends BaseDO {
    // 主键
    @TableId(value = "id")
    private Long id;

    // 续费编号；对于批量续费就是批次号
    @TableField("renew_no")
    private Long renewNo;

    // VIN
    @TableField("car_vin")
    private String carVin;

    // 续费服务类型 1：APPD 2:CU
    @TableField("renew_service_type")
    private Integer renewServiceType;

    // 续费日期
    @TableField("renew_date")
    private LocalDateTime renewDate;

    // 客户订单号；客户订单号for联通续费ext_book_id，雪花算法ID
    @TableField("cus_order_id")
    private String cusOrderId;

    // 操作人账号
    @TableField("operator")
    private String operator;

    // 数据来源 1单次续费 2批量续费
    @TableField("data_source")
    private Integer dataSource;

    // 续费状态 0：待续费 2：续费成功 3：续费失败
    @TableField("renew_status")
    private Integer renewStatus;

    // 续费前到期日
    @TableField("renew_before_expiry_date")
    private LocalDateTime renewBeforeExpiryDate;

    // 续费后到期日
    @TableField("renew_after_expiry_date")
    private LocalDateTime renewAfterExpiryDate;

    // 续费结果
    @TableField("order_result_code")
    private String orderResultCode;

    // 续费失败原因
    @TableField("error_desc")
    private String errorDesc;

    // 租户号
    @TableField("tenant_id")
    private Integer tenantId;
}
