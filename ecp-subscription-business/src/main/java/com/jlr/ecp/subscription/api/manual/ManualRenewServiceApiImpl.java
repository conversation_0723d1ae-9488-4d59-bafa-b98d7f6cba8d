package com.jlr.ecp.subscription.api.manual;

import cn.hutool.core.text.CharSequenceUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.manual.dto.CheckRecordsInTransitDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuSingleRenewalDTO;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.service.amap.AmaPSingleManualRenewalService;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.remote.RemoteSingleRenewalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@RestController
@Validated
@Slf4j
public class ManualRenewServiceApiImpl implements ManualRenewServiceApi {

    @Resource
    private AmaPSingleManualRenewalService amaPSingleManualRenewalService;

    @Resource
    private AppDCuSingleRenewalService appDCuSingleRenewalService;

    @Resource
    private RemoteSingleRenewalService remoteSingleRenewalService;

    @Override
    public CommonResult<Boolean> checkRecordsInTransit(List<CheckRecordsInTransitDTO> dtoList) {
        log.info("checkRecordsInTransit校验是否存在在途手动续费记录, dtoList:{}", dtoList);
        // 遍历dtoList，根据服务类型查询并校验是否存在在途手动续费记录
        for (CheckRecordsInTransitDTO dto : dtoList) {
            if (Objects.isNull(dto) || CharSequenceUtil.isBlank(dto.getCarVin())) {
                continue;
            }
            if (checkRecordsByServiceType(dto)) {
                return CommonResult.success(true);
            }
        }
        return CommonResult.success(false);
    }

    /**
     * 根据服务类型检查手动续费的记录
     * 此方法旨在检查给定车辆识别号（carVin）和一组服务类型中是否存在特定的续费记录
     * 它主要关注REMOTE和PIVI服务类型，并根据服务类型调用不同的服务进行记录检查
     *
     * @param dto 包含车辆识别号和服务类型列表的传输对象，用于检查记录
     * @return 如果找到对应的续费记录，则返回true；否则返回false
     */
    private boolean checkRecordsByServiceType(CheckRecordsInTransitDTO dto) {
        String carVin = dto.getCarVin();
        for (Integer serviceType : dto.getServiceTypeList()) {
            if (ServiceTypeEnum.REMOTE.getCode().equals(serviceType)) {
                boolean checkRemoteRecord = remoteSingleRenewalService.checkProcessRecord(carVin);
                if (checkRemoteRecord) {
                    log.info("checkRecordsInTransit校验存在在途REMOTE手动续费记录, carVin:{}", carVin);
                    return true;
                }
            } else if (ServiceTypeEnum.PIVI.getCode().equals(serviceType)) {
                boolean checkAMapRecord = amaPSingleManualRenewalService.checkProcessRecord(carVin);
                if (checkAMapRecord) {
                    log.info("checkRecordsInTransit校验存在在途AMap手动续费记录, carVin:{}", carVin);
                    return true;
                }

                AppDCuSingleRenewalDTO appDCuSingleRenewalDTO = new AppDCuSingleRenewalDTO();
                appDCuSingleRenewalDTO.setCarVin(carVin);
                // 设置到期时间是为了后续方法中不为空，做APPD和CU的校验
                appDCuSingleRenewalDTO.setAppDExpireDate(LocalDateTime.now().toString());
                appDCuSingleRenewalDTO.setCuExpireDate(LocalDateTime.now().toString());
                boolean checkAppCuRecord = appDCuSingleRenewalService.checkProcessRecord(appDCuSingleRenewalDTO);
                if (checkAppCuRecord) {
                    log.info("checkRecordsInTransit校验存在在途AppD或Cu手动续费记录, carVin:{}", carVin);
                    return true;
                }
            }
        }
        return false;
    }
}
