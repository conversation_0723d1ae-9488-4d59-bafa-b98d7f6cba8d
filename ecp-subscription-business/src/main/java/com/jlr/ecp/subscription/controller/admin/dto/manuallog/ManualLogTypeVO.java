package com.jlr.ecp.subscription.controller.admin.dto.manuallog;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "查询中心 - 修改日志分页req参数")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ManualLogTypeVO extends PageParam {

    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "文本")
    private String text;


}
