package com.jlr.ecp.subscription.dal.mysql.remotepackage;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.RemotePackageDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【t_remote_package(t_remote_package)】的数据库操作Mapper
* @createDate 2023-12-08 01:29:01
* @Entity com.jlr.ecp.subscription.dal.dataobject.remotepackage.RemotePackageDO
*/
@Mapper
public interface RemotePackageDOMapper extends BaseMapperX<RemotePackageDO> {


}




