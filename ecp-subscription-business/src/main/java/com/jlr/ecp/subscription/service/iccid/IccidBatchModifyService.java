package com.jlr.ecp.subscription.service.iccid;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.SendICCIDBatchModifyDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidBatchModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyUploadVO;
import org.springframework.web.multipart.MultipartFile;

public interface IccidBatchModifyService {

    /**
     * 获取ICCID批量修改上传模版
     *
     * @return 模板的URL地址
     */
    String getIccidTemplateUrl();


    /**
     * 上传ICCID批量修改文件
     *
     * @param multipartFile 文件
     * @return 上传结果
     */
    CommonResult<IccidBatchModifyUploadVO> uploadIccidExcelModify(MultipartFile multipartFile);

    /**
     * ICCID批量修改日志分页
     *
     * @param pageDto 分页参数
     * @return 分页结果
     */
    PageResult<IccidBatchModifyPageVO> queryBatchModifyPageList(IccidBatchModifyPageDTO pageDto);

    /**
     * ICCID批量修改发送
     *
     * @param iccidBatchModifyDTO 发送参数
     * @return CommonResult<String> 返回操作结果的封装对象，包含成功、错误等状态及相应消息
     */
    CommonResult<String> batchModifyIccid(SendICCIDBatchModifyDTO iccidBatchModifyDTO);
}
