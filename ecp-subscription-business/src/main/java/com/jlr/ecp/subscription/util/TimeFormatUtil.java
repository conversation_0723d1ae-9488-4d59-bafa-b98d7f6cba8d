package com.jlr.ecp.subscription.util;

import cn.smallbun.screw.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
@Slf4j
public class TimeFormatUtil {
    public static final DateTimeFormatter formatter_1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 创建格式器
    public static final DateTimeFormatter formatter_2 = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    public static final DateTimeFormatter formatter_3 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter formatter_4 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static final DateTimeFormatter formatter_5 = DateTimeFormatter.ofPattern("yyyy-M-d HH:mm");
    public static final DateTimeFormatter formatter_6 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    public static final DateTimeFormatter formatter_7 = DateTimeFormatter.ofPattern("yyyy/MM/dd");


    /**
     *  localDate转化为String
     * @param  dateTime 时间
     * @return String
     * */
    public static String localDateToString(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return "";
        }
        // 使用格式化器将 LocalDateTime 转换为字符串
        return dateTime.format(formatter_1);
    }

    /**
     * 把string转化为LocalDateTime
     * @param dateString  时间
     * @return LocalDateTime
     * */
    public static LocalDateTime stringToLocalDate(String dateString) {
        return stringToLocalDate(dateString, formatter_1);
    }

    /**
     *  转化为代理商需要的时间格式
     * @param  localDateTime 时间
     * @return String
     * */
    public static String changeToSendTime(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return "";
        }
        return localDateTime.format(formatter_2);
    }

    /**
     * 转化时间格式
     * @param time 时间
     * @return LocalDateTime 时间
     * */
    public static LocalDateTime changeTimeFormat(LocalDateTime time) {
        String str = localDateToString(time);
        return stringToLocalDate(str);
    }

    /**
     * LocalDateTime按照指定格式转化为String
     * @param time 时间
     * @param formatter 格式
     * @return String
     * */
    public static String timeToStringByFormat(LocalDateTime time, DateTimeFormatter formatter) {
        if (Objects.isNull(time)) {
            return "";
        }
        return time.format(formatter);
    }

    /**
     * 把string按照指定格式转化为LocalDateTime
     * @param dateString  时间
     * @param formatter 格式
     * @return LocalDateTime
     * */
    public static LocalDateTime stringToLocalDate(String dateString, DateTimeFormatter formatter) {
        if(StringUtils.isBlank(dateString)) {
            return null;
        }
        return LocalDateTime.parse(dateString, formatter);
    }

    /**
     * 把string按照指定格式转化为LocalDateTime
     * @param dateString  时间
     * @return LocalDateTime
     * */
    public static LocalDateTime dmsToLocalDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        List<DateTimeFormatter> formatters = Arrays.asList(
                DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/MM/d HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/M/dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/M/d HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/MM/dd H:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/MM/d H:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/M/dd H:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy/M/d H:mm:ss"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"),
                DateTimeFormatter.ofPattern("dd/M/yyyy HH:mm"),
                DateTimeFormatter.ofPattern("d/MM/yyyy HH:mm"),
                DateTimeFormatter.ofPattern("d/M/yyyy HH:mm"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy H:mm"),
                DateTimeFormatter.ofPattern("dd/M/yyyy H:mm"),
                DateTimeFormatter.ofPattern("d/MM/yyyy H:mm"),
                DateTimeFormatter.ofPattern("d/M/yyyy H:mm")
        );
        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDateTime.parse(dateString, formatter);
            } catch (DateTimeParseException e) {
                // Try the next formatter
                log.warn(e.getMessage());
            }
        }
        return null;
    }

    /**
     *  localDateTime转化为String
     * @param  dateTime 时间
     * @return String
     * */
    public static String localDateTimeToString(LocalDateTime dateTime, DateTimeFormatter formatter) {
        if (Objects.isNull(dateTime)) {
            return "";
        }
        // 使用格式化器将 LocalDateTime 转换为字符串
        return dateTime.format(formatter);
    }
}