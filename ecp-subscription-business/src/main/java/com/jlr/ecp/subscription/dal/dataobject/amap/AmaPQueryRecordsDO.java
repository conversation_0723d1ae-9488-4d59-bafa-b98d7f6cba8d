package com.jlr.ecp.subscription.dal.dataobject.amap;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_amap_query_records")
public class AmaPQueryRecordsDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * VIN;
     */
    @TableField("car_vin")
    private String carVin;

    /**
     * 续费编号;续费编号，对于批量续费就是批次号
     */
    @TableField("renew_no")
    private Long renewNo;

    /**
     *  处理状态 0：查询中 1：查询完成 2：查询失败
     * */
    @TableField("query_status")
    private Integer queryStatus;

    /**
     * 操作人账号;操作人账号
     */
    @TableField("operator")
    private String operator;

    /**
     * status：服务状态 0：未激活 2：过期 3：使用中
     */
    @TableField("service_status")
    private Integer serviceStatus;

    /**
     * amap续费后到期日
     */
    @TableField("amap_expiry_date")
    private LocalDateTime amaPExpiryDate;

    /**
     * ecp到期日;
     */
    @TableField("ecp_expiry_date")
    private LocalDateTime ecpExpiryDate;


    /**
     *  高德查询结果 1成功 其他值失败
     * */
    @TableField("result_code")
    private String resultCode;

    /**
     * 续费失败原因;续费失败原因
     */
    @TableField("error_desc")
    private String errorDesc;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
