package com.jlr.ecp.subscription.dal.dataobject.remotepackage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "t_pivi_package_log")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PIVIPackageLogDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 修改人
     * */
    @TableField(value = "modify_user")
    private String modifyUser;

    /**
     * 修改时间
     * */
    @TableField(value = "modify_time")
    private LocalDateTime modifyTime;

    /**
     * 车辆VIN
     * */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     *  修改前发票日期
     * */
    @TableField(value = "modify_invoice_date_before")
    private LocalDateTime modifyInvoiceDateBefore;

    /**
     *  修改后发票日期
     * */
    @TableField(value = "modify_invoice_date_after")
    private LocalDateTime modifyInvoiceDateAfter;



    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
