package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import com.jlr.ecp.subscription.dal.mysql.remoteservice.RemoteOriginalDataMapper;
import com.jlr.ecp.subscription.dal.repository.RemoteOriginalDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Remote原始数据Repository实现类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteOriginalDataRepositoryImpl extends ServiceImpl<RemoteOriginalDataMapper, RemoteOriginalDataDO> implements RemoteOriginalDataRepository {

    @Override
    public Page<RemoteOriginalDataDO> selectPage(Page<RemoteOriginalDataDO> page, String dataNo, Integer status, Integer missCountMax) {
        log.info("分页查询Remote原始数据, dataNo: {}, status: {}, missCountMax: {}, page: {}", dataNo, status, missCountMax, page.getCurrent());
        return page(page, new LambdaQueryWrapperX<RemoteOriginalDataDO>()
                .eqIfPresent(RemoteOriginalDataDO::getDataNo, dataNo)
                .eq(RemoteOriginalDataDO::getStatus, status)
                .lt(RemoteOriginalDataDO::getMissCount, missCountMax));
    }

    @Override
    public boolean insertBatch(List<RemoteOriginalDataDO> insertList) {
        log.info("批量插入Remote原始数据, size: {}", insertList.size());
        return saveBatch(insertList);
    }

    @Override
    public RemoteOriginalDataDO selectById(Long id) {
        log.info("根据ID查询Remote原始数据, id: {}", id);
        return getById(id);
    }

    @Override
    public List<RemoteOriginalDataDO> selectByIdList(List<Long> idList) {
        log.info("根据ID列表查询Remote原始数据, idList size: {}", idList.size());
        return listByIds(idList);
    }

    @Override
    public boolean updateById(RemoteOriginalDataDO remoteOriginalDataDO) {
        log.info("更新Remote原始数据, id: {}", remoteOriginalDataDO.getId());
        return super.updateById(remoteOriginalDataDO);
    }

    @Override
    public boolean updateBatch(List<RemoteOriginalDataDO> dataList) {
        log.info("批量更新Remote原始数据, size: {}", dataList.size());
        try {
            baseMapper.updateBatch(dataList);
        } catch (Exception e) {
            log.info("批量更新Remote原始数据异常：", e);
            return false;
        }
        return true;
    }
}
