package com.jlr.ecp.subscription.service.remotepackage;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageCreateDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackagePageReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.BatchUploadRespVO;
import com.jlr.ecp.subscription.api.remotepackage.vo.RemotePackageListRespVO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.RemotePackageDO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_remote_package(t_remote_package)】的数据库操作Service
* @createDate 2023-12-08 01:29:01
*/
public interface RemotePackageDOService extends IService<RemotePackageDO> {

    /**
     * 创建服务包配置
     * @param createDTO 创建入参
     * @return Boolean
     */
    Boolean createRemotePackage(RemotePackageCreateDTO createDTO);

    /**
     * 分页列表
     * @param dto
     * @return PageResult<RemotePackageListRespVO>
     */
    PageResult<RemotePackageListRespVO> getPage(RemotePackagePageReqDTO dto);

    /**
     * 删除
     * @param packageCode 服务包code
     * @return Boolean
     */
    Boolean deleteByPackageCode(String packageCode);

    /**
     * 批量上传
     * @param file,carSystemModel
     * @return BatchUploadRespVO
     */
    CommonResult<BatchUploadRespVO> processBatchUpload(MultipartFile file, String carSystemModel);

    /**
     * 获取模板地址
     * @return String
     */
    String getTemplateUrl();

    /**
     * 获取存在的PIVI车机服务包名
     * @param packageCodes
     * @return
     */
    List<String> getExistPackageCode(List<String> packageCodes);

    List<String> getAllPackageCode();

    List<String> getAllRemotePackageCode();
}
