package com.jlr.ecp.subscription.service.vin.expiry;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.api.unicom.dto.GenerateReportRequestV2;
import com.jlr.ecp.subscription.api.unicom.dto.MonthlyRnrBatchQueryDTO;
import com.jlr.ecp.subscription.api.unicom.vo.InitResponse;
import com.jlr.ecp.subscription.dal.dataobject.vin.expiry.VinExpiryMonthlyDetailDO;

import java.util.List;

public interface VinExpiryMonthlyDetailService extends IService<VinExpiryMonthlyDetailDO> {

    /**
     * 查询需要查询的ID
     * @param jobId
     * @return
     */
    List<Long> selectQueryVinRecordsByJobId(Long jobId);


    /**
     * 批量查询实名
     * @param monthlyRnrBatchQueryDTO
     * @return
     */
    Integer rnrBatchQueryExecutor(MonthlyRnrBatchQueryDTO monthlyRnrBatchQueryDTO);

    /**
     * 初始化VIN月到期记录
     * @param request
     * @return
     */
    InitResponse initializeRecords(GenerateReportRequestV2 request);

    /**
     * 获取 已经初始化的 数量
     * @param jobId
     * @return
     */
    Integer getInitializedCount(Long jobId);


    /**
     * 插入续费率report数据
     *
     * @param jobId 任务ID，用于标识需要处理的报告数据
     * @return 插入数据的结果，返回影响的记录数
     */
    Integer getInsertReportData(Long jobId);
}
