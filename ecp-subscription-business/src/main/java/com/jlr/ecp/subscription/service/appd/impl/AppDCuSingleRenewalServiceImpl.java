package com.jlr.ecp.subscription.service.appd.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuSingleRenewalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleExpireVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSinglePageListV0;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleRenewalV0;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.appd.AppDDataSourceEnum;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.CallActivationStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceModifyTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.LoginUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import com.jlr.ecp.subscription.util.pivi.UnicomMaxExpireTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppDCuSingleRenewalServiceImpl implements AppDCuSingleRenewalService {

    @Resource
    private PIVIAppDService piviAppDService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    private static final String TODAY_END = " 23:59:59";

    @Resource
    private Snowflake snowflake;

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private SubscriptionServiceMapper serviceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper serviceLogMapper;

    @Resource
    private OrderCheckService orderCheckService;

    /**
     * APPD和联通单个续费操作
     *
     * @param appDCuSingleRenewalDTO 续费请求对象，包含续费所需的各项参数
     * @return 返回一个表示操作结果的CommonResult对象，此处返回的是一个字符串类型的CommonResult对象
     */
    @Override
    public CommonResult<AppDCuSingleRenewalV0> appDCuSingleOperateRenewal(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO) {
        log.info("APPD和联通单个续费操作，appDCuSingleRenewalDTO:{}", appDCuSingleRenewalDTO);
        if (!CarVinUtil.checkVinFormat(appDCuSingleRenewalDTO.getCarVin())) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        if (StringUtils.isBlank(appDCuSingleRenewalDTO.getAppDExpireDate())
                && StringUtils.isBlank(appDCuSingleRenewalDTO.getCuExpireDate())) {
            return CommonResult.error(ErrorCodeConstants.APPDUC_SINGLE_RENEWAL_REQUEST_EMPTY);
        }
        // sprint47:校验是否存在续费中的记录
        if (checkProcessRecord(appDCuSingleRenewalDTO)) {
            return CommonResult.error(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR);
        }

        // sprint47:校验是否存在在途订单
        CommonResult<String> checkOrderInTransit = orderCheckService.checkOrderInTransit(appDCuSingleRenewalDTO.getCarVin(), ServiceTypeEnum.PIVI);
        if (!checkOrderInTransit.isSuccess()) {
            return CommonResult.error(checkOrderInTransit.getCode(), checkOrderInTransit.getMsg());
        }
        AppDCuSingleRenewalV0 appDCuSingleRenewalV0 = new AppDCuSingleRenewalV0();
        appDManualRenewal(appDCuSingleRenewalDTO, appDCuSingleRenewalV0);
        unicomManualRenewal(appDCuSingleRenewalDTO, appDCuSingleRenewalV0);
        appDCuSingleRenewalV0.setRenewalResult("请求已经发送，请前往续费记录查看结果");
        return CommonResult.success(appDCuSingleRenewalV0);
    }

    /**
     * 单个续费手动续订AppD
     *
     * @param appDCuSingleRenewalDTO 包含AppD续订信息的数据传输对象，包括AppD的到期日期等
     * @param appDCuSingleRenewalV0 存储单个续费手动续订结果
     */
    private void appDManualRenewal(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO, AppDCuSingleRenewalV0 appDCuSingleRenewalV0) {
        if (Objects.isNull(appDCuSingleRenewalDTO) || StringUtils.isBlank(appDCuSingleRenewalDTO.getAppDExpireDate())) {
            return ;
        }
        LocalDateTime appDEndDate = SubscribeTimeFormatUtil.stringToTimeByFormat(appDCuSingleRenewalDTO.getAppDExpireDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2);
        LocalDateTime appDBeforeExpiryDate = getAppDExpiryDate(appDCuSingleRenewalDTO.getCarVin());
        AppDCuRenewRecords appDCuRenewRecords = insertAppDRenewRecords(appDCuSingleRenewalDTO, appDBeforeExpiryDate);
        log.info("单个续费手动续订AppD，appDCuRenewRecords:{}", appDCuRenewRecords);
        if (Objects.isNull(appDBeforeExpiryDate)) {
            log.info("单个续费手动续订AppD, 查询AppD的之前到期时间为空, appDCuSingleRenewalDTO:{}", appDCuSingleRenewalDTO);
        }
        CommonResult<VcsOrderFufilmentCall> appDRenewal = piviAppDService.appDManualRenewal(appDCuSingleRenewalDTO.getCarVin(), appDEndDate);
        log.info("单个续费手动续订AppD，appDRenewal:{}", appDRenewal);
        if (Objects.isNull(appDRenewal)) {
            log.info("单个续费手动续订AppD, 获取续费结果appDRenewal为空, appDCuSingleRenewalDTO:{}", appDCuSingleRenewalDTO);
            return ;
        }
        if (appDRenewal.isSuccess()) {
            updateAppDRenewRecords(appDCuRenewRecords, appDRenewal);
            appDCuSingleRenewalV0.setAppDResultCode(appDRenewal.getCode());
        } else {
            updateAppDRenewRecordsStatus(appDCuRenewRecords, appDRenewal);
            appDCuSingleRenewalV0.setAppDResultCode(appDRenewal.getCode());
            appDCuSingleRenewalV0.setAppDResultDesc(appDRenewal.getMsg());
        }
    }

    /**
     * 更新应用端续费记录的状态为续费失败
     *
     * @param appDCuRenewRecords 应用端续费记录对象，包含续费相关信息
     * @param appDRenewal 续费操作的调用结果，包含续费操作的状态码和消息
     */
    private void updateAppDRenewRecordsStatus(AppDCuRenewRecords appDCuRenewRecords,
                                              CommonResult<VcsOrderFufilmentCall> appDRenewal) {
        appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
        appDCuRenewRecords.setOrderResultCode(String.valueOf(appDRenewal.getCode()));
        appDCuRenewRecords.setErrorDesc(appDRenewal.getMsg());
        appDCuRenewRecords.setUpdatedTime(LocalDateTime.now());
        appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
    }

    /**
     * 单个续费手动续订联通
     *
     * @param appDCuSingleRenewalDTO 包含续订信息的DTO如果DTO为空或CU到期日期为空，则方法直接返回
     * @param appDCuSingleRenewalV0 存储单个续费手动续订结果
     */
    private void unicomManualRenewal(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO, AppDCuSingleRenewalV0 appDCuSingleRenewalV0) {
        if (Objects.isNull(appDCuSingleRenewalDTO) || StringUtils.isBlank(appDCuSingleRenewalDTO.getCuExpireDate())) {
            return ;
        }
        LocalDateTime cuEndDate = SubscribeTimeFormatUtil.stringToTimeByFormat(appDCuSingleRenewalDTO.getCuExpireDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2);
        AppDCuRenewRecords appDCuRenewRecords = insertUnicomRenewRecords(appDCuSingleRenewalDTO);
        log.info("单个续费手动续订联通, appDCuRenewRecords:{}", appDCuRenewRecords);
        CommonResult<UnicomRespVO> cuRenewal = piviUnicomService.unicomManualRenewal(appDCuSingleRenewalDTO.getCarVin(), cuEndDate);
        log.info("单个续费手动续订联通，cuRenewal:{}", cuRenewal);
        if(cuRenewal.isSuccess()) {
            if (Objects.nonNull(cuRenewal.getData())) {
                updateUnicomRenewRecords(appDCuRenewRecords, cuRenewal);
            } else {
                appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_PROGRESS.getStatus());
                appDCuRenewRecords.setUpdatedTime(LocalDateTime.now());
                appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
            }
            appDCuSingleRenewalV0.setUnicomResultCode(cuRenewal.getCode());
        } else {
            updateUnicomRenewRecordsStatus(appDCuRenewRecords, cuRenewal);
            appDCuSingleRenewalV0.setUnicomResultCode(cuRenewal.getCode());
            appDCuSingleRenewalV0.setUnicomResultDesc(cuRenewal.getMsg());
        }
    }

    /**
     * 更新联通续费记录状态
     *
     * @param appDCuRenewRecords 续费记录对象，包含续费相关信息
     * @param unicomRespVOResult 联通接口返回结果封装对象，包含接口调用结果码和结果描述
     */
    private void updateUnicomRenewRecordsStatus(AppDCuRenewRecords appDCuRenewRecords, CommonResult<UnicomRespVO> unicomRespVOResult) {
        appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
        appDCuRenewRecords.setOrderResultCode(String.valueOf(UnicomResultEnum.ICCID_MISSING.code));
        appDCuRenewRecords.setErrorDesc(unicomRespVOResult.getMsg());
        appDCuRenewRecords.setUpdatedTime(LocalDateTime.now());
        appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
    }

    /**
     * 更新联通续费记录
     *
     * @param appDCuRenewRecords 应用续费记录对象，包含续费相关数据
     * @param unicomRespVOResult 联通接口返回的数据对象，包含续费结果信息
     */
     @Override
     public void updateUnicomRenewRecords(AppDCuRenewRecords appDCuRenewRecords, CommonResult<UnicomRespVO> unicomRespVOResult) {
        log.info("更新联通续费记录, appDCuRenewRecords:{}, unicomRespVOResult:{}", appDCuRenewRecords, unicomRespVOResult);
        if (Objects.isNull(appDCuRenewRecords) || Objects.isNull(unicomRespVOResult) || !unicomRespVOResult.isSuccess()) {
            return ;
        }
        if (!unicomRespVOResult.isSuccess() || Objects.isNull(unicomRespVOResult.getData())) {
            appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
            appDCuRenewRecords.setOrderResultCode(String.valueOf(unicomRespVOResult.getCode()));
            appDCuRenewRecords.setErrorDesc(unicomRespVOResult.getMsg());
            appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
            return ;
        }
         try {
             LocalDateTime renewalAfterDate = appDCuRenewRecords.getRenewDate();
             UnicomRespVO unicomRespVO = unicomRespVOResult.getData();
             if (UnicomResultEnum.SUCCESS.getCode().equals(unicomRespVO.getResponseCode())) {
                 appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_SUCCESS.getStatus());
                 if (Objects.nonNull(renewalAfterDate) && renewalAfterDate.isAfter(appDCuRenewRecords.getRenewBeforeExpiryDate())) {
                     appDCuRenewRecords.setRenewAfterExpiryDate(renewalAfterDate);
                 } else {
                     appDCuRenewRecords.setRenewAfterExpiryDate(appDCuRenewRecords.getRenewBeforeExpiryDate());
                 }
                 // 更新CU的ServiceDO的续期时间
                 updateServiceOrPIVIPackage(appDCuRenewRecords, ServicePackageEnum.DATA_PLAN);
                 appDCuRenewRecords.setOrderResultCode(unicomRespVO.getResponseCode());
             } else {
                 appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
                 appDCuRenewRecords.setOrderResultCode(unicomRespVO.getResponseCode());
                 appDCuRenewRecords.setErrorDesc(unicomRespVO.getResponseDesc());
             }
             appDCuRenewRecords.setUpdatedTime(LocalDateTime.now());
             appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
             //修改日志记录插入
             if(AppDRenewStatusEnum.RENEW_SUCCESS.getStatus().equals(appDCuRenewRecords.getRenewStatus())){
                 manualModifyLogDOService.recordLog(appDCuRenewRecords);
             }
         } catch (Exception e) {
             log.info("更新联通续费记录,异常：{}", e.getMessage());
         }
     }

    /**
     * 查询单个APPD和联通的分页列表信息
     *
     * @param pageDTO 分页查询参数对象，包含分页及筛选条件
     * @return 返回分页结果对象，包含单页列表和总记录数
     */
    @Override
    public PageResult<AppDCuSinglePageListV0> getAppDCuSinglePageList(AppDUcSingleRenewalPageDTO pageDTO) {
        log.info("查询单个APPD的分页列表信息, pageDTO:{}", pageDTO);
        Page<AppDCuRenewRecords> pageResult = queryAppDCuSinglePageList(pageDTO);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageResult<>();
        }
        List<AppDCuRenewRecords> records = pageResult.getRecords();
        List<AppDCuSinglePageListV0> pageListV0List = buildAppDCuSinglePageVOList(records);
        return new PageResult<>(pageListV0List, pageResult.getTotal());
    }

    /**
     * 将续订记录列表转换为应用DC单页视图对象列表
     *
     * @param records 续订记录列表，包含了多个续订记录
     * @return 返回一个应用DC单页视图对象列表，每个记录对应一个展示在单页上的数据对象
     */
    private List<AppDCuSinglePageListV0> buildAppDCuSinglePageVOList(List<AppDCuRenewRecords> records) {
        List<AppDCuSinglePageListV0> resp = new ArrayList<>();
        if (CollUtil.isEmpty(records)) {
            return resp;
        }
        for (AppDCuRenewRecords appDCuRenewRecord : records) {
            resp.add(buildAppDCuSinglePageVO(appDCuRenewRecord));
        }
        return resp;
    }

    /**
     * 将续订记录实体转换为单页面展示的VO对象
     *
     * @param appDCuRenewRecords 续订记录实体，包含车辆续订的相关信息
     * @return 返回转换后的AppDCuSinglePageListV0对象，包含前端展示所需的信息
     */
    private AppDCuSinglePageListV0 buildAppDCuSinglePageVO(AppDCuRenewRecords appDCuRenewRecords) {
        if (Objects.isNull(appDCuRenewRecords)) {
            return new AppDCuSinglePageListV0();
        }
        return AppDCuSinglePageListV0.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(appDCuRenewRecords.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .carVin(appDCuRenewRecords.getCarVin())
                .operator(appDCuRenewRecords.getOperator())
                .renewalServiceName(RenewServiceTypeEnum.getServiceName(appDCuRenewRecords.getRenewServiceType()))
                .batchNo(String.valueOf(appDCuRenewRecords.getRenewNo()))
                .build();
    }

    /**
     * 更新AppD续订记录
     * 根据VcsOrderFufilmentCall中的激活状态，更新AppDCuRenewRecords表中的续订状态及相关信息
     *
     * @param appDCuRenewRecords AppD续订记录对象，包含车辆VIN号和续订状态等信息
     * @param vcsOrderFufilmentCallResp VCS订单履行调用对象，包含激活状态和失败原因等信息
     */
    @Override
    public void updateAppDRenewRecords(AppDCuRenewRecords appDCuRenewRecords,
                                       CommonResult<VcsOrderFufilmentCall> vcsOrderFufilmentCallResp) {
        if (Objects.isNull(appDCuRenewRecords)) {
            return ;
        }
        if (Objects.isNull(vcsOrderFufilmentCallResp) || Objects.isNull(vcsOrderFufilmentCallResp.getData())) {
            log.info("更新AppD续订记录,vcsOrderFufilmentCall为空");
            return ;
        }
        if(!vcsOrderFufilmentCallResp.isSuccess()) {
            appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
            appDCuRenewRecords.setOrderResultCode(String.valueOf(vcsOrderFufilmentCallResp.getCode()));
            appDCuRenewRecords.setErrorDesc(vcsOrderFufilmentCallResp.getMsg());
            appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
            return ;
        }
        VcsOrderFufilmentCall vcsOrderFufilmentCall = vcsOrderFufilmentCallResp.getData();
        if (CallActivationStatusEnum.SUCCESS.getStatus().equals(vcsOrderFufilmentCall.getActivationStatus())) {
            LocalDateTime afterExpiryDate = appDCuRenewRecords.getRenewDate();
            if (Objects.isNull(appDCuRenewRecords.getRenewBeforeExpiryDate())) {
                appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
                appDCuRenewRecords.setErrorDesc("JLR Subscription ID不存在");
            } else {
                appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_SUCCESS.getStatus());
                appDCuRenewRecords.setRenewAfterExpiryDate(afterExpiryDate);
                // 更新APPD的ServiceDO的续期时间
                updateServiceOrPIVIPackage(appDCuRenewRecords, ServicePackageEnum.ONLINE_PACK);
            }
        } else {
            appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
            appDCuRenewRecords.setErrorDesc(vcsOrderFufilmentCall.getActivationFailedMsg());
        }
        appDCuRenewRecords.setUpdatedTime(LocalDateTime.now());
        appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);

        //修改日志记录插入
        if(AppDRenewStatusEnum.RENEW_SUCCESS.getStatus().equals(appDCuRenewRecords.getRenewStatus())){
            manualModifyLogDOService.recordLog(appDCuRenewRecords);
        }
    }

    /**
     * 根据车辆识别号（VIN）获取AppDCu单次过期列表
     *
     * @param carVin 车辆识别号，用于查询过期信息
     * @return 包含AppDCu单次过期信息的列表
     */
    @Override
    public List<AppDCuSingleExpireVO> getAppDCuSingleExpireList(String carVin) {
        List<AppDCuSingleExpireVO> resp = new ArrayList<>();
        if (StringUtils.isBlank(carVin)) {
            return resp;
        }
        CompletableFuture<LocalDateTime> appDExpiredFuture = CompletableFuture.supplyAsync(
                () ->  getAppDExpiryDate(carVin), subscribeAsyncThreadPool
        );
        CompletableFuture<UnicomRespVO> unicomFuture = CompletableFuture.supplyAsync(
                () -> piviUnicomService.getSimCarInfoByCarVin(carVin), subscribeAsyncThreadPool
        );
        LocalDateTime appDExpiryDate = appDExpiredFuture.join();
        resp.add(AppDCuSingleExpireVO.builder()
                .vin(carVin)
                .service(ServicePackageEnum.ONLINE_PACK.getDescCN())
                .oldDate(TimeFormatUtil.timeToStringByFormat(appDExpiryDate, TimeFormatUtil.formatter_7))
                .build()
        );
        UnicomRespVO unicomRespVO = unicomFuture.join();
        LocalDateTime unicomExpireDate = UnicomMaxExpireTimeUtil.getUnicomMaxExpireDate(unicomRespVO);
        resp.add(AppDCuSingleExpireVO.builder()
                .vin(carVin)
                .service(ServicePackageEnum.DATA_PLAN.getDescCN())
                .oldDate(TimeFormatUtil.timeToStringByFormat(unicomExpireDate, TimeFormatUtil.formatter_7))
                .build()
        );
        return resp;
    }


    /**
     * 单个续费手动续费联通
     * 该方法负责将联通用户的续保信息插入到数据库中
     *
     * @param appDCuSingleRenewalDTO 包含单个续保用户信息的DTO对象
     * @return 返回插入的续保记录对象
     */
    private AppDCuRenewRecords insertUnicomRenewRecords(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO) {
        AppDCuRenewRecords appDCuRenewRecords = new AppDCuRenewRecords();
        appDCuRenewRecords.setCarVin(CarVinUtil.carVinToUpperCase(appDCuSingleRenewalDTO.getCarVin()));
        appDCuRenewRecords.setRenewNo(snowflake.nextId());
        appDCuRenewRecords.setRenewServiceType(RenewServiceTypeEnum.UNICOM.getServiceType());
        appDCuRenewRecords.setRenewDate(SubscribeTimeFormatUtil.stringToTimeByFormat(appDCuSingleRenewalDTO.getCuExpireDate()+ TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2));
        appDCuRenewRecords.setCusOrderId(snowflake.nextIdStr());
        appDCuRenewRecords.setOperator(LoginUtil.getLoginUserName());
        appDCuRenewRecords.setDataSource(AppDDataSourceEnum.SINGLE.getDateSource());
        appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        UnicomRespVO unicomRespVO = piviUnicomService.getSimCarInfoByCarVin(appDCuSingleRenewalDTO.getCarVin());
        appDCuRenewRecords.setRenewBeforeExpiryDate(UnicomMaxExpireTimeUtil.getUnicomMaxExpireDate(unicomRespVO));
        appDCuRenewRecordsMapper.insert(appDCuRenewRecords);
        return appDCuRenewRecords;
    }


    /**
     * 插入AppD续订记录
     *
     * @param appDCuSingleRenewalDTO 包含AppD产品单次续订信息的DTO对象
     * @param beforeExpiryDate AppD续订之前到期的日期时间对象
     * @return 返回插入的AppD续订记录对象
     */
    private AppDCuRenewRecords insertAppDRenewRecords(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO,
                                                      LocalDateTime beforeExpiryDate) {
        AppDCuRenewRecords appDCuRenewRecords = new AppDCuRenewRecords();
        appDCuRenewRecords.setCarVin(CarVinUtil.carVinToUpperCase(appDCuSingleRenewalDTO.getCarVin()));
        appDCuRenewRecords.setRenewNo(snowflake.nextId());
        appDCuRenewRecords.setRenewServiceType(RenewServiceTypeEnum.APPD.getServiceType());
        appDCuRenewRecords.setRenewDate(SubscribeTimeFormatUtil.stringToTimeByFormat(appDCuSingleRenewalDTO.getAppDExpireDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2));
        appDCuRenewRecords.setCusOrderId(snowflake.nextIdStr());
        appDCuRenewRecords.setOperator(LoginUtil.getLoginUserName());
        appDCuRenewRecords.setDataSource(AppDDataSourceEnum.SINGLE.getDateSource());
        appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        appDCuRenewRecords.setRenewBeforeExpiryDate(beforeExpiryDate);
        appDCuRenewRecordsMapper.insert(appDCuRenewRecords);
        return appDCuRenewRecords;
    }

    /**
     * 根据车辆VIN号获取AppD订阅的到期日期
     *
     * @param carVin 车辆VIN号，用于查询特定车辆的订阅信息
     * @return 返回AppD订阅的到期日期如果找不到有效的订阅信息，则返回null
     */
    private LocalDateTime getAppDExpiryDate(String carVin) {
        AppDSubscriptionResp appDSubscriptionResp = piviAppDService.getVinSubscriptions(carVin);
        if (Objects.isNull(appDSubscriptionResp) || CollUtil.isEmpty(appDSubscriptionResp.getResult())) {
            log.info("根据车辆VIN号获取AppD订阅的到期日期为空， carVin:{}", carVin);
            return null;
        }
        return appDSubscriptionResp.getResult().get(0).getExpiresDate();
    }

    /**
     * 查询AppDCu续费记录的分页列表
     *
     * @param pageDTO 分页和查询条件封装对象
     * @return 分页结果，包含查询到的AppDCu续费记录
     */
    private Page<AppDCuRenewRecords> queryAppDCuSinglePageList(AppDUcSingleRenewalPageDTO pageDTO) {
        if (Objects.isNull(pageDTO)) {
            return new Page<>();
        }
        Page<AppDCuRenewRecords> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewRecords::getIsDeleted, false)
                .eq(AppDCuRenewRecords::getDataSource, AppDDataSourceEnum.SINGLE.getDateSource());
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(AppDCuRenewRecords::getCreatedTime);
            queryWrapper.orderByAsc(AppDCuRenewRecords::getId);
        } else {
            queryWrapper.orderByDesc(AppDCuRenewRecords::getCreatedTime);
            queryWrapper.orderByDesc(AppDCuRenewRecords::getId);
        }
        return appDCuRenewRecordsMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 更新AppDCu服务DO信息
     *
     * @param appDCuRenewRecords 续费记录对象，包含续费相关信息如果为null，则不执行任何操作
     */
    public void updateServiceOrPIVIPackage(AppDCuRenewRecords appDCuRenewRecords, ServicePackageEnum packageEnum) {
        log.info("更新AppDCu服务DO信息, appDCuRenewRecords:{}", appDCuRenewRecords);
        if (Objects.isNull(appDCuRenewRecords) || Objects.isNull(appDCuRenewRecords.getRenewAfterExpiryDate())) {
            return ;
        }

        //1.查询ServiceDO
        List<SubscriptionServiceDO> appDCuServiceDOs = serviceDOMapper.queryServiceDOByCarVinAndPackage(appDCuRenewRecords.getCarVin(),
                packageEnum.getPackageName());
        //2.ServiceDO不为空，表示用户已经登录了，直接修改ServiceDO和添加日志
        if (CollUtil.isNotEmpty(appDCuServiceDOs)) {
            insertAppDCuServiceDOLog(appDCuRenewRecords, appDCuServiceDOs, packageEnum);
            updateAppDCuServiceDO(appDCuRenewRecords, appDCuServiceDOs);
        }
    }

    /**
     * 插入AppDCu服务日志记录
     *
     * @param appDCuRenewRecords 包含续订记录信息的对象，用于获取续订相关的字段值
     * @param appDCuServiceDOs 包含订阅服务信息的对象，用于获取订阅服务相关的字段值
     * @param packageEnum packageEnum
     */
    public void insertAppDCuServiceDOLog(AppDCuRenewRecords appDCuRenewRecords, List<SubscriptionServiceDO> appDCuServiceDOs
                                        , ServicePackageEnum packageEnum) {
        // stream流遍历appDServiceDOs，组装SubscriptionServiceLogDO
        List<SubscriptionServiceLogDO> logDOList = appDCuServiceDOs.stream().map(serviceDO -> SubscriptionServiceLogDO.builder()
                .subscriptionId(serviceDO.getSubscriptionId())
                .fufilmentId(String.valueOf(appDCuRenewRecords.getRenewNo()))
                .refreshBeforeDate(serviceDO.getExpiryDate())
                .refreshAfterDate(appDCuRenewRecords.getRenewAfterExpiryDate())
                .serviceName(packageEnum.getDescCN())
                .modifyType(ServiceModifyTypeEnum.MANUAL_RENEWAL.getType())
                .build()).collect(Collectors.toList());
        serviceLogMapper.insertBatch(logDOList);
    }

    /**
     * 更新服务信息
     *
     * @param appDCuRenewRecords 续费记录对象，包含续费后的过期日期等信息
     * @param appDCuServiceDOs 服务对象，需要更新的信息
     */
    public void updateAppDCuServiceDO(AppDCuRenewRecords appDCuRenewRecords, List<SubscriptionServiceDO> appDCuServiceDOs) {
        appDCuServiceDOs.forEach(serviceDO -> {
            serviceDO.setExpiryDate(appDCuRenewRecords.getRenewAfterExpiryDate());
            serviceDO.setExpireDateUtc0(appDCuRenewRecords.getRenewAfterExpiryDate().minusHours(8));
            serviceDO.setUpdatedBy(LoginUtil.getLoginUserName());
            serviceDO.setUpdatedTime(LocalDateTime.now());
        });
        serviceDOMapper.updateBatch(appDCuServiceDOs);
    }

    /**
     * 检查是否存在同一车辆的续费进行中记录
     * 此方法旨在确保对于特定车辆，没有重复的续费操作正在进行中
     * 它主要针对两种服务类型（APPD和CU）进行检查，以避免续费操作的冲突
     *
     * @param appDCuSingleRenewalDTO 包含车辆VIN和续费相关信息的DTO对象
     * @return 如果没有找到进行中的续费记录，返回成功结果；否则，返回错误结果
     */
    @Override
    public boolean checkProcessRecord(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO) {
        List<AppDCuRenewRecords> processRecordList = appDCuRenewRecordsMapper.getByVinAndStatus(appDCuSingleRenewalDTO.getCarVin(), AppDRenewStatusEnum.RENEW_PROGRESS.getStatus());
        if (CollUtil.isEmpty(processRecordList)) {
            return false;
        }
        // 判断是否存在续费中的appd记录
        boolean appDMatch = processRecordList.stream()
                .anyMatch(records -> RenewServiceTypeEnum.APPD.getServiceType().equals(records.getRenewServiceType()));
        if (appDMatch && CharSequenceUtil.isNotBlank(appDCuSingleRenewalDTO.getAppDExpireDate())) {
            log.info("vin:{}存在续费中的appd手动续费记录", appDCuSingleRenewalDTO.getCarVin());
            return true;
        }
        // 判断是否存在续费中的cu记录
        boolean cuMatch = processRecordList.stream()
                .anyMatch(records -> RenewServiceTypeEnum.UNICOM.getServiceType().equals(records.getRenewServiceType()));
        if (cuMatch && CharSequenceUtil.isNotBlank(appDCuSingleRenewalDTO.getCuExpireDate())) {
            log.info("vin:{}存在续费中的cu手动续费记录", appDCuSingleRenewalDTO.getCarVin());
            return true;
        }
        return false;
    }
}
