package com.jlr.ecp.subscription.service.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.model.dto.ModelYearDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataCreateDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataUpdateDTO;
import com.jlr.ecp.subscription.api.model.vo.*;
import com.jlr.ecp.subscription.config.DPService;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.model.VehicleModelMasterDataDO;
import com.jlr.ecp.subscription.dal.mysql.model.VehicleModelMasterDataMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.RemotePackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.CALL_DP_ERROR;

/**
 * t_product_brand(ProductBrand)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07 20:13:51
 */

@Service("vehicleModelMasterDataService")
@Validated
@Slf4j
public class VehicleModelMasterDataServiceImpl implements VehicleModelMasterDataService {

    @Resource
    private VehicleModelMasterDataMapper vehicleModelMasterDataMapper;

    @Resource
    private DPService dpService;

    @Resource
    RedisService redisService;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private RemotePackageDOMapper remotePackageDOMapper;

    @Resource
    private Environment environment;


    @Override
    public Boolean createVehicleModelMasterData(VehicleModelMasterDataCreateDTO createDTO) {
        List<VehicleModelMasterDataDO> list = new ArrayList<>();
        String seriesCode = createDTO.getSeriesCode();
        String carSystemModel = createDTO.getCarSystemModel();
        checkSeriesCodeExist(seriesCode,null);
        List<ModelYearDTO> modelYearList = createDTO.getModelYearList();
        if (modelYearList.size()!= new HashSet<>(modelYearList).size()) {
            // 列表中有value值重复元素
            throw exception(ErrorCodeConstants.SERIES_MODEL_YEAR_EXISTS_ERROR);
        }
        for (ModelYearDTO modelYearDTO : modelYearList) {
            VehicleModelMasterDataDO dataDO = new VehicleModelMasterDataDO();
            dataDO.setModelYear(modelYearDTO.getModelYear());
            dataDO.setCarSystemModel(carSystemModel);
            dataDO.setSeriesCode(seriesCode);
            dataDO.setTenantId(1);
            list.add(dataDO);
        }
        vehicleModelMasterDataMapper.insertBatch(list);
        return true;
    }

    private void checkSeriesCodeExist(String seriesCode,List<Long> ids) {
        LambdaQueryWrapperX<VehicleModelMasterDataDO> wrapperX = new LambdaQueryWrapperX<VehicleModelMasterDataDO>()
                .eq(VehicleModelMasterDataDO::getSeriesCode, seriesCode);
        if(ids!=null && !ids.isEmpty()){
            wrapperX.notIn(VehicleModelMasterDataDO::getId,ids);
        }
        wrapperX.eq(BaseDO::getIsDeleted, false)
                .last("limit 1");
        VehicleModelMasterDataDO vehicleModelMasterDataDO = vehicleModelMasterDataMapper.selectOne(wrapperX);
        if(vehicleModelMasterDataDO != null){
            throw exception(ErrorCodeConstants.SERIES_VALUE_EXIST_ERROR);
        }
    }

    @Override
    public Boolean editVehicleModelMasterData(VehicleModelMasterDataUpdateDTO updateDTO) {
        List<ModelYearDTO> modelYearList = updateDTO.getModelYearList();
        List<Long> ids = modelYearList.stream().map(ModelYearDTO::getId).collect(Collectors.toList());
        if(!updateDTO.getNewSeriesCode().equals(updateDTO.getSeriesCode())){
            checkSeriesCodeExist(updateDTO.getNewSeriesCode(),ids);
        }
        List<ModelYearDTO> newModelYearList = updateDTO.getModelYearList();
        List<String> nameList = newModelYearList.stream().map(ModelYearDTO::getModelYear).collect(Collectors.toList());
        if (nameList.size()!= new HashSet<>(nameList).size()) {
            // 列表中有value值重复元素
            throw exception(ErrorCodeConstants.SERIES_MODEL_YEAR_EXISTS_ERROR);
        }
        //查询old modelYearList 做对比
        VehicleModelMasterDataVO vo = getOneBySeriesCode(updateDTO.getSeriesCode());
        List<ModelYearDTO> oldModelYearList = vo.getModelYearList();

        //更新：oldlist里有的id，newlist有的id
        List<VehicleModelMasterDataDO> updateList = newModelYearList.stream()
                .filter(newModelYear -> oldModelYearList.stream()
                        .anyMatch(oldModelYear -> {
                            if (oldModelYear.getId()!=null
                                    && oldModelYear.getId().equals(newModelYear.getId())
                                    && !oldModelYear.getRevision().equals(newModelYear.getRevision())) {
                                throw exception(ErrorCodeConstants.SERIES_UPDATE_REVISION_ERROR);
                            }
                            if(oldModelYear.getId() != null){
                                return oldModelYear.getId().equals(newModelYear.getId());
                            }
                            return false;
                        }))
                .map(dto -> createVehicleModelMasterDataDO(dto,updateDTO))
                .collect(Collectors.toList());

        //删除arr：oldlist里有的id，newlist没有的
        List<Long> deleteList = oldModelYearList.stream()
                .map(ModelYearDTO::getId)
                .filter(id -> newModelYearList.stream()
                        .noneMatch(newModelYear -> {
                            if(newModelYear.getId()!=null){
                              return newModelYear.getId().equals(id);
                            }
                          return false;
                        }))
                .collect(Collectors.toList());

        //新增：newlist没有传入id
        List<VehicleModelMasterDataDO> createList = newModelYearList.stream()
                .filter(newModelYear -> newModelYear.getId() == null)
                .map(dto -> createVehicleModelMasterDataDO(dto,updateDTO))
                .collect(Collectors.toList());
        if(!createList.isEmpty()){
            vehicleModelMasterDataMapper.insertBatch(createList);
        }
        if(!updateList.isEmpty()){
            vehicleModelMasterDataMapper.updateBatch(updateList);
        }
        if(!deleteList.isEmpty()){
            vehicleModelMasterDataMapper.deleteBatchIds(deleteList);
        }
        return true;
    }

    private VehicleModelMasterDataDO createVehicleModelMasterDataDO(ModelYearDTO modelYearDTO,VehicleModelMasterDataUpdateDTO updateDTO) {
        VehicleModelMasterDataDO vehicleModelMasterDataDO = new VehicleModelMasterDataDO();
        BeanUtils.copyProperties(modelYearDTO,vehicleModelMasterDataDO);
        vehicleModelMasterDataDO.setSeriesCode(updateDTO.getNewSeriesCode());
        vehicleModelMasterDataDO.setCarSystemModel(updateDTO.getCarSystemModel());
        vehicleModelMasterDataDO.setTenantId(1);
        return vehicleModelMasterDataDO;
    }

    @Override
    public VehicleModelMasterDataVO getOneBySeriesCode(String seriesCode) {
        List<VehicleModelMasterDataDO> list = vehicleModelMasterDataMapper
                .selectList(new LambdaQueryWrapperX<VehicleModelMasterDataDO>()
                        .eq(VehicleModelMasterDataDO::getSeriesCode,seriesCode)
                        .eq(BaseDO::getIsDeleted,false));
        VehicleModelMasterDataVO vo = new VehicleModelMasterDataVO();
        List<ModelYearDTO> modelYearList = new ArrayList<>();
        for (VehicleModelMasterDataDO vehicleModelMasterDataDO : list) {
            ModelYearDTO modelYearDTO = new ModelYearDTO();
            modelYearDTO.setRevision(vehicleModelMasterDataDO.getRevision());
            modelYearDTO.setModelYear(vehicleModelMasterDataDO.getModelYear());
            modelYearDTO.setId(vehicleModelMasterDataDO.getId());
            vo.setCarSystemModel(vehicleModelMasterDataDO.getCarSystemModel());
            modelYearList.add(modelYearDTO);
        }
        vo.setSeriesCode(seriesCode);
        vo.setModelYearList(modelYearList);
        return vo;
    }

    @Override
    public PageResult<VehicleModelMasterDataPageVO> getPage(PageParam param) {
        Page<VehicleModelMasterDataPageVO> pageResult = vehicleModelMasterDataMapper.getPage(new Page<>(param.getPageNo(), param.getPageSize()));
        List<VehicleModelMasterDataPageVO> list = pageResult.getRecords();
        for (VehicleModelMasterDataPageVO vo : list) {
            String modelYears = vo.getModelYears();
            if (modelYears != null && !modelYears.isEmpty()) {
                List<String> modelYearList = Arrays.asList(modelYears.split(","));
                vo.setModelYearList(modelYearList);
            } else {
                vo.setModelYearList(new ArrayList<>());
            }
        }
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public Boolean deleteBySeriesCode(String seriesCode) {
        VehicleModelMasterDataDO vehicleModelMasterDataDO = new VehicleModelMasterDataDO();
        vehicleModelMasterDataDO.setSeriesCode(seriesCode);
        vehicleModelMasterDataDO.setIsDeleted(true);
        int update = vehicleModelMasterDataMapper.delete(new LambdaUpdateWrapper<VehicleModelMasterDataDO>()
                        .eq(VehicleModelMasterDataDO::getSeriesCode, seriesCode));
        return update>0;
    }


    @Override
    public List<UserDPResultVO> findDpList(List<String> vinList) {
        List<DPResultVO> dpResultVOList;
        // 除了prd都需要mock返回值
        String env = environment.getProperty(Constants.PROFILE_ACTIVE);
        if ("dev".equals(env) || "test".equals(env) || "uat".equals(env)) {
            // 优化：使用安全的批量查询替代HGETALL，避免Redis超时
            dpResultVOList = getTestVinDataSafely(vinList);
        }else {
            //根据vinList查询DP数据
            DPResponseVO dpResponseVO = dpService.callApi(vinList);
            dpResultVOList = dpResponseVO.getResult();
        }

        //DP数据过滤PIVI车型 根据series_code 和model_year 查询 t_vehicle_model_master_data表
        if (CollUtil.isNotEmpty(dpResultVOList)) {
            //组装车型编码list - 添加null值过滤
            List<String> seriesCodeList = dpResultVOList.stream()
                    .filter(Objects::nonNull)
                    .map(DPResultVO::getSeriesCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<String> modelYearList = dpResultVOList.stream()
                    .filter(Objects::nonNull)
                    .map(DPResultVO::getModelYear)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            List<VehicleModelMasterDataDO> list = vehicleModelMasterDataMapper.selectList(new LambdaQueryWrapperX<VehicleModelMasterDataDO>()
                    .in(VehicleModelMasterDataDO::getSeriesCode, seriesCodeList)
                    .in(VehicleModelMasterDataDO::getModelYear, modelYearList)
                    //                            .eq(VehicleModelMasterDataDO::getBrandCode,brandCode)
                    .eq(VehicleModelMasterDataDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode())
                    .eq(BaseDO::getIsDeleted, false));
            //数据对比并且组装
            List<UserDPResultVO> collect = dpResultVOList.stream()

                    .map(vo -> {
                        UserDPResultVO dpResultVO = new UserDPResultVO();
                        BeanUtils.copyProperties(vo, dpResultVO);
                        VehicleModelMasterDataDO vehicleModelMasterDataDO = list.stream()
                                .filter(dataDO -> dataDO.getModelYear().equals(dpResultVO.getModelYear())
                                        && dataDO.getSeriesCode().equals(dpResultVO.getSeriesCode()))
                                .findFirst().orElse(new VehicleModelMasterDataDO());
                        dpResultVO.setCarSystemModel(vehicleModelMasterDataDO.getCarSystemModel());
                        return dpResultVO;
                    }).collect(Collectors.toList());
            log.info("查询DP结果, vinList:{}, size={}", vinList, collect.size());
            return collect;
        }
        return new ArrayList<>();
    }

    @Override
    public List<UserDPResultVO> findDpListByVinInit(List<String> vinList) {
        List<DPResultVO> dpResultVOList;
        // 除了prd都需要mock返回值
        String env = environment.getProperty(Constants.PROFILE_ACTIVE);
        if ("dev".equals(env) || "test".equals(env) || "uat".equals(env)) {
            // 优化：使用安全的批量查询替代HGETALL，避免Redis超时
            dpResultVOList = getTestVinDataSafely(vinList);
        } else {
            //根据vinList查询DP数据
            DPResponseVO dpResponseVO = dpService.callApi(vinList);
            // queryResult不为空代表发生了异常
            if (Objects.nonNull(dpResponseVO.getQueryResult())) {
                throw exception(CALL_DP_ERROR);
            }
            dpResultVOList = dpResponseVO.getResult();
        }

        //DP数据过滤PIVI车型 根据series_code 和model_year 查询 t_vehicle_model_master_data表
        if (CollUtil.isEmpty(dpResultVOList)) {
            log.info("DP查询结果为空, vinList:{}", vinList);
            return new ArrayList<>();
        }

        //组装车型编码list - 添加null值过滤
        List<String> seriesCodeList = dpResultVOList.stream()
                .filter(Objects::nonNull)
                .map(DPResultVO::getSeriesCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> modelYearList = dpResultVOList.stream()
                .filter(Objects::nonNull)
                .map(DPResultVO::getModelYear)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<VehicleModelMasterDataDO> list = vehicleModelMasterDataMapper.selectList(new LambdaQueryWrapperX<VehicleModelMasterDataDO>()
                .in(VehicleModelMasterDataDO::getSeriesCode, seriesCodeList)
                .in(VehicleModelMasterDataDO::getModelYear, modelYearList)
                //                            .eq(VehicleModelMasterDataDO::getBrandCode,brandCode)
                .eq(VehicleModelMasterDataDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode())
                .eq(BaseDO::getIsDeleted, false));
        //数据对比并且组装
        List<UserDPResultVO> collect = dpResultVOList.stream()

                .map(vo -> {
                    UserDPResultVO dpResultVO = new UserDPResultVO();
                    BeanUtils.copyProperties(vo, dpResultVO);
                    VehicleModelMasterDataDO vehicleModelMasterDataDO = list.stream()
                            .filter(dataDO -> dataDO.getModelYear().equals(dpResultVO.getModelYear())
                                    && dataDO.getSeriesCode().equals(dpResultVO.getSeriesCode()))
                            .findFirst().orElse(new VehicleModelMasterDataDO());
                    dpResultVO.setCarSystemModel(vehicleModelMasterDataDO.getCarSystemModel());
                    return dpResultVO;
                }).collect(Collectors.toList());
        log.info("查询DP结果, vinList:{}, size={}", vinList, collect.size());
        return collect;
    }

    @Override
    public UserDPResultVO findDp(String vin) {
        UserDPResultVO resultVO = new UserDPResultVO();
        try {
            List<DPResultVO> dpResultVOList = new ArrayList<>();
            // 除了prd都需要mock返回值
            String env = environment.getProperty(Constants.PROFILE_ACTIVE);
            if ("dev".equals(env) || "test".equals(env) || "uat".equals(env)) {
                // 优化：直接查询单个VIN，避免获取整个Map
                DPResultVO singleVinData = redisService.getCacheMapValue(Constants.REDIS_KEY.MOCK_DP_KEY, vin);
                if (singleVinData != null) {
                    dpResultVOList.add(singleVinData);
                }
            }else {
                //根据vinList查询DP数据
                DPResponseVO dpResponseVO = dpService.callApi(Collections.singletonList(vin));
                // 错误结果不为空直接返回
                if (CharSequenceUtil.isNotBlank(dpResponseVO.getQueryResult())) {
                    resultVO.setQueryResult(dpResponseVO.getQueryResult());
                    return resultVO;
                }
                dpResultVOList = dpResponseVO.getResult();
            }

            if (CollUtil.isEmpty(dpResultVOList) || Objects.isNull(dpResultVOList.get(0))) {
                return resultVO;
            }
            DPResultVO dpResultVO = dpResultVOList.get(0);
            BeanUtils.copyProperties(dpResultVO, resultVO);
            String seriesCode = dpResultVO.getSeriesCode();
            String modelYear = dpResultVO.getModelYear();
            //DP数据过滤PIVI车型 根据series_code 和model_year 查询 t_vehicle_model_master_data表
            VehicleModelMasterDataDO vehicleModelMasterDataDO = vehicleModelMasterDataMapper.selectOne(new LambdaQueryWrapperX<VehicleModelMasterDataDO>()
                    .eq(VehicleModelMasterDataDO::getSeriesCode, seriesCode)
                    .eq(VehicleModelMasterDataDO::getModelYear, modelYear)
                    .eq(VehicleModelMasterDataDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode())
                    .eq(BaseDO::getIsDeleted, false)
                    .last("limit 1"));
            //数据对比并且组装
            if (Objects.nonNull(vehicleModelMasterDataDO)) {
                resultVO.setCarSystemModel(vehicleModelMasterDataDO.getCarSystemModel());
            }
        }catch (Exception e){
            log.warn("bau 调用dp error", e);
            resultVO.setQueryResult(e.getMessage());
        }
        return resultVO;
    }

    /**
     * 安全地获取测试VIN数据，包含类型转换和错误处理
     *
     * @param vinList VIN列表
     * @return DP结果列表
     */
    private List<DPResultVO> getTestVinDataSafely(List<String> vinList) {
        List<DPResultVO> dpResultVOList = new ArrayList<>();

        if (CollUtil.isEmpty(vinList)) {
            return dpResultVOList;
        }

        try {
            // 使用优化的Pipeline方法
            List<Object> rawResults = redisService.batchGetHashValues(Constants.REDIS_KEY.MOCK_DP_KEY, vinList);

            // 安全的类型转换和过滤
            for (int i = 0; i < vinList.size(); i++) {
                String expectedVin = vinList.get(i);
                Object rawResult = i < rawResults.size() ? rawResults.get(i) : null;

                DPResultVO dpResult = safeCastToDPResultVO(rawResult, expectedVin);
                if (dpResult != null) {
                    dpResultVOList.add(dpResult);
                }
            }

            log.info("安全批量查询TEST_VIN_MAP完成，请求VIN数量: {}, 返回数据量: {}",
                    vinList.size(), dpResultVOList.size());

        } catch (Exception e) {
            log.info("安全批量查询TEST_VIN_MAP失败，VIN数量: {}, 尝试降级处理", vinList.size(), e);
            // 降级处理：逐个查询关键VIN
            dpResultVOList = fallbackGetTestVinData(vinList);
        }

        return dpResultVOList;
    }

    /**
     * 安全的类型转换方法
     *
     * @param rawResult Redis返回的原始结果
     * @param expectedVin 期望的VIN
     * @return 转换后的DPResultVO，失败时返回null
     */
    private DPResultVO safeCastToDPResultVO(Object rawResult, String expectedVin) {
        if (rawResult == null) {
            log.debug("VIN {} 在Redis中不存在", expectedVin);
            return null;
        }

        try {
            DPResultVO dpResult = null;

            if (rawResult instanceof DPResultVO) {
                dpResult = (DPResultVO) rawResult;
            } else if (rawResult instanceof String) {
                String jsonStr = ((String) rawResult).trim();
                if (jsonStr.isEmpty()) {
                    log.debug("VIN {} 对应的JSON字符串为空", expectedVin);
                    return null;
                }
                dpResult = JSON.parseObject(jsonStr, DPResultVO.class);
            } else if (rawResult instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) rawResult;
                if (map.isEmpty()) {
                    log.debug("VIN {} 对应的Map为空", expectedVin);
                    return null;
                }
                dpResult = JSON.parseObject(JSON.toJSONString(map), DPResultVO.class);
            } else {
                log.warn("无法转换Redis结果为DPResultVO，VIN: {}, 类型: {}",
                        expectedVin, rawResult.getClass().getSimpleName());
                return null;
            }

            // 验证VIN匹配（数据一致性检查）
            if (dpResult != null && expectedVin.equals(dpResult.getVin())) {
                return dpResult;
            } else if (dpResult != null) {
                log.warn("VIN不匹配，期望: {}, 实际: {}", expectedVin, dpResult.getVin());
            }

            return null;

        } catch (Exception e) {
            log.error("转换Redis结果异常，VIN: {}, 原始数据类型: {}",
                    expectedVin, rawResult.getClass().getSimpleName(), e);
            return null;
        }
    }


    /**
     * 降级处理：逐个查询关键VIN数据
     *
     * @param vinList VIN列表
     * @return DP结果列表
     */
    private List<DPResultVO> fallbackGetTestVinData(List<String> vinList) {
        List<DPResultVO> dpResultVOList = new ArrayList<>();

        // 限制降级查询的数量，避免过多的Redis请求
        int maxFallbackCount = Math.min(vinList.size(), 100);

        for (int i = 0; i < maxFallbackCount; i++) {
            try {
                String vin = vinList.get(i);
                Object rawResult = redisService.getCacheMapValue(Constants.REDIS_KEY.MOCK_DP_KEY, vin);
                DPResultVO dpResult = safeCastToDPResultVO(rawResult, vin);
                if (dpResult != null) {
                    dpResultVOList.add(dpResult);
                }
            } catch (Exception e) {
                log.info("降级查询VIN数据失败，VIN: {}", vinList.get(i), e);
            }
        }

        log.info("降级查询TEST_VIN_MAP完成，尝试查询VIN数量: {}, 返回数据量: {}",
                maxFallbackCount, dpResultVOList.size());

        return dpResultVOList;
    }
}

