package com.jlr.ecp.subscription.excel.listener.iccid;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyExcel;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyResultExcel;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.IccidUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class IccidBatchModifyCheckListener extends AnalysisEventListener<IccidBatchModifyExcel> {

    private static final int BATCH_COUNT = 500;

    private List<IccidBatchModifyExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<IccidBatchModifyExcel> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<IccidBatchModifyResultExcel> resultExcelList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Set<String> vinSet = new HashSet<>();

    //ICCID set集合
    private Set<String> iccidSet = new HashSet<>();

    private Boolean formatError = false;

    private Boolean checkResult = true;

    private static final String VIN_TITLE = "VIN";
    private static final String NEW_ICCID_TITLE = "新ICCID";

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(IccidBatchModifyExcel iccidBatchModifyExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(iccidBatchModifyExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                checkUploadExcel(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("ICCID批量修改解析Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("ICCID批量修改处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            checkUploadExcel(dataList);
        } catch (Exception e) {
            log.info("ICCID批量修改处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("ICCID批量修改所有数据解析完成!");
    }

    public void checkUploadExcel(List<IccidBatchModifyExcel> iccidBatchModifyExcelList) {
        if (headers.isEmpty() || headers.get(0).size() < 2 || !VIN_TITLE.equals(headers.get(0).get(0)) || !NEW_ICCID_TITLE.equals(headers.get(0).get(1))) {
            formatError = true;
            log.info("ICCID批量修改的Excel文件title的格式错误");
            return;
        }
        for (IccidBatchModifyExcel iccidBatchModifyExcel : iccidBatchModifyExcelList) {
            IccidBatchModifyResultExcel resultExcel = new IccidBatchModifyResultExcel();
            resultExcel.setCarVin(iccidBatchModifyExcel.getCarVin());
            resultExcel.setNewIccid(iccidBatchModifyExcel.getNewIccid());
            if (checkCarVin(iccidBatchModifyExcel, resultExcel) && checkIccidFormat(iccidBatchModifyExcel, resultExcel)) {
                resultExcel.setCheckResult("有效");
            }
            vinSet.add(CarVinUtil.carVinToUpperCase(iccidBatchModifyExcel.getCarVin()));
            iccidSet.add(iccidBatchModifyExcel.getNewIccid());
            resultExcelList.add(resultExcel);
        }
    }

    private boolean checkCarVin(IccidBatchModifyExcel iccidBatchModifyExcel, IccidBatchModifyResultExcel resultExcel) {
        if (StringUtils.isBlank(iccidBatchModifyExcel.getCarVin())) {
            resultExcel.setCheckResult("VIN缺失");
            checkResult = false;
            return false;
        } else if (!CarVinUtil.checkVinFormat(iccidBatchModifyExcel.getCarVin())) {
            resultExcel.setCheckResult("VIN需由17位数字及字母组成");
            checkResult = false;
            return false;
        } else if (vinSet.contains(CarVinUtil.carVinToUpperCase(iccidBatchModifyExcel.getCarVin()))) {
            resultExcel.setCheckResult("VIN重复");
            checkResult = false;
            return false;
        }
        return true;
    }

    private boolean checkIccidFormat(IccidBatchModifyExcel iccidBatchModifyExcel, IccidBatchModifyResultExcel resultExcel) {
        if (StringUtils.isBlank(iccidBatchModifyExcel.getNewIccid())) {
            resultExcel.setCheckResult("ICCID为空");
            checkResult = false;
            return false;
        } else if (!IccidUtil.checkIccidFormat(iccidBatchModifyExcel.getNewIccid())) {
            resultExcel.setCheckResult("ICCID需由20位数字组成");
            checkResult = false;
            return false;
        } else if (iccidSet.contains(iccidBatchModifyExcel.getNewIccid())) {
            resultExcel.setCheckResult("ICCID重复");
            checkResult = false;
            return false;
        }
        return true;
    }
}