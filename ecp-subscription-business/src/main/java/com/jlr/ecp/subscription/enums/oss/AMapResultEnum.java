package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AMAP查询结果枚举
 */
@AllArgsConstructor
@Getter
public enum AMapResultEnum {

    SUCCESS(1, "是", null),

    SYSTEM_ERROR(2, "否，系统异常", "AMAP系统异常"),

    VIN_NOT_FOUND(3, "否，未找到该VIN", "AMAP未找到该VIN"),

    EXPIRY_DATE_NOT_FOUND(4, "否，到期日不存在", "AMAP系统异常");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 错误信息
     * */
    public final String errorMessage;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (AMapResultEnum status : AMapResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    public static String getErrorMessage(Integer code){
        if (code == null) {
            return null;
        }
        for (AMapResultEnum status : AMapResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getErrorMessage();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
