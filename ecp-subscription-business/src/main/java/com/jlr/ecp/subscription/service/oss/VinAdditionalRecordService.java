package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordVO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;

/**
 * vin补录服务接口
 *
 * <AUTHOR>
 */
public interface VinAdditionalRecordService {

    /**
     * vin补录
     * @param recordDTO recordDTO
     */
    CommonResult<String> additionalRecord(VinAdditionalRecordDTO recordDTO);

    /**
     * vin补录记录
     * @param pageDTO pageDTO
     */
    PageResult<VinAdditionalRecordVO> getAdditionalRecordPage(VinAdditionalRecordPageDTO pageDTO);

    /**
     * 检查SOTA状态
     *
     * @param recordsDO     用于记录SOTA检查结果的原始数据记录
     * @return 如果SOTA检查成功，则返回true；否则返回false
     */
    boolean checkSOTA(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO);

    /**
     * 获取并校验ICCID信息
     * 通过调用运营商服务来获取SIM卡的信息，并根据ICCID进行校验确保卡片有效
     *
     * @return boolean 校验结果，如果查询到有效的ICCID信息则返回true，否则返回false
     */
    boolean checkCU(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO);

    /**
     * 根据车辆识别号(VIN)检查APPD信息
     *
     * @return 如果查询到有效的APPD信息，则返回true；否则返回false
     */
    boolean checkAPPD(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO);

    /**
     * 检查并更新AMap到期日期
     * 该方法通过调用AMap服务查询给定车辆识别码（VIN）的AMap到期日期如果查询成功且日期非空，则更新数据库中的到期日期
     *
     * @return 如果AMap到期日期查询成功且非空，则返回true；否则返回false
     */
    boolean checkAMAP(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO);

    /**
     * 检查DP信息
     * <p>
     * 本函数用于检查给定PIVI包、原始数据和记录数据的DP信息它尝试通过车辆识别号（VIN）从车辆模型主数据服务中获取DP信息，
     * 并根据获取的结果更新原始数据和记录数据的状态如果查询到DP异常或DP数据为空，它会相应地更新状态并返回null；
     * 否则，它会更新状态表示查询成功，并返回DP配置代码
     *
     * @param originalDataDO 原始数据对象，用于记录处理状态和结果
     * @param recordsDO      记录数据对象，用于存储处理过程中的详细信息
     * @return DP配置代码如果查询成功且DP数据不为空，返回DP配置代码；否则返回null
     */
    String checkDP(DmsOssOriginalDataDO originalDataDO, DmsOssOriginalDataRecordsDO recordsDO);

    /**
     * 判断是否为特殊履约方式的车型，并处理相关数据
     *
     * @param originalDataDO 原始数据对象
     * @param configCode     车型配置编码
     * @param recordsDO      记录数据对象
     */
    void judgeSpecialGroup(DmsOssOriginalDataDO originalDataDO, String configCode, DmsOssOriginalDataRecordsDO recordsDO);
}

