package com.jlr.ecp.subscription.dal.dataobject.icrorder;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName(value = "t_vehicle_dms")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VehicleDmsDO {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     *   车辆VIN码
     * */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     *  发票日期
     * */
    @TableField(value = "invoice_date")
    private String invoiceDate;
}
