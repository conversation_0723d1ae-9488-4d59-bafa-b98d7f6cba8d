package com.jlr.ecp.subscription.service.remote;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface RemoteBatchRenewalService {

    /**
     * 获取Remote批量续费的模板URL
     *
     * @return 模板的URL地址
     */
    String getRemoteTemplateUrl();

    /**
     *  Remote 批量续费分页查询列表
     *
     * @param pageDTO 分页查询参数
     * @return 续费页面的数据列表和总记录数
     */
    PageResult<RemoteBatchRenewalPageVO> queryRemoteBatchRenewalPageList(RemoteBatchRenewalPageDTO pageDTO);

    /**
     * 上传Remote批量续费的Excel文件
     *
     * @param multipartFile 多部分文件对象，包含上传的Excel文件
     * @return CommonResult<RemoteBatchRenewalUploadVO> 返回一个通用结果对象，包含上传结果和相关信息
     */
    CommonResult<RemoteBatchRenewalUploadVO> uploadRemoteExcelRenewal(MultipartFile multipartFile);

    /**
     * 批量发送Remote续费通知
     *
     * @param remoteBatchSendDTO 批量发送Remote续费的参数对象，包含批次号等信息
     * @return CommonResult<String> 返回操作结果的封装对象，包含成功、错误等状态及相应消息
     */
    CommonResult<String> batchSendRemoteRenewal(RemoteBatchSendDTO remoteBatchSendDTO);

    /**
     * 批量发送Remote续费通知
     *
     * @param list 待处理的约记录列表
     */
    void batchSendRenewalAndUpdate(List<RemoteRenewDetailRecords> list, Map<String, RemoteSearchResultDTO> allMap);
}
