package com.jlr.ecp.subscription.dal.dataobject.vehicle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * t_unicom_check
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_unicom_check")
public class UnicomCheckDO {


    /**
    * 主键
    */
    @TableId
    private Long id;

    /**
    * 车辆VIN码;车辆VIN码
    */
    @TableField(value = "iccid")
    private String iccid;

    /**
     * 车辆VIN码;车辆VIN码
     */
    @TableField(value = "ext_book_id")
    private String extBookId;

    /**
     * 车辆VIN码;车辆VIN码
     */
    @TableField(value = "book_status")
    private String bookStatus;
    /**
     * 过期时间
     */
    @TableField(value = "expiry_time")
    private String expiryTime;

    @TableField(value = "active_time")
    private String activeTime;


    private String errorMsg;
    /**
     * 租户号
     */
    private Integer tenantId;
}

