package com.jlr.ecp.subscription.dal.dataobject.iccid;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_iccid_modify_batch_records
 * @TableName t_iccid_modify_batch_records
 */
@TableName(value ="t_iccid_modify_batch_records")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class IccidModifyBatchRecordsDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 批次号
     */
    @TableField(value = "batch_no")
    private Long batchNo;

    /**
     * 上传原始文件S3文件URL
     */
    @TableField(value = "upload_file")
    private String uploadFile;

    /**
     * 校验结果;校验结果;校验结果;校验结果 0：不通过 1：通过
     */
    @TableField(value = "verify_result")
    private Integer verifyResult;

    /**
     * 处理状态;处理状态 0：待处理  1：处理中 2：已处理
     */
    @TableField(value = "deal_status")
    private Integer dealStatus;

    /**
     * 校验结果文件;对于失败的校验结果S3文件URL
     */
    @TableField(value = "verify_result_file")
    private String verifyResultFile;

    /**
     * 操作人账号;操作人账号
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}