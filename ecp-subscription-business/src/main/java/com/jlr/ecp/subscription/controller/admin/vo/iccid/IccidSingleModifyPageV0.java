package com.jlr.ecp.subscription.controller.admin.vo.iccid;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "ICCID单个修改操作记录分页VO")
public class IccidSingleModifyPageV0 {

    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "车辆识别号（VIN）")
    private String carVin;

    @Schema(description = "新ICCID：修改后ICCID")
    private String modifyAfterIccid;

    @Schema(description = "批次编码")
    private String modifyNo;
}