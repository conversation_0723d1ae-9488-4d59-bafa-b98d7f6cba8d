package com.jlr.ecp.subscription.excel.pojo.appduc;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = false)
public class AppDCuBatchRenewalExcel {
    @ExcelProperty("VIN")
    private String carVin;

    @ExcelProperty("信息娱乐服务续期日期(yyyy/mm/dd)")
    private String appDRenewalDate;

    @ExcelProperty("网络流量续期日期(yyyy/mm/dd)")
    private String unicomRenewalDate;
}
