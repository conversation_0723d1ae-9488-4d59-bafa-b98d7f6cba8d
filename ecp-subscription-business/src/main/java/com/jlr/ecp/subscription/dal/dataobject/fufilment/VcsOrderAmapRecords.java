package com.jlr.ecp.subscription.dal.dataobject.fufilment;

import com.baomidou.mybatisplus.annotation.*;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "t_vcs_order_amap_records")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsOrderAmapRecords extends BaseDO {

    @TableId
    private Long id;

    /**
     * 履约ID
     */
    @TableField(value = "fufilment_id")
    private String fufilmentId;

    /**
     * 履约订单号;履约订单号（两次拆分成order-1和order-2）
     */
    @TableField(value = "vcs_order_code")
    private String vcsOrderCode;

    /**
     * 充值订单续费结果;充值状态 0：失败；1：成功；
     */
    @TableField(value = "charge_order_status")
    private Integer chargeOrderStatus;

    /**
     * 充值订单查询结果;充值结果状态 0：失败；1：成功；2:待查询
     */
    @TableField(value = "query_order_status")
    private Integer queryOrderStatus;

    /**
     * 高德充值订单查询结果;高德充值订单查询结果
     */
    @TableField(value = "query_response")
    private String queryResponse;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
