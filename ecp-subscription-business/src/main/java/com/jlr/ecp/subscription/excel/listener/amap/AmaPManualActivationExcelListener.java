package com.jlr.ecp.subscription.excel.listener.amap;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPManualActivationExcel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class AmaPManualActivationExcelListener extends AnalysisEventListener<AmaPManualActivationExcel> {

    private static final int BATCH_COUNT = 500;

    private List<AmaPManualActivationExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<AmaPManualActivationExcel> allDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(AmaPManualActivationExcel amaPManualActivationExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(amaPManualActivationExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.error("解析电话号码异常：", e);
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("高德手动激活处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
        } catch (Exception e) {
            log.error("高德手动激活处理最后剩余的数据异常：", e);
        }
        dataList = null;
        log.info("高德手动激活所有数据解析完成！");
    }
}
