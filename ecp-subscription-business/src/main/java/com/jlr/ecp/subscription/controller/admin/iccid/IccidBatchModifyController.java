package com.jlr.ecp.subscription.controller.admin.iccid;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.SendICCIDBatchModifyDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidBatchModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyUploadVO;
import com.jlr.ecp.subscription.service.iccid.IccidBatchModifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "ICCID-批量修改")
@RequestMapping("/iccid/batch/modify")
@Validated
public class IccidBatchModifyController {

    @Resource
    private IccidBatchModifyService iccidBatchModifyService;

    @GetMapping("/download/template")
    @Operation(summary = "下载 ICCID修改上传模版")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-multiple-modify:forms')")
    public CommonResult<String> downloadTemplateUrl() {
        // 获取模板文件的URL
        String templateUrl = iccidBatchModifyService.getIccidTemplateUrl();
        return CommonResult.success(templateUrl);
    }

    @PostMapping("/uploadExcel")
    @Operation(summary = "上传 ICCID批量修改 excel文件")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-multiple-modify:forms')")
    public CommonResult<IccidBatchModifyUploadVO> uploadExcelBatchModify(@RequestBody MultipartFile multipartFile) {
        return iccidBatchModifyService.uploadIccidExcelModify(multipartFile);
    }

    @PostMapping("/queryBatchPageList")
    @Operation(summary = "Iccid批量修改，日志分页")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-multiple-modify:forms')")
    public CommonResult<PageResult<IccidBatchModifyPageVO>> queryBatchModifyPageList(@RequestBody @Valid IccidBatchModifyPageDTO pageDto) {
        return CommonResult.success(iccidBatchModifyService.queryBatchModifyPageList(pageDto));
    }

    @PostMapping("/sendBatchModifyICCID")
    @Operation(summary = "ICCID批量修改发送")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-multiple-modify:forms')")
    public CommonResult<String> sendBatchModifyICCID(@RequestBody @Valid SendICCIDBatchModifyDTO iccidBatchModifyDTO) {
        return iccidBatchModifyService.batchModifyIccid(iccidBatchModifyDTO);
    }
}