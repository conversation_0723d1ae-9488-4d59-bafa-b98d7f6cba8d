package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 查询SIM卡信息
 */
@Data
public class UnicomReqDTO implements Serializable {

    @Schema(description = "车架号列表")
    @NotNull(message = "车架号不能为空")
    private List<String> carVinList;
}
