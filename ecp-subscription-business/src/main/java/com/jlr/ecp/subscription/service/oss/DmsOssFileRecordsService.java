package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.bau.dto.HandleFileResultVO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.api.bau.dto.TransferFileVO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeLogVO;

import java.util.Set;

/**
 * OSS文件记录(DmsOssFileRecords)表服务接口
 *
 * <AUTHOR>
 */
public interface DmsOssFileRecordsService {

    /**
     * OSS拉取文件上传到S3
     * @param dateStr YYYYMMDD格式
     * @return
     */
    TransferFileVO transferFile(String dateStr);


    /**
     * 处理S3的DMS文件
     * @param dateStr YYYYMMDD格式
     * @return
     */
    HandleFileResultVO handleFile(String dateStr);

    /**
     * VIN在线服务初始化日志查询
     */
    PageResult<OnlineServiceInitializeLogVO> getLogPageList(VinInitialLogPageDTO logPageDTO);

    /**
     * 更新同步结果
     */
    CommonResult<Boolean> updateSyncResult(Set<Long> bauJobIdSet);

    /**
     * 更新补录同步结果
     */
    void updateManualSyncResult(Long bauJobId);
}

