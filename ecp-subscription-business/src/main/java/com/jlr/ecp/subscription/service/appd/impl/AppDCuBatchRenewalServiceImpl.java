package com.jlr.ecp.subscription.service.appd.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.amap.VerifyResultEnum;
import com.jlr.ecp.subscription.enums.appd.AppDDataSourceEnum;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.excel.listener.appduc.AppDUcBatchRenewalCheckListener;
import com.jlr.ecp.subscription.excel.listener.appduc.AppDUcBatchRenewalReadListener;
import com.jlr.ecp.subscription.excel.pojo.appduc.AppDCuBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.appduc.AppDCuBatchRenewalResultExcel;
import com.jlr.ecp.subscription.excel.utils.AppDUcExcelUtil;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.properties.AppDUcProperties;
import com.jlr.ecp.subscription.service.appd.AppDCuBatchRenewalService;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.util.*;
import com.jlr.ecp.subscription.util.pivi.UnicomMaxExpireTimeUtil;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppDCuBatchRenewalServiceImpl implements AppDCuBatchRenewalService {
    @Resource
    private AppDUcProperties appDUcProperties;

    @Resource
    private Snowflake snowflake;

    @Resource
    private FileService fileService;

    @Resource
    private PIVIAppDService piviAppDService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    @Resource
    private AppDCuRenewBatchRecordsMapper appDCuRenewBatchRecordsMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor  subscribeAsyncThreadPool;

    @Resource
    private AppDCuSingleRenewalService appDCuSingleRenewalService;

    @Resource
    private Redisson redisson;

    @Resource
    private OrderCheckService orderCheckService;

    @Resource
    private PermissionApi permissionApi;


    private static final String FILE_CODE = "AWS_S3_FILE";

    private static final String EXCEL_FORMATTER = ".xlsx";

    private static final String TODAY_END = " 23:59:59";

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024;

    /**
     * 获取AppDUC批量续费的模板URL
     *
     * @return 模板的URL地址
     */
    @Override
    public String getAppDUcTemplateUrl() {
        return appDUcProperties.getRenewalExcelUrl();
    }

    /**
     *  APPD-UC 批量续费分页查询列表
     *
     * @param pageDTO 分页查询参数
     *
     * @return 续费页面的数据列表和总记录数
     */
    @Override
    public PageResult<AppDUcBatchRenewalPageVO> queryAppDUcBatchRenewalPageList(AppDUcBatchRenewalPageDTO pageDTO) {
        log.info("APPD-UC批量续费分页查询列表, pageDTO:{}", pageDTO);
        Page<AppDCuRenewBatchRecords> pageResp = queryAppDCuRenewBatchPage(pageDTO);
        List<AppDCuRenewBatchRecords> batchRecordsList = pageResp.getRecords();
        List<AppDUcBatchRenewalPageVO> pageVOList = buildAppDUcBatchRenewalPageVOList(batchRecordsList);
        return new PageResult<>(pageVOList, pageResp.getTotal());
    }

    /**
     * 上传AppDUc批量续费的Excel文件
     *
     * @param multipartFile 多部分文件对象，包含上传的Excel文件
     * @return CommonResult<AppDUcBatchRenewalUploadVO> 返回一个通用结果对象，包含上传结果和相关信息
     */
    @Override
    public CommonResult<AppDUcBatchRenewalUploadVO> uploadAppDUcExcelRenewal(MultipartFile multipartFile) {
        log.info("上传AppDUc批量续费的Excel文件, multipartFile:{}", multipartFile.toString());
        long startTime = System.currentTimeMillis();
        // 检查文件大小
        if (multipartFile.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        AppDUcBatchRenewalCheckListener renewalCheckListener = new AppDUcBatchRenewalCheckListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), AppDCuBatchRenewalExcel.class,
                    renewalCheckListener).sheet().doRead();
        } catch (Exception e) {
            log.info("上传AppDUc批量续费的Excel文件异常:{}", e.getMessage());
        }
        AppDUcBatchRenewalUploadVO renewalUploadVO = new AppDUcBatchRenewalUploadVO();
        renewalUploadVO.setType("success");
        renewalUploadVO.setMsg("请关注续费文件校验结果");
        long endTime = System.currentTimeMillis();
        log.info("上传AppDUc批量续费的excel文件解析完成，花费时间:{}毫秒", (endTime-startTime));
        if (Boolean.TRUE.equals(renewalCheckListener.getFormatError())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(renewalCheckListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        addAppDUcBatchRenewalRecords(renewalCheckListener);
        return CommonResult.success(renewalUploadVO);
    }

    /**
     * 批量发送AppDCu续费通知
     *
     * @param appDUcBatchSendDTO 批量发送AppDUc续费的参数对象，包含批次号等信息
     * @return CommonResult<String> 返回操作结果的封装对象，包含成功、错误等状态及相应消息
     */
    @Override
    public CommonResult<String> batchSendAppDCuRenewal(AppDUcBatchSendDTO appDUcBatchSendDTO) {
        log.info("AppDCu批量发送续费, appDCuBatchSendDTO:{}", appDUcBatchSendDTO);
        RLock lock = redisson.getLock(Constants.REDIS_KEY.APPDCU_BATCH_RENEWAL_KEY);
        try {
            if (!RLockUtil.tryLock(lock, 30, 60, TimeUnit.SECONDS)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            Long batchNo = Long.parseLong(appDUcBatchSendDTO.getBatchNo());
            List<AppDCuRenewBatchRecords> progressRecordsDOList = queryAppDUcBatchByDealStatus(DealStatusEnum.PROGRESS.getStatus());
            if (CollUtil.isNotEmpty(progressRecordsDOList)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            AppDCuRenewBatchRecords batchRecordsDO = queryAppDCuRenewalByBatchNo(batchNo);
            log.info("AppDCu批量发送续费, batchRecordsDO:{}", batchRecordsDO);
            if (Objects.isNull(batchRecordsDO)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
            }
            batchRecordsDO.setDealStatus(DealStatusEnum.PROGRESS.getStatus());
            appDCuRenewBatchRecordsMapper.updateById(batchRecordsDO);
            String operator = LoginUtil.getLoginUserName();
            log.info("AppDCu批量发送续费, operator:{}", operator);
            CompletableFuture.runAsync(() -> appDBatchInsertAndSendRenewal(batchRecordsDO, batchNo, operator));
        } catch (Exception e) {
            log.info("AppDCu批量发送续费异常:{}", e.getMessage());
            return CommonResult.error(new ErrorCode(1000001, e.getMessage()));
        } finally {
            RLockUtil.unlock(lock, 3);
        }
        return CommonResult.success("请求已经发送，请前往续费记录查看结果");
    }

    /**
     * 批量插入数据库记录并发送续期通知
     *
     * @param batchRecordsDO 批处理记录实体类，包含批处理相关信息
     * @param batchNo 批号，用于标识特定的批处理
     * @param operator 操作员，用于记录操作人员信息
     */
    private void appDBatchInsertAndSendRenewal(AppDCuRenewBatchRecords batchRecordsDO, Long batchNo, String operator) {
        try {
            AppDCuBatchRenewalServiceImpl bean = applicationContext.getBean(getClass());
            byte[] content = fileService.getFileContentByAllPath(FILE_CODE, batchRecordsDO.getUploadFile());
            List<List<AppDCuBatchRenewalExcel>> batchRenewalExcelList = doReadAppDUcRenewalExcel(content);
            for (List<AppDCuBatchRenewalExcel> batchRenewalExcels : batchRenewalExcelList) {
                bean.batchInsertAppDUcRenewalRecord(batchRenewalExcels, batchNo, operator);
            }
            appDCuManualBatchRenewal(batchRecordsDO);
        } catch (Exception e) {
            log.info("批量插入数据库记录并发送续期通知异常：{}", e.getMessage());
        }
    }

    /**
     * 手动批量续费AppDCu
     * 该方法根据批处理记录号获取待续费的AppDCu记录，并分别对不同服务类型进行批量续费操作，最后更新批处理记录的状态
     *
     * @param batchRecordsDO 批处理记录对象，包含批处理号等信息
     */
    private void appDCuManualBatchRenewal(AppDCuRenewBatchRecords batchRecordsDO) {
        log.info("AppDCu批量续费, batchRecordsDO:{}", batchRecordsDO);
        if (Objects.isNull(batchRecordsDO)) {
            return ;
        }
        Long batchNo = batchRecordsDO.getBatchNo();
        List<AppDCuRenewRecords> appdList = getAppDCuRecordsByType(batchNo,
                RenewServiceTypeEnum.APPD.getServiceType(), AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        List<AppDCuRenewRecords> unicomList = getAppDCuRecordsByType(batchNo,
                RenewServiceTypeEnum.UNICOM.getServiceType(), AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        appDBatchSendRenewalAndUpdate(appdList, AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        unicomBatchSendRenewalAndUpdate(unicomList, AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        //更新批量记录的状态
        batchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        appDCuRenewBatchRecordsMapper.updateById(batchRecordsDO);
    }

    /**
     * APPD批量发送续订请求并更新续订记录
     *
     * @param appdList 待处理的续订记录列表
     * @param renewStatus 更新状态
     */
     @Override
     public void appDBatchSendRenewalAndUpdate(List<AppDCuRenewRecords> appdList, Integer renewStatus) {
        List<List<AppDCuRenewRecords>> groupList = getAppDCuRenewListByGroup(appdList, 10);
        if (CollUtil.isEmpty(groupList)) {
            return ;
        }
        for (List<AppDCuRenewRecords> recordList : groupList) {
            RLock rlock = redisson.getLock(Constants.REDIS_KEY.APPD_ASYNC_RENEWAL_KEY);
            try {
                if (RLockUtil.tryLock(rlock, 5, 10, TimeUnit.SECONDS)) {
                    List<AppDCuRenewRecords> validRenewAppDList = filterInvalidRecord(recordList, renewStatus);
                    asyncRenewalAppDList(validRenewAppDList);
                }
            } catch (Exception e) {
                log.info("AppD批量发送续订请求并更新续订记录异常：", e);
            } finally {
                RLockUtil.unlock(rlock, 3);
            }
        }
    }

    /**
     * 异步更新待续订的AppD列表
     *
     * @param waitedRenewList 待续订的应用D列表
     */
    public void asyncRenewalAppDList(List<AppDCuRenewRecords> waitedRenewList) {
        if (CollUtil.isEmpty(waitedRenewList)) {
            return ;
        }
        // sprint47:校验是否存在续费中的记录
        List<AppDCuRenewRecords> filteredList = checkProcessRecordsByVinList(waitedRenewList, RenewServiceTypeEnum.APPD);
        if (CollUtil.isEmpty(filteredList)) {
            log.info("批量续费APPD服务，全部是正在在续费中的记录，waitedRenewList:{}", waitedRenewList);
            return ;
        }
        // sprint47:校验是否存在在途订单
        List<AppDCuRenewRecords> notInTransitVinList = checkOrderInTransitByVinList(filteredList, RenewServiceTypeEnum.APPD);
        if (CollUtil.isEmpty(notInTransitVinList)) {
            log.info("批量续费APPD服务，全部是在途订单，filteredList:{}", filteredList);
            return ;
        }
        for (AppDCuRenewRecords appDCuRenewRecords : notInTransitVinList) {
            CompletableFuture.runAsync(() -> {
                CommonResult<VcsOrderFufilmentCall> callResp = piviAppDService.appDManualRenewal(
                        appDCuRenewRecords.getCarVin(), appDCuRenewRecords.getRenewDate());
                if (callResp.isSuccess()) {
                    appDCuSingleRenewalService.updateAppDRenewRecords(appDCuRenewRecords, callResp);
                } else {
                    appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
                    appDCuRenewRecords.setOrderResultCode(String.valueOf(callResp.getCode()));
                    appDCuRenewRecords.setErrorDesc(callResp.getMsg());
                    appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
                }
            }, subscribeAsyncThreadPool);
        }
    }

    /**
     * 批量发送联通续约请求，并更新续约记录
     *
     * @param unicomList 待处理的联通续约记录列表
     * @param renewStatus 更新状态
     */
    @Override
    public void unicomBatchSendRenewalAndUpdate(List<AppDCuRenewRecords> unicomList, Integer renewStatus) {
        List<List<AppDCuRenewRecords>> groupList = getAppDCuRenewListByGroup(unicomList, 5);
        if (CollUtil.isEmpty(groupList)) {
            return ;
        }
        for (List<AppDCuRenewRecords> recordList : groupList) {
            RLock rLock = redisson.getLock(Constants.REDIS_KEY.UNICOM_ASYNC_RENEWAL_KEY);
            try {
                if (RLockUtil.tryLock(rLock, 5, 10, TimeUnit.SECONDS)) {
                    List<AppDCuRenewRecords> waitedRenewUnicomList = filterInvalidRecord(recordList, renewStatus);
                    asyncRenewalUnicomList(waitedRenewUnicomList);
                }
            } catch (Exception e) {
                log.info("批量发送联通续约请求，并更新续约记录异常：", e);
            } finally {
                RLockUtil.unlock(rLock, 3);
            }
        }
    }

    /**
     * 异步更新待续订的联通
     *
     * @param waitedRenewList 待续订的应用D列表
     */
    public void asyncRenewalUnicomList(List<AppDCuRenewRecords> waitedRenewList) {
        long startTime = System.currentTimeMillis();
        if (CollUtil.isEmpty(waitedRenewList)) {
            return ;
        }
        // sprint47:校验是否存在续费中的记录
        List<AppDCuRenewRecords> filteredList = checkProcessRecordsByVinList(waitedRenewList, RenewServiceTypeEnum.UNICOM);
        if (CollUtil.isEmpty(filteredList)) {
            log.info("批量续费UNICOM服务，全部是正在在续费中的记录，waitedRenewList:{}", waitedRenewList);
            return ;
        }
        // sprint47:校验是否存在在途订单
        List<AppDCuRenewRecords> notInTransitVinList = checkOrderInTransitByVinList(filteredList, RenewServiceTypeEnum.UNICOM);
        if (CollUtil.isEmpty(notInTransitVinList)) {
            log.info("批量续费UNICOM服务，全部是在途订单，filteredList:{}", filteredList);
            return ;
        }
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (AppDCuRenewRecords appDCuRenewRecords : notInTransitVinList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> updateUnicomRenewRecords(appDCuRenewRecords), subscribeAsyncThreadPool);
            futureList.add(future);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        long endTime = System.currentTimeMillis();
        log.info("异步更新数量待续订的联通数量：{}，耗时：{}ms", waitedRenewList.size(), endTime - startTime);
        if (endTime - startTime < 1000) {
            try {
                Thread.sleep(1000 - (endTime - startTime));
            } catch (Exception e) {
                log.info("异步更新待续订的联通异常:", e);
            }
        }
    }

    /**
     * 更新联通续订记录
     *
     * 该方法调用联通手动续订接口，并根据返回结果更新续订记录的状态和相关信息
     *
     * @param appDCuRenewRecords 续订记录对象，包含续订相关信息如车辆VIN、续订日期和客户订单ID
     */
    private void updateUnicomRenewRecords(AppDCuRenewRecords appDCuRenewRecords) {
        CommonResult<UnicomRespVO> unicomResp = piviUnicomService.unicomManualRenewalJob(
                appDCuRenewRecords.getCarVin(), appDCuRenewRecords.getRenewDate(), appDCuRenewRecords.getCusOrderId());
        appDCuRenewRecords.setUpdatedTime(LocalDateTime.now());
        if (unicomResp.isSuccess()) {
            if (Objects.nonNull(unicomResp.getData())) {
                appDCuSingleRenewalService.updateUnicomRenewRecords(appDCuRenewRecords, unicomResp);
            } else {
                appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_PROGRESS.getStatus());
                appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
            }
        } else {
            appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
            appDCuRenewRecords.setOrderResultCode(String.valueOf(UnicomResultEnum.ICCID_MISSING.code));
            appDCuRenewRecords.setErrorDesc(unicomResp.getMsg());
            appDCuRenewRecordsMapper.updateById(appDCuRenewRecords);
        }
    }

    /**
     * 过滤掉非等待状态的记录
     *
     * @param recordList AppDCuRenewRecords对象列表，代表一些需要被过滤的记录
     * @param renewStatus 待过滤的AppDCuRenewRecords对象的状态码
     * @return 返回一个过滤后的AppDCuRenewRecords对象列表
     */
    private List<AppDCuRenewRecords> filterInvalidRecord(List<AppDCuRenewRecords> recordList, Integer renewStatus) {
        if (CollUtil.isEmpty(recordList)) {
            return recordList;
        }
        List<Long> idList = recordList.stream().map(AppDCuRenewRecords::getId).collect(Collectors.toList());
        return getAppDRecordsByIdListAndStatus(idList, renewStatus);
    }

    /**
     * 根据ID列表和状态获取AppD续费记录
     *
     * @param idList ID列表，用于筛选特定的记录
     * @param status 状态码，用于筛选特定状态的记录
     * @return 符合条件的AppDCuRenewRecords记录列表
     */
    private List<AppDCuRenewRecords> getAppDRecordsByIdListAndStatus(List<Long> idList, Integer status) {
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppDCuRenewRecords::getId, idList)
                .eq(AppDCuRenewRecords::getRenewStatus, status)
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        return appDCuRenewRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 根据分组大小将应用度量记录列表切分为多个子列表
     *
     * @param appdList 应用度量记录列表，包含所有记录
     * @param groupSize 每个分组的大小，用于确定每个子列表包含的元素数量
     * @return 返回一个列表，其中包含按分组大小切分的子列表
     */
    private List<List<AppDCuRenewRecords>> getAppDCuRenewListByGroup(List<AppDCuRenewRecords> appdList,
                                                               Integer groupSize) {
        List<List<AppDCuRenewRecords>> resp = new ArrayList<>();
        for (int i = 0; i < appdList.size(); i += groupSize) {
            int endIdx = Math.min(i+groupSize, appdList.size());
            resp.add(appdList.subList(i, endIdx));
        }
        return resp;
    }


    /**
     * 根据服务类型和续费状态获取AppDCu续费记录
     *
     * @param batchNo 批次号，用于定位特定的一组记录
     * @param serviceType 服务类型，用于区分不同的服务
     * @param renewStatus 续费状态，用于筛选特定状态的记录
     * @return 返回符合指定条件的续费记录列表
     */
    private List<AppDCuRenewRecords> getAppDCuRecordsByType(Long batchNo, Integer serviceType, Integer renewStatus) {
        LambdaQueryWrapper<AppDCuRenewRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewRecords::getRenewNo, batchNo)
                .eq(AppDCuRenewRecords::getRenewServiceType, serviceType)
                .eq(AppDCuRenewRecords::getRenewStatus, renewStatus)
                .eq(AppDCuRenewRecords::getIsDeleted, false);
        return appDCuRenewRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 批量插入AppDUc续费记录
     *
     * @param batchRenewalExcelList 会员续费Excel列表，包含多个会员的续费信息
     * @param batchNo 批次编号，用于标识当前批次的唯一性
     * @param operator 操作人，用于记录续费记录的操作人员
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertAppDUcRenewalRecord(List<AppDCuBatchRenewalExcel> batchRenewalExcelList, Long batchNo,
                                               String operator) {
        if (CollUtil.isEmpty(batchRenewalExcelList)) {
            log.info("批量插入AppDUc续费记录为空, batchNo:{}", batchNo);
            return ;
        }
        List<AppDCuBatchRenewalExcel> appDExcels = batchRenewalExcelList.stream()
                .filter(excel -> StringUtils.isNotBlank(excel.getAppDRenewalDate()))
                .collect(Collectors.toList());
        List<AppDCuBatchRenewalExcel> unicomExcels = batchRenewalExcelList.stream()
                .filter(excel -> StringUtils.isNotBlank(excel.getUnicomRenewalDate()))
                .collect(Collectors.toList());
        log.info("批量插入AppDUc续费记录, batchNo:{}, carVin数量:{}, appD续期不为空的数量：{}, 联通续期不为空的数量：{}",
                batchNo, batchRenewalExcelList.size(), appDExcels.size(), unicomExcels.size());
        Map<String, LocalDateTime> appDBeforeRenewDateMap = getAppDBeforeRenewalDateMap(appDExcels);
        Map<String, UnicomRespVO> unicomBeforeRenewDateMap = getUnicomBeforeRenewalDateMap(unicomExcels);
        log.info("批量插入AppDUc续费记录, appDBeforeRenewDateMap的数量:{}, unicomBeforeRenewDateMap的数量:{}",
                appDBeforeRenewDateMap.size(), unicomBeforeRenewDateMap.size());
        List<AppDCuRenewRecords> appDRecordList = new ArrayList<>();
        List<AppDCuRenewRecords> unicomRecordList = new ArrayList<>();
        for (AppDCuBatchRenewalExcel amaPBatchRenewalExcel : batchRenewalExcelList) {
            if (StringUtils.isNotBlank(amaPBatchRenewalExcel.getAppDRenewalDate())) {
                LocalDateTime appDBeforeRenewDate = appDBeforeRenewDateMap.get(amaPBatchRenewalExcel.getCarVin());
                AppDCuRenewRecords appDRenewRecordsDO = buildAppDRenewalRecord(amaPBatchRenewalExcel, batchNo,
                        operator, appDBeforeRenewDate);
                appDRecordList.add(appDRenewRecordsDO);
            }
            if (StringUtils.isNotBlank(amaPBatchRenewalExcel.getUnicomRenewalDate())) {
                UnicomRespVO unicomRespVO = unicomBeforeRenewDateMap.get(amaPBatchRenewalExcel.getCarVin());
                AppDCuRenewRecords unicomRenewRecordsDO = buildUnicomRenewalRecord(amaPBatchRenewalExcel, batchNo,
                        operator, unicomRespVO);
                unicomRecordList.add(unicomRenewRecordsDO);
            }
        }
        if (CollUtil.isNotEmpty(appDRecordList)) {
            appDCuRenewRecordsMapper.insertBatch(appDRecordList);
        }
        if (CollUtil.isNotEmpty(unicomRecordList)) {
            appDCuRenewRecordsMapper.insertBatch(unicomRecordList);
        }
    }

    /**
     * 获取AppD在之前的续费日期

     * @param appDCuExcelList 包含应用数据库续费信息的列表
     * @return 返回一个映射，键为数据库标识，值为续费日期之前的时间
     */
    private Map<String, LocalDateTime> getAppDBeforeRenewalDateMap(List<AppDCuBatchRenewalExcel> appDCuExcelList) {
        Map<String, LocalDateTime> map = new HashMap<>();
        Map<String, CompletableFuture<LocalDateTime>> futureMap = new HashMap<>();
        List<List<AppDCuBatchRenewalExcel>> groupList = getAppDCuExcelListByGroup(appDCuExcelList, 10);
        for (List<AppDCuBatchRenewalExcel> excelList : groupList) {
            long startTime = System.currentTimeMillis();
            List<CompletableFuture<LocalDateTime>> futureList = new ArrayList<>();
            for (AppDCuBatchRenewalExcel appDExcel : excelList) {
                if (StringUtils.isBlank(appDExcel.getAppDRenewalDate())) {
                    log.info("获取AppD在之前的续费日期, APPD续费时间为空, appDExcel:{}", appDExcel);
                    continue;
                }
                CompletableFuture<LocalDateTime> future = CompletableFuture.supplyAsync(
                        () -> getAppDExpiryDate(appDExcel.getCarVin()), subscribeAsyncThreadPool);
                futureMap.put(appDExcel.getCarVin(), future);
                futureList.add(future);
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
            long endTime = System.currentTimeMillis();
            if (endTime - startTime < 1000) {
                try {
                    Thread.sleep(1000 - (endTime-startTime));
                } catch (Exception e) {
                    log.info("获取AppD在之前的续费日期异常:", e);
                }
            }
        }
        for (Map.Entry<String, CompletableFuture<LocalDateTime>> entry : futureMap.entrySet()) {
            map.put(entry.getKey(), entry.getValue().join());
        }
        return map;
    }

    /**
     * 获取联通用户在续约日期前的信息映射
     *
     * @param unicomExcelList 一个包含联通用户续约信息的列表，用于提取用户信息如车架号等
     * @return 返回一个映射，键为车架号（String），值为从服务中获取的用户信息（UnicomRespVO）
     */
    private Map<String, UnicomRespVO> getUnicomBeforeRenewalDateMap(List<AppDCuBatchRenewalExcel> unicomExcelList) {
        Map<String, UnicomRespVO> map = new HashMap<>();
        Map<String, CompletableFuture<UnicomRespVO>> futureMap = new HashMap<>();
        List<List<AppDCuBatchRenewalExcel>> groupList = getAppDCuExcelListByGroup(unicomExcelList, 5);
        for (List<AppDCuBatchRenewalExcel> excelList : groupList) {
            long startTime = System.currentTimeMillis();
            List<CompletableFuture<UnicomRespVO>> futureList = new ArrayList<>();
            for (AppDCuBatchRenewalExcel unicomExcel : excelList) {
                if (StringUtils.isBlank(unicomExcel.getUnicomRenewalDate())) {
                    log.info("获取联通用户在续约日期前的信息映射, 联通续期时间为空, unicomExcel:{}", unicomExcel);
                    continue;
                }
                CompletableFuture<UnicomRespVO> future = CompletableFuture.supplyAsync(
                        () -> piviUnicomService.getSimCarInfoByCarVin(unicomExcel.getCarVin()), subscribeAsyncThreadPool);
                futureMap.put(unicomExcel.getCarVin(), future);
                futureList.add(future);
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
            long endTime = System.currentTimeMillis();
            if (endTime - startTime < 1000) {
                try {
                    Thread.sleep(1000 - (endTime-startTime));
                } catch (Exception e) {
                    log.info("获取联通用户在续约日期前的信息映射异常:", e);
                }
            }
        }
        for (Map.Entry<String, CompletableFuture<UnicomRespVO>> entry : futureMap.entrySet()) {
            map.put(entry.getKey(), entry.getValue().join());
        }
        return map;
    }

    /**
     * 根据组大小将AppDCu批处理续费Excel列表分组
     *
     * @param appdList 原始的AppDCu批处理续费Excel列表
     * @param groupSize 每个组的大小
     * @return 返回一个列表，其中每个元素是原始列表的一个子列表，代表一个组
     */
    private List<List<AppDCuBatchRenewalExcel>> getAppDCuExcelListByGroup(List<AppDCuBatchRenewalExcel> appdList,
                                                                     Integer groupSize) {
        List<List<AppDCuBatchRenewalExcel>> resp = new ArrayList<>();
        for (int i = 0; i < appdList.size(); i += groupSize) {
            int endIdx = Math.min(i+groupSize, appdList.size());
            resp.add(appdList.subList(i, endIdx));
        }
        return resp;
    }

    /**
     * 构建AppD续订记录对象
     *
     * @param appDCuBatchRenewalExcel 包含车辆信息和续订日期的Excel数据对象
     * @param batchNo 批次编号，用于标识续订事件
     * @param operator 操作人
     * @param appDBeforeRenewDate AppD在之前的续费日期
     * @return 返回构建的AppD续订记录对象
     */
    private AppDCuRenewRecords buildAppDRenewalRecord(AppDCuBatchRenewalExcel appDCuBatchRenewalExcel, Long batchNo,
                                                      String operator, LocalDateTime appDBeforeRenewDate) {
        AppDCuRenewRecords appDCuRenewRecords = new AppDCuRenewRecords();
        appDCuRenewRecords.setCarVin(CarVinUtil.carVinToUpperCase(appDCuBatchRenewalExcel.getCarVin()));
        appDCuRenewRecords.setRenewNo(batchNo);
        appDCuRenewRecords.setRenewServiceType(RenewServiceTypeEnum.APPD.getServiceType());
        appDCuRenewRecords.setRenewDate(SubscribeTimeFormatUtil.stringToTimeByFormat(appDCuBatchRenewalExcel.getAppDRenewalDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2));
        appDCuRenewRecords.setCusOrderId(snowflake.nextIdStr());
        appDCuRenewRecords.setOperator(operator);
        appDCuRenewRecords.setDataSource(AppDDataSourceEnum.BATCH.getDateSource());
        appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        appDCuRenewRecords.setRenewBeforeExpiryDate(appDBeforeRenewDate);
        return appDCuRenewRecords;
    }

    /**
     * 构建联通续约记录对象
     *
     * @param appDCuBatchRenewalExcel 批量续约Excel数据对象，包含车辆信息和续约信息
     * @param batchNo 批次编号，用于标识续约批次
     * @param operator 操作人，用于记录操作人员
     * @param unicomRespVO 获取到的UnicomRespVO对象，包含车辆信息
     * @return 返回构建好的联通续约记录对象
     */
    private AppDCuRenewRecords buildUnicomRenewalRecord(AppDCuBatchRenewalExcel appDCuBatchRenewalExcel, Long batchNo,
                                                        String operator, UnicomRespVO unicomRespVO) {
        AppDCuRenewRecords appDCuRenewRecords = new AppDCuRenewRecords();
        appDCuRenewRecords.setCarVin(CarVinUtil.carVinToUpperCase(appDCuBatchRenewalExcel.getCarVin()));
        appDCuRenewRecords.setRenewNo(batchNo);
        appDCuRenewRecords.setRenewServiceType(RenewServiceTypeEnum.UNICOM.getServiceType());
        appDCuRenewRecords.setRenewDate(SubscribeTimeFormatUtil.stringToTimeByFormat(appDCuBatchRenewalExcel.getUnicomRenewalDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2));
        appDCuRenewRecords.setCusOrderId(snowflake.nextIdStr());
        appDCuRenewRecords.setOperator(operator);
        appDCuRenewRecords.setDataSource(AppDDataSourceEnum.BATCH.getDateSource());
        appDCuRenewRecords.setRenewStatus(AppDRenewStatusEnum.WAIT_RENEW.getStatus());
        appDCuRenewRecords.setRenewBeforeExpiryDate(UnicomMaxExpireTimeUtil.getUnicomMaxExpireDate(unicomRespVO));
        return appDCuRenewRecords;
    }


    /**
     * 根据车辆VIN号获取AppD订阅的到期日期
     *
     * @param carVin 车辆VIN号，用于查询特定车辆的订阅信息
     * @return 返回AppD订阅的到期日期如果找不到有效的订阅信息，则返回null
     */
    private LocalDateTime getAppDExpiryDate(String carVin) {
        AppDSubscriptionResp appDSubscriptionResp = piviAppDService.getVinSubscriptions(carVin);
        if (Objects.isNull(appDSubscriptionResp) || CollUtil.isEmpty(appDSubscriptionResp.getResult())) {
            log.info("根据车辆VIN号获取AppD订阅的到期日期为空， carVin:{}", carVin);
            return null;
        }
        return appDSubscriptionResp.getResult().get(0).getExpiresDate();
    }

    /**
     * 读取AppDUc续费Excel文件
     *
     * @param bytes Excel文件的字节内容
     * @return 解析后的Excel数据列表
     */
    public List<List<AppDCuBatchRenewalExcel>> doReadAppDUcRenewalExcel(byte[] bytes) {
        List<List<AppDCuBatchRenewalExcel>> resp = new ArrayList<>();
        if (bytes == null || bytes.length < 1) {
            log.info("读取AppDUc续费Excel文件, bytes为空");
            return resp;
        }
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "AppDUcBatchRenewalReadS3Excel" + System.currentTimeMillis() + EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("读取AppDUc续费Excel文件, filePath:{}", filePath);
        AppDUcBatchRenewalReadListener appDUcReadListener = new AppDUcBatchRenewalReadListener();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(bytes);
            outputStream.flush();
            EasyExcel.read(fileName, AppDCuBatchRenewalExcel.class, appDUcReadListener).sheet().doRead();
            log.info("读取AppDUc续费Excel文件, appDUcReadListener读取数据的数量:{}", appDUcReadListener.getAllList().size());
            resp = appDUcReadListener.getAllList();
            log.info("读取AppDUc续费Excel文件, filePath:{}", filePath);
        } catch (Exception e) {
            log.info("读取AppDUc续费Excel文件, 解析excel文件异常:{}", e.getMessage());
        } finally {
            FileUtil.del(fileName);
        }
        return resp;
    }

    /**
     * 添加AppDUc批量续期记录
     *
     * @param renewalCheckListener 续期检查监听器，用于获取续期检查的相关数据和结果
     */
    private void addAppDUcBatchRenewalRecords(AppDUcBatchRenewalCheckListener renewalCheckListener) {
        AppDCuRenewBatchRecords appDCuRenewBatchRecords = new AppDCuRenewBatchRecords();
        String uploadS3AppDUcSourcePath = uploadAppDUcRenewalSourceToS3File(renewalCheckListener.getAllDataList());
        appDCuRenewBatchRecords.setBatchNo(snowflake.nextId());
        appDCuRenewBatchRecords.setUploadFile(uploadS3AppDUcSourcePath);
        if(Boolean.FALSE.equals(renewalCheckListener.getCheckResult())) {
            String uploadAmaPResultPath = uploadAppDUcRenewalResultToS3File(renewalCheckListener.getResultExcelList());
            appDCuRenewBatchRecords.setVerifyResult(VerifyResultEnum.FAIL.getCode());
            appDCuRenewBatchRecords.setVerifyResultFile(uploadAmaPResultPath);
        } else {
            appDCuRenewBatchRecords.setVerifyResult(VerifyResultEnum.SUCCESS.getCode());
        }
        appDCuRenewBatchRecords.setDealStatus(DealStatusEnum.WAITED.getStatus());
        appDCuRenewBatchRecords.setOperator(LoginUtil.getLoginUserName());
        appDCuRenewBatchRecordsMapper.insert(appDCuRenewBatchRecords);
    }

    /**
     * 将AppDUc批量续订的Excel源文件上传到S3
     *
     * @param batchRenewalExcelList 包含AppDUc批量续订数据的列表，用于生成Excel文件
     * @return 返回上传到S3的文件路径如果生成的本地文件为空或在上传过程中发生异常，则返回空字符串
     */
    private String uploadAppDUcRenewalSourceToS3File(List<AppDCuBatchRenewalExcel> batchRenewalExcelList) {
        String uploadS3FilePath = "";
        File uploadS3TempSourceExcel = null;
        try {
            uploadS3TempSourceExcel = AppDUcExcelUtil.appdUcBatchRenewalWriteSourceFile(batchRenewalExcelList);
            if (Objects.isNull(uploadS3TempSourceExcel)) {
                log.info("AppDUc批量续订Excel源文件上传至S3, 生成本地临时文件为空");
                return uploadS3FilePath;
            }
            uploadS3FilePath = fileService.createFile(null, "AppDCuBatchRenewalExcel" + File.separator
                    + System.currentTimeMillis() + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempSourceExcel), FILE_CODE);
            log.info("AppDUc批量续订Excel源文件上传至S3, 本地文件原始文件路径:{}", uploadS3TempSourceExcel.getPath());
        } catch (Throwable e) {
            log.info("AppDUc批量续订Excel源文件上传至S3异常:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempSourceExcel)) {
                FileUtil.del(uploadS3TempSourceExcel);
                log.info("AppDUc批量续订Excel源文件上传至S3, 成功删除本地临时文件");
            }
        }
        return uploadS3FilePath;
    }

    /**
     * 将APPD&CU批量续费结果Excel文件上传到S3存储
     *
     * @param batchRenewalResultExcels 包含批量续费结果的列表
     * @return 上传到S3的文件路径，如果上传失败则返回空字符串
     */
    private String uploadAppDUcRenewalResultToS3File(List<AppDCuBatchRenewalResultExcel> batchRenewalResultExcels) {
        String uploadS3ResultFilePath = "";
        File uploadS3TempResultExcel = null;
        try {
            uploadS3TempResultExcel = AppDUcExcelUtil.appdUcBatchRenewalWriteResultFile(batchRenewalResultExcels);
            if (Objects.isNull(uploadS3TempResultExcel)) {
                log.info("appDUc批量续费结果Excel文件上传至S3存储, 生成本地临时文件为空");
                return uploadS3ResultFilePath;
            }
            String fileName = "APPD&CU批量续费校验结果" +  System.currentTimeMillis();
            uploadS3ResultFilePath = fileService.createFile(null, "AppDCuBatchRenewalResultExcel" + File.separator +
                    fileName + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempResultExcel), FILE_CODE);
            log.info("AppDUc批量续费结果Excel文件上传至S3存储, 本地文件原始文件路径:{}", uploadS3TempResultExcel.getPath());
        } catch (Throwable e) {
            log.info("AppDUc批量续费结果Excel文件上传至S3存储:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempResultExcel)) {
                FileUtil.del(uploadS3TempResultExcel);
                log.info("AppDUc批量续费结果Excel文件上传至S3存储, 成功删除本地临时文件");
            }
        }
        return uploadS3ResultFilePath;
    }

    /**
     * 构建应用批量续期页面VO列表
     *
     * @param batchRecordsList AppDCuRenewBatchRecords对象列表，记录了批量续期的信息
     * @return 返回一个AppDUcBatchRenewalPageVO对象列表，用于展示批量续期页面的信息
     */
    private List<AppDUcBatchRenewalPageVO> buildAppDUcBatchRenewalPageVOList(List<AppDCuRenewBatchRecords> batchRecordsList) {
        List<AppDUcBatchRenewalPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(batchRecordsList)) {
            return resp;
        }
        for (AppDCuRenewBatchRecords records : batchRecordsList) {
            resp.add(buildAppDUcBatchRenewalPageVO(records));
        }
        return resp;
    }

    /**
     * 构建 AppDUcBatchRenewalPageVO 对象
     *
     * @param batchRecord 批量续订的记录对象，包含批量续订的相关信息
     * @return 如果 batchRecord 不为 null，则返回转换后的 AppDUcBatchRenewalPageVO 对象；否则返回 null
     */
    private AppDUcBatchRenewalPageVO buildAppDUcBatchRenewalPageVO(AppDCuRenewBatchRecords batchRecord) {
        if (Objects.isNull(batchRecord)) {
            return null;
        }
        return AppDUcBatchRenewalPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(batchRecord.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(batchRecord.getOperator())
                .checkResultStatus(batchRecord.getVerifyResult())
                .checkResultDesc(VerifyResultEnum.getDescByCode(batchRecord.getVerifyResult()))
                .operateStatus(batchRecord.getDealStatus())
                .batchNo(batchRecord.getBatchNo())
                .errorDetailPath(batchRecord.getVerifyResultFile())
                .build();
    }


    /**
     * 查询AppD-CU续费批次分页列表
     *
     * @param pageDTO 分页查询参数对象，包含分页信息和排序信息
     * @return 返回分页列表对象，包含查询结果和分页信息
     */
    private Page<AppDCuRenewBatchRecords> queryAppDCuRenewBatchPage(AppDUcBatchRenewalPageDTO pageDTO) {
        Page<AppDCuRenewBatchRecords> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<AppDCuRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewBatchRecords::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(AppDCuRenewBatchRecords::getCreatedTime);
            queryWrapper.orderByAsc(AppDCuRenewBatchRecords::getId);
        } else {
            queryWrapper.orderByDesc(AppDCuRenewBatchRecords::getCreatedTime);
            queryWrapper.orderByDesc(AppDCuRenewBatchRecords::getId);
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }
        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(AppDCuRenewBatchRecords::getOperator, userName);
            log.info("查询AppD-CU续费批次分页列表,user:{}, pageDTO:{}", userName, pageDTO);
        }
        return appDCuRenewBatchRecordsMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 根据处理状态查询AppDCuRenewBatchRecords
     *
     * @param dealStatus 处理状态，用于筛选记录
     * @return 符合条件的AppDCuRenewBatchRecords列表
     */
    private List<AppDCuRenewBatchRecords> queryAppDUcBatchByDealStatus(Integer dealStatus) {
        LambdaQueryWrapper<AppDCuRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewBatchRecords::getDealStatus, dealStatus)
                .eq(AppDCuRenewBatchRecords::getIsDeleted, false);
        return appDCuRenewBatchRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 根据批次编号查询AppDCuRenewBatchRecords对象
     *
     * @param batchNo 批次编号，用于查询特定批次的记录
     * @return 如果找到对应的记录，则返回AppDCuRenewBatchRecords对象；如果未找到或有多条记录，则返回null
     */
    private AppDCuRenewBatchRecords queryAppDCuRenewalByBatchNo(Long batchNo) {
        LambdaQueryWrapper<AppDCuRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDCuRenewBatchRecords::getBatchNo, batchNo)
                .eq(AppDCuRenewBatchRecords::getIsDeleted, false);
        List<AppDCuRenewBatchRecords> resp = appDCuRenewBatchRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询AppDCuRenewBatchRecords对象, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    /**
     * 根据VIN列表检查并处理正在续费过程中的记录
     * 此方法旨在筛选出给定VIN列表中正处于续费状态，并且服务类型匹配的车辆记录
     * 如果没有找到正在续费的记录，则直接返回原始列表；否则，更新记录并返回新的列表
     *
     * @param renewList 待续费的车辆记录列表，包含多个AppDCuRenewRecords对象
     * @param serviceTypeEnum 续费服务类型枚举，用于指定特定的服务类型
     * @return 返回处理后的车辆续费记录列表，如果未找到正在续费的记录，则返回原始列表
     */
    public List<AppDCuRenewRecords> checkProcessRecordsByVinList(List<AppDCuRenewRecords> renewList, RenewServiceTypeEnum serviceTypeEnum) {
        Long renewNo = renewList.get(0).getRenewNo();
        // renewList转为map，key为vin，value为主键id
        Map<String, Long> vinMap = renewList.stream()
                .collect(Collectors.toMap(AppDCuRenewRecords::getCarVin,
                        AppDCuRenewRecords::getId, (v1, v2) -> v1));
        List<String> vinList = new ArrayList<>(vinMap.keySet());

        // 查询是否存在非当前批次号的续费中的记录
        List<String> processVinList = appDCuRenewRecordsMapper.getByVinListAndStatus(vinList,
                        AppDRenewStatusEnum.RENEW_PROGRESS.getStatus(), renewNo, serviceTypeEnum.getServiceType())
                .stream()
                // 当前记录的id在vinMap.get(records.getCarVin())之前，才认为是存在续费中的记录
                .filter(records -> records.getId() < vinMap.get(records.getCarVin()))
                .map(AppDCuRenewRecords::getCarVin)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(processVinList)) {
            log.info("不存在续费中的手动续费记录, serviceType:{}, vinList:{}", serviceTypeEnum.getServiceName(), vinList);
            return renewList;
        }
        log.info("存在续费中的手动续费记录, serviceType:{}, processVinList:{}", serviceTypeEnum.getServiceName(), processVinList);
        return updateRecordsAndReturnNew(renewList, processVinList);
    }

    /**
     * 更新续费记录并返回新的记录列表
     * 该方法遍历续费记录列表，对于车辆VIN在指定处理列表中的记录，更新其订单结果代码、续费状态和错误描述
     * 不在列表中的记录被视为新的续费记录，被保留并返回
     *
     * @param renewList 续费记录列表
     * @param processVinList 需要处理的车辆VIN列表
     * @return 新的续费记录列表
     */
    private List<AppDCuRenewRecords> updateRecordsAndReturnNew(List<AppDCuRenewRecords> renewList, List<String> processVinList) {
        // 需要继续处理的记录
        List<AppDCuRenewRecords> newRenewList = new ArrayList<>();
        // 待更新的续费记录
        List<AppDCuRenewRecords> updateList = new ArrayList<>();
        for (AppDCuRenewRecords renewRecords : renewList) {
            if (processVinList.contains(renewRecords.getCarVin())) {
                renewRecords.setOrderResultCode(String.valueOf(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR.getCode()));
                renewRecords.setRenewStatus(AppDRenewStatusEnum.RENEW_FAIL.getStatus());
                renewRecords.setErrorDesc(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR.getMsg());
                updateList.add(renewRecords);
            } else {
                newRenewList.add(renewRecords);
            }
        }
        appDCuRenewRecordsMapper.updateBatch(updateList);
        return newRenewList;
    }

    /**
     * 根据VIN列表检查订单是否在途中
     * 此方法首先从过滤列表中提取所有VIN，并调用服务检查这些VIN是否有在途订单
     * 如果检查订单状态失败或没有在途订单，则返回过滤列表
     * 如果存在在途订单，则更新记录并返回新的记录列表
     *
     * @param filteredList 过滤后的AppDCuRenewRecords列表，用于检查在途订单
     * @param serviceTypeEnum 服务类型枚举，指示检查订单的服务类型
     * @return 返回可能更新后的AppDCuRenewRecords列表，取决于是否有在途订单
     */
    public List<AppDCuRenewRecords> checkOrderInTransitByVinList(List<AppDCuRenewRecords> filteredList, RenewServiceTypeEnum serviceTypeEnum) {
        List<String> filteredVinList = filteredList.stream().map(AppDCuRenewRecords::getCarVin).collect(Collectors.toList());
        CommonResult<List<String>> checkOrderInTransit = orderCheckService.checkOrderInTransitByVinList(filteredVinList, ServiceTypeEnum.PIVI);
        if (!checkOrderInTransit.isSuccess()) {
            return Collections.emptyList();
        }
        List<String> inTransitVinList = checkOrderInTransit.getData();
        if (CollUtil.isEmpty(inTransitVinList)) {
            log.info("不存在在途订单, serviceType:{}, vinList:{}", serviceTypeEnum.getServiceName(), filteredVinList);
            return filteredList;
        }
        log.info("存在在途订单, serviceType:{}, inTransitVinList:{}", serviceTypeEnum.getServiceName(), inTransitVinList);
        return updateRecordsAndReturnNew(filteredList, inTransitVinList);
    }
}
