package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 实名制变更列表
 * <AUTHOR>
 */
@Data
public class UnicomRealNameOrder implements Serializable {

    /**
     * 状态 必传
     * active：生效
     * cancel：作废
     */
    @JsonProperty("order_type")
    private String orderType;

    /**
     * 订单时间 yyyyMMddHHmmSS
     */
    @JsonProperty("order_time")
    private String orderTime;

}