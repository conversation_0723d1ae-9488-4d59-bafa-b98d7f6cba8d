package com.jlr.ecp.subscription.service.unicom;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.*;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomRnrBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomRnrQueryRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomRnrBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomRnrQueryRecordsMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomBookStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRealnameFlagEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRnrQueryStatusEnum;
import com.jlr.ecp.subscription.excel.listener.rnr.RnrQueryCheckListener;
import com.jlr.ecp.subscription.excel.pojo.rnr.RnrQueryExcel;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.file.util.S3FileUtil;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import com.jlr.ecp.subscription.util.*;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnicomRnrBatchRecordsServiceImpl implements UnicomRnrBatchRecordsService {

    @Value("${unicom.rnrTemplateUrl}")
    private String unicomRnrTemplateExcelUrl;

    @Value("${unicom.rnrJobName}")
    private String rnrJobName;

    @Resource
    private Snowflake snowflake;

    @Resource
    private FileService fileService;

    @Resource
    private UnicomRnrBatchRecordsMapper unicomRnrBatchRecordsMapper;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private UnicomRnrQueryRecordsMapper unicomRnrQueryRecordsMapper;

    @Resource
    private SOTAService sotaService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private Redisson redisson;

    @Resource(name = "rnrBatchQueryThreadPool")
    private ThreadPoolTaskExecutor rnrBatchQueryThreadPool;

    @Resource
    private PermissionApi permissionApi;

    private static final String TOO_MANY_REQUEST_ERROR = "系统接口请求速率过快，请稍后重试";

    @Override
    public CommonResult<String> downloadUnicomRnrTemplate() {

        return CommonResult.success(unicomRnrTemplateExcelUrl);
    }

    @Override
    public CommonResult<UnicomRnrBatchRecordsVO> uploadRnrQueryExcel(MultipartFile multipartFile) {
        UnicomRnrBatchRecordsVO unicomRnrBatchRecordsVO = new UnicomRnrBatchRecordsVO();
        RLock lock = redisson.getLock(Constants.REDIS_KEY.RNR_BATCH_QUERY_KEY);
        try {
            if (!RLockUtil.tryLock(lock, 25, 60, TimeUnit.SECONDS)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            //检查查询状态
            if(checkTaskDealStatus()){
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            //校验上传的是否是空
            if (Objects.isNull(multipartFile)) {
                return CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_IS_EMPTY);
            }
            //读取文件解析
            byte[] readBytes;
            try {

                readBytes = IoUtil.readBytes(multipartFile.getInputStream());
            } catch (IOException e) {
                log.info("上传ICCID批量查询文件异常:{}", e.getMessage());
                return CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_UPLOAD_ERROR);
            }
            // excel格式校验
            if (!FileCheckUtil.isExcelFile(readBytes)) {
                return CommonResult.error(ErrorCodeConstants.RNR_QUERY_FILE_INVALID);
            }


            // 解析文件内容判断文件内容是不是空
            RnrQueryCheckListener rnrQueryListener = new RnrQueryCheckListener();
            try {
                EasyExcel.read(multipartFile.getInputStream(), RnrQueryExcel.class,
                        rnrQueryListener).sheet().doRead();
            } catch (Exception e) {
                log.info("读取ICCID批量查询文件异常:{}", e.getMessage());
                return CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_UPLOAD_ERROR);
            }

            if (Boolean.TRUE.equals(rnrQueryListener.getIsExcelFormatError())) {
                return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_FORMAT_ERROR);
            }
            //内容判断
            if (CollUtil.isEmpty(rnrQueryListener.getAllDataList())) {
                return CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_IS_EMPTY);
            }
            // 通过了校验 上传文件到S3
            String url = uploadRnrQueryExcelToS3(multipartFile);
            if(StringUtils.isEmpty(url)){
                return CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_UPLOAD_ERROR);
            }
            //记录批量查询记录表t_unicom_rnr_batch_records
            UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = new UnicomRnrBatchRecordsDO();
            unicomRnrBatchRecordsDO.setBatchNo(snowflake.nextId());
            unicomRnrBatchRecordsDO.setUploadFileS3Url(url);
            unicomRnrBatchRecordsDO.setOperator(WebFrameworkUtils.getLoginUserName());
            int insert = unicomRnrBatchRecordsMapper.insert(unicomRnrBatchRecordsDO);

            if(insert <= 0){
                log.info("文件上传失败,insert t_unicom_rnr_batch_records出错");
                return  CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_UPLOAD_ERROR);
            }
            unicomRnrBatchRecordsVO.setBeanName(rnrJobName);
            unicomRnrBatchRecordsVO.setBatchNo(unicomRnrBatchRecordsDO.getBatchNo());
        } catch (IORuntimeException e) {
            log.info("文件上传失败,insert t_unicom_rnr_batch_records出错");
            return CommonResult.error(ErrorCodeConstants.RNR_QUERY_EXCEL_UPLOAD_ERROR);
        } finally {
            RLockUtil.unlock(lock, 3);
        }
        return CommonResult.success(unicomRnrBatchRecordsVO);
    }

    private boolean checkTaskDealStatus() {
        //查询最近的一个批次号
        UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = unicomRnrBatchRecordsMapper.selectOne(new LambdaQueryWrapperX<UnicomRnrBatchRecordsDO>()
                .orderByDesc(BaseDO::getCreatedTime)
                .last(Constants.LIMIT_ONE)
                .eq(BaseDO::getIsDeleted, false));
        if(unicomRnrBatchRecordsDO != null){
            //统计是否还有查询中单数据，如果没有就返回false 正常往下执行逻辑
            UnicomRnrQueryRecordsDO unicomRnrQueryRecordsDO = unicomRnrQueryRecordsMapper.selectOne(new LambdaQueryWrapperX<UnicomRnrQueryRecordsDO>()
                    .eq(UnicomRnrQueryRecordsDO::getBatchNo, unicomRnrBatchRecordsDO.getBatchNo())
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(UnicomRnrQueryRecordsDO::getQueryStatus, UnicomRnrQueryStatusEnum.QUERYING.getType())
                    .last(Constants.LIMIT_ONE)
                    .select(UnicomRnrQueryRecordsDO::getId));
            return unicomRnrQueryRecordsDO != null;
        }
        return false;
    }

    /**
     * JOB入参：batch_no
     * executor-service JOB逻辑：通过job入参获取batch_no，触发subscription service接口调用
     * subscription service处理逻辑：根据batch_no查询t_unicom_rnr_batch_records表获取S3文件，
     * 下载文件解析文件内容，遍历处理每一行的VIN，
     * 优先查询ECP的t_pivi_package表中该VIN是否存在，
     * 是则获得其ICCID，若该表中不存在则通过SOTA的查询接口（注意其TPS=1只能单线程执行），
     * 获取该VIN对应的ICCID，得到List<VIN>，
     * 持久化到t_unicom_rnr_query_records表中，
     * 记录其batch_no、car_vin、query_status默认为1：查询中
     * @param batchNo
     * @return
     */
    @Override
    public Integer rnrBatchQueryParseJob(Long batchNo)  {
        UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = unicomRnrBatchRecordsMapper.selectOne(new LambdaQueryWrapperX<UnicomRnrBatchRecordsDO>()
                .eq(UnicomRnrBatchRecordsDO::getBatchNo, batchNo)
                .eq(BaseDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));

        String path = S3FileUtil.getUploadPathKey(unicomRnrBatchRecordsDO.getUploadFileS3Url());
        int total =0;
        try {
            byte[] content = fileService.getFileContent(Constants.FILE_CODE, path);
            List<List<RnrQueryExcel>> batchRnrExcelList = doReadRnrQueryExcel(content);
            // 使用 CompletableFuture 来异步处理每个 batchRnrExcels
            List<CompletableFuture<Integer>> futures = batchRnrExcelList.stream()
                    .map(batchRnrExcels -> CompletableFuture.supplyAsync(() ->
                        batchInsertRnrQueryRecords(batchRnrExcels, batchNo, unicomRnrBatchRecordsDO), rnrBatchQueryThreadPool)
                            .exceptionally(
                                    ex -> {
                                        log.info("batchInsertRnrQueryRecords失败", ex);
                                        return 0;
                                    }
                            ))
                    .collect(Collectors.toList());

            // 等待所有异步任务完成并汇总结果
            for (CompletableFuture<Integer> future : futures) {
                total += future.get();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.info("rnrBatchQueryParseJob获取文件失败 error：{}", e.getMessage());
        }
        log.info("rnrBatchQueryParseJob 处理解析文件条数 ：{}",total);

        return total;
    }

    @Override
    public PageResult<UnicomRnrBatchRecordListVO> getBatchFilePage(UnicomBatchFilePageDTO pageDTO) {
        log.info("pageDTO:{}", pageDTO);
        Page<UnicomRnrBatchRecordsDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<UnicomRnrBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnicomRnrBatchRecordsDO::getIsDeleted, false);
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }
        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(UnicomRnrBatchRecordsDO::getOperator, userName);
            log.info("联通ICCID批量查询文件分页查询,user:{}, pageDTO:{}", userName, pageDTO);
        }

        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(UnicomRnrBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(UnicomRnrBatchRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(UnicomRnrBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(UnicomRnrBatchRecordsDO::getId);
        }
        Page<UnicomRnrBatchRecordsDO> pageResult = unicomRnrBatchRecordsMapper.selectPage(pageParam, queryWrapper);

        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageResult<>();
        }
        List<UnicomRnrBatchRecordsDO> records = pageResult.getRecords();
        List<UnicomRnrBatchRecordListVO> pageListV0List = records.stream().map(vo->{
            UnicomRnrBatchRecordListVO unicomRnrBatchRecordListVO = new UnicomRnrBatchRecordListVO();
            unicomRnrBatchRecordListVO.setBatchNo(vo.getBatchNo());
            unicomRnrBatchRecordListVO.setState("文件上传成功");
            unicomRnrBatchRecordListVO.setOperateTime(
                    SubscribeTimeFormatUtil.timeToStringByFormat(vo.getCreatedTime(),
                    SubscribeTimeFormatUtil.FORMAT_2));
            unicomRnrBatchRecordListVO.setOperator(vo.getOperator());
            return unicomRnrBatchRecordListVO;
        }).collect(Collectors.toList());
        return new PageResult<>(pageListV0List, pageResult.getTotal());
    }

    @Override
    public String getUnicomQueryBatchNo(String batchNo) {
        log.info("根据批次号查询ICCID的批次号, batchNo:{}", batchNo);
        try {
            Long batchNoLong = Long.valueOf(batchNo);
            UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = queryUnicomBatchByBatchNo(batchNoLong);
            if (Objects.isNull(unicomRnrBatchRecordsDO)) {
                //查询单表
                UnicomRnrQueryRecordsDO unicomRnrQueryRecordsDO = queryUnciomByBatchNo(batchNoLong);
                if (Objects.isNull(unicomRnrQueryRecordsDO)) {
                    return null;
                }
                return String.valueOf(unicomRnrQueryRecordsDO.getBatchNo());
            }
            return String.valueOf(unicomRnrBatchRecordsDO.getBatchNo());
        } catch (Exception e) {
            log.info("根据批次号查询AMAP的批次号异常：{}", e.getMessage());
        }
        return null;
    }

    private UnicomRnrQueryRecordsDO queryUnciomByBatchNo(Long batchNoLong) {
        if (Objects.isNull(batchNoLong)) {
            return null;
        }
        LambdaQueryWrapperX<UnicomRnrQueryRecordsDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UnicomRnrQueryRecordsDO::getBatchNo, batchNoLong)
                .eq(UnicomRnrQueryRecordsDO::getIsDeleted, false);
        List<UnicomRnrQueryRecordsDO> resp = new ArrayList<>();
        try {
            resp = unicomRnrQueryRecordsMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据批次编号查询QUERY记录异常：{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询QUERY记录,数量大于1, batchNo:{}", batchNoLong);
        }
        return resp.get(0);
    }


    /**
     * 根据批次编号查询续费批次记录
     *
     * @param batchNo 批次编号，用于标识特定的续费批次
     * @return 返回查询到的续费批次记录，如果没有查询到，则返回null
     */
    private UnicomRnrBatchRecordsDO queryUnicomBatchByBatchNo(Long batchNo) {
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapperX<UnicomRnrBatchRecordsDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UnicomRnrBatchRecordsDO::getBatchNo, batchNo)
                .eq(UnicomRnrBatchRecordsDO::getIsDeleted, false);
        List<UnicomRnrBatchRecordsDO> resp = new ArrayList<>();
        try {
            resp = unicomRnrBatchRecordsMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据批次编号查询ICCID批次记录异常：{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询ICCID批次记录,数量大于1, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    private Integer batchInsertRnrQueryRecords(List<RnrQueryExcel> batchRnrExcels, Long batchNo,UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO) {
        List<UnicomRnrQueryRecordsDO> batchInsert = new ArrayList<>();
        for (RnrQueryExcel batchRnrExcel : batchRnrExcels) {
            //空的VIN跳过
            if(StringUtils.isNotBlank(batchRnrExcel.getCarVin())){
                UnicomRnrQueryRecordsDO rnrQueryRecordsDO = new UnicomRnrQueryRecordsDO();
                rnrQueryRecordsDO.setBatchNo(batchNo);
                rnrQueryRecordsDO.setCarVin(batchRnrExcel.getCarVin().toUpperCase());
                rnrQueryRecordsDO.setCreatedBy(unicomRnrBatchRecordsDO.getOperator());
                rnrQueryRecordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.QUERYING.getType());
                //校验VIN是否合法 VIN需由17位数字及字母组成
                if(!CarVinUtil.checkVinFormat(batchRnrExcel.getCarVin())){
                    rnrQueryRecordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
                    rnrQueryRecordsDO.setErrorDesc(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR.getMsg());
                }else {
                    PIVIPackageDO serviceDO =  piviPackageDOMapper.findICCIDByCarVin(batchRnrExcel.getCarVin());
                    if (serviceDO != null && !StringUtils.isEmpty(serviceDO.getIccid())) {
                        rnrQueryRecordsDO.setIccid(serviceDO.getIccid());
                    }
                }
                batchInsert.add(rnrQueryRecordsDO);
            }

        }

        if(CollUtil.isNotEmpty(batchInsert)){
            unicomRnrQueryRecordsMapper.insertBatch(batchInsert);
        }
        return batchInsert.size();
    }

    private List<List<RnrQueryExcel>> doReadRnrQueryExcel(byte[] content) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "RnrQueryExcel" + System.currentTimeMillis() + Constants.EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("读取ICCID批量查询Excel文件, filePath:{}", filePath);
        RnrQueryCheckListener checkListener =  new RnrQueryCheckListener();
        List<List<RnrQueryExcel>> resp = new ArrayList<>();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(content);
            outputStream.flush();
            EasyExcel.read(fileName, RnrQueryExcel.class, checkListener).sheet().doRead();
            resp = checkListener.getAllDataList();
            log.info("读取ICCID批量查询Excel文件, filePath:{}", filePath);
        } catch (Exception e) {
            log.info("ICCID批量查询Excel文件, 解析excel文件异常:{}", e.getMessage());
        } finally {
            FileUtil.del(fileName);
        }
        return resp;
    }

    private String uploadRnrQueryExcelToS3(MultipartFile multipartFile) {
        String uploadFilePath = "";
        try {
            String fileName = "RnrBatchQueryExcel" +  System.currentTimeMillis() +".xlsx";
            uploadFilePath = fileService.createFile(null, fileName, multipartFile.getBytes() , Constants.FILE_CODE);
            log.info("RNR批量查询文件上传到上s3, 出来结果的文件路径:{}", uploadFilePath);
        } catch (Exception e) {
            log.info("RNR批量查询文件上传到上s3,异常:{}", e.getMessage());
        }
        return uploadFilePath;
    }



    /**
     * 通过接口获得List<id>，遍历处理待查询的数据，调用联通的卡状态查询接口，获取其卡状态、实名状态记录到该records的DO里，
     * 如联通查询不到，记录状态为 3查询失败，记录其失败原因为 3：联通查询失败。
     * 所有数据全部处理完成后，
     * 批量更新该批次的处理记录。
     * @param ids
     * @return
     */
    @Override
    public Integer rnrBatchQueryExecutor(List<Long> ids) {
        // 查询
        List<UnicomRnrQueryRecordsDO> list = unicomRnrQueryRecordsMapper.selectList(new LambdaQueryWrapperX<UnicomRnrQueryRecordsDO>()
                .in(UnicomRnrQueryRecordsDO::getId, ids)
                .eq(BaseDO::getIsDeleted, false));

        // 成功查询数量
        int successNum = 0;
        for (UnicomRnrQueryRecordsDO recordsDO : list) {
            // 记录开始时间
            Instant start = Instant.now();
            //查不到去sota查询iccid 限流1s
            try {
                if (!buildRecord(recordsDO)) {
                    continue;
                }

                successNum++;
            }catch (TooManyRequestException tooManyRequestException){
                log.info("rnrBatchQueryExecutor tooManyRequestException：{}",tooManyRequestException.getMessage());
                recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
                recordsDO.setErrorDesc(TOO_MANY_REQUEST_ERROR);
            } catch (Exception e) {
                log.info("rnrBatchQueryExecutor调用异常：{}",e.getMessage());
                buildErrorRecord(recordsDO);
            }
            // 计算并打印处理耗时
            Duration elapsed = Duration.between(start, Instant.now());
            log.info("Processing time for this partition: " + elapsed.toMillis() + " ms");
            if (elapsed.toMillis() < TimeUnit.SECONDS.toMillis(1)) {
                try {
                    // sleep 1 second - elapsed.toMillis()
                    Thread.sleep(1000L-elapsed.toMillis());
                } catch (InterruptedException e) {
                    log.info("rnrBatchQueryExecutor sleep error：{}" , e.getMessage());
                    Thread.currentThread().interrupt();
                }
            }
        }

        if(CollUtil.isNotEmpty(list)){
            unicomRnrQueryRecordsMapper.updateBatch(list);
        }

        return successNum;
    }

    private static void buildErrorRecord(UnicomRnrQueryRecordsDO recordsDO) {
        if(StringUtils.isEmpty(recordsDO.getIccid())){
            recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
            recordsDO.setErrorDesc(ErrorCodeConstants.RNR_QUERY_ICCID_NOT_FOUND.getMsg());
        }else {
            recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
            recordsDO.setErrorDesc(ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
        }
    }

    /**
     * 设置ICCID号
     * 如果记录中的ICCID号为空，则尝试从SOTA系统查询ICCID号
     *
     * @param recordsDO 中国联通RNR查询记录对象
     * @return 如果成功设置了ICCID号，则返回true；否则返回false
     */
    private boolean buildRecord(UnicomRnrQueryRecordsDO recordsDO) {
        if(StringUtils.isEmpty(recordsDO.getIccid())) {
            SOTAResultVO sotaInfoByVin = sotaService.getSOTAInfoByVin(recordsDO.getCarVin());
            if (sotaInfoByVin == null || StringUtils.isEmpty(sotaInfoByVin.getEsimiccid())) {
                //iccid也没查到
                recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
                recordsDO.setErrorDesc(ErrorCodeConstants.RNR_QUERY_ICCID_NOT_FOUND.getMsg());
                return false;
            } else {
                recordsDO.setIccid(sotaInfoByVin.getEsimiccid());
            }
        }
        UnicomRespVO unicomRespVO = piviUnicomService.getSimCardInfo(recordsDO.getIccid());
        handleErrorResponse(unicomRespVO, recordsDO, recordsDO.getIccid());
        if (UnicomRnrQueryStatusEnum.FAILED.getType().equals(recordsDO.getQueryStatus())) {
            return false;
        }

        SimCardInfo simCardInfo = unicomRespVO.getUnicomRespData().getSimCardInfo();
        if (simCardInfo == null) {
            logInfo("联通查询返回数据包的卡信息为空", recordsDO.getIccid());
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return false;
        }

        if (StringUtils.isEmpty(simCardInfo.getRealnameFlag())) {
            logInfo("联通查询返回卡信息里面的实名信息为空", recordsDO.getIccid());
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return false;
        }

        handleRealNameTime(recordsDO, unicomRespVO,simCardInfo);

        recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.SUCCESS.getType());
        recordsDO.setIccid(simCardInfo.getIccid());
        recordsDO.setCardState(simCardInfo.getCardState());
        recordsDO.setRealNameFlag(Integer.parseInt(simCardInfo.getRealnameFlag()));
        return true;
    }

    private void handleRealNameTime(UnicomRnrQueryRecordsDO recordsDO, UnicomRespVO unicomRespVO,SimCardInfo simCardInfo) {
        List<UnicomRealNameOrder> realNameOrderList = unicomRespVO.getUnicomRespData().getRealNameOrderList();
        if(UnicomRealnameFlagEnum.TRUE.getCode().equals(simCardInfo.getRealnameFlag()) && CollUtil.isNotEmpty(realNameOrderList)){
            //过滤realNameOrderList时间倒叙排，取值状态为active的第一条
            UnicomRealNameOrder unicomRealNameOrder = realNameOrderList.stream().filter(vo -> UnicomBookStatusEnum.ACTIVE.getType().equals(vo.getOrderType())).max(Comparator.comparing(UnicomRealNameOrder::getOrderTime)).orElse(null);
            log.info("处理realname时间过滤内容unicomRealNameOrder:{}",unicomRealNameOrder);
            if(unicomRealNameOrder != null){
                //将unicomRealNameOrder.getOrderTime()时间戳字符串转换成yyyy/MM/dd HH:mm:ss格式的LocalDateTime
                recordsDO.setRealNameUpdateTime(TimeFormatUtil.stringToLocalDate(unicomRealNameOrder.getOrderTime(),TimeFormatUtil.formatter_4));
            }
        }

    }

    private void handleErrorResponse(UnicomRespVO unicomRespVO, UnicomRnrQueryRecordsDO recordsDO, String iccid) {
        if (unicomRespVO == null) {
            logInfo("查询到联通结果为空", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
        }else if (!UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc())) {
            logInfo("联通查询失败", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
        }else if (CharSequenceUtil.isNotBlank(unicomRespVO.getQueryResult())) {
            logWarn("ICCID批量查询联通异常", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
        }else if (unicomRespVO.getUnicomRespData() == null) {
            logInfo("联通查询返回数据包为空", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
        }
    }

    private void setFailedStatus(UnicomRnrQueryRecordsDO recordsDO, String errorDesc) {
        recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
        recordsDO.setErrorDesc(errorDesc);
    }

    private void logInfo(String message, String iccid) {
        log.info("{}，iccid: {}", message, iccid);
    }

    private void logWarn(String message, String iccid) {
        log.warn("{}，iccid: {}", message, iccid);
    }




}