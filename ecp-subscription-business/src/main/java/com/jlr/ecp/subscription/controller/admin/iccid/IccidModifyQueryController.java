package com.jlr.ecp.subscription.controller.admin.iccid;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidModifyQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidModifyQueryPageVO;
import com.jlr.ecp.subscription.service.iccid.IccidModifyQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "ICCID修改结果")
@RequestMapping("/iccid/modify/query")
@Validated
public class IccidModifyQueryController {

    @Resource
    private IccidModifyQueryService iccidModifyQueryService;

    @PostMapping("/queryModifyRecord/pageList")
    @Operation(summary = "分页查询 修改结果")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-modify-result:forms')")
    public CommonResult<PageResult<IccidModifyQueryPageVO>> query(@RequestBody @Valid IccidModifyQueryPageDTO pageDTO) {
        PageResult<IccidModifyQueryPageVO> resp = iccidModifyQueryService.queryModifyResultPageList(pageDTO);
        return CommonResult.success(resp);
    }

    @GetMapping("/queryBatchNo")
    @Operation(summary = "查询续费编号")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-modify-result:forms')")
    public CommonResult<String> queryModifyBatchNo(@RequestParam("batchNo") String batchNo) {
        return CommonResult.success(iccidModifyQueryService.queryModifyBatchNo(batchNo));
    }
}