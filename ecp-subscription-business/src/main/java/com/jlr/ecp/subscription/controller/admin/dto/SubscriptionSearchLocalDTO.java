package com.jlr.ecp.subscription.controller.admin.dto;

import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "过期服务查询出参")
public class SubscriptionSearchLocalDTO {

    @Schema(description = "PIVI车机属性 'PIVI'标识是，否则不是")
    private String carSystemModel;


    @Schema(description = "服务数据集合")
    private List<ServiceExpireInfoVO> serviceList;

    @Schema(description = "车辆的5000号信息及品牌 对象")
    private Vehicle5000InfoDTO vehicle5000Info;
}
