package com.jlr.ecp.subscription.dal.dataobject.fufilment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@TableName(value = "t_vcs_order_fufilment_call")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsOrderFufilmentCall extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 履约号ID; 雪花算法
     */
    @TableField(value = "fufilment_id")
    private String fufilmentId;

    /**
     *  服务包编码
     * */
    @TableField(value = "service_package")
    private String servicePackage;

    /**
     *  服务名
     * */
    @TableField(value = "service_name")
    private String serviceName;

    /**
     *  请求参数JSON
     * */
    @TableField(value = "request_param")
    private String requestParam;

    /**
     *   请求响应JSON
     * */
    @TableField(value = "request_result")
    private String requestResult;

    /**
     *  激活接口调用状态; 0：激活接口调用失败；1：激活接口调用成功；2：激活接口调用异步
     * */
    @TableField(value = "activation_status")
    private Integer activationStatus;

    /**
     *  接口调用失败MSG
     * */
    @TableField(value = "activation_failed_msg")
    private String activationFailedMsg;

    /**
     *  租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;

}
