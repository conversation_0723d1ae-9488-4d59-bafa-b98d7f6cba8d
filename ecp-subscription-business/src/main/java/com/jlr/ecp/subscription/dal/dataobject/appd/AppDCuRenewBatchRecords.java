package com.jlr.ecp.subscription.dal.dataobject.appd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_appdcu_renew_batch_records")
public class AppDCuRenewBatchRecords extends BaseDO {

    // 主键
    @TableId(value = "id")
    private Long id;

    // 批次处理号；雪花算法ID
    @TableField("batch_no")
    private Long batchNo;

    // 上传原始文件；S3文件URL
    @TableField("upload_file")
    private String uploadFile;

    // 校验结果；0：不通过 1：通过
    @TableField("verify_result")
    private Integer verifyResult;

    // 处理状态；0：待处理 1：处理中 2：已处理
    @TableField("deal_status")
    private Integer dealStatus;

    // 对于失败的校验结果S3文件URL
    @TableField("verify_result_file")
    private String verifyResultFile;

    // 操作人账号
    @TableField("operator")
    private String operator;

    // 租户号
    @TableField("tenant_id")
    private Integer tenantId;
}
