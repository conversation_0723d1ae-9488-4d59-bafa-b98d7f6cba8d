package com.jlr.ecp.subscription.enums.manuallog;

import com.jlr.ecp.subscription.enums.unicom.UnicomRnrQueryStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  1：激活服务 2：关闭服务
 */
@Getter
@AllArgsConstructor
public enum ManualModifyLogTypeEnum {

    /**
     * 修改类型;修改类型 1：在线导航续费 2：信息娱乐续费 3：网络流量续费 4：修改ICCID 5：修改发票日期 6:手动补录VIN 7:远程车控续费
     */
    AMAP_RENEW(1, "在线导航续费"),
    APPD_RENEW(2, "信息娱乐续费"),
    UNICOM_RENEW(3, "网络流量续费"),
    ICCID_UPDATED(4, "修改ICCID"),
    INVOICE_DATE_UPDATED(5, "修改发票日期"),
    MANUAL_VIN_ADDED(6, "手动补录VIN"),
    REMOTE_RENEW(7, "远程车控续费");

    private final Integer type;

    private final String desc;


    public static String getDescriptionByType(Integer type) {
        // 或者返回一个默认描述，如 "未知"
        if (type == null) {
            return "-";
        }

        for (ManualModifyLogTypeEnum status : ManualModifyLogTypeEnum.values()) {
            if (status.getType().equals(type)) {
                return status.getDesc();
            }
        }
        return type.toString();
    }
}
