//package com.jlr.ecp.subscription.controller.admin.amap;
//
//import com.jlr.ecp.framework.common.pojo.CommonResult;
//import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
//import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchResponseDTO;
//import com.jlr.ecp.subscription.model.dto.AmaPOrderChargeResponseDTO;
//import com.jlr.ecp.subscription.model.dto.AmaPOrderChargerRequestDTO;
//import com.jlr.ecp.subscription.properties.AmaPProperties;
//import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.annotation.security.PermitAll;
//
//@RestController
//@Tag(name = "amap-uat压测")
//@RequestMapping("/amap/stress/test")
//public class AmaPStressTestController {
//
//    @Resource
//    private PIVIAmaPService amaPService;
//
//    @Resource
//    private AmaPProperties amapProperties;
//
//    @PostMapping("/charge")
//    @Operation(summary = "amap充值")
//    @PermitAll
//    public CommonResult<AmaPOrderChargeResponseDTO> chargeAmaPOrder(@RequestBody AmaPOrderChargerRequestDTO requestDTO) {
//        return CommonResult.success(amaPService.chargeAmaPOrder(requestDTO));
//    }
//
//    @GetMapping("/queryAmaPChargeInfo")
//    @Operation(summary = "查询amap充值结果")
//    @PermitAll
//    public CommonResult<AmaPChargeSearchResponseDTO> queryAmaPChargeInfo(@RequestParam(value = "cusOrderId") String cusOrderId) {
//        return CommonResult.success(amaPService.queryAmaPChargeInfo(cusOrderId));
//    }
//
//    @GetMapping("/getCarInfo")
//    @Operation(summary = "查询vin到期时间")
//    @PermitAll
//    public CommonResult<AmaPCarInfoResponse> getCarAmaPInfo(@RequestParam(value = "vin") String vin) {
//        return CommonResult.success(amaPService.getCarAmaPInfo(amapProperties.getPid(), vin));
//    }
//}
