package com.jlr.ecp.subscription.api.cdp;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResponseVO;
import com.jlr.ecp.subscription.config.CdpService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class CdpApiImpl implements CdpAPI {

    @Resource
    private CdpService cdpService;

    @Override
    public CommonResult<List<CdpResponseVO>> getCdpMobile(List<String> vinList) {

        return CommonResult.success(cdpService.callApiNew(vinList));
    }
}
