package com.jlr.ecp.subscription.controller.admin.dto.iccid;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "ICCID修改记录 查询pageDTO")
public class IccidModifyQueryPageDTO extends PageParam {
    @Schema(description = "车辆编号")
    private String carVin;

    @Schema(description = "批次号")
    private List<String> batchNoList;

    @Schema(description = "修改状态;修改状态：1：进行中 2：修改成功 3：修改失败")
    private Integer modifyStatus;

    @Schema(description = "操作开始时间")
    private String operateStartTime;

    @Schema(description = "操作结束时间")
    private String operateEndTime;

    @Schema(description = "操作人员")
    private String operator;

    @Schema(description = "操作时间排序 asc:正序 desc:倒叙")
    private String operateTimeSort;
}