package com.jlr.ecp.subscription.dal.dataobject.incontrol;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * t_incontrol_customer(t_incontrol_customer)实体类
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2023-12-20 14:42:17
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_incontrol_customer")
public class IncontrolCustomerDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * incontrol账号;incontrol账号，唯一索引
     */
    @TableField(value = "incontrol_id")
    private String incontrolId;
    /**
     * TSDP侧的用户id
     */
    @TableField(value = "userid")
    private String userid;
    /**
     * 姓
     */
    @TableField(value = "firstName")
    private String firstName;
    /**
     * 名
     */
    @TableField(value = "surname")
    private String surname;
    /**
     * 加密手机号
     */
    @TableField(value = "phone_encrypt")
    private String phoneEncrypt;
    /**
     * 用户来源;1主动登录 2过期服务查询
     */
    @TableField(value = "source")
    private Integer source;
    /**
     * 上次登录时间
     */
    @TableField(value = "last_login_time")
    private LocalDateTime lastLoginTime;

}