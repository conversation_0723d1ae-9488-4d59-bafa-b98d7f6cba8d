package com.jlr.ecp.subscription.dal.dataobject.search;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;


@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_expire_search_detail_record")
public class ExpireSearchDetailRecordDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 批次处理号；批次处理号，雪花算法ID
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     *  处理状态 0：查询中 1：查询完成 2：查询失败
     * */
    @TableField("query_status")
    private Integer queryStatus;

    /**
     *  品牌名称
     * */
    @TableField(value = "brand_name")
    private String brandName;

    /**
     * 配置名称可能很长
     * */
    @TableField(value = "config_name")
    private String configName;

    /**
     *  型号年款
     * */
    @TableField(value = "model_year")
    private String modelYear;

    /**
     *  配置编码
     * */
    @TableField(value = "config_code")
    private String configCode;

    /**
     *  系列编码
     * */
    @TableField(value = "series_code")
    private String seriesCode;

    /**
     * 品牌编码
     * */
    @TableField(value = "brand_code")
    private String brandCode;

    /**
     *  系列名称
     * */
    @TableField(value = "series_name")
    private String seriesName;

    /**
     *  PIVI车机属性 'PIVI'标识是，否则不是
     * */
    @TableField(value = "carSystemModel")
    private String carSystemModel;

    /**
     * 是否具备信息娱乐服务相关配置 N 否 Y 是
     * */
    @TableField(value = "has_info_entertain")
    private String hasInfoEntertain;

    /**
     *  InControl远程车控服务
     * */
    @TableField(value = "remote_service_date")
    private String remoteServiceDate;

    /**
     *  InControl在线服务
     * */
    @TableField(value = "pivi_service_date")
    private String piviServiceDate;

    /**
     *  信息娱乐服务
     * */
    @TableField(value = "appd_service_date")
    private String appDServiceDate;

    /**
     *  实时交通信息
     * */
    @TableField(value = "amap_service_date")
    private String amaPServiceDate;

    /**
     *  网络流量
     * */
    @TableField(value = "unicom_service_date")
    private String unicomServiceDate;

    /**
     *  车辆发票日期
     * */
    @TableField(value = "invoice_service_date")
    private String invoiceServiceDate;

    /**
     *  操作时间
     * */
    @TableField(value = "operate_time")
    private LocalDateTime operateTime;

    /**
     * 操作人账号；操作人账号
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 失败原因
     * */
    @TableField(value = "error_reason")
    private String errorReason;

    @TableField("tenant_id")
    private Integer tenantId;
}
