package com.jlr.ecp.subscription.enums.appd;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AppDRenewStatusEnum {
    WAIT_RENEW(0, "待续费", "进行中"),
    RENEW_SUCCESS(2, "续费成功", "成功"),
    RENEW_FAIL(3, "续费失败", "失败"),
    RENEW_PROGRESS(4, "续费中", "进行中");

    private final Integer status;

    private final String desc;

    private final String display;

    /**
     * 根据状态码获取描述
     *
     * @param status 状态码，类型为Integer
     * @return 返回与状态码对应的状态描述如果找不到匹配的状态码，则返回空字符串
     */
    public static String getDescByStatus(Integer status) {
        for (AppDRenewStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据状态码获取对应的显示文本
     *
     * @param status 状态码，代表不同的续订状态
     * @return 返回与状态码对应的显示文本，如果没有找到对应的状态，则返回空字符串
     */
    public static String getDisplayByStatus(Integer status) {
        for (AppDRenewStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum.getDisplay();
            }
        }
        return "";
    }
}
