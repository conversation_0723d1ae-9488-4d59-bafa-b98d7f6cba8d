package com.jlr.ecp.subscription.service.vin.expiry;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.report.api.ReportApi;
import com.jlr.ecp.report.api.expiryvin.dto.GenerateReportRequest;
import com.jlr.ecp.subscription.api.unicom.dto.GenerateReportRequestV2;
import com.jlr.ecp.subscription.api.unicom.dto.MonthlyRnrBatchQueryDTO;
import com.jlr.ecp.subscription.api.unicom.vo.InitResponse;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.SimCardInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.expiryvin.VinExpiryMonthlyRecordDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.vin.expiry.VinExpiryMonthlyDetailDO;
import com.jlr.ecp.subscription.dal.dataobject.vin.expiry.VinExpiryReportDataDO;
import com.jlr.ecp.subscription.dal.mysql.expiryvin.VinExpiryMonthlyRecordDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.vehicle.VinExpiryReportDataDOMapper;
import com.jlr.ecp.subscription.dal.mysql.vin.expiry.VinExpiryMonthlyDetailMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fufil.VinExpiryExcelServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.VinExpiryServiceTypeEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRealnameFlagEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRnrQueryStatusEnum;
import com.jlr.ecp.subscription.enums.vin.expriy.ServiceTypeEnum;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.vin.expiry.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;

@Service
@Slf4j
public class VinExpiryMonthlyDetailServiceImpl extends ServiceImpl<VinExpiryMonthlyDetailMapper, VinExpiryMonthlyDetailDO> implements VinExpiryMonthlyDetailService {

    @Resource
    private PIVIUnicomService piviUnicomService;

    private static final String TOO_MANY_REQUEST_ERROR = "系统接口请求速率过快，请稍后重试";

    @Resource
    private Snowflake ecpIdUtil;

    @Resource
    private VinExpiryMonthlyRecordDOMapper vinExpiryMonthlyRecordDOMapper;

    @Resource
    PIVIPackageDOMapper piviPackageDOMapper;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleMapper;

    @Resource
    private VinExpiryReportDataDOMapper vinExpiryReportDataDOMapper;

    @Resource
    private ReportApi reportApi;

    //"yyyy-MM-dd"
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final int MAX_SEARCH_COUNT_FOR_MOM = 10000;

    private static final long LAST_FLAG = -1;
    // 定义常量
    private static final String PRODUCTION_EN_LOCAL = "LOCAL";
    private static final String DOMESTIC = "国产";
    private static final String IMPORTED = "进口";

    // 封装逻辑到私有方法
    private String determineProductionType(String productionEn) {
        if (StringUtils.isBlank(productionEn)) {
            return productionEn;
        }
        return PRODUCTION_EN_LOCAL.equals(productionEn) ? DOMESTIC : IMPORTED;
    }

    /**
     * 根据jobid查询t_vin_expiry_monthly_detail 已经初始化的总数subCount
     */
    @Override
    public Integer getInitializedCount(Long jobId) {
        log.info("getInitializedCount方法开始, jobId:{}", jobId);

        // 使用MyBatis-Plus的QueryWrapper构建查询条件
        LambdaQueryWrapperX<VinExpiryMonthlyDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(VinExpiryMonthlyDetailDO::getJobId, jobId)  // 根据job_id过滤
                .eq(VinExpiryMonthlyDetailDO::getQueryStatus, UnicomRnrQueryStatusEnum.QUERYING.getType())
                .eq(VinExpiryMonthlyDetailDO::getIsDeleted, 0); // 过滤未删除的记录

        // 调用BaseMapper的selectCount方法统计符合条件的记录数
        return Math.toIntExact(baseMapper.selectCount(queryWrapper));
    }

    /**
     * 插入续费率report数据
     *
     * @param jobId 任务ID，用于标识需要处理的报告数据
     * @return 插入数据的结果，返回影响的记录数
     */
    @Override
    public Integer getInsertReportData(Long jobId) {
        log.info("插入续费率report数据, jobId:{}", jobId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int remoteRes = vinExpiryReportDataDOMapper.insertRemoteDataFromDetail(jobId);
        int nonRemoteRes = vinExpiryReportDataDOMapper.insertNonRemoteDataFromDetail(jobId);
        int res = remoteRes + nonRemoteRes;
        stopWatch.stop();
        log.info("插入续费率report数据, remoteRes：{}, nonRemoteRes:{}, 总数量：{}, 耗时:{}ms", remoteRes, nonRemoteRes,
                res, stopWatch.getTotalTimeMillis());
        return res;
    }

    /**
     * 1.同步落库 初始化t_vin_expiry_monthly_records（report_s3_file暂不填入）
     * 2.a.查询下月到期vin的List
     * b.将list处理成excelMergedList（vin的remote到期日和pivi到期日为同一天的、serviceType标记为“Both”，不同日期生成两条记录 serviceType分别为"Remote"、"Subscription"）
     * c.InitResponse保存excelMergedList.size()
     * 3.异步落库 初始化t_vin_expiry_monthly_detail （query_status为1：查询中）
     * 4.返回jobid 和 处理好的excelMergedList.size()
     */
    @Override
    public InitResponse initializeRecords(GenerateReportRequestV2 request) {
        log.info("initializeRecords, request:{}", JSON.toJSONString(request));
        LocalDateTime beginDate = DateUtil.parseLocalDateTime(request.getBeginDate().format(DateTimeFormatter.ISO_LOCAL_DATE), DATE_FORMAT_YYYY_MM_DD);
        LocalDateTime endDate = DateUtil.parseLocalDateTime(request.getEndDate().format(DateTimeFormatter.ISO_LOCAL_DATE), DATE_FORMAT_YYYY_MM_DD).plusDays(1);
        log.info("beginDate:{}, endDate:{}", beginDate, endDate);
        InitResponse initResponse = new InitResponse();

        // 1.同步落库 初始化t_vin_expiry_monthly_records（report_s3_file暂不填入）
        request.setJobId(ecpIdUtil.nextId());
        log.info("同步落库 初始化t_vin_expiry_monthly_records（report_s3_file暂不填入），request:{} ", JSON.toJSONString(request));
        updateOrInsertExpiryMonthlyRecord(request);
        initResponse.setJobId(request.getJobId());

        //2.开始处理，去获取excelMergedList.size()
        log.info("开始处理，去获取excelMergedList.size()");
        StopWatch stopWatch = new StopWatch();

        //a.查询下月到期vin的List
        List<VinExpireServiceDTO> list = getOriginalVinExpireServiceDTOS(stopWatch, beginDate, endDate);
        if (CollUtil.isEmpty(list)) {
            log.info("getVinExpireServiceByDataRangeForAllNoPage 查询下月到期vin 数据为空，beginDate:{}, endDate:{}", beginDate, endDate);
            initResponse.setTotalCount(0);
            return initResponse;
        }

        //b.将list处理成excelMergedList（vin的remote到期日和pivi到期日为同一天的、serviceType标记为“Both”，不同日期生成两条记录 serviceType分别为"Remote"、"Subscription"）
        // 按VIN分组，合并同一VIN的Remote和Subscription过期信息 和 iccid信息
        Map<String, VinExpireServiceDTO> mergedDataMap = getMergedDataMap(list);
//        log.info("mergedDataMap处理后的数据的数量：{}, mergedDataMap处理后的数据：{}", mergedDataMap.size(), JSON.toJSONString(mergedDataMap));
        log.info("mergedDataMap处理后的数据的数量：{}", mergedDataMap.size());

        // 组装excelMergedList, vin的remote到期日和pivi到期日为同一天的、serviceType标记为“Both”，不同日期生成两条记录 serviceType分别为"Remote"、"Subscription"
        ArrayList<VinExpireServiceDTO> excelMergedList = getExcelMergedList(list, mergedDataMap);
//        log.info("excelMergedList处理后的数据的数量：{}, excelMergedList处理后的数据：{}", excelMergedList.size(), JSON.toJSONString(excelMergedList));
        log.info("excelMergedList处理后的数据的数量：{}", excelMergedList.size());

        //c.InitResponse保存excelMergedList.size()
        long dataCount = excelMergedList.size();
        initResponse.setTotalCount((int) dataCount);

        // 3.异步落库 初始化t_vin_expiry_monthly_detail （query_status为1：查询中）
        CompletableFuture.runAsync(() -> {
            log.info("异步落库 初始化t_vin_expiry_monthly_detail （query_status为1：查询中）");
            savePageVinExpiryMonthlyDetail(excelMergedList, request, dataCount, beginDate, endDate);
        }, subscribeAsyncThreadPool).exceptionally(ex -> {
            log.info("异步落库 初始化t_vin_expiry_monthly_detail 异常", ex);
            return null;
        });

        // 4.返回jobid 和 下月到期vin的总数count
        return initResponse;
    }

    private @NotNull ArrayList<VinExpireServiceDTO> getExcelMergedList(List<VinExpireServiceDTO> list, Map<String, VinExpireServiceDTO> mergedDataMap) {
        ArrayList<VinExpireServiceDTO> excelMergedList = new ArrayList<>(list.size());
        mergedDataMap.forEach((carVin, serviceDTO) -> {
            // 处理ICCID，服务类型和过期日期
            String iccid = serviceDTO.getIccid();
            LocalDateTime remoteDate = serviceDTO.getRemoteExpireDate();
            LocalDateTime piviDate = serviceDTO.getPiviExpireDate();

            if (remoteDate != null && piviDate != null) {
                LocalDateTime localDateTime = remoteDate.isBefore(piviDate) ? remoteDate : piviDate;
                // remoteDate和piviDate取月份进行比较是否时间相等
                if (remoteDate.toLocalDate().getMonth().equals(piviDate.toLocalDate().getMonth())) {
                    excelMergedList.add(createMergedRow(carVin, ServiceTypeEnum.BOTH.getValue(), localDateTime, iccid));
                } else {
                    // 添加两条记录
                    excelMergedList.add(createMergedRow(carVin, ServiceTypeEnum.REMOTE.getValue(), remoteDate, iccid));
                    excelMergedList.add(createMergedRow(carVin, ServiceTypeEnum.SUBSCRIPTION.getValue(), piviDate, iccid));
                }
            } else if (remoteDate != null) {
                excelMergedList.add(createMergedRow(carVin, ServiceTypeEnum.REMOTE.getValue(), remoteDate, iccid));
            } else if (piviDate != null) {
                excelMergedList.add(createMergedRow(carVin, ServiceTypeEnum.SUBSCRIPTION.getValue(), piviDate, iccid));
            }
        });
        return excelMergedList;
    }

    private static @NotNull Map<String, VinExpireServiceDTO> getMergedDataMap(List<VinExpireServiceDTO> list) {
        Map<String, VinExpireServiceDTO> mergedData = list.stream()
                .collect(Collectors.toMap(
                        VinExpireServiceDTO::getCarVin,
                        dto -> dto,
                        (existing, replacement) -> {
                            // 合并同一VIN的Remote和Subscription日期
                            if (existing.getRemoteExpireDate() == null) {
                                existing.setRemoteExpireDate(replacement.getRemoteExpireDate());
                            }
                            if (existing.getPiviExpireDate() == null) {
                                existing.setPiviExpireDate(replacement.getPiviExpireDate());
                            }
                            if (existing.getIccid() == null) {
                                existing.setIccid(replacement.getIccid());
                            }
                            return existing;
                        }
                ));
        return mergedData;
    }

    private @NotNull List<VinExpireServiceDTO> getOriginalVinExpireServiceDTOS(StopWatch stopWatch, LocalDateTime beginDate, LocalDateTime endDate) {
        stopWatch.start();
        List<VinExpireServiceDTO> list = piviPackageDOMapper.getVinExpireServiceByDataRangeForAllNoPage(beginDate, endDate);
        long originalDataCount = list.size();
        stopWatch.stop();
//        log.info("getVinExpireServiceByDataRangeForAllNoPage 查询下月到期vin的总数花费时间：{}s, originalDataCount:{}, 得到的list:{}",
//                stopWatch.getTotalTimeSeconds(), originalDataCount, JSON.toJSONString(list));
        log.info("getVinExpireServiceByDataRangeForAllNoPage 查询下月到期vin的总数花费时间：{}s, originalDataCount:{}",
                stopWatch.getTotalTimeSeconds(), originalDataCount);
        return list;
    }

    private void savePageVinExpiryMonthlyDetail(ArrayList<VinExpireServiceDTO> excelMergedList, GenerateReportRequestV2 request, long dataCount, LocalDateTime beginDate, LocalDateTime endDate) {
        log.info("savePageVinExpiryMonthlyDetail, request:{}, dataCount:{}, beginDate:{}, endDate:{}", JSON.toJSONString(request), dataCount, beginDate, endDate);

        if (CollUtil.isEmpty(excelMergedList)) {
            log.info("savePageVinExpiryMonthlyDetail,excelMergedList为空，无需保存");
            return;
        }

        //excelMergedList 按过期日期降序排序（最新过期的排前面）
        excelMergedList.sort(Comparator.comparing(VinExpireServiceDTO::getExpiryDate).reversed());

        // 分批处理
        Lists.partition(excelMergedList, MAX_SEARCH_COUNT_FOR_MOM).forEach(batch -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            // 组装excel数据
            ArrayList<VinExpiryMonthlyDetailDO> dataList = new ArrayList<>(batch.size());
            ArrayList<VinExpiryReportDataDO> vinExpiryReportList = new ArrayList<>(batch.size());
            // 处理单个批次
            getData(batch, beginDate, endDate, request,dataList,vinExpiryReportList);
//            log.info("savePageVinExpiryMonthlyDetail, 分批处理后 getData 大小：{}, getData数据:{}", dataList.size(), JSON.toJSONString(dataList));
            log.info("savePageVinExpiryMonthlyDetail, 分批处理后 getData 大小：{}", dataList.size());
            if (CollUtil.isNotEmpty(dataList)) {
                log.info("savePageVinExpiryMonthlyDetail 分批处理后 getData 为空");
                saveBatch(dataList);

            }
//            saveVinExpiryReportDataDO(vinExpiryReportList);

            stopWatch.stop();
            log.info("处理批次完成，批次大小：{}，耗时：{}ms", batch.size(), stopWatch.getTotalTimeMillis());
        });
    }

    public void getData(List<VinExpireServiceDTO> subExcelMergedList, LocalDateTime beginDate,
                        LocalDateTime endDate, GenerateReportRequestV2 request,
                        ArrayList<VinExpiryMonthlyDetailDO> excelList,ArrayList<VinExpiryReportDataDO> vinExpiryReportList) {
//        log.info("getData, beginDate:{}, endDate:{}, request:{}, subExcelMergedList:{}", beginDate, endDate, request, JSON.toJSONString(subExcelMergedList));
        log.info("getData, beginDate:{}, endDate:{}, request:{}", beginDate, endDate, request);
        StopWatch stopWatch = new StopWatch();
        if (CollUtil.isEmpty(subExcelMergedList)) {
            return ;
        }
        // 查车机信息
        stopWatch.start();
        List<IncontrolVehicleDTO> vehicles = incontrolVehicleMapper.getReportOrds(convertList(subExcelMergedList, e -> e.getCarVin()));
        stopWatch.stop();
        log.info("异步落库 初始化t_vin_expiry_monthly_detail, 到期VIN导出之车机表查询耗时：{}s", stopWatch.getTotalTimeSeconds());
        // vehicles 转换为map，key为 carVin
        Map<String, IncontrolVehicleDTO> vehicleMap = vehicles.stream().collect(Collectors.toMap(e -> e.getCarVin(), e -> e, (p1, p2) -> p2));


        for (VinExpireServiceDTO vinExpireServiceDTO : subExcelMergedList) {
            VinExpiryMonthlyDetailDO vinExpiryMonthlyDetailDO = getVinExpiryMonthlyDetailDO(request, vinExpiryReportList, vehicleMap, vinExpireServiceDTO);
            excelList.add(vinExpiryMonthlyDetailDO);
        }

    }

    @NotNull
    private  VinExpiryMonthlyDetailDO getVinExpiryMonthlyDetailDO(GenerateReportRequestV2 request, ArrayList<VinExpiryReportDataDO> vinExpiryReportList, Map<String, IncontrolVehicleDTO> vehicleMap, VinExpireServiceDTO vinExpireServiceDTO) {
        String carVin = vinExpireServiceDTO.getCarVin();

        VinExpiryMonthlyDetailDO vinExpiryMonthlyDetailDO = new VinExpiryMonthlyDetailDO();
        VinExpiryReportDataDO vinExpiryReportDataDO = new VinExpiryReportDataDO();

        //处理PIVI车机
        handleCarSystemModel(vehicleMap, carVin, vinExpiryMonthlyDetailDO);

        vinExpiryMonthlyDetailDO.setJobId(request.getJobId());
        vinExpiryMonthlyDetailDO.setJobDate(request.getJobDate());
        vinExpiryMonthlyDetailDO.setQueryStatus(UnicomRnrQueryStatusEnum.QUERYING.getType());

        vinExpiryMonthlyDetailDO.setIccid(vinExpireServiceDTO.getIccid());
        vinExpiryMonthlyDetailDO.setCarVin(carVin);

        vinExpiryMonthlyDetailDO.setServiceType(vinExpireServiceDTO.getServiceType());
        vinExpiryMonthlyDetailDO.setExpiryDate(vinExpireServiceDTO.getExpiryDate());


        if (vehicleMap.containsKey(carVin)) {
            IncontrolVehicleDTO vehicleInfo = vehicleMap.getOrDefault(vinExpireServiceDTO.getCarVin(), new IncontrolVehicleDTO());
            vinExpiryMonthlyDetailDO.setBrand(vehicleInfo.getBrandName());
            vinExpiryMonthlyDetailDO.setSeriesName(vehicleInfo.getSeriesName());
            vinExpiryMonthlyDetailDO.setImportedEn(determineProductionType(vehicleInfo.getProductionEn()));
            if(VinExpiryExcelServiceTypeEnum.REMOTE.getType().equals(vinExpireServiceDTO.getServiceType())){
                //只添加remote服务类型的，因为不需要实名查询 先入库
                vinExpiryReportDataDO.setCarVin(carVin);
                vinExpiryReportDataDO.setServiceType(vinExpireServiceDTO.getServiceType());
                vinExpiryReportDataDO.setExpiryDate(vinExpireServiceDTO.getExpiryDate());
                vinExpiryReportDataDO.setBrand(vehicleInfo.getBrandName());
                vinExpiryReportDataDO.setSeriesName(vehicleInfo.getSeriesName());
                vinExpiryReportDataDO.setImportedEn(determineProductionType(vehicleInfo.getProductionEn()));
                vinExpiryReportList.add(vinExpiryReportDataDO);
            }
        }
        return vinExpiryMonthlyDetailDO;
    }

    /**
     * 处理车辆是否为PIVI车机
     *
     * @param vehicleMap 缓存车辆信息
     * @param carVin  vin
     * @param vinExpiryMonthlyDetailDO vinExpiryMonthlyDetailDO
     */
    private void handleCarSystemModel(Map<String, IncontrolVehicleDTO> vehicleMap, String carVin, VinExpiryMonthlyDetailDO vinExpiryMonthlyDetailDO) {
        if (!vehicleMap.containsKey(carVin)) {
            // vin在veh表中 查不到数据，就查询pivi底表，如果有数据就设置为PIVI
            PIVIPackageDO piviPackageDO = piviPackageDOMapper.selectOnePiviDataByVin(carVin);
            if (piviPackageDO != null){
                vinExpiryMonthlyDetailDO.setCarSystemModel(CarSystemModelEnum.PIVI.getCode());
            }
        } else {
            // vin在vehicleMap 查到数据，根据vehicleMap.get(vin).carSystemModel设置carSystemModel（这里icr_vehicle中数据可能为 NULL or 'PIVI'）
            vinExpiryMonthlyDetailDO.setCarSystemModel(vehicleMap.get(carVin).getCarSystemModel());
        }
    }

    private VinExpireServiceDTO createMergedRow(String carVin, String serviceType, LocalDateTime expiryDate, String iccid) {
        VinExpireServiceDTO mergedRow = new VinExpireServiceDTO();
        mergedRow.setCarVin(carVin);
        mergedRow.setServiceType(serviceType);
        mergedRow.setExpiryDate(expiryDate);
        mergedRow.setIccid(iccid);
        return mergedRow;
    }

    private void updateOrInsertExpiryMonthlyRecord(GenerateReportRequestV2 request) {
        Integer jobMonth = request.getJobMonth();
        Integer jobYear = request.getJobYear();
        VinExpiryMonthlyRecordDO vinExpiryMonthlyRecordDO = vinExpiryMonthlyRecordDOMapper.selectByYearMonth(jobYear, jobMonth);
        log.info("根据月份和年份查询t_vin_expiry_monthly_records记录，结果：{}", JSON.toJSONString(vinExpiryMonthlyRecordDO));
        if (vinExpiryMonthlyRecordDO != null) {
            vinExpiryMonthlyRecordDO.setJobId(request.getJobId());
            vinExpiryMonthlyRecordDO.setJobDate(request.getJobDate());
            vinExpiryMonthlyRecordDO.setJobParam(request.getJobParam());
            vinExpiryMonthlyRecordDO.setBeginDate(request.getBeginDate());
            vinExpiryMonthlyRecordDO.setEndDate(request.getEndDate());
            vinExpiryMonthlyRecordDO.setJobMonth(jobMonth.toString());
            vinExpiryMonthlyRecordDO.setJobYear(jobYear.toString());
            vinExpiryMonthlyRecordDO.setReportS3File(null);
            vinExpiryMonthlyRecordDOMapper.updateById(vinExpiryMonthlyRecordDO);
            log.info("更新t_vin_expiry_monthly_records记录成功，结果：{}", JSON.toJSONString(vinExpiryMonthlyRecordDO));
        } else {
            VinExpiryMonthlyRecordDO newRecord = new VinExpiryMonthlyRecordDO(null, request.getJobId(), request.getJobDate(), request.getJobParam(), request.getBeginDate(), request.getEndDate(), jobMonth.toString(), jobYear.toString(), null);
            vinExpiryMonthlyRecordDOMapper.insert(newRecord);
            log.info("插入t_vin_expiry_monthly_records记录成功，结果：{}", JSON.toJSONString(newRecord));
        }
    }


    @Override
    public List<Long> selectQueryVinRecordsByJobId(Long jobId) {
        List<VinExpiryMonthlyDetailDO> list = baseMapper.selectList(new LambdaQueryWrapperX<VinExpiryMonthlyDetailDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(VinExpiryMonthlyDetailDO::getQueryStatus, UnicomRnrQueryStatusEnum.QUERYING.getType())
                .in(VinExpiryMonthlyDetailDO::getServiceType, VinExpiryServiceTypeEnum.BOTH, VinExpiryServiceTypeEnum.SUBSCRIPTION)
                .eq(VinExpiryMonthlyDetailDO::getJobId, jobId));
        return list.stream().map(VinExpiryMonthlyDetailDO::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer rnrBatchQueryExecutor(MonthlyRnrBatchQueryDTO monthlyRnrBatchQueryDTO) {
        log.info("VinExpiredRnrBatchQueryExecutor 入参:{}",JSON.toJSONString(monthlyRnrBatchQueryDTO));

        // 查询
        List<VinExpiryMonthlyDetailDO> list = baseMapper.selectList(new LambdaQueryWrapperX<VinExpiryMonthlyDetailDO>()
                .in(VinExpiryMonthlyDetailDO::getId, monthlyRnrBatchQueryDTO.getIdList())
                .eq(BaseDO::getIsDeleted, false));

        List<VinExpiryReportDataDO> vinExpiryReportDataList = new ArrayList<>(list.size());
        // 成功查询数量
        int successNum = 0;
        for (VinExpiryMonthlyDetailDO recordsDO : list) {
            // 记录开始时间
            Instant start = Instant.now();
            //查不到去sota查询iccid 限流1s
            try {
                if (!buildRecord(recordsDO,vinExpiryReportDataList)) {
                    continue;
                }
                successNum++;
            } catch (TooManyRequestException tooManyRequestException) {
                log.info("VinExpiredRnrBatchQueryExecutor tooManyRequestException：{}", tooManyRequestException.getMessage());
                setFailedStatus(recordsDO, TOO_MANY_REQUEST_ERROR);
            } catch (Exception e) {
                log.info("VinExpiredRnrBatchQueryExecutor调用异常：{}", e.getMessage());
                setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            }
            // 计算并打印处理耗时
            Duration elapsed = Duration.between(start, Instant.now());
            log.info("Processing time for this partition: " + elapsed.toMillis() + " ms");
            if (elapsed.toMillis() < TimeUnit.SECONDS.toMillis(1)) {
                try {
                    // sleep 1 second - elapsed.toMillis()
                    Thread.sleep(1000L - elapsed.toMillis());
                } catch (InterruptedException e) {
                    log.info("VinExpiredRnrBatchQueryExecutor sleep error：{}", e.getMessage());
                    Thread.currentThread().interrupt();
                }
            }
        }

        if (CollUtil.isNotEmpty(list)) {
            baseMapper.updateBatch(list);

        }

    //    saveVinExpiryReportDataDO(vinExpiryReportDataList);

        return successNum;
    }


//    private void saveVinExpiryReportDataDO(List<VinExpiryReportDataDO> vinExpiryReportDataList) {
//        try {
//            log.info("批量保存VinExpiryReportDataDO数据开始过滤vinExpiryReportDataList的size:{}",CollUtil.size(vinExpiryReportDataList));
//            //过滤掉brandCode为null的数据
//
//            vinExpiryReportDataList = vinExpiryReportDataList.stream()
//                    .filter(vinExpiryReportDataDO -> StringUtils.isNotBlank(vinExpiryReportDataDO.getBrand()))
//                    .collect(Collectors.toList());
//            log.info("批量保存VinExpiryReportDataDO数据过滤完成vinExpiryReportDataList的size:{}",CollUtil.size(vinExpiryReportDataList));
//            if (CollUtil.isNotEmpty(vinExpiryReportDataList)) {
//                vinExpiryReportDataDOMapper.insertBatch(vinExpiryReportDataList);
//            }
//        }catch (Exception e){
//            log.error("批量保存VinExpiryReportDataDO数据异常：{}", e);
//        }
//
//    }

    private boolean buildRecord(VinExpiryMonthlyDetailDO recordsDO,List<VinExpiryReportDataDO> vinExpiryReportDataList) {
        UnicomRespVO unicomRespVO = piviUnicomService.getSimCardInfo(recordsDO.getIccid());
        if (handleErrorResponse(unicomRespVO, recordsDO, recordsDO.getIccid())) {
            return false;
        }

        SimCardInfo simCardInfo = unicomRespVO.getUnicomRespData().getSimCardInfo();
        if (simCardInfo == null) {
            logInfo("联通查询返回数据包的卡信息为空", recordsDO.getIccid());
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return false;
        }

        if (StringUtils.isEmpty(simCardInfo.getRealnameFlag())) {
            logInfo("联通查询返回卡信息里面的实名信息为空", recordsDO.getIccid());
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return false;
        }

        recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.SUCCESS.getType());
        recordsDO.setIccid(simCardInfo.getIccid());
        recordsDO.setCardState(simCardInfo.getCardState());
        recordsDO.setRealNameFlag(Integer.parseInt(simCardInfo.getRealnameFlag()));

        if(UnicomRealnameFlagEnum.TRUE.getCode().equals(simCardInfo.getRealnameFlag())){
            VinExpiryReportDataDO vinExpiryReportDataDO = new VinExpiryReportDataDO();
            vinExpiryReportDataDO.setBrand(recordsDO.getBrand());
            vinExpiryReportDataDO.setSeriesName(recordsDO.getSeriesName());
            vinExpiryReportDataDO.setImportedEn(recordsDO.getImportedEn());
            vinExpiryReportDataDO.setCarVin(recordsDO.getCarVin());
            vinExpiryReportDataDO.setServiceType(recordsDO.getServiceType());
            vinExpiryReportDataDO.setExpiryDate(recordsDO.getExpiryDate());
            vinExpiryReportDataList.add(vinExpiryReportDataDO);

        }

        return true;
    }

    private boolean handleErrorResponse(UnicomRespVO unicomRespVO, VinExpiryMonthlyDetailDO recordsDO, String iccid) {
        if (unicomRespVO == null) {
            logInfo("查询到联通结果为空", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return true;
        }
        if (!UnicomResultEnum.SUCCESS.getDesc().equals(unicomRespVO.getResponseDesc())) {
            logInfo("联通查询失败", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return true;
        }
        if (CharSequenceUtil.isNotBlank(unicomRespVO.getQueryResult())) {
            logWarn(iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return true;
        }
        if (unicomRespVO.getUnicomRespData() == null) {
            logInfo("联通查询返回数据包为空", iccid);
            setFailedStatus(recordsDO, ErrorCodeConstants.RNR_QUERY_NOT_FOUND_INFO.getMsg());
            return true;
        }
        return false;
    }

    private void setFailedStatus(VinExpiryMonthlyDetailDO recordsDO, String errorDesc) {
        recordsDO.setQueryStatus(UnicomRnrQueryStatusEnum.FAILED.getType());
        recordsDO.setErrorDesc(errorDesc);
    }

    private void logInfo(String message, String iccid) {
        log.info("{}，iccid: {}", message, iccid);
    }

    private void logWarn(String iccid) {
        log.warn("{}，iccid: {}", "ICCID批量查询联通异常", iccid);
    }


    private void generateVinExpirationReport(Long jobId) {
        GenerateReportRequest generateReportRequest = GenerateReportRequest.builder().jobId(jobId).build();
        log.info("reportApi.generateVinExpirationReportV2方法处理 入参jobId:{}", jobId);
        CommonResult<String> result = reportApi.generateVinExpirationReportV2(generateReportRequest);

        if (result == null || !result.isSuccess()) {
            log.info("generateVinExpirationReportV2方法失败");
        }
    }
}
