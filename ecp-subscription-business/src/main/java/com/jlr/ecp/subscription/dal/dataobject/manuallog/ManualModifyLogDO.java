package com.jlr.ecp.subscription.dal.dataobject.manuallog;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_manual_modify_log
 * @TableName t_manual_modify_log
 */
@TableName(value ="t_manual_modify_log")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ManualModifyLogDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * VIN;VIN
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 修改类型;修改类型 1：AMAP修改 2：APPDCU修改 3：ICCID修改 4：发票日期修改 5：手动补录VIN
     */
    @TableField(value = "modify_type")
    private Integer modifyType;

    /**
     * 修改前值;修改前值
     */
    @TableField(value = "modify_before_value")
    private String modifyBeforeValue;

    /**
     * 修改后值;修改后值
     */
    @TableField(value = "modify_after_value")
    private String modifyAfterValue;

    /**
     * 修改人;修改人
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 修改时间;修改时间
     */
    @TableField(value = "operate_time")
    private LocalDateTime operateTime;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}