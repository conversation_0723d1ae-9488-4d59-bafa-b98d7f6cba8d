package com.jlr.ecp.subscription.kafka.message.cancel;

import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 服务通知消息
 *
 * <AUTHOR>
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelMessage extends BaseMessage {

    /**
     * 退单订单号
     */
    private String refundOrderCode;

    /**
     * 履约号ID;履约号ID，雪花算法
     */
    private String fufilmentId;

    /**
     * 联通刷新前到期日
     */
    private LocalDateTime refreshBeforeDate;
}
