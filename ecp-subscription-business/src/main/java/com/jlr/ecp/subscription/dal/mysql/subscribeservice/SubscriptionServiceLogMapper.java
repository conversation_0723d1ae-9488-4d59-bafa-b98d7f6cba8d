package com.jlr.ecp.subscription.dal.mysql.subscribeservice;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import org.apache.ibatis.annotations.Mapper;

import static com.jlr.ecp.subscription.constant.Constants.LIMIT_ONE;


/**
 * t_subscription_service_log表数据库访问层
 * <AUTHOR>
 */
@Mapper
public interface SubscriptionServiceLogMapper extends BaseMapperX<SubscriptionServiceLogDO> {
   default SubscriptionServiceLogDO findLastLogByFulfilmentId(String fulfilmentId){
       return selectOne(new LambdaQueryWrapperX<SubscriptionServiceLogDO>()
               .eq(SubscriptionServiceLogDO::getFufilmentId,fulfilmentId)
               .eq(BaseDO::getIsDeleted, false)
               .isNull(SubscriptionServiceLogDO::getRollbackFufilmentId)
               .orderByDesc(BaseDO::getCreatedTime)
               .last(LIMIT_ONE));
   }
}
