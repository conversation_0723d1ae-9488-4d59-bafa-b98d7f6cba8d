package com.jlr.ecp.subscription.config;

import ch.qos.logback.classic.pattern.ThrowableProxyConverter;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.ThrowableProxyUtil;
import org.slf4j.MDC;

public class SingleLineThrowableProxyConverter extends ThrowableProxyConverter {
    public static final String EXCEPTION = "[Exception][ID:";
    @Override
    protected String throwableProxyToString(IThrowableProxy tp) {
        try {
            // 参数校验
            if (tp == null) {
                return "[Exception][ID:NO_EXCEPTION] 异常信息为空";
            }

            String raw = ThrowableProxyUtil.asString(tp);
            if (raw == null || raw.trim().isEmpty()) {
                return "[Exception][ID:NO_STACK] 异常堆栈为空";
            }

            String[] lines = raw.split("\\r?\\n");
            if (lines.length == 0) {
                return "[Exception][ID:NO_LINES] 异常信息解析失败";
            }

            // 生成异常关联ID（优先使用 traceId，失败则生成自定义ID）
            String exceptionId = generateExceptionId(lines);

            // 构建第一行
            String firstLine = buildFirstLine(exceptionId, lines);
            
            // 构建第二行
            String stackLine = buildStackLine(exceptionId, lines);

            return firstLine + "\n" + stackLine;

        } catch (Exception e) {
            // 如果转换器本身出错，返回一个带ID的fallback信息，确保两行日志能关联
            String fallbackId = "ERROR-" + System.currentTimeMillis();
            return EXCEPTION + fallbackId + "] 异常日志转换失败: " + e.getMessage() +
                   "\n[StackTrace][ID:" + fallbackId + "] 转换器异常，无法显示堆栈信息";
        }
    }

    /**
     * 生成异常关联ID
     * 优先使用 traceId，如果获取失败则生成自定义ID
     */
    private String generateExceptionId(String[] lines) {
        // 尝试获取 traceId
        String traceId = getTraceIdSafely();
        if (traceId != null) {
            return traceId;
        }

        // traceId 获取失败，生成自定义ID
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String threadName = Thread.currentThread().getName();
            
            // 安全地获取异常类型
            String exceptionType = "Unknown";
            if (lines.length > 0 && lines[0] != null) {
                String firstLine = lines[0].trim();
                if (firstLine.contains(":")) {
                    exceptionType = firstLine.split(":")[0].trim();
                }
            }

            // 生成格式：时间戳后6位-线程名-异常类型
            return timestamp.substring(Math.max(0, timestamp.length() - 6)) + "-" + 
                   (threadName != null ? threadName.replaceAll("[^a-zA-Z0-9]", "") : "unknown") + "-" +
                   exceptionType.replaceAll("[^a-zA-Z0-9]", "");
        } catch (Exception e) {
            // 如果生成失败，返回时间戳作为ID
            return "GEN-" + System.currentTimeMillis();
        }
    }

    /**
     * 安全地获取 traceId
     */
    private String getTraceIdSafely() {
        try {
            String traceId = MDC.get("traceId");
            if (traceId != null && !traceId.trim().isEmpty() && !"N/A".equals(traceId)) {
                return traceId;
            }
        } catch (Exception e) {
            // MDC 操作可能失败，忽略异常，继续使用自定义ID生成逻辑
        }
        return null;
    }

    /**
     * 构建第一行
     */
    private String buildFirstLine(String exceptionId, String[] lines) {
        try {
            StringBuilder firstLine = new StringBuilder(EXCEPTION);
            firstLine.append(exceptionId).append("] ");
            
            // 添加异常信息
            if (lines.length > 0 && lines[0] != null) {
                firstLine.append(lines[0].trim());
            } else {
                firstLine.append("未知异常");
            }
            
            // 添加首个调用点
            String firstAt = findFirstStackTraceLine(lines);
            if (firstAt != null) {
                firstLine.append(" | ").append(firstAt);
            }

            return firstLine.toString();
        } catch (Exception e) {
            return EXCEPTION + exceptionId + "] 异常信息构建失败";
        }
    }

    /**
     * 构建第二行
     */
    private String buildStackLine(String exceptionId, String[] lines) {
        try {
            StringBuilder stackLine = new StringBuilder("[StackTrace][ID:");
            stackLine.append(exceptionId).append("]");
            
            int count = 0;
            int maxLines = 30; // 最多显示30行堆栈
            
            for (int i = 1; i < lines.length && count < maxLines; i++) {
                String line = lines[i];
                if (line != null && (line.trim().startsWith("at ") || line.trim().startsWith("Caused by:"))) {
                    stackLine.append(" | ").append(line.trim());
                    count++;
                }
            }
            
            // 如果还有更多堆栈，添加省略提示
            if (count >= maxLines) {
                stackLine.append(" | ... ");
            }

            return stackLine.toString();
        } catch (Exception e) {
            return "[StackTrace][ID:" + exceptionId + "] 堆栈信息构建失败";
        }
    }

    /**
     * 查找第一个堆栈跟踪行
     */
    private String findFirstStackTraceLine(String[] lines) {
        try {
            for (String line : lines) {
                if (line != null && line.trim().startsWith("at ")) {
                    return line.trim();
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }
}