package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 初始化状态枚举
 */
@AllArgsConstructor
@Getter
public enum InitialStateEnum {

    PENDING(0, "初始化中"),

    SUCCESS(1, "初始化成功"),

    FAIL(2, "初始化失败");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (InitialStateEnum status : InitialStateEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
