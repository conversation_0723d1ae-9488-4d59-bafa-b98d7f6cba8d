package com.jlr.ecp.subscription.service.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import org.springframework.web.multipart.MultipartFile;

public interface AmaPBatchManualRenewalService {

    /**
     *  AMAP 批量续费分页查询列表
     *
     * @param amaPPageDto 续费页面的查询参数
     * @return 续费页面的数据列表和总记录数
     */
    PageResult<AmaPBatchRenewalPageVO> queryBatchRenewalPageList(AmaPBatchRenewalPageDTO amaPPageDto);


    /**
     * 上传高德批量续费的Excel文件
     *
     * @param multipartFile 多部分文件对象，包含上传的Excel文件
     * @return CommonResult<AmaPBatchRenewalUploadVO> 返回一个通用结果对象，包含上传结果和相关信息
     */
    CommonResult<AmaPBatchRenewalUploadVO> uploadAmaPExcelRenewal(MultipartFile multipartFile);

    /**
     * 批量发送高德续费
     *
     * @param amaPBatchSendDTO 批量发送AMA P续订的通知对象，包含文件的源路径和批次编号等信息
     */
    CommonResult<String> batchSendAmaPRenewal(AmaPBatchSendDTO amaPBatchSendDTO);

    /**
     * 获取模板URL
     *
     * @return 模板的URL地址
     */
    String getTemplateUrl();
}
