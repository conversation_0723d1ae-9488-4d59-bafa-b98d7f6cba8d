package com.jlr.ecp.subscription.excel.listener.rnr;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.rnr.RnrQueryExcel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class RnrQueryCheckListener extends AnalysisEventListener<RnrQueryExcel> {


    private static final int BATCH_COUNT = 1000;

    private static final String VIN_TITLE = "VIN";

    private List<RnrQueryExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<RnrQueryExcel>> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Boolean isExcelFormatError = false;


    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(RnrQueryExcel rnrQueryExcel, AnalysisContext analysisContext) {
        try {
            if (Boolean.FALSE.equals(isExcelFormatError)) {
                checkUploadExcelTitle();
            }
            dataList.add(rnrQueryExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.add(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("RNR批量查询实名状态Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("RNR批量查询实名状态数据");
        if (Boolean.FALSE.equals(isExcelFormatError)) {
            checkUploadExcelTitle();
        }
        try {
            if (CollUtil.isNotEmpty(dataList)) {
                allDataList.add(dataList);
            }
        } catch (Exception e) {
            log.info("RNR批量查询实名状态数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("RNR批量查询实名状态数据解析完成！");
    }


    /**
     * 检查上传的Excel文件中的
     *
     */
    public void checkUploadExcelTitle() {
        log.info("检查上传的Excel文件中的, headers:{}", headers);
        if (headers.isEmpty() || !VIN_TITLE.equals(headers.get(0).get(0))) {
            isExcelFormatError = true;
        }
    }

}
