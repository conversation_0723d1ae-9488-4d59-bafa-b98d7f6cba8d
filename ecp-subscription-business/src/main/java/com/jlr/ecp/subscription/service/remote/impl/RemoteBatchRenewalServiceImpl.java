package com.jlr.ecp.subscription.service.remote.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.amap.VerifyResultEnum;
import com.jlr.ecp.subscription.enums.appd.AppDDataSourceEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyErrorEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyStatusEnum;
import com.jlr.ecp.subscription.excel.listener.remote.RemoteBatchRenewalCheckListener;
import com.jlr.ecp.subscription.excel.listener.remote.RemoteBatchRenewalReadListener;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalResultExcel;
import com.jlr.ecp.subscription.excel.utils.RemoteExcelUtil;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.remote.RemoteBatchRenewalService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remote.RemoteSingleRenewalService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.util.*;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RemoteBatchRenewalServiceImpl implements RemoteBatchRenewalService {

    @Value("${tsdp.renewalExcelUrl}")
    private String renewalExcelUrl;

    private static final String FILE_CODE = "AWS_S3_FILE";

    private static final String EXCEL_FORMATTER = ".xlsx";

    private static final String TODAY_END = " 23:59:59";

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024;

    @Resource
    private RemoteRenewBatchRecordsMapper remoteRenewBatchRecordsMapper;

    @Resource
    private RemoteRenewDetailRecordsMapper remoteRenewDetailRecordsMapper;

    @Resource
    private Snowflake snowflake;

    @Resource
    private FileService fileService;

    @Resource
    private Redisson redisson;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private SubscriptionService subscriptionService;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private RemoteSingleRenewalService remoteSingleRenewalService;

    @Resource
    private OrderCheckService orderCheckService;

    @Resource
    private PermissionApi permissionApi;

    @Override
    public String getRemoteTemplateUrl() {
        return renewalExcelUrl;
    }

    @Override
    public PageResult<RemoteBatchRenewalPageVO> queryRemoteBatchRenewalPageList(RemoteBatchRenewalPageDTO pageDTO) {
        log.info("Remote批量续费分页查询列表, pageDTO:{}", pageDTO);
        Page<RemoteRenewBatchRecords> pageResp = queryRemoteRenewBatchPage(pageDTO);
        List<RemoteRenewBatchRecords> batchRecordsList = pageResp.getRecords();
        List<RemoteBatchRenewalPageVO> pageVOList = buildRemoteBatchRenewalPageVOList(batchRecordsList);
        return new PageResult<>(pageVOList, pageResp.getTotal());
    }

    @Override
    public CommonResult<RemoteBatchRenewalUploadVO> uploadRemoteExcelRenewal(MultipartFile multipartFile) {
        log.info("上传Remote批量续费的Excel文件, multipartFile:{}", multipartFile.toString());
        long startTime = System.currentTimeMillis();
        // 检查文件大小
        if (multipartFile.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SIZE_EXCEED_LIMIT);
        }
        // 检查文件是否为excel
        byte[] readBytes;
        try {
            readBytes = IoUtil.readBytes(multipartFile.getInputStream());
        } catch (IOException e) {
            log.info("上传Remote批量续费的Excel文件读取异常:{}", e.getMessage());
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_UPLOAD_ERROR);
        }
        // excel格式校验
        if (!FileCheckUtil.isExcelFile(readBytes)) {
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_FILE_INVALID);
        }
        RemoteBatchRenewalCheckListener renewalCheckListener = new RemoteBatchRenewalCheckListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), RemoteBatchRenewalExcel.class,
                    renewalCheckListener).sheet().doRead();
        } catch (Exception e) {
            log.info("上传Remote批量续费的Excel文件异常:{}", e.getMessage());
        }
        RemoteBatchRenewalUploadVO renewalUploadVO = new RemoteBatchRenewalUploadVO();
        renewalUploadVO.setType("success");
        renewalUploadVO.setMsg(Constants.ATTENTION_RESULT);
        long endTime = System.currentTimeMillis();
        log.info("上传Remote批量续费的excel文件解析完成，花费时间:{}毫秒", (endTime-startTime));
        if (Boolean.TRUE.equals(renewalCheckListener.getFormatError())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(renewalCheckListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        addBatchRenewalRecords(renewalCheckListener);
        return CommonResult.success(renewalUploadVO);
    }

    @Override
    public CommonResult<String> batchSendRemoteRenewal(RemoteBatchSendDTO remoteBatchSendDTO) {
        log.info("Remote批量发送续费, remoteBatchSendDTO:{}", remoteBatchSendDTO);
        RLock lock = redisson.getLock(Constants.REDIS_KEY.REMOTE_BATCH_RENEWAL_KEY);
        try {
            if (!RLockUtil.tryLock(lock, 30, 60, TimeUnit.SECONDS)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            Long batchNo = Long.parseLong(remoteBatchSendDTO.getBatchNo());
            List<RemoteRenewBatchRecords> progressRecordsDOList = queryBatchByDealStatus(DealStatusEnum.PROGRESS.getStatus());
            if (CollUtil.isNotEmpty(progressRecordsDOList)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            RemoteRenewBatchRecords batchRecordsDO = queryRenewalByBatchNo(batchNo);
            log.info("Remote批量发送续费, batchRecordsDO:{}", batchRecordsDO);
            if (Objects.nonNull(batchRecordsDO)) {
                batchRecordsDO.setDealStatus(DealStatusEnum.PROGRESS.getStatus());
                remoteRenewBatchRecordsMapper.updateById(batchRecordsDO);
            }
            String operator = LoginUtil.getLoginUserName();
            log.info("Remote批量发送续费, operator:{}", operator);
            CompletableFuture.runAsync(() -> batchInsertAndSendRenewal(batchRecordsDO, batchNo, operator));
        } catch (Exception e) {
            log.info("Remote批量发送续费异常:{}", e.getMessage());
            return CommonResult.error(new ErrorCode(1000001, e.getMessage()));
        } finally {
            RLockUtil.unlock(lock, 3);
        }
        return CommonResult.success("请求已经发送，请前往续费记录查看结果");
    }

    /**
     * 查询Remote续费批次分页列表
     *
     * @param pageDTO 分页查询参数对象，包含分页信息和排序信息
     * @return 返回分页列表对象，包含查询结果和分页信息
     */
    private Page<RemoteRenewBatchRecords> queryRemoteRenewBatchPage(RemoteBatchRenewalPageDTO pageDTO) {
        Page<RemoteRenewBatchRecords> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<RemoteRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewBatchRecords::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(RemoteRenewBatchRecords::getCreatedTime);
            queryWrapper.orderByAsc(RemoteRenewBatchRecords::getId);
        } else {
            queryWrapper.orderByDesc(RemoteRenewBatchRecords::getCreatedTime);
            queryWrapper.orderByDesc(RemoteRenewBatchRecords::getId);
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }
        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(RemoteRenewBatchRecords::getOperator, userName);
            log.info("查询remote续费批次分页列表,user:{}, pageDTO:{}", userName, pageDTO);
        }
        return remoteRenewBatchRecordsMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 构建应用批量续期页面VO列表
     *
     * @param batchRecordsList RemoteRenewBatchRecords对象列表，记录了批量续期的信息
     * @return 返回一个RemoteBatchRenewalPageVO对象列表，用于展示批量续期页面的信息
     */
    private List<RemoteBatchRenewalPageVO> buildRemoteBatchRenewalPageVOList(List<RemoteRenewBatchRecords> batchRecordsList) {
        List<RemoteBatchRenewalPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(batchRecordsList)) {
            return resp;
        }
        for (RemoteRenewBatchRecords records : batchRecordsList) {
            resp.add(buildRemoteBatchRenewalPageVO(records));
        }
        return resp;
    }

    /**
     * 构建 RemoteBatchRenewalPageVO 对象
     *
     * @param batchRecord 批量续订的记录对象，包含批量续订的相关信息
     * @return 如果 batchRecord 不为 null，则返回转换后的 RemoteBatchRenewalPageVO 对象；否则返回 null
     */
    private RemoteBatchRenewalPageVO buildRemoteBatchRenewalPageVO(RemoteRenewBatchRecords batchRecord) {
        if (Objects.isNull(batchRecord)) {
            return null;
        }
        return RemoteBatchRenewalPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(batchRecord.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(batchRecord.getOperator())
                .checkResultStatus(batchRecord.getVerifyResult())
                .checkResultDesc(VerifyResultEnum.getDescByCode(batchRecord.getVerifyResult()))
                .operateStatus(batchRecord.getDealStatus())
                .batchNo(batchRecord.getBatchNo())
                .errorDetailPath(batchRecord.getVerifyResultFile())
                .build();
    }

    /**
     * 添加Remote批量续期记录
     *
     * @param renewalCheckListener 续期检查监听器，用于获取续期检查的相关数据和结果
     */
    private void addBatchRenewalRecords(RemoteBatchRenewalCheckListener renewalCheckListener) {
        RemoteRenewBatchRecords remoteRenewBatchRecords = new RemoteRenewBatchRecords();
        String uploadS3AppDUcSourcePath = uploadRenewalSourceToS3File(renewalCheckListener.getAllDataList());
        remoteRenewBatchRecords.setBatchNo(snowflake.nextId());
        remoteRenewBatchRecords.setUploadFile(uploadS3AppDUcSourcePath);
        if(Boolean.FALSE.equals(renewalCheckListener.getCheckResult())) {
            String uploadAmaPResultPath = uploadRenewalResultToS3File(renewalCheckListener.getResultExcelList());
            remoteRenewBatchRecords.setVerifyResult(VerifyResultEnum.FAIL.getCode());
            remoteRenewBatchRecords.setVerifyResultFile(uploadAmaPResultPath);
        } else {
            remoteRenewBatchRecords.setVerifyResult(VerifyResultEnum.SUCCESS.getCode());
        }
        remoteRenewBatchRecords.setDealStatus(DealStatusEnum.WAITED.getStatus());
        remoteRenewBatchRecords.setOperator(LoginUtil.getLoginUserName());
        remoteRenewBatchRecordsMapper.insert(remoteRenewBatchRecords);
    }

    /**
     * 将批量续订的Excel源文件上传到S3
     *
     * @param batchRenewalExcelList 包含批量续订数据的列表，用于生成Excel文件
     * @return 返回上传到S3的文件路径如果生成的本地文件为空或在上传过程中发生异常，则返回空字符串
     */
    private String uploadRenewalSourceToS3File(List<RemoteBatchRenewalExcel> batchRenewalExcelList) {
        String uploadS3FilePath = "";
        File uploadS3TempSourceExcel = null;
        try {
            uploadS3TempSourceExcel = RemoteExcelUtil.batchRenewalWriteSourceFile(batchRenewalExcelList);
            if (Objects.isNull(uploadS3TempSourceExcel)) {
                log.info("Remote批量续订Excel源文件上传至S3, 生成本地临时文件为空");
                return uploadS3FilePath;
            }
            uploadS3FilePath = fileService.createFile(null, "RemoteBatchRenewalExcel" + File.separator
                    + System.currentTimeMillis() + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempSourceExcel), FILE_CODE);
            log.info("Remote批量续订Excel源文件上传至S3, 本地文件原始文件路径:{}", uploadS3TempSourceExcel.getPath());
        } catch (Exception e) {
            log.info("Remote批量续订Excel源文件上传至S3异常:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempSourceExcel)) {
                FileUtil.del(uploadS3TempSourceExcel);
                log.info("Remote批量续订Excel源文件上传至S3, 成功删除本地临时文件");
            }
        }
        return uploadS3FilePath;
    }

    /**
     * 将批量续费结果Excel文件上传到S3存储
     *
     * @param batchRenewalResultExcels 包含批量续费结果的列表
     * @return 上传到S3的文件路径，如果上传失败则返回空字符串
     */
    private String uploadRenewalResultToS3File(List<RemoteBatchRenewalResultExcel> batchRenewalResultExcels) {
        String uploadS3ResultFilePath = "";
        File uploadS3TempResultExcel = null;
        try {
            uploadS3TempResultExcel = RemoteExcelUtil.batchRenewalWriteResultFile(batchRenewalResultExcels);
            if (Objects.isNull(uploadS3TempResultExcel)) {
                log.info("Remote批量续费结果Excel文件上传至S3存储, 生成本地临时文件为空");
                return uploadS3ResultFilePath;
            }
            String fileName = "REMOTE批量续费校验结果" +  System.currentTimeMillis();
            uploadS3ResultFilePath = fileService.createFile(null, "RemoteBatchRenewalResultExcel" + File.separator +
                    fileName + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempResultExcel), FILE_CODE);
            log.info("Remote批量续费结果Excel文件上传至S3存储, 本地文件原始文件路径:{}", uploadS3TempResultExcel.getPath());
        } catch (Exception e) {
            log.info("Remote批量续费结果Excel文件上传至S3存储:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempResultExcel)) {
                FileUtil.del(uploadS3TempResultExcel);
                log.info("Remote批量续费结果Excel文件上传至S3存储, 成功删除本地临时文件");
            }
        }
        return uploadS3ResultFilePath;
    }

    /**
     * 根据处理状态查询RemoteRenewBatchRecords
     *
     * @param dealStatus 处理状态，用于筛选记录
     * @return 符合条件的RemoteRenewBatchRecords列表
     */
    private List<RemoteRenewBatchRecords> queryBatchByDealStatus(Integer dealStatus) {
        LambdaQueryWrapper<RemoteRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewBatchRecords::getDealStatus, dealStatus)
                .eq(RemoteRenewBatchRecords::getIsDeleted, false);
        return remoteRenewBatchRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 根据批次编号查询RemoteRenewBatchRecords对象
     *
     * @param batchNo 批次编号，用于查询特定批次的记录
     * @return 如果找到对应的记录，则返回RemoteRenewBatchRecords对象；如果未找到或有多条记录，则返回null
     */
    private RemoteRenewBatchRecords queryRenewalByBatchNo(Long batchNo) {
        LambdaQueryWrapper<RemoteRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewBatchRecords::getBatchNo, batchNo)
                .eq(RemoteRenewBatchRecords::getIsDeleted, false);
        List<RemoteRenewBatchRecords> resp = remoteRenewBatchRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询RemoteRenewBatchRecords对象, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    /**
     * 批量插入数据库记录并发送续期通知
     *
     * @param batchRecordsDO 批处理记录实体类，包含批处理相关信息
     * @param batchNo 批号，用于标识特定的批处理
     * @param operator 操作员，用于记录操作人员信息
     */
    private void batchInsertAndSendRenewal(RemoteRenewBatchRecords batchRecordsDO, Long batchNo, String operator) {
        try {
            RemoteBatchRenewalServiceImpl bean = applicationContext.getBean(getClass());
            byte[] content = fileService.getFileContentByAllPath(FILE_CODE, batchRecordsDO.getUploadFile());
            List<List<RemoteBatchRenewalExcel>> batchRenewalExcelList = doReadRenewalExcel(content);
            Map<String, RemoteSearchResultDTO> allMap = new HashMap<>();
            for (List<RemoteBatchRenewalExcel> batchRenewalExcels : batchRenewalExcelList) {
                Map<String, RemoteSearchResultDTO> map = bean.batchInsertRenewalRecord(batchRenewalExcels, batchNo, operator);
                if(CollUtil.isNotEmpty(map)){
                    allMap.putAll(map);
                }
            }
            manualBatchRenewal(batchRecordsDO, allMap);
        } catch (Exception e) {
            log.info("批量插入数据库记录并发送续期通知异常：{}", e.getMessage());
        }
    }

    /**
     * 读取Remote续费Excel文件
     *
     * @param bytes Excel文件的字节内容
     * @return 解析后的Excel数据列表
     */
    public List<List<RemoteBatchRenewalExcel>> doReadRenewalExcel(byte[] bytes) {
        List<List<RemoteBatchRenewalExcel>> resp = new ArrayList<>();
        if (bytes == null || bytes.length < 1) {
            log.info("读取Remote续费Excel文件, bytes为空");
            return resp;
        }
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "RemoteBatchRenewalReadS3Excel" + System.currentTimeMillis() + EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("读取Remote续费Excel文件, filePath:{}", filePath);
        RemoteBatchRenewalReadListener renewalReadListener = new RemoteBatchRenewalReadListener();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(bytes);
            outputStream.flush();
            EasyExcel.read(fileName, RemoteBatchRenewalExcel.class, renewalReadListener).sheet().doRead();
            log.info("读取Remote续费Excel文件, renewalReadListener读取数据的数量:{}", renewalReadListener.getAllList().size());
            resp = renewalReadListener.getAllList();
            log.info("读取Remote续费Excel文件, filePath:{}", filePath);
        } catch (Exception e) {
            log.info("读取Remote续费Excel文件, 解析excel文件异常:{}", e.getMessage());
        } finally {
            FileUtil.del(fileName);
        }
        return resp;
    }

    /**
     * 批量插入续费记录
     *
     * @param batchRenewalExcelList 会员续费Excel列表，包含多个会员的续费信息
     * @param batchNo 批次编号，用于标识当前批次的唯一性
     * @param operator 操作人，用于记录续费记录的操作人员
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, RemoteSearchResultDTO> batchInsertRenewalRecord(List<RemoteBatchRenewalExcel> batchRenewalExcelList, Long batchNo,
                                               String operator) {
        if (CollUtil.isEmpty(batchRenewalExcelList)) {
            log.info("批量插入Remote续费记录为空, batchNo:{}", batchNo);
            return Collections.emptyMap();
        }
        List<RemoteBatchRenewalExcel> excels = batchRenewalExcelList.stream()
                .filter(excel -> StringUtils.isNotBlank(excel.getRenewalDate()))
                .peek(excel -> excel.setCarVin(CarVinUtil.carVinToUpperCase(excel.getCarVin())))
                .collect(Collectors.toList());
        log.info("批量插入Remote续费记录, batchNo:{}, carVin数量:{}, Remote续期时间不为空的数量：{}",
                batchNo, batchRenewalExcelList.size(), excels.size());
        if (CollUtil.isEmpty(excels)) {
            log.info("批量插入Remote续费记录, 续期时间不为空的数量为0, batchNo:{}", batchNo);
            return Collections.emptyMap();
        }
        Map<String, RemoteSearchResultDTO> renewResultMap = getRemoteSearchResultDTOMap(excels);
        List<RemoteRenewDetailRecords> records = new ArrayList<>();
        for (RemoteBatchRenewalExcel renewalExcel : excels) {
            RemoteSearchResultDTO resultDTO = renewResultMap.get(renewalExcel.getCarVin());
            RemoteRenewDetailRecords renewalRecord = buildRenewalRecord(renewalExcel, batchNo,
                    operator, resultDTO);
            records.add(renewalRecord);
            // 移除状态为失败的记录
            if(Objects.equals(RemoteModifyStatusEnum.MODIFY_FAIL.getStatus(), renewalRecord.getModifyStatus())){
                renewResultMap.remove(renewalExcel.getCarVin());
            }
        }
        if (CollUtil.isNotEmpty(records)) {
            remoteRenewDetailRecordsMapper.insertBatch(records);
        }
        return renewResultMap;
    }

    /**
     * 手动批量续费
     * 该方法根据批处理记录号获取待续费的记录，并分别对不同服务类型进行批量续费操作，最后更新批处理记录的状态
     *
     * @param batchRecordsDO 批处理记录对象，包含批处理号等信息
     */
    private void manualBatchRenewal(RemoteRenewBatchRecords batchRecordsDO, Map<String, RemoteSearchResultDTO> allMap) {
        log.info("Remote批量续费, batchRecordsDO:{}", batchRecordsDO);
        if (Objects.isNull(batchRecordsDO)) {
            return ;
        }
        if (CollUtil.isEmpty(allMap)) {
            log.info("Remote批量续费, 服务数据集合allMap为空, 直接更新批量记录的状态");
            //更新批量记录的状态
            batchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
            remoteRenewBatchRecordsMapper.updateById(batchRecordsDO);
            return ;
        }
        Long batchNo = batchRecordsDO.getBatchNo();
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewDetailRecords::getBatchNo, batchNo)
                .eq(RemoteRenewDetailRecords::getModifyStatus, RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus())
                .eq(RemoteRenewDetailRecords::getIsDeleted, false);
        List<RemoteRenewDetailRecords> records = remoteRenewDetailRecordsMapper.selectList(queryWrapper);
        batchSendRenewalAndUpdate(records, allMap);
        //更新批量记录的状态
        batchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        remoteRenewBatchRecordsMapper.updateById(batchRecordsDO);
    }

    /**
     * 获取在之前的续费日期

     * @param renewalExcelList 包含应用数据库续费信息的列表
     * @return 返回一个映射，键为数据库标识，值为续费日期之前的时间
     */
    private Map<String, RemoteSearchResultDTO> getRemoteSearchResultDTOMap(List<RemoteBatchRenewalExcel> renewalExcelList) {
        List<List<RemoteBatchRenewalExcel>> partition = Lists.partition(renewalExcelList, 10);
        List<RemoteSearchResultDTO> allResult = new ArrayList<>(renewalExcelList.size());
        for (List<RemoteBatchRenewalExcel> excelList : partition) {
            List<String> vinList = excelList.stream().map(RemoteBatchRenewalExcel::getCarVin).collect(Collectors.toList());
            List<RemoteSearchResultDTO> resultDTOList = subscriptionService.getRemoteExpireDateByVinList(vinList);
            if(CollUtil.isNotEmpty(resultDTOList)){
                allResult.addAll(resultDTOList);
            }
        }
        return allResult.stream().collect(Collectors.toMap(RemoteSearchResultDTO::getCarVin, Function.identity(), (o, n) -> o));
    }

    /**
     * 构建续订记录对象
     *
     * @param renewalExcel 包含车辆信息和续订日期的Excel数据对象
     * @param batchNo 批次编号，用于标识续订事件
     * @param operator 操作人
     * @param resultDTO resultDTO
     * @return 返回构建的续订记录对象
     */
    private RemoteRenewDetailRecords buildRenewalRecord(RemoteBatchRenewalExcel renewalExcel, Long batchNo,
                                                      String operator, RemoteSearchResultDTO resultDTO) {
        RemoteRenewDetailRecords remoteRenewDetailRecords = new RemoteRenewDetailRecords();
        remoteRenewDetailRecords.setCarVin(CarVinUtil.carVinToUpperCase(renewalExcel.getCarVin()));
        remoteRenewDetailRecords.setBatchNo(batchNo);
        remoteRenewDetailRecords.setModifyAfterDate(SubscribeTimeFormatUtil.stringToTimeByFormat(renewalExcel.getRenewalDate() + TODAY_END,
                SubscribeTimeFormatUtil.FORMAT_2));
        remoteRenewDetailRecords.setOperator(operator);
        remoteRenewDetailRecords.setDataSource(AppDDataSourceEnum.BATCH.getDateSource());
        remoteRenewDetailRecords.setModifyBeforeDate(resultDTO.getBeforeExpiryDate());
        // vin在ECP不存在
        if(!resultDTO.isExistInEcp()){
            remoteRenewDetailRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_FAIL.getStatus());
            remoteRenewDetailRecords.setErrorDesc(RemoteModifyErrorEnum.VIN_NOT_FOUND.getDesc());
        // vin不是pivi
        }else if(!resultDTO.isPiviModel()){
            remoteRenewDetailRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_FAIL.getStatus());
            remoteRenewDetailRecords.setErrorDesc(RemoteModifyErrorEnum.VIN_NOT_PIVI.getDesc());
        // vin没有remote服务
        }else if(CollUtil.isEmpty(resultDTO.getServiceDOList())){
            remoteRenewDetailRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_FAIL.getStatus());
            remoteRenewDetailRecords.setErrorDesc(RemoteModifyErrorEnum.SERVICE_DATA_EMPTY.getDesc());
        }else {
            remoteRenewDetailRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_PROGRESS.getStatus());
        }
        return remoteRenewDetailRecords;
    }

    /**
     * 批量发送续订请求并更新续订记录
     *
     * @param list 待处理的续订记录列表
     */
    @Override
    public void batchSendRenewalAndUpdate(List<RemoteRenewDetailRecords> list, Map<String, RemoteSearchResultDTO> allMap) {
        if (CollUtil.isEmpty(list) || CollUtil.isEmpty(allMap)) {
            return ;
        }
        List<List<RemoteRenewDetailRecords>> groupList = Lists.partition(list, 10);
        for (List<RemoteRenewDetailRecords> recordList : groupList) {
            RLock rlock = redisson.getLock(Constants.REDIS_KEY.REMOTE_ASYNC_RENEWAL_KEY);
            try {
                if (RLockUtil.tryLock(rlock, 30, 60, TimeUnit.SECONDS)) {
                    asyncRenewalList(recordList, allMap);
                }
            } catch (Exception e) {
                log.info("Remote批量发送续订请求并更新续订记录异常：", e);
            } finally {
                RLockUtil.unlock(rlock, 3);
            }
        }
    }

    /**
     * 异步更新待续订的列表
     *
     * @param waitedRenewList 待续订的应用D列表
     */
    public void asyncRenewalList(List<RemoteRenewDetailRecords> waitedRenewList, Map<String, RemoteSearchResultDTO> allMap) {
        if (CollUtil.isEmpty(waitedRenewList)) {
            return ;
        }
        // sprint47:校验是否存在续费中的记录
        List<RemoteRenewDetailRecords> filteredList = checkProcessRecordsByVinList(waitedRenewList);
        if (CollUtil.isEmpty(filteredList)) {
            log.info("批量续费REMOTE服务，全部是正在在续费中的记录，waitedRenewList:{}", waitedRenewList);
            return ;
        }
        // sprint47:校验是否存在在途订单
        List<RemoteRenewDetailRecords> notInTransitVinList = checkOrderInTransitByVinList(filteredList);
        if (CollUtil.isEmpty(notInTransitVinList)) {
            log.info("批量续费REMOTE服务，全部是在途订单，filteredList:{}", filteredList);
            return ;
        }
        log.info("批量续费REMOTE服务开始，notInTransitVinList:{}", notInTransitVinList);
        for (RemoteRenewDetailRecords remoteRenewDetailRecords : notInTransitVinList) {
            RemoteSearchResultDTO resultDTO = allMap.get(remoteRenewDetailRecords.getCarVin());
            // 调用TSDP执行续费
            OrdFufilmentBusiDTO busiDTO = remoteSingleRenewalService.buildCallApiParam(resultDTO, remoteRenewDetailRecords);
            RemoteModifyRespDTO respDTO = remoteCallService.concurrentCallTSDPRenew(busiDTO);
            remoteSingleRenewalService.updateRemoteRenewRecords(remoteRenewDetailRecords, respDTO);
        }
    }

    /**
     * 根据车架号列表检查并更新远程手动续费记录
     * 此方法首先提取给定列表中的所有车架号（VIN），然后查询正在续费过程中的记录
     * 如果没有正在续费的记录，则直接返回原始列表
     * 如果有正在续费的记录，则调用另一个方法来更新记录并返回更新后的列表
     *
     * @param renewList 包含远程手动续费详情记录的列表
     * @return 更新后的远程手动续费详情记录列表
     */
    public List<RemoteRenewDetailRecords> checkProcessRecordsByVinList(List<RemoteRenewDetailRecords> renewList) {
        Long batchNo = renewList.get(0).getBatchNo();
        // renewList转为map，key为vin，value为主键id
        Map<String, Long> vinMap = renewList.stream()
                .collect(Collectors.toMap(RemoteRenewDetailRecords::getCarVin,
                        RemoteRenewDetailRecords::getId, (v1, v2) -> v1));

        // 查询是否存在非当前批次号的续费中的记录
        List<String> processVinList = remoteRenewDetailRecordsMapper.getProcessRecordsByVinSetAndBatchNo(vinMap.keySet(), batchNo)
                .stream()
                // 当前记录的id在vinMap.get(records.getCarVin())之前，才认为是存在续费中的记录
                .filter(records -> records.getId() < vinMap.get(records.getCarVin()))
                .map(RemoteRenewDetailRecords::getCarVin)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(processVinList)) {
            log.info("不存在续费中的REMOTE手动续费记录, vinSet:{}", vinMap.keySet());
            return renewList;
        }
        log.info("存在续费中的REMOTE手动续费记录, processVinList:{}", processVinList);
        return updateRecordsAndReturnNew(renewList, processVinList);
    }

    /**
     * 更新续费记录并返回新的记录列表
     * 该方法用于处理给定的续费记录列表，根据车辆VIN是否在处理列表中来决定记录的更新与否
     * 如果车辆VIN在处理列表中，则将该记录标记为修改失败；否则，保留该记录为新的续费记录
     *
     * @param renewList 续费记录列表，需要被处理和更新
     * @param processVinList 正在处理的车辆VIN列表，用于判断续费记录是否需要被更新
     * @return 返回新的续费记录列表，不包含那些被标记为修改失败的记录
     */
    private List<RemoteRenewDetailRecords> updateRecordsAndReturnNew(List<RemoteRenewDetailRecords> renewList, List<String> processVinList) {
        // 需要继续处理的记录
        List<RemoteRenewDetailRecords> newRenewList = new ArrayList<>();
        // 待更新的续费记录
        List<RemoteRenewDetailRecords> updateList = new ArrayList<>();
        for (RemoteRenewDetailRecords renewRecords : renewList) {
            if (processVinList.contains(renewRecords.getCarVin())) {
                renewRecords.setModifyStatus(RemoteModifyStatusEnum.MODIFY_FAIL.getStatus());
                renewRecords.setErrorDesc(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR.getMsg());
                updateList.add(renewRecords);
            } else {
                newRenewList.add(renewRecords);
            }
        }
        remoteRenewDetailRecordsMapper.updateBatch(updateList);
        return newRenewList;
    }

    /**
     * 根据VIN列表检查订单是否在途中
     * 此方法首先提取过滤列表中的所有VIN号，然后调用订单检查服务检查这些VIN号是否有在途订单
     * 如果检查操作不成功或没有在途订单，则返回过滤列表本身，否则更新记录并返回新的列表
     *
     * @param filteredList 过滤后的远程续保详情记录列表
     * @return 如果存在在途订单，则返回更新后的记录列表，否则返回空列表或原始过滤列表
     */
    public List<RemoteRenewDetailRecords> checkOrderInTransitByVinList(List<RemoteRenewDetailRecords> filteredList) {
        List<String> filteredVinList = filteredList.stream().map(RemoteRenewDetailRecords::getCarVin).collect(Collectors.toList());
        CommonResult<List<String>> checkOrderInTransit = orderCheckService.checkOrderInTransitByVinList(filteredVinList, ServiceTypeEnum.REMOTE);
        if (!checkOrderInTransit.isSuccess()) {
            return Collections.emptyList();
        }
        List<String> inTransitVinList = checkOrderInTransit.getData();
        if (CollUtil.isEmpty(inTransitVinList)) {
            log.info("不存在REMOTE的在途订单, vinList:{}", filteredVinList);
            return filteredList;
        }
        log.info("存在REMOTE的在途订单, inTransitVinList:{}", inTransitVinList);
        return updateRecordsAndReturnNew(filteredList, inTransitVinList);
    }
}
