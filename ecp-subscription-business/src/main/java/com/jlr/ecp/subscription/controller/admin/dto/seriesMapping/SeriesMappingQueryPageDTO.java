package com.jlr.ecp.subscription.controller.admin.dto.seriesMapping;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "车型展示名称配置 查询pageDTO")
public class SeriesMappingQueryPageDTO extends PageParam {

    @Schema(description = "车型系列编码")
    private String seriesCode;

    @Schema(description = "车型系列中文名")
    private String dpSeriesName;
}