package com.jlr.ecp.subscription.util.common;

import com.jlr.ecp.subscription.config.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共代码处理工具
 * <AUTHOR>
 */
@Component
@Slf4j
public class CommonUtil {

    /**
    处理传入参数list参数
     */
   public static  List<String> handleListParam(String params){
       List<String> paramList = new ArrayList<>();
        if(params !=null && !params.isEmpty()){
             paramList = Arrays.stream(params.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
        }
        return paramList;
   }

    /**
     * 根据code获取name
     * @param redisService
     * @param cacheKey
     * @param code
     * @return
     */
    public static String getValueFromRedis(RedisService redisService, String cacheKey, String code) {
       try {
           String cacheMapValue = redisService.getCacheMapValue(cacheKey, code);
           if(cacheMapValue !=null && !cacheMapValue.isEmpty()){
               return cacheMapValue;
           }
       }catch (Exception e){
           log.info("redis 缓存error");
       }

        return code;
    }





}
