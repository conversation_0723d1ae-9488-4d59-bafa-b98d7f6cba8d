package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EsimInfoVO {

    @Schema(description = "车架号")
    private String vin;

    @Schema(description = "ICCID")
    private String iccid;


    @Schema(description = "实名制状态：0未实名 1已实名")
    private String realnameFlag;

    @Schema(description = "卡状态")
    private String cardState;

    @Schema(description = "卡状态文本")
    private String cardStateTxt;
}
