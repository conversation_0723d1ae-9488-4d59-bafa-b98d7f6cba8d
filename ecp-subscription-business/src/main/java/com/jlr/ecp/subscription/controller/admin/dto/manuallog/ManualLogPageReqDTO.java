package com.jlr.ecp.subscription.controller.admin.dto.manuallog;

import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.subscription.util.CarVinUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "查询中心 - 修改日志分页req参数")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ManualLogPageReqDTO extends PageParam {
    @Schema(description = "VIN")
    private String vin;

    @Schema(description = "修改日志类型")
    private Integer modifyType;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;


    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String operateTimeSort;

    @Schema(description = "操作人")
    private String operator;

}
