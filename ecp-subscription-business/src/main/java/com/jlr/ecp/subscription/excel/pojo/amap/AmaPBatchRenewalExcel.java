package com.jlr.ecp.subscription.excel.pojo.amap;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = false)
public class AmaPBatchRenewalExcel {
    @ExcelProperty("VIN")
    private String carVin;

    @ExcelProperty("续费时长（仅支持填写一年或三年）")
    private String renewalYear;
}
