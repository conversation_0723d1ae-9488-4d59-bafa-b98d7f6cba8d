package com.jlr.ecp.subscription.controller.admin.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalStatusVO;
import com.jlr.ecp.subscription.service.amap.AmaPManualRenewalQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/amap/manual/renewal/query")
@Validated
@Tag(name = "AMAP手动续费-续费记录查询")
public class AmaPManualRenewalQueryController {
    @Resource
    private AmaPManualRenewalQueryService amaPManualRenewalQueryService;

    @GetMapping("/queryBatchNo")
    @Operation(summary = "查询续费编号")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-renewalrecord:forms')")
    public CommonResult<String> queryRenewalBatchNo(@RequestParam("batchNo") String batchNo) {
        String resp = amaPManualRenewalQueryService.queryAmaPRenewalBatchNo(batchNo);
        return CommonResult.success(resp);
    }

    @GetMapping("/queryRenewalStatus")
    @Operation(summary = "查询续费状态")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-renewalrecord:forms')")
    public CommonResult<List<AmaPRenewalStatusVO>> queryRenewalStatus() {
        List<AmaPRenewalStatusVO> resp = amaPManualRenewalQueryService.queryAmaPRenewalStatus();
        return CommonResult.success(resp);
    }

    @PostMapping("/queryRenewalRecord/pageList")
    @Operation(summary = "分页查询续费记录")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-renewalrecord:forms')")
    public CommonResult<PageResult<AmaPRenewalQueryPageVO>> query(@RequestBody AmaPRenewalQueryPageDTO queryPageDTO) {
        PageResult<AmaPRenewalQueryPageVO> resp = amaPManualRenewalQueryService.queryRenewalRecordPageList(queryPageDTO);
        return CommonResult.success(resp);
    }
}
