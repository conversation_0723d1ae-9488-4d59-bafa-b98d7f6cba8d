package com.jlr.ecp.subscription.excel.listener.iccid;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyExcel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class IccidBatchModifyReadListener extends AnalysisEventListener<IccidBatchModifyExcel> {

    private static final int BATCH_COUNT = 500;

    private List<IccidBatchModifyExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<IccidBatchModifyExcel>> allList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    @Override
    public void invoke(IccidBatchModifyExcel iccidBatchModifyExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(iccidBatchModifyExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allList.add(dataList);
                // 重置空间
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("ICCID批量修改读取异常:{}", e.getMessage());
            // 重置空间
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("ICCID批量修改读取最后剩余的数据");
        try {
            // 添加最后剩余的数据
            allList.add(dataList);
        } catch (Exception e) {
            log.info("ICCID批量修改读取最后剩余的数据异常:{}", e.getMessage());
        } finally {
            dataList = null;
        }
        log.info("ICCID批量修改读取最后剩余的数据，所有数据解析完成!");
    }
}