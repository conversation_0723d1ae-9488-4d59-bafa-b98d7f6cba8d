package com.jlr.ecp.subscription.excel.listener.amap;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.amap.SearchAmaPExpireBatchExcel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class SearchAmaPExpireCheckListener extends AnalysisEventListener<SearchAmaPExpireBatchExcel> {


    private static final int BATCH_COUNT = 500;

    private static final String VIN_TITLE = "VIN";

    private List<SearchAmaPExpireBatchExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<SearchAmaPExpireBatchExcel>> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Boolean isExcelFormatError = false;

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(SearchAmaPExpireBatchExcel searchAmaPExpireBatchExcel, AnalysisContext analysisContext) {
        try {
            if (Boolean.FALSE.equals(isExcelFormatError)) {
                checkUploadExcelTitle();
            }
            dataList.add(searchAmaPExpireBatchExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.add(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("AMAP批量查询到期日解析Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("AMAP批量查询到期日处理最后剩余的数据");
        if (Boolean.FALSE.equals(isExcelFormatError)) {
            checkUploadExcelTitle();
        }
        try {
            if (CollUtil.isNotEmpty(dataList)) {
                allDataList.add(dataList);
            }
        } catch (Exception e) {
            log.info("AMAP批量查询到期日处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("AMAP批量查询到期日所有数据解析完成！");
    }


    /**
     * 检查上传的Excel文件中的
     *
     */
    public void checkUploadExcelTitle() {
        log.info("检查上传的Excel文件中的, headers:{}", headers);
        if (headers.isEmpty() || !VIN_TITLE.equals(headers.get(0).get(0))) {
            isExcelFormatError = true;
        }
    }

}
