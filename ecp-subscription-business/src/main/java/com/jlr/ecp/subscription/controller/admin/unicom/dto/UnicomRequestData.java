package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class UnicomRequestData {

    /**
     * iccid
     */
    private String iccid;

    /**
     * MSISDN 非必传
     */
    private String msisdn;

    /**
     * 请求唯一流水号
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 请求类型
     * order：订购
     * cancel：退订
     * change：变更
     */
    @JsonProperty("request_type")
    private String requestType;

    /**
     * 产品明细
     */
    @JsonProperty("product_book_info")
    private List<ProductBookInfo> productBookInfo;
}