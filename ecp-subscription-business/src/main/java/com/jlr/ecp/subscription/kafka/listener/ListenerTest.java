package com.jlr.ecp.subscription.kafka.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ListenerTest {
    public ListenerTest(){
        log.info("ListenerTest initialized");
    }

    @KafkaListener(topics = "test-topic", groupId = "ecp_subscription_my_group", properties = "max.poll.records:1")
    public void onMessage(String message)
    {

        if (log.isInfoEnabled()) {
            log.info("收到消息：" + message);
        }

    }
}
