package com.jlr.ecp.subscription.kafka.message.fufil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 服务激活成功订单状态修改
 *
 * <AUTHOR>
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FufilmentSuccessMessage {
    /**
     * 消息id，具有唯一性
     * */
    private String messageId;

    /**
     * 租户号
     * */
    private Long tenantId;

    /**
     *   VCS订单编码
     * */
    private String vcsOrderCode;


    /**
     * 是否需要修改订单状态
     */
    private Boolean updateStatus;


}
