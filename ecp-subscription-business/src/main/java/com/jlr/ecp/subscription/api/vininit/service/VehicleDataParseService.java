package com.jlr.ecp.subscription.api.vininit.service;

import com.jlr.ecp.subscription.api.remoteservice.RemoteOriginalDataParseBO;
import com.jlr.ecp.subscription.api.vininit.dto.parse.VinInitParseErrorDTO;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;

import java.util.List;
import java.util.Set;

/**
 * 车辆数据解析服务接口
 */
public interface VehicleDataParseService {
    
    /**
     * 解析原始数据
     *
     * @param records 原始数据记录
     * @return 解析结果，包含解析成功的数据和失败的ID集合
     */
    ParseResult parseRawData(List<RemoteOriginalDataDO> records);
    
    /**
     * 解析结果包装类
     */
    class ParseResult {
        private final List<RemoteOriginalDataParseBO> parsedData;
        private final List<VinInitParseErrorDTO> parseErrorDTO;
        
        public ParseResult(List<RemoteOriginalDataParseBO> parsedData, List<VinInitParseErrorDTO> parseErrorDTO) {
            this.parsedData = parsedData;
            this.parseErrorDTO = parseErrorDTO;
        }
        
        public List<RemoteOriginalDataParseBO> getParsedData() {
            return parsedData;
        }
        
        public List<VinInitParseErrorDTO> getErrorIds() {
            return parseErrorDTO;
        }

        public boolean hasErrors() {
            return parseErrorDTO != null && !parsedData.isEmpty();
        }

        public boolean hasParsedData() {
            return parsedData != null && !parsedData.isEmpty();
        }
    }
}
