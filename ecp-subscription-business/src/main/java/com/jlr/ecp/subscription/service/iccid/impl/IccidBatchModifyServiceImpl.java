package com.jlr.ecp.subscription.service.iccid.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidBatchModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.SendICCIDBatchModifyDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.amap.VerifyResultEnum;
import com.jlr.ecp.subscription.enums.iccid.ModifyDataSourceEnum;
import com.jlr.ecp.subscription.enums.iccid.ModifyStatusEnum;
import com.jlr.ecp.subscription.excel.listener.iccid.IccidBatchModifyCheckListener;
import com.jlr.ecp.subscription.excel.listener.iccid.IccidBatchModifyReadListener;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyExcel;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyResultExcel;
import com.jlr.ecp.subscription.excel.utils.IccidExcelUtil;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.properties.IccidProperties;
import com.jlr.ecp.subscription.service.iccid.IccidBatchModifyService;
import com.jlr.ecp.subscription.util.*;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.constant.Constants.LIMIT_ONE;
import static com.jlr.ecp.subscription.constant.Constants.UPLOAD_ICCID_EXCEL_SUCCESS_MSG;


@Service
@Slf4j
public class IccidBatchModifyServiceImpl implements IccidBatchModifyService {

    @Resource
    private IccidProperties iccidProperties;

    @Resource
    private Snowflake snowflake;

    @Resource
    private IccidModifyBatchRecordsDOMapper iccidBatchModifyRecordsMapper;

    @Resource
    private FileService fileService;

    @Resource
    private Redisson redisson;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private IccidModifyRecordsDOMapper iccidModifyRecordsMapper;

    @Resource
    private PermissionApi permissionApi;

    //发送批量修改 xxljobname iccidBatchModifyJob
    private static final String IC_MODIFY_JOB_NAME = "iccidBatchModifyJob";

    /**
     * 发送 ICCID批量修改请求
     * <p>
     * 1. 用户点击发起iccid修改
     * a.参考batchSendAppDCuRenewal 做分布式锁、前置校验等处理
     * b.更新batchRecordsDO为PROGRESS
     * c.读upload_file中url转为List<List<IccidBatchModifyExcel>>
     * init data（modify_records tips 不在ecp库中这里也更新），batch insert（500）
     * d.返回batch no + xxljob name
     *
     * @param sendICCIDBatchModifyDTO 发送参数
     * @return CommonResult<String> 返回操作结果的封装对象，包含成功、错误等状态及相应消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> batchModifyIccid(SendICCIDBatchModifyDTO sendICCIDBatchModifyDTO) {
        log.info("send ICCID批量修改, sendICCIDBatchModifyDTO:{}", sendICCIDBatchModifyDTO);
        RLock lock = redisson.getLock(Constants.REDIS_KEY.ICCID_BATCH_MODIFY_KEY);
        Long batchNo;
        try {
            // 1. 获取分布式锁
            if (!RLockUtil.tryLock(lock, 30, 60, TimeUnit.SECONDS)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }

            // 2. 检查是否有处理中的任务
            batchNo = Long.parseLong(sendICCIDBatchModifyDTO.getBatchNo());
            List<IccidModifyBatchRecordsDO> progressRecords = queryICCIDBatchByDealStatus(DealStatusEnum.PROGRESS.getStatus());
            if (CollUtil.isNotEmpty(progressRecords)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }

            // 3. 获取并更新批次记录状态
            IccidModifyBatchRecordsDO batchRecord = queryICCIDBatchByBatchNo(batchNo);
            log.info("发送 ICCID批量修改请求,查询到的批次记录为:{}", batchRecord);
            if (Objects.isNull(batchRecord)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
            }
            batchRecord.setDealStatus(DealStatusEnum.PROGRESS.getStatus());
            iccidBatchModifyRecordsMapper.updateById(batchRecord);

            // 4. 读取excel文件批量入库
            String operator = LoginUtil.getLoginUserName();
            log.info("发送 ICCID批量修改, operator:{}", operator);
            batchModifyAndInsertRecords(batchRecord, batchNo, operator);
            checkRecordsData(batchNo, batchRecord);
        } catch (Exception e) {
            log.info("发送 ICCID批量修改异常:{}", e.getMessage(), e);
            return CommonResult.error(new ErrorCode(1000001, e.getMessage()));
        } finally {
            RLockUtil.unlock(lock, 3);
        }
        return CommonResult.success(batchNo + ":" + IC_MODIFY_JOB_NAME);
    }

    private void checkRecordsData(Long batchNo, IccidModifyBatchRecordsDO batchRecord) {
        //查询上传的records有无需要job处理的，如果没有就直接完成。
        IccidModifyRecordsDO iccidModifyRecordsDO = iccidModifyRecordsMapper.selectOne(new LambdaQueryWrapperX<IccidModifyRecordsDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(IccidModifyRecordsDO::getModifyNo, batchNo)
                .eq(IccidModifyRecordsDO::getModifyStatus, ModifyStatusEnum.IN_PROGRESS.getStatus())
                .last(LIMIT_ONE));
        if(iccidModifyRecordsDO == null){
            //需改为处理完成
            batchRecord.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
            iccidBatchModifyRecordsMapper.updateById(batchRecord);
        }
    }

    private static final String EXCEL_FORMATTER = ".xlsx";

    /**
     * 批量修改并插入modify_records记录
     *
     * @param batchRecord 批处理记录实体类，包含批处理相关信息
     * @param batchNo     批号，用于标识特定的批处理
     * @param operator    操作员，用于记录操作人员信息
     */
    private void batchModifyAndInsertRecords(IccidModifyBatchRecordsDO batchRecord, Long batchNo, String operator) {
        try {
            IccidBatchModifyServiceImpl bean = applicationContext.getBean(getClass());
            byte[] content = fileService.getFileContentByAllPath(FILE_CODE, batchRecord.getUploadFile());
            List<List<IccidBatchModifyExcel>> batchModifyExcelList = doReadIccidModifyExcel(content);
            for (List<IccidBatchModifyExcel> batchModifyExcels : batchModifyExcelList) {
                bean.batchInsertIccidModifyRecords(batchModifyExcels, batchNo, operator);
            }
        } catch (Exception e) {
            log.info("批量修改并插入modify_records记录异常:{}", e.getMessage(), e);
        }
    }

    /**
     * 批量插入t_iccid_modify_records修改记录
     *
     * @param batchModifyExcels
     * @param batchNo
     * @param operator
     */
    public void batchInsertIccidModifyRecords(List<IccidBatchModifyExcel> batchModifyExcels, Long batchNo, String operator) {
        log.info("批量插入t_iccid_modify_records修改记录, batchModifyExcels:{}, batchNo:{}, operator:{}", batchModifyExcels, batchNo, operator);
        List<IccidModifyRecordsDO> modifyRecords = new ArrayList<>();

        // extract vin
        List<String> carVinList = batchModifyExcels.stream().filter(Objects::nonNull) // 过滤掉 null 对象
                .map(IccidBatchModifyExcel::getCarVin).collect(Collectors.toList());
        log.info("批量插入t_iccid_modify_records修改记录, vinList:{}", carVinList);

        // vinList 查询pivi_package 1.vinList都不存在这张表中 2.vinList部分存在
        List<PIVIPackageDO> piviPackageDOList = piviPackageDOMapper.selectList(new LambdaQueryWrapperX<PIVIPackageDO>().in(PIVIPackageDO::getVin, carVinList).eq(PIVIPackageDO::getIsDeleted, false));
        if (piviPackageDOList == null) {
            piviPackageDOList = Collections.emptyList();
        }
        log.info("批量插入t_iccid_modify_records修改记录, piviPackageDOList:{}", piviPackageDOList);
        Map<String, String> carVinMap = new HashMap<>();
        if (CollUtil.isNotEmpty(piviPackageDOList)) {
            carVinMap = piviPackageDOList.stream().collect(Collectors.toMap(PIVIPackageDO::getVin, PIVIPackageDO::getIccid));
        }
        log.info("批量插入t_iccid_modify_records修改记录, carVinMap:{}", carVinMap);

        for (IccidBatchModifyExcel excel : batchModifyExcels) {
            String vin = CarVinUtil.carVinToUpperCase(excel.getCarVin());
            IccidModifyRecordsDO record = new IccidModifyRecordsDO();
            record.setModifyNo(batchNo);
            record.setCarVin(vin);
            record.setModifyAfterIccid(excel.getNewIccid());
            record.setDataSource(ModifyDataSourceEnum.BATCH.getDataSource());

            if (CollUtil.isEmpty(carVinMap) || !carVinMap.containsKey(vin)) {
                record.setModifyBeforeIccid("-");
                record.setModifyStatus(ModifyStatusEnum.FAILURE.getStatus());
                record.setErrorDesc("在ECP数据库中未找到该VIN");
            } else {
                record.setModifyBeforeIccid(carVinMap.get(vin));
                record.setModifyStatus(ModifyStatusEnum.IN_PROGRESS.getStatus());
            }

            modifyRecords.add(record);
        }


        // 批量插入记录
        // 批量插入记录
        try {
            iccidModifyRecordsMapper.insertBatch(modifyRecords);
            log.info("批量插入t_iccid_modify_records修改记录成功, 记录数: {}", modifyRecords.size());
        } catch (Exception e) {
            log.error("批量插入t_iccid_modify_records修改记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取ICCID批量修改Excel文件，并将每页的数据转换为List<List<IccidBatchModifyExcel>>对象
     *
     * @param bytes Excel文件的字节数组
     * @return 解析后的Excel数据列表
     */
    private List<List<IccidBatchModifyExcel>> doReadIccidModifyExcel(byte[] bytes) {
        List<List<IccidBatchModifyExcel>> resp = new ArrayList<>();
        if (bytes == null || bytes.length < 1) {
            log.info("读取ICCID批量修改Excel文件, bytes为空");
            return resp;
        }
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "IccidBatchModifyReadS3Excel" + System.currentTimeMillis() + EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("读取ICCID批量修改Excel文件, filePath:{}", filePath);

        IccidBatchModifyReadListener iccidReadListener = new IccidBatchModifyReadListener();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(bytes);
            outputStream.flush();
            EasyExcel.read(fileName, IccidBatchModifyExcel.class, iccidReadListener).sheet().doRead();
            log.info("读取ICCID批量修改Excel文件, iccidReadListener读取数据的数量:{}", iccidReadListener.getAllList().size());
            resp = iccidReadListener.getAllList();
            log.info("读取ICCID批量修改Excel文件, filePath:{}", filePath);
        } catch (Exception e) {
            log.info("读取ICCID批量修改Excel文件, 解析excel文件异常:{}", e.getMessage());
        } finally {
            FileUtil.del(fileName);
        }
        return resp;

    }


    /**
     * 根据批次号查询ICCID批量修改记录
     *
     * @param batchNo 批次号
     * @return 如果找到对应的记录, 则返回该记录; 如果未找到或有多条记录,否则返回null
     */
    private IccidModifyBatchRecordsDO queryICCIDBatchByBatchNo(Long batchNo) {
        LambdaQueryWrapper<IccidModifyBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyBatchRecordsDO::getBatchNo, batchNo)
                .eq(IccidModifyBatchRecordsDO::getIsDeleted, false);
        List<IccidModifyBatchRecordsDO> resp = iccidBatchModifyRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次编号查询IccidModifyBatchRecords对象, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    /**
     * 根据处理状态查询ICCID批量修改记录
     *
     * @param dealStatus 处理状态
     * @return 批量修改记录列表
     */
    private List<IccidModifyBatchRecordsDO> queryICCIDBatchByDealStatus(Integer dealStatus) {
        LambdaQueryWrapper<IccidModifyBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyBatchRecordsDO::getDealStatus, dealStatus)
                .eq(IccidModifyBatchRecordsDO::getIsDeleted, false);
        return iccidBatchModifyRecordsMapper.selectList(queryWrapper);
    }

    @Override
    public PageResult<IccidBatchModifyPageVO> queryBatchModifyPageList(IccidBatchModifyPageDTO pageDto) {
        log.info("查询ICCID批量修改日志分页列表, pageDto:{}", pageDto);
        Page<IccidModifyBatchRecordsDO> pageResp = queryIccidBatchModifyPage(pageDto);
        List<IccidModifyBatchRecordsDO> batchRecordsList = pageResp.getRecords();
        List<IccidBatchModifyPageVO> pageVOList = buildIccidBatchModifyPageVOList(batchRecordsList);
        return new PageResult<>(pageVOList, pageResp.getTotal());
    }

    /**
     * 构建ICCID批量修改页面VO列表
     *
     * @param batchRecordsList IccidModifyBatchRecordsDO对象列表，记录了批量修改的信息
     * @return 返回一个IccidBatchModifyPageVO对象列表，用于展示批量修改页面的信息
     */
    private List<IccidBatchModifyPageVO> buildIccidBatchModifyPageVOList(List<IccidModifyBatchRecordsDO> batchRecordsList) {
        List<IccidBatchModifyPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(batchRecordsList)) {
            return resp;
        }
        for (IccidModifyBatchRecordsDO records : batchRecordsList) {
            resp.add(buildIccidBatchModifyPageVO(records));
        }
        return resp;
    }

    /**
     * 构建 IccidBatchModifyPageVO 对象
     *
     * @param batchRecord 批量修改的记录对象，包含批量修改的相关信息
     * @return 如果 batchRecord 不为 null，则返回转换后的 IccidBatchModifyPageVO 对象；否则返回 null
     */
    private IccidBatchModifyPageVO buildIccidBatchModifyPageVO(IccidModifyBatchRecordsDO batchRecord) {
        if (ObjectUtil.isNull(batchRecord)) {
            return null;
        }
        return IccidBatchModifyPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(batchRecord.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(batchRecord.getOperator())
                .checkResultStatus(batchRecord.getVerifyResult())
                .checkResultDesc(VerifyResultEnum.getDescByCode(batchRecord.getVerifyResult()))
                .dealStatus(batchRecord.getDealStatus())
                .batchNo(batchRecord.getBatchNo())
                .errorDetailPath(batchRecord.getVerifyResultFile())
                .build();
    }

    /**
     * 查询ICCID批量修改日志分页列表
     *
     * @param pageDTO 分页查询参数对象，包含分页信息和排序信息
     * @return 返回分页列表对象，包含查询结果和分页信息
     */
    private Page<IccidModifyBatchRecordsDO> queryIccidBatchModifyPage(IccidBatchModifyPageDTO pageDTO) {
        Page<IccidModifyBatchRecordsDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<IccidModifyBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyBatchRecordsDO::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(IccidModifyBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(IccidModifyBatchRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(IccidModifyBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(IccidModifyBatchRecordsDO::getId);
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }
        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(IccidModifyBatchRecordsDO::getOperator, userName);
            log.info("查询ICCID批量修改日志分页列表,user:{}, pageDTO:{}", userName, pageDTO);
        }
        return iccidBatchModifyRecordsMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public String getIccidTemplateUrl() {
        return iccidProperties.getModifyExcelUrl();
    }

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024;

    private static final String FILE_CODE = "AWS_S3_FILE";

    /**
     * 上传ICCID批量修改的Excel文件
     *
     * @param multipartFile
     * @return
     */
    @Override
    public CommonResult<IccidBatchModifyUploadVO> uploadIccidExcelModify(MultipartFile multipartFile) {
        log.info("上传ICCID批量修改的Excel文件, multipartFile:{}", multipartFile.toString());

        // a.检查文件大小
        if (multipartFile.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }

        // 解析Excel文件
        long startTime = System.currentTimeMillis();
        IccidBatchModifyCheckListener modifyCheckListener = new IccidBatchModifyCheckListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), IccidBatchModifyExcel.class,
                    modifyCheckListener).sheet().doRead();
        } catch (Exception e) {
            log.info("上传ICCID批量修改的Excel文件异常:{}", e.getMessage());
        }
        long endTime = System.currentTimeMillis();
        log.info("上传ICCID批量修改的excel文件解析完成，花费时间:{}毫秒", (endTime - startTime));

        // b.校验文件格式是否正确
        if (Boolean.TRUE.equals(modifyCheckListener.getFormatError())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }

        // c.校验文件是否为空
        if (CollUtil.isEmpty(modifyCheckListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }

        // d.校验excel中数据行数除表头外不超过500行
        if (modifyCheckListener.getAllDataList().size() > 500) {
            return CommonResult.error(ErrorCodeConstants.UPLOAD_FILE_LIMIT);
        }

        // 添加ICCID批量修改记录
        addIccidBatchModifyRecords(modifyCheckListener);

        IccidBatchModifyUploadVO modifyUploadVO = new IccidBatchModifyUploadVO();
        modifyUploadVO.setType("success");
        modifyUploadVO.setMsg(UPLOAD_ICCID_EXCEL_SUCCESS_MSG);
        return CommonResult.success(modifyUploadVO);
    }

    /**
     * 添加ICCID批量修改记录
     *
     * @param modifyCheckListener 修改检查监听器，用于获取修改检查的相关数据和结果
     */
    private void addIccidBatchModifyRecords(IccidBatchModifyCheckListener modifyCheckListener) {
        IccidModifyBatchRecordsDO iccidBatchModifyRecords = new IccidModifyBatchRecordsDO();
        String uploadS3IccidSourcePath = uploadIccidModifySourceToS3File(modifyCheckListener.getAllDataList());
        iccidBatchModifyRecords.setBatchNo(snowflake.nextId());
        iccidBatchModifyRecords.setUploadFile(uploadS3IccidSourcePath);
        if (Boolean.FALSE.equals(modifyCheckListener.getCheckResult())) {
            String uploadIccidResultPath = uploadIccidModifyResultToS3File(modifyCheckListener.getResultExcelList());
            iccidBatchModifyRecords.setVerifyResult(VerifyResultEnum.FAIL.getCode());
            iccidBatchModifyRecords.setVerifyResultFile(uploadIccidResultPath);
        } else {
            iccidBatchModifyRecords.setVerifyResult(VerifyResultEnum.SUCCESS.getCode());
        }
        iccidBatchModifyRecords.setDealStatus(DealStatusEnum.WAITED.getStatus());
        iccidBatchModifyRecords.setOperator(LoginUtil.getLoginUserName());
        iccidBatchModifyRecordsMapper.insert(iccidBatchModifyRecords);
    }

    /**
     * 将ICCID批量修改的Excel源文件上传到S3
     *
     * @param iccidBatchModifyExcelList 包含ICCID批量修改数据的列表，用于生成Excel文件
     * @return 返回上传到S3的文件路径，如果生成的本地文件为空或在上传过程中发生异常，则返回空字符串
     */
    private String uploadIccidModifySourceToS3File(List<IccidBatchModifyExcel> iccidBatchModifyExcelList) {
        String uploadS3FilePath = "";
        File uploadS3TempSourceExcel = null;
        try {
            uploadS3TempSourceExcel = IccidExcelUtil.iccidBatchModifyWriteSourceFile(iccidBatchModifyExcelList);
            if (Objects.isNull(uploadS3TempSourceExcel)) {
                log.info("ICCID批量修改Excel源文件上传至S3, 生成本地临时文件为空");
                return uploadS3FilePath;
            }
            uploadS3FilePath = fileService.createFile(null, "IccidBatchModifyExcel" + File.separator
                    + System.currentTimeMillis() + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempSourceExcel), FILE_CODE);
            log.info("ICCID批量修改Excel源文件上传至S3, 本地文件原始文件路径:{}", uploadS3TempSourceExcel.getPath());
        } catch (Throwable e) {
            log.info("ICCID批量修改Excel源文件上传至S3异常:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempSourceExcel)) {
                FileUtil.del(uploadS3TempSourceExcel);
                log.info("ICCID批量修改Excel源文件上传至S3, 成功删除本地临时文件");
            }
        }
        return uploadS3FilePath;
    }

    /**
     * 将ICCID批量修改结果Excel文件上传到S3存储
     *
     * @param resultExcels 包含ICCID批量修改结果的列表
     * @return 上传到S3的文件路径，如果上传失败则返回空字符串
     */
    private String uploadIccidModifyResultToS3File(List<IccidBatchModifyResultExcel> resultExcels) {
        String uploadS3ResultFilePath = "";
        File uploadS3TempResultExcel = null;
        try {
            uploadS3TempResultExcel = IccidExcelUtil.iccidBatchModifyWriteResultFile(resultExcels);
            if (Objects.isNull(uploadS3TempResultExcel)) {
                log.info("ICCID批量修改结果Excel文件上传至S3存储, 生成本地临时文件为空");
                return uploadS3ResultFilePath;
            }
            String fileName = "ICCID批量修改校验结果" + System.currentTimeMillis();
            uploadS3ResultFilePath = fileService.createFile(null, "IccidBatchModifyResultExcel" + File.separator +
                    fileName + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempResultExcel), FILE_CODE);
            log.info("ICCID批量修改结果Excel文件上传至S3存储, 本地文件原始文件路径:{}", uploadS3TempResultExcel.getPath());
        } catch (Throwable e) {
            log.info("ICCID批量修改结果Excel文件上传至S3存储:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempResultExcel)) {
                FileUtil.del(uploadS3TempResultExcel);
                log.info("ICCID批量修改结果Excel文件上传至S3存储, 成功删除本地临时文件");
            }
        }
        return uploadS3ResultFilePath;
    }
}
