package com.jlr.ecp.subscription.dal.dataobject.consumer;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * t_consumer_incontrol(t_consumer_incontrol)实体类
 *
 * <AUTHOR>
 * @since 2023-12-20 14:42:17
 * @description 由 Mybatisplus Code Generator 创建
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_consumer_incontrol")
public class ConsumerIncontrolDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
	private Long id;
    /**
     * 用户编码;用户编码
     */
    private String consumerCode;
    /**
     * incontrol账号;incontrol账号，唯一索引
     */
    private String incontrolId;
    /**
     * 绑定时间;绑定时间
     */
    private LocalDateTime bindTime;
    /**
     * 绑定渠道，LAN:路虎，JAG：捷豹
     */
    private String bindChannel;
    /**
     * 解绑时间;解绑时间
     */
    private LocalDateTime unbindTime;
    /**
     * 绑定状态;绑定状态 0：已解绑 1已绑定
     */
    private Integer bindStatus;

}