package com.jlr.ecp.subscription.api.vininit.model;

import lombok.Data;

import java.util.Map;

/**
 * 处理结果包装类
 */
@Data
public class ProcessResult {
    
    /**
     * 成功处理数量
     */
    private final int successCount;
    
    /**
     * 失败处理数量
     */
    private final int failCount;
    
    /**
     * 总处理数量
     */
    private final int totalCount;
    
    public ProcessResult(int successCount, int failCount) {
        this.successCount = successCount;
        this.failCount = failCount;
        this.totalCount = successCount + failCount;
    }
    
    /**
     * 创建处理结果
     *
     * @param success 成功数量
     * @param fail    失败数量
     * @return 处理结果
     */
    public static ProcessResult of(int success, int fail) {
        return new ProcessResult(success, fail);
    }
    
    /**
     * 创建空结果
     *
     * @return 空处理结果
     */
    public static ProcessResult empty() {
        return new ProcessResult(0, 0);
    }
    
    /**
     * 转换为Map格式
     *
     * @return Map<成功数量, 失败数量>
     */
    public Map<Integer, Integer> toMap() {
        return Map.of(successCount, failCount);
    }
    
    /**
     * 合并处理结果
     *
     * @param other 其他处理结果
     * @return 合并后的结果
     */
    public ProcessResult merge(ProcessResult other) {
        return new ProcessResult(
            this.successCount + other.successCount,
            this.failCount + other.failCount
        );
    }
    
    /**
     * 是否有处理数据
     *
     * @return true如果有处理数据
     */
    public boolean hasProcessed() {
        return totalCount > 0;
    }
    
    /**
     * 是否全部成功
     *
     * @return true如果全部成功
     */
    public boolean isAllSuccess() {
        return totalCount > 0 && failCount == 0;
    }
    
    /**
     * 获取成功率
     *
     * @return 成功率（0-1之间）
     */
    public double getSuccessRate() {
        return totalCount == 0 ? 0.0 : (double) successCount / totalCount;
    }
    
    @Override
    public String toString() {
        return String.format("ProcessResult{total=%d, success=%d, fail=%d, successRate=%.2f%%}", 
            totalCount, successCount, failCount, getSuccessRate() * 100);
    }
}
