package com.jlr.ecp.subscription.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CarVinUtil {
    // 静态成员变量存储预先编译的正则表达式模式
    private static final Pattern VIN_PATTERN = Pattern.compile("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{17}$");

    /**
     * 检查车辆识别码（VIN）是否有效。
     *
     * @param carVin 车辆识别码
     * @return 如果VIN有效返回true，否则返回false
     */
    public static boolean checkVinFormat(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return false;
        }
        Matcher matcher = VIN_PATTERN.matcher(carVin);
        return matcher.matches();
    }

    /**
     * 将汽车VIN码转换为大写字符串
     *
     * @param carVin 汽车VIN码，VIN码是一种唯一标识车辆的代码
     * @return 转换为大写后的VIN码字符串如果输入的VIN码为空或空白，则返回空字符串
     */
    public static String carVinToUpperCase(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return "";
        }
        return carVin.toUpperCase();
    }

}
