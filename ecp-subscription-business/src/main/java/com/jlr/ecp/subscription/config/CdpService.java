package com.jlr.ecp.subscription.config;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResponse;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResponseVO;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResult;
import com.jlr.ecp.subscription.api.cdp.vo.CdpResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Slf4j
public class CdpService {

    @Value("${cdp.domain}")
    String domain;

    @Value("${cdp.ak}")
    String ak;

    @Value("${cdp.sk}")
    String sk ;

    private Sign sign;

    /**
     * 最近绑车人的属性ID：3782
     * */
    private static final String RECENT_CAR_HITTER = "3782";

    private static final String ACTION_VALUE = "QueryOpenPlatformOpenApi";

    private static final String API_ACTION_KEY = "ApiAction";

    private static final String API_VERSION_KEY = "ApiVersion";

    private static final String API_VERSION_VALUE_2023_02_10 = "2023-02-10";

    private static final Long ZERO = 0L;

    // @PostConstruct
    public void init() {
        if (domain == null || ak == null || sk == null) {
            throw new RuntimeException("cdp configuration is missing");
        }
        sign = new Sign(domain, ak, sk);
    }

    /**
     * 接口版本号
     */
    private static final String VERSION = "2021-12-16";



    @SuppressWarnings("all")
    public List<CdpResultVO> callApi(List<String> vinList)  {
        List<CdpResultVO> cdp= new ArrayList<>();
        if(CollUtil.isNotEmpty(vinList)){
            for (String vin : vinList) {
                //跨主体转换策略ID。跨主体转换时需要指定策略ID，非跨主体转换不需要传。跨主体转换，源ID和目标ID必须有一个是 baseid 类型，
                //比如从车的 vin 转换到车主手机号，需要先讲vin转换为 baseid_2,之后通过 baseid_2 查找车主的 phone。
                //也可以先通过 vin 查车主的 base_id ，之后通过 base_id 找到车主的手机号。
                // 通过 vin 找车的 baseid_2
                try {
                    String idMapping = getIdMapping(vin, "vin", "baseid_2", null);
                    CdpResult stepOne = JSON.parseObject(idMapping,CdpResult.class);
                    if(stepOne.getCode().equals(0)&& StringUtils.isNotBlank(stepOne.getData())){
                        // 跨主体转换，通过车的 baseid_2 找到绑车人的手机号。
                        String response = getIdMapping(stepOne.getData(), "baseid_2", "phone_appuser_mobile", 31);
                        CdpResult responseCdp = JSON.parseObject(response,CdpResult.class);
                        //请求成功之后
                        if(responseCdp.getCode().equals(0)){
                            //组装数据
                            CdpResultVO cdpResultVO = new CdpResultVO();
                            cdpResultVO.setVin(vin);
                            cdpResultVO.setMobile(responseCdp.getData());
                            cdpResultVO.setAllMobile(responseCdp.getAllData());
                            cdp.add(cdpResultVO);
                        }
                    }
                } catch (Exception e) {
                    log.error("cpd请求错误 error:{}",e.getMessage());
                }
            }
        }
        return cdp;
    }

    /**
     * 使用vin获取手机号码
     * @param vinList vin列表
     * @return List<CdpResultVO>
     * */
    public List<CdpResponseVO> callApiNew(List<String> vinList)  {
        log.info("使用vin获取手机号码, vinList:{}", vinList);
        List<CdpResponseVO> cdp= new ArrayList<>();
        if(CollUtil.isEmpty(vinList)){
            return new ArrayList<>();
        }
        for (String vin : vinList) {
            CdpResponseVO cdpResponseVO = getCdpResponseVo(vin);
            if (Objects.nonNull(cdpResponseVO)) {
                cdp.add(cdpResponseVO);
            }
        }
        return cdp;
    }

    /**
     *  获取CdpResponseVO
     *  @param vin carVin号码
     *  @return CdpResponseVO
     * */
    public CdpResponseVO getCdpResponseVo(String vin) {
        String idMapping = getIdMapping(vin);
        if (StringUtils.isBlank(idMapping)) {
            return null;
        }
        CdpResponse cdpResponse = JSON.parseObject(idMapping, CdpResponse.class);
        CdpResponseVO cdpResponseVO = new CdpResponseVO();
        if (!ZERO.equals(cdpResponse.getCode()) || CollectionUtils.isEmpty(cdpResponse.getProperties())) {
            log.warn("cdp获取最近帮车人电话号码没有查找到, carVin:{}, cdpResponse:{}", vin, cdpResponse);
            return null;
        }
        //请求成功之后, 组装数据
        cdpResponseVO.setVin(vin);
        Map<String, String> map = cdpResponse.getProperties();
        if (!map.containsKey(RECENT_CAR_HITTER)) {
            log.info("当前没有包含最近绑车人id, vin:{}, cdpResponse:{}", vin, cdpResponse);
            return null;
        } else {
            cdpResponseVO.setMobile(map.get(RECENT_CAR_HITTER).replace("\"", ""));
        }
        return cdpResponseVO;
    }

    /**
     *   获取idMapping  最多重试3次
     *   @param vin carVin号码
     *   @return String
     * */
    public String getIdMapping(String vin) {
        String idMapping = null;
        try {
            idMapping = getUserProfileWithPrivacy(2, vin,"vin", RECENT_CAR_HITTER, null);
        } catch (Exception e) {
            int sleepTime = 5000;
            for (int cnt = 1; cnt <= 3; cnt++) {
                try {
                    Thread.sleep(sleepTime);
                    idMapping = getUserProfileWithPrivacy(2, vin,"vin", RECENT_CAR_HITTER, null);
                    sleepTime *= 2;
                } catch (InterruptedException exception) {
                    Thread.currentThread().interrupt();
                } catch (Exception ex) {
                    log.error("第{}次重试cdp, carVin:{}, 错误原因:", cnt, vin, ex);
                }
            }
        }
        return idMapping;
    }



    /**
     * 1. 按租户获取ID类型
     * 接口官网地址：https://www.volcengine.com/docs/7139/1154986
     *
     * @throws Exception
     */
    public void listIdTypeMetaByTenant() throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "ListIdTypeMetaByTenant");
        queryMap.put(API_VERSION_KEY, API_VERSION_VALUE_2023_02_10);
        sign.doRequest("GET", queryMap, null, ACTION_VALUE, VERSION, 2);
    }

    /**
     * 获取资产输出任务列表
     *
     * @throws Exception
     */
    public void getFileDateAssetTaskList() throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "GetFileDateAssetTaskList");
        queryMap.put(API_VERSION_KEY, API_VERSION_VALUE_2023_02_10);
        sign.doRequest("GET", queryMap, null, ACTION_VALUE, VERSION, 2);
    }

    /**
     * 下载分群明细
     *
     * @param taskId
     * @param filePath
     * @throws Exception
     */
    public  void downloadFileDateAsset(Integer taskId, String filePath) throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "DownloadFileDateAsset");
        queryMap.put(API_VERSION_KEY, "2023-06-20");
        queryMap.put("taskId", String.valueOf(taskId));
        queryMap.put("filePath", filePath);
        sign.doRequest("GET", queryMap, null, ACTION_VALUE, VERSION, 2);
    }


    /**
     * 获取标签列表接口
     * @throws Exception
     */
    public void postLabels() throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "PostLabels");
        queryMap.put(API_VERSION_KEY, "2023-07-03");
        queryMap.put("subjectId", String.valueOf(1));
        queryMap.put("type", "label");
        String body = "{\"ids\":[\"151\"]}";
        sign.doRequest("POST", queryMap, body, ACTION_VALUE, VERSION, 2);
    }

    /**
     * 获取标签树
     *
     * @throws Exception
     */
    public void getLabelTree() throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "GetLabelTree");
        queryMap.put("subjectId", "1");
        queryMap.put(API_VERSION_KEY, API_VERSION_VALUE_2023_02_10);
        sign.doRequest("GET", queryMap, null, ACTION_VALUE, VERSION, 2);
    }

    /**
     * 在线服务接口，2.1 获取用户属性/标签信息
     * 接口官网地址：https://www.volcengine.com/docs/7139/1154992#_2-1-%E8%8E%B7%E5%8F%96%E7%94%A8%E6%88%B7%E5%B1%9E%E6%80%A7-%E6%A0%87%E7%AD%BE%E4%BF%A1%E6%81%AF
     *
     * @throws Exception
     */
    public void getUserProfileWithPrivacy() throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "getUserProfileWithPrivacy");
        queryMap.put(API_VERSION_KEY, API_VERSION_VALUE_2023_02_10);
        String body = "{\"project\":\"2\",\"profile_request\":{\"id\":\"18768764777\",\"id_type\":\"phone_all\",\"tags\":[151,159,160,163,333,450,480,484,485,486,500,509,518,529,530,532,534,544,549,550,556,565,575,578,579,716,727,728,729,730,827,829,830,831,834,835]}}";
        sign.doRequest("POST", queryMap, body, ACTION_VALUE, VERSION, 2);
    }


    /**
     * IDMapping 转换
     * @param id  需要转换的ID
     * @param sourceId  源ID类型
     * @param targetId  目标ID类型
     * @param strategyID  跨主体转换策略ID。跨主体转换时需要指定策略ID，非跨主体转换不需要传。跨主体转换，源ID和目标ID必须有一个是 baseid 类型，
     *                    比如从车的 vin 转换到车主手机号，需要先讲vin转换为 baseid_2,之后通过 baseid_2 查找车主的 phone。
     *                    也可以先通过 vin 查车主的 base_id ，之后通过 base_id 找到车主的手机号。
     *                    CDP-1.22 版本以后支持直接转换。
     * @throws Exception
     */
    public String getIdMapping(String id ,String sourceId,String targetId,Integer strategyID) throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "GetIdMapping");
        queryMap.put(API_VERSION_KEY, API_VERSION_VALUE_2023_02_10);
        String body = null ;
        if (null == strategyID){
            body = String.format("{\"id\":\"%s\",\"source_entity_name\":\"%s\",\"target_entity_name\":\"%s\"}",id,
                    sourceId,targetId);
        }else{
            body = String.format("{\"id\":\"%s\",\"source_entity_name\":\"%s\",\"target_entity_name\":\"%s\"," +
                    "\"interentity_strategy_id\":\"%s\"}",id,sourceId,targetId,strategyID);
        }
        String response = sign.doRequest("POST", queryMap, body, ACTION_VALUE, VERSION, 2);
        return  response;
    }

    /**
     * 在线服务接口
     * 2.1 获取用户属性/标签信息
     * 接口官网地址：https://www.volcengine.com/docs/7139/1154992#_2-1-%E8%8E%B7%E5%8F%96%E7%94%A8%E6%88%B7%E5%B1%9E%E6%80%A7-%E6%A0%87%E7%AD%BE%E4%BF%A1%E6%81%AF
     * @param projectId  项目ID
     * @param id  查询标签所使用的用户ID
     * @param idType 查询所使用的ID类型
     * @param propertyIds 用户属性ID列表，不查属性值的时候可以传 null
     * @param tagIds 标签ID列表，不查标签值的时候可以传 null
     *               属性列表和标签列表，必须有一个是有值的，不能都传null。
     * @throws Exception
     */
    public String getUserProfileWithPrivacy(int projectId, String id, String idType, String propertyIds, String tagIds)
            throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "getUserProfileWithPrivacy");
        queryMap.put(API_VERSION_KEY, API_VERSION_VALUE_2023_02_10);
        String body = null ;
        if(null == propertyIds && null == tagIds){
            throw new RuntimeException("标签ID列表和属性ID列表不能都为空");
        }else if (null == propertyIds) {
            body = String.format("{\"project\":\"%s\",\"profile_request\":{\"id\":\"%s\",\"id_type\":\"%s\",\"tags\":[%s]}}",
                    projectId, id, idType, tagIds);
        }else if (null == tagIds) {
            body = String.format("{\"project\":\"%s\",\"profile_request\":{\"id\":\"%s\",\"id_type\":\"%s\",\"properties\":[%s]}}",
                    projectId, id, idType, propertyIds);
        }else {
            body = String.format("{\"project\":\"%s\",\"profile_request\":{\"id\":\"%s\",\"id_type\":\"%s\",\"properties\":[%s],\"tags\":[%s]}}",
                    projectId, id, idType, propertyIds,tagIds);
        }
        String cdpResp = sign.doRequest("POST", queryMap, body, ACTION_VALUE, VERSION, 2);
        log.info("请求cdp, queryMap:{}, body:{}, action:{}, version:{}, projectId:{}, cdpResp:{}", queryMap, body,
                ACTION_VALUE, VERSION, 2, cdpResp);
        return cdpResp;
    }

    /**
     *
     * @throws Exception
     */
    public  void getResourceTable() throws Exception {
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put(API_ACTION_KEY, "GetResourceTable");
        queryMap.put(API_VERSION_KEY, "2023-08-08");
        String body = "{\"resourceType\":\"label\",\"pageNum\":1,\"pageSize\":10}" ;
        sign.doRequest("POST", queryMap, body, ACTION_VALUE, VERSION, 1);
    }




}
