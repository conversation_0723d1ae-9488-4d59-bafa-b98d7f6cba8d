package com.jlr.ecp.subscription.enums.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AmaPQueryStatusEnum {
    PROGRESS(0,"查询中"),
    SUCCESS(1,"查询成功"),
    FAIL(2,"查询失败");

    private final Integer queryStatus;

    private final String queryDesc;

    /**
     * 根据状态码获取描述
     *
     * @param status 状态码，用于查找对应的描述
     * @return 与给定状态码对应的描述，如果没有找到匹配项，则为一个空字符串
     */
    public static String getDescByStatus(Integer status) {
        for (AmaPQueryStatusEnum queryStatusEnum : AmaPQueryStatusEnum.values()) {
            if (queryStatusEnum.getQueryStatus().equals(status)) {
                return queryStatusEnum.getQueryDesc();
            }
        }
        return "";
    }
}
