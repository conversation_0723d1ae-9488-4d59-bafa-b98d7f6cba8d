package com.jlr.ecp.subscription.controller.admin.dto.seriesMapping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 -  车型展示名称配置编辑DTO ")
@ToString(callSuper = true)
public class SeriesMappingDataUpdateDTO  {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "车型展示名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型展示名称不能为空")
    private String seriesName;

    @Schema(description = "House Of Brand", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "House Of Brand不能为空")
    private String brandNameView;


    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Integer revision;
}
