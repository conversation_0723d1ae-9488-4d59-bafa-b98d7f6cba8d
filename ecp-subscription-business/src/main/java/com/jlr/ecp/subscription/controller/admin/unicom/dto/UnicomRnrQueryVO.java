package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UnicomRnrQueryVO {



    @Schema(description = "VIN")
    private String carVin;


    @Schema(description = "查询状态")
    private Integer queryStatus;


    @Schema(description = "查询状态文本")
    private String queryStatusTxt;

    @Schema(description = "iccid")
    private String iccid;

    @Schema(description = "实名制状态")
    private Integer realNameFlag;

    @Schema(description = "实名制状态文本")
    private String realNameFlagTxt;

    @Schema(description = "卡状态")
    private String cardState;

    @Schema(description = "卡状态文本")
    private String cardStateTxt;

    @Schema(description = "最新实名时间")
    private String realNameTime;
    /**
     * 查询失败原因;失败原因 1：VIN格式校验错误  2：ICCID查询失败  3：联通查询失败
     */
    @Schema(description = "查询失败原因")
    private Integer failedType;

    @Schema(description = "查询失败原因文本")
    private String failedTypeTxt;

    @Schema(description = "失败原因")
    private String errorDesc;
}

