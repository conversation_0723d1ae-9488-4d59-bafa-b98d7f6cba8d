package com.jlr.ecp.subscription.service.remotepackage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.excel.core.util.ExcelUtils;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageCreateDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageImportExcelDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackagePageReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.BatchUploadRespVO;
import com.jlr.ecp.subscription.api.remotepackage.vo.RemotePackageListRespVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.RemotePackageDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.RemotePackageDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.remotepackage.SortOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;

/**
 * <AUTHOR>
 * @description 针对表【t_remote_package(t_remote_package)】的数据库操作Service实现
 * @createDate 2023-12-08 01:29:01
 */
@Service
@Validated
@Slf4j
public class RemotePackageDOServiceImpl extends ServiceImpl<RemotePackageDOMapper, RemotePackageDO>
        implements RemotePackageDOService {

    @Resource
    private RemotePackageDOMapper remotePackageDOMapper;

    private void checkPackageCodeExist(String packageCode) {
        RemotePackageDO existingPackage = remotePackageDOMapper.selectOne(
                new LambdaQueryWrapperX<RemotePackageDO>()
                        .eq(RemotePackageDO::getPackageCode, packageCode)
                        .eq(RemotePackageDO::getIsDeleted, false)
                        .last("LIMIT 1")
        );
        if (Objects.nonNull(existingPackage)) {
            // 如果服务包编码已存在，则抛出异常
            throw exception(ErrorCodeConstants.PACKAGE_CODE_ALREADY_EXIST);
        }
    }

    @Override
    public Boolean createRemotePackage(RemotePackageCreateDTO createDTO) {
        // 检查服务包编码是否存在
        checkPackageCodeExist(createDTO.getPackageCode());

        // 创建 RemotePackageDO 对象并设置属性
        RemotePackageDO remotePackageDO = new RemotePackageDO();
        remotePackageDO.setCarSystemModel(createDTO.getCarSystemModel());
        remotePackageDO.setPackageCode(createDTO.getPackageCode());
        remotePackageDO.setCreatedUser(WebFrameworkUtils.getLoginUserName());
        //TODO: 设置其他属性，如 created_user, created_by 等
        // 示例租户ID，应根据实际情况获取
        remotePackageDO.setTenantId(1);
//        remotePackageDO.setIsDeleted(false);

        // 插入到数据库
        int insertCount = remotePackageDOMapper.insert(remotePackageDO);
        return insertCount > 0;
    }


    @Override
    public PageResult<RemotePackageListRespVO> getPage(RemotePackagePageReqDTO dto) {
        // 构建分页
        Page<RemotePackageDO> page = new Page<>(dto.getPageNo(), dto.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<RemotePackageDO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(RemotePackageDO::getIsDeleted, false);

        // 根据创建时间排序，默认按创建时间降序排序
        boolean isAscending = false;
        if (StringUtils.isNotBlank(dto.getCreatedTimeSort())) {
            // 如果传入了排序参数，则使用传入的参数
            isAscending = SortOrder.ASCENDING.getCode().equals(dto.getCreatedTimeSort().toLowerCase());
            queryWrapper.orderBy(true, isAscending, RemotePackageDO::getCreatedTime);
        }else{
            queryWrapper.orderBy(true, isAscending, RemotePackageDO::getCreatedTime);
        }
        queryWrapper.orderBy(true, isAscending, RemotePackageDO::getId);
        // 执行查询
        page = remotePackageDOMapper.selectPage(page, queryWrapper);

        // 转换为VO
        List<RemotePackageListRespVO> respVOList = page.getRecords().stream().map(remotePackageDO -> {
            RemotePackageListRespVO respVO = new RemotePackageListRespVO();
            BeanUtils.copyProperties(remotePackageDO, respVO);
            return respVO;
        }).collect(Collectors.toList());

        // 构建返回结果
        return new PageResult<>(respVOList, page.getTotal());
    }

    @Override
    public Boolean deleteByPackageCode(String packageCode) {
        // 首先检查 packageCode 是否存在
        RemotePackageDO existingPackage = remotePackageDOMapper.selectOne(
                new LambdaQueryWrapper<RemotePackageDO>()
                        .eq(RemotePackageDO::getPackageCode, packageCode)
                        .eq(RemotePackageDO::getIsDeleted, false));

        // 如果不存在，返回 false 或抛出异常
        if (Objects.isNull(existingPackage)) {
            log.info("Package with code '{}' not found. Deletion aborted.", packageCode);
            throw exception(ErrorCodeConstants.PACKAGE_NOT_FOUND);
        }

        // 如果存在，执行逻辑删除
//        existingPackage.setIsDeleted(true);
        int updatedRows = remotePackageDOMapper.deleteById(existingPackage);
        return updatedRows > 0;
    }

    @Override
    public CommonResult<BatchUploadRespVO> processBatchUpload(MultipartFile file, String carSystemModel) {
        try {
            // 读取Excel文件
            List<String> excelPackageCodes = ExcelUtils.read(file, RemotePackageImportExcelDTO.class)
                    .stream()
                    .map(RemotePackageImportExcelDTO::getPackageCode)
                    .collect(Collectors.toList());

            // 校验服务包编码不能超过30个字符
            boolean anyMatch = excelPackageCodes.stream().anyMatch(code -> StrUtil.isNotBlank(code) && code.length() > Constants.PACKAGE_CODE_MAX_LENGTH);
            if (anyMatch) {
                return CommonResult.error(ErrorCodeConstants.PACKAGE_CODE_LENGTH_LIMIT);
            }
            // 从数据库获取现有的服务包编码
            List<String> existingPackageCodes = remotePackageDOMapper.selectList(
                    new LambdaQueryWrapper<RemotePackageDO>()
                            .eq(RemotePackageDO::getIsDeleted, false))
                    .stream()
                    .map(RemotePackageDO::getPackageCode)
                    .collect(Collectors.toList());

            // 筛选出不存在于数据库中的服务包编码
            List<RemotePackageDO> validPackageList = excelPackageCodes.stream()
                    .distinct()
                    .filter(code -> !existingPackageCodes.contains(code))
                    .map(code -> RemotePackageDO.builder()
                            .carSystemModel(carSystemModel)
                            .packageCode(code)
                            .createdUser(WebFrameworkUtils.getLoginUserName())
                            .tenantId(1)
                            .build())
                    .collect(Collectors.toList());

            // 总记录数
            int totalRecords = CollUtil.size(excelPackageCodes);
            // 重复记录数
            int duplicateRecords = totalRecords - CollUtil.size(validPackageList);
            // 批量插入数据库
            if (CollUtil.isNotEmpty(validPackageList)) {
                saveBatch(validPackageList);
            }

            // 计算成功记录数
            int successfulRecords = CollUtil.size(validPackageList);

            // 创建并返回响应对象
            return CommonResult.success(new BatchUploadRespVO(totalRecords, duplicateRecords, successfulRecords));
        } catch (Exception e) {
            log.error("Error processing batch upload", e);
            return CommonResult.error(ErrorCodeConstants.PACKAGE_BATCH_UPLOAD_FAIL);
        }
    }

    @Value("${template.url}")
    private String templateUrl;

    @Override
    public String getTemplateUrl() {
        return templateUrl;
    }

    @Override
    public List<String> getExistPackageCode(List<String> packageCodes) {
        if (CollUtil.isEmpty(packageCodes)){
            return Collections.emptyList();
        }
        List<RemotePackageDO> selectList = remotePackageDOMapper.selectList(
                new LambdaQueryWrapper<RemotePackageDO>()
                        .select(RemotePackageDO::getPackageCode)
                        .eq(RemotePackageDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode())
                        .in(RemotePackageDO::getPackageCode, packageCodes));

        return convertList(selectList, e->e.getPackageCode());
    }

    @Override
    public List<String> getAllPackageCode() {
        List<RemotePackageDO> selectList = remotePackageDOMapper.selectList(
                new LambdaQueryWrapper<RemotePackageDO>()
                        .select(RemotePackageDO::getPackageCode));

        return convertList(selectList, e -> e.getPackageCode());
    }

    @Override
    public List<String> getAllRemotePackageCode() {
        List<RemotePackageDO> selectList = remotePackageDOMapper.selectList(
                new LambdaQueryWrapper<RemotePackageDO>()
                        .select(RemotePackageDO::getPackageCode)
                        .eq(RemotePackageDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode()));

        return convertList(selectList, e -> e.getPackageCode());
    }
}




