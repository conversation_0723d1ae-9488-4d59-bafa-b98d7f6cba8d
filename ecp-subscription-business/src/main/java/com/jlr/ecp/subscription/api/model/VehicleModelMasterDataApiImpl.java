package com.jlr.ecp.subscription.api.model;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.model.dto.UserDPResultDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataCreateDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataUpdateDTO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataPageVO;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 适用车型年款API  提供 RESTful API 接口，给 Feign 调用
 * <AUTHOR>
 */
@Tag(name = "RPC 服务 - 适用车型年款配置")
@RestController
@Validated
@Slf4j
public class VehicleModelMasterDataApiImpl implements VehicleModelMasterDataApi {

    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;

    @Override
    public CommonResult<String> createVehicleModelMasterData(VehicleModelMasterDataCreateDTO createDTO) {
        Boolean success = vehicleModelMasterDataService.createVehicleModelMasterData(createDTO);
        if(success){
            return CommonResult.success(Constants.SERIES_CREATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_CREATE_FAIL);
    }

    @Override
    public CommonResult<String> editVehicleModelMasterData(VehicleModelMasterDataUpdateDTO updateDTO) {
        Boolean success = vehicleModelMasterDataService.editVehicleModelMasterData(updateDTO);
        if(success){
            return CommonResult.success(Constants.SERIES_UPDATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_UPDATE_FAIL);
    }

    @Override
    public CommonResult<VehicleModelMasterDataVO> view(String seriesCode) {
        VehicleModelMasterDataVO vo =vehicleModelMasterDataService.getOneBySeriesCode(seriesCode);
        return CommonResult.success(vo);
    }

    @Override
    public CommonResult<PageResult<VehicleModelMasterDataPageVO>> page(Integer pageNo, Integer pageSize) {
        PageParam param = new PageParam();
        param.setPageNo(pageNo);
        param.setPageSize(pageSize);
        return CommonResult.success(vehicleModelMasterDataService.getPage(param));
    }


    @Override
    public CommonResult<String> deleteBySeriesCode(String seriesCode) {
        Boolean success = vehicleModelMasterDataService.deleteBySeriesCode(seriesCode);
        if(success){
            return CommonResult.success(Constants.SERIES_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_DELETE_FAIL);
    }

    @Override
    public CommonResult<List<UserDPResultVO>> findDpList(UserDPResultDTO resultDTO) {

        return CommonResult.success(vehicleModelMasterDataService.findDpList(resultDTO.getVinList()));
    }
}
