package com.jlr.ecp.subscription.controller.admin.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP续费的状态VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPRenewalStatusVO {
    @Schema(description = "续费状态code")
    private Integer renewalStatusCode;

    @Schema(description = "续费状态描述")
    private String renewalStatusDesc;
}
