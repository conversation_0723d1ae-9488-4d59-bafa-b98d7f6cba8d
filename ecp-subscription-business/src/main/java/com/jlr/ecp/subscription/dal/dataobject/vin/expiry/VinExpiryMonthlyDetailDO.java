package com.jlr.ecp.subscription.dal.dataobject.vin.expiry;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_vin_expiry_monthly_detail")
public class VinExpiryMonthlyDetailDO extends TenantBaseDO {

    @TableId
    private Long id;

    @TableField("job_id")
    private Long jobId;

    @TableField("job_date")
    private LocalDateTime jobDate;

    @TableField("car_system_model")
    private String carSystemModel;

    @TableField("car_vin")
    private String carVin;

    @TableField("query_status")
    private Integer queryStatus;

    @TableField("iccid")
    private String iccid;

    @TableField("real_name_flag")
    private Integer realNameFlag;

    @TableField("card_state")
    private String cardState;

    @TableField("failed_type")
    private Integer failedType;

    @TableField("error_desc")
    private String errorDesc;

    @TableField("service_type")
    private String serviceType;

    @TableField("expiry_date")
    private LocalDateTime expiryDate;

    @TableField("imported_en")
    private String importedEn;

    @TableField("brand")
    private String brand;

    @TableField("series_name")
    private String seriesName;
}
