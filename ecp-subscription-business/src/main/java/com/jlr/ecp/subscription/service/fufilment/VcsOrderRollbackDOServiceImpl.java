package com.jlr.ecp.subscription.service.fufilment;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderRollbackDOMapper;
import com.jlr.ecp.subscription.enums.fufil.CancelServiceStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【t_vcs_order_rollback(t_vcs_order_rollback)】的数据库操作Service实现
 * @createDate 2024-01-19 14:15:09
 */
@Service
@Slf4j
public class VcsOrderRollbackDOServiceImpl extends ServiceImpl<VcsOrderRollbackDOMapper, VcsOrderRollbackDO>
        implements VcsOrderRollbackDOService {

    @Resource
    private VcsOrderRollbackDOMapper vcsOrderRollbackDOMapper;

    @Override
    public void updateServiceStatusFail(String rollbackFulfilmentId) {
        vcsOrderRollbackDOMapper.update(null, new LambdaUpdateWrapper<VcsOrderRollbackDO>()
                .eq(VcsOrderRollbackDO::getRollbackFufilmentId, rollbackFulfilmentId)
                .set(VcsOrderRollbackDO::getServiceStatus, CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus()));
    }
}




