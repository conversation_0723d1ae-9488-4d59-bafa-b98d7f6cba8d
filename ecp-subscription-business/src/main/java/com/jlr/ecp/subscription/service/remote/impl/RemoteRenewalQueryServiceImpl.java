package com.jlr.ecp.subscription.service.remote.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteRenewalQueryPageVO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyStatusEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteServiceTypeEnum;
import com.jlr.ecp.subscription.service.remote.RemoteRenewalQueryService;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RemoteRenewalQueryServiceImpl implements RemoteRenewalQueryService {

    @Resource
    private RemoteRenewBatchRecordsMapper remoteRenewBatchRecordsMapper;

    @Resource
    private RemoteRenewDetailRecordsMapper remoteRenewDetailRecordsMapper;

    @Override
    public String queryRemoteRenewalBatchNo(String batchNo) {
        log.info("查询remote续期批次的批号, batchNo:{}", batchNo);
        RemoteRenewBatchRecords remoteRenewBatchRecords;
        try {
            Long batchNoLong = Long.parseLong(batchNo);
            remoteRenewBatchRecords = getRemoteBatchByBatchNo(batchNoLong);
            // 不为批处理的批次号
            if (Objects.isNull(remoteRenewBatchRecords)) {
                RemoteRenewDetailRecords remoteRenewDetailRecords = getRemoteRecordByBatchNo(batchNoLong);
                if (Objects.isNull(remoteRenewDetailRecords)) {
                    return "";
                }
                return String.valueOf(remoteRenewDetailRecords.getBatchNo());
            }
        } catch (Exception e) {
            log.info("查询Remote续期批次的批号异常:{}, batchNo:{}", e.getMessage(), batchNo);
            return "";
        }
        return String.valueOf(remoteRenewBatchRecords.getBatchNo());
    }

    @Override
    public List<RemoteEnumQueryVO> queryRemoteRenewalStatus() {
        List<RemoteEnumQueryVO> resp = new ArrayList<>();
        for (RemoteModifyStatusEnum remoteModifyStatusEnum : RemoteModifyStatusEnum.values()) {
            RemoteEnumQueryVO remoteEnumQueryVO = RemoteEnumQueryVO.builder()
                    .code(remoteModifyStatusEnum.getStatus())
                    .desc(remoteModifyStatusEnum.getDesc())
                    .build();
            resp.add(remoteEnumQueryVO);
        }
        return resp;
    }

    @Override
    public List<RemoteEnumQueryVO> queryRemoteRenewalService() {
        List<RemoteEnumQueryVO> resp = new ArrayList<>();
        for (RemoteServiceTypeEnum remoteServiceTypeEnum : RemoteServiceTypeEnum.values()) {
            RemoteEnumQueryVO remoteEnumQueryVO = RemoteEnumQueryVO.builder()
                    .code(remoteServiceTypeEnum.getServiceType())
                    .desc(remoteServiceTypeEnum.getServiceName())
                    .build();
            resp.add(remoteEnumQueryVO);
        }
        return resp;
    }

    @Override
    public PageResult<RemoteRenewalQueryPageVO> queryRemoteRenewalPageList(RemoteRenewalQueryPageDTO queryPageDTO) {
        log.info("查询Remote续期的分页列表, queryPageDTO:{}", queryPageDTO);
        Page<RemoteRenewDetailRecords> pageParam = new Page<>(queryPageDTO.getPageNo(), queryPageDTO.getPageSize());
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = buildRemoteQueryWrapper(queryPageDTO);
        Page<RemoteRenewDetailRecords> remoteRenewDetailRecordsPage = remoteRenewDetailRecordsMapper.selectPage(pageParam, queryWrapper);
        if (Objects.isNull(remoteRenewDetailRecordsPage) || CollUtil.isEmpty(remoteRenewDetailRecordsPage.getRecords())) {
            log.info("查询Remote续期的分页列表结果为空, queryPageDTO:{}", queryPageDTO);
            return new PageResult<>();
        }
        List<RemoteRenewalQueryPageVO> pageVOList = buildRemoteRenewalQueryPageVOList(remoteRenewDetailRecordsPage.getRecords());
        return new PageResult<>(pageVOList, remoteRenewDetailRecordsPage.getTotal());
    }


    /**
     * 根据批次号获取AppDCu续费批次记录
     *
     * @param batchNo 批次号，用于识别特定的续费批次
     * @return AppDCuRenewBatchRecords对象，表示找到的批次记录如果找不到匹配的记录，则返回null
     */
    private RemoteRenewBatchRecords getRemoteBatchByBatchNo(Long batchNo) {
        log.info("根据批次号获取Remote续费批次记录，batchNo:{}", batchNo);
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapper<RemoteRenewBatchRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewBatchRecords::getBatchNo, batchNo)
                .eq(RemoteRenewBatchRecords::getIsDeleted, false);
        List<RemoteRenewBatchRecords> respList = remoteRenewBatchRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(respList)) {
            log.info("根据批次号获取Remote续费批次记录为空，batchNo:{}", batchNo);
            return null;
        }
        if (respList.size() > 1) {
            log.info("根据批次号获取Remote续费批次记录数量大于1，batchNo:{}", batchNo);
        }
        return respList.get(0);
    }

    /**
     * 根据批号获取Remote续费记录
     *
     * @param batchNo 续费操作的批号，用于查询特定的续费记录
     * @return 如果存在符合条件的续费记录，则返回该记录；否则返回null
     */
    private RemoteRenewDetailRecords getRemoteRecordByBatchNo(Long batchNo) {
        log.info("根据批号获取Remote续费详情，batchNo:{}", batchNo);
        if (Objects.isNull(batchNo)) {
            return null;
        }
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteRenewDetailRecords::getBatchNo, batchNo)
                .eq(RemoteRenewDetailRecords::getIsDeleted, false);
        List<RemoteRenewDetailRecords> respList = remoteRenewDetailRecordsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(respList)) {
            log.info("根据批号获取Remote续费详情为空，batchNo:{}", batchNo);
            return null;
        }
        if (respList.size() > 1) {
            log.info("根据批号获取Remote续费详情数量大于1，batchNo:{}", batchNo);
        }
        return respList.get(0);
    }

    /**
     * 构建RemoteRenewDetailRecords的查询参数
     *
     * @param queryPageDTO 包含查询参数的DTO对象
     * @return 返回构建的查询包装器对象，如果输入参数为空则返回null
     */
    private LambdaQueryWrapper<RemoteRenewDetailRecords> buildRemoteQueryWrapper(RemoteRenewalQueryPageDTO queryPageDTO) {
        log.info("构建RemoteRenewDetailRecords的查询参数, queryPageDTO:{}", queryPageDTO);
        if (Objects.isNull(queryPageDTO)) {
            return null;
        }
        LambdaQueryWrapper<RemoteRenewDetailRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(queryPageDTO.getCarVin()), RemoteRenewDetailRecords::getCarVin,
                queryPageDTO.getCarVin());
        if (CollUtil.isNotEmpty(queryPageDTO.getBatchNoList())) {
            List<Long> renewNoList = new ArrayList<>();
            for (String batchNo : queryPageDTO.getBatchNoList()) {
                Long renewNo = Long.valueOf(batchNo);
                renewNoList.add(renewNo);
            }
            queryWrapper.in(RemoteRenewDetailRecords::getBatchNo, renewNoList);
        }
        if (StringUtils.isNotBlank(queryPageDTO.getOperateStartTime()) && StringUtils.isNotBlank(queryPageDTO.getOperateEndTime())) {
            LocalDateTime startTime = SubscribeTimeFormatUtil.stringToTimeByFormat(queryPageDTO.getOperateStartTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            LocalDateTime endTime = SubscribeTimeFormatUtil.stringToTimeByFormat(queryPageDTO.getOperateEndTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            queryWrapper.ge(RemoteRenewDetailRecords::getCreatedTime, startTime)
                    .le(RemoteRenewDetailRecords::getCreatedTime, endTime);
        }
        queryWrapper.eq(Objects.nonNull(queryPageDTO.getRenewalStatus()), RemoteRenewDetailRecords::getModifyStatus, queryPageDTO.getRenewalStatus());
        queryWrapper.eq(StringUtils.isNotBlank(queryPageDTO.getOperator()), RemoteRenewDetailRecords::getOperator, queryPageDTO.getOperator());
        if (StringUtils.isNotBlank(queryPageDTO.getOperateTimeSort())) {
            if (SortTypeEnum.ASC.getSortType().equals(queryPageDTO.getOperateTimeSort())) {
                queryWrapper.orderByAsc(RemoteRenewDetailRecords::getCreatedTime);
                queryWrapper.orderByAsc(RemoteRenewDetailRecords::getId);
            } else {
                queryWrapper.orderByDesc(RemoteRenewDetailRecords::getCreatedTime);
                queryWrapper.orderByDesc(RemoteRenewDetailRecords::getId);
            }
        }
        return queryWrapper;
    }

    /**
     * 构建续期查询页面VO列表
     *
     * @param recordList 记录列表，包含所有的续期记录
     * @return 返回一个列表，包含转换后的VO对象
     */
    private List<RemoteRenewalQueryPageVO> buildRemoteRenewalQueryPageVOList(List<RemoteRenewDetailRecords> recordList) {
        List<RemoteRenewalQueryPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(recordList)) {
            return resp;
        }
        for (RemoteRenewDetailRecords remoteRenewDetailRecords : recordList) {
            resp.add(buildRemoteRenewalQueryPageVO(remoteRenewDetailRecords));
        }
        return resp;
    }

    /**
     * 构建Remote续订查询页面VO对象
     *
     * @param remoteRenewDetailRecords Remote的续订记录实体，包含车辆VIN码、续订服务类型等信息
     * @return RemoteRenewalQueryPageVO 续订查询页面VO对象，包含车辆VIN码、续订批次号、续订服务类型等信息
     */
    private RemoteRenewalQueryPageVO buildRemoteRenewalQueryPageVO(RemoteRenewDetailRecords remoteRenewDetailRecords) {
        if (Objects.isNull(remoteRenewDetailRecords)) {
            return null;
        }
        return RemoteRenewalQueryPageVO.builder()
                .batchNo(String.valueOf(remoteRenewDetailRecords.getBatchNo()))
                .carVin(remoteRenewDetailRecords.getCarVin())
                .renewalServiceDesc(RemoteServiceTypeEnum.REMOTE.getServiceName())
                .renewalStatus(remoteRenewDetailRecords.getModifyStatus())
                .renewalStatusDesc(RemoteModifyStatusEnum.getDescByStatus(remoteRenewDetailRecords.getModifyStatus()))
                .renewalBeforeExpiryDate(SubscribeTimeFormatUtil.timeToStringByFormat(remoteRenewDetailRecords.getModifyBeforeDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .renewalAfterExpiryDate(SubscribeTimeFormatUtil.timeToStringByFormat(remoteRenewDetailRecords.getModifyAfterDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(remoteRenewDetailRecords.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(remoteRenewDetailRecords.getOperator())
                .errorDesc(remoteRenewDetailRecords.getErrorDesc())
                .build();
    }
}
