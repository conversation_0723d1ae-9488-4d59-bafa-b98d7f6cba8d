package com.jlr.ecp.subscription.controller.admin.vo.iccid;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "ICCID 修改结果 page VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class IccidModifyQueryPageVO {
    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "车辆编号")
    private String carVin;

    @Schema(description = "修改前ICCID")
    private String modifyBeforeIccid;

    @Schema(description = "修改后ICCID")
    private String modifyAfterIccid;

    @Schema(description = "修改状态")
    private Integer modifyStatus;

    @Schema(description = "修改状态描述")
    private String modifyStatusDesc;

    @Schema(description = "错误信息描述")
    private String errorDesc;
}
