package com.jlr.ecp.subscription.excel.listener.amap;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPBatchRenewalResultExcel;
import com.jlr.ecp.subscription.util.CarVinUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


@Slf4j
@Data
public class AmaPBatchRenewalCheckListener extends AnalysisEventListener<AmaPBatchRenewalExcel> {

    private static final int BATCH_COUNT = 500;

    private static final String ONE_YEAR = "一年";

    private static final String THREE_YEAR = "三年";

    private static final String VIN_TITLE = "VIN";

    private static final String RENEWAL_YEAR_TITLE = "续费时长（仅支持填写一年或三年）";

    private List<AmaPBatchRenewalExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<AmaPBatchRenewalExcel> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<AmaPBatchRenewalResultExcel> resultExcelList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Set<String> vinSet = new HashSet<>();

    private Boolean formatError = false;

    private Boolean checkResult = true;

    private Boolean vinRepeatable = false;

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(AmaPBatchRenewalExcel amaPBatchRenewalExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(amaPBatchRenewalExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                checkUploadExcel(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("AMAP批量续费解析Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("AMAP批量续费处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            checkUploadExcel(dataList);
        } catch (Exception e) {
            log.info("AMAP批量续费处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("AMAP批量续费所有数据解析完成！");
    }

    /**
     * 检查上传的Excel文件中的续费信息
     *
     * @param renewalExcelList 包含多个续费信息的Excel文件列表
     */
    public void checkUploadExcel(List<AmaPBatchRenewalExcel> renewalExcelList) {
        if (headers.isEmpty() ||  headers.get(0).size() < 2 || !VIN_TITLE.equals(headers.get(0).get(0)) ||
                !RENEWAL_YEAR_TITLE.equals(headers.get(0).get(1))) {
            formatError = true;
            return ;
        }
        for (AmaPBatchRenewalExcel amaPBatchRenewalExcel : renewalExcelList) {
            AmaPBatchRenewalResultExcel resultExcel = new AmaPBatchRenewalResultExcel();
            resultExcel.setCarVin(amaPBatchRenewalExcel.getCarVin());
            resultExcel.setRenewalYear(amaPBatchRenewalExcel.getRenewalYear());
            if (StringUtils.isBlank(amaPBatchRenewalExcel.getCarVin())) {
                resultExcel.setCheckResult("VIN 缺失");
                checkResult = false;
            } else if (StringUtils.isBlank(amaPBatchRenewalExcel.getRenewalYear())) {
                resultExcel.setCheckResult("续费时长缺失");
                checkResult = false;
            } else if (!ONE_YEAR.equals(amaPBatchRenewalExcel.getRenewalYear()) &&
                    !THREE_YEAR.equals(amaPBatchRenewalExcel.getRenewalYear())) {
                resultExcel.setCheckResult("续费时长需填写一年或三年");
                checkResult = false;
            } else if (!CarVinUtil.checkVinFormat(amaPBatchRenewalExcel.getCarVin())) {
                resultExcel.setCheckResult("VIN需由17位数字及字母组成");
                checkResult = false;
            } else if (vinSet.contains(amaPBatchRenewalExcel.getCarVin())) {
                resultExcel.setCheckResult("VIN重复");
                vinRepeatable = true;
            } else {
                resultExcel.setCheckResult("有效");
            }
            vinSet.add(amaPBatchRenewalExcel.getCarVin());
            resultExcelList.add(resultExcel);
        }
    }
}
