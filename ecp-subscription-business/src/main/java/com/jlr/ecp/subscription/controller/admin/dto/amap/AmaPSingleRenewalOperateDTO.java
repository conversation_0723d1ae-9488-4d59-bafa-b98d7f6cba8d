package com.jlr.ecp.subscription.controller.admin.dto.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "AMAP单个续费分页查询DTO")
public class AmaPSingleRenewalOperateDTO {
    @Schema(description = "车架号")
    @NotNull
    private String carVin;

    @Schema(description = "续费年限编码")
    @NotNull
    private Integer renewYearCode;
}
