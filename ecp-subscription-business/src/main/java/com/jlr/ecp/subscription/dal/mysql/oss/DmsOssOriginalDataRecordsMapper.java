package com.jlr.ecp.subscription.dal.mysql.oss;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
 * t_dms_oss_original_data_records表数据库访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface DmsOssOriginalDataRecordsMapper extends BaseMapperX<DmsOssOriginalDataRecordsDO> {
    List<DmsOssOriginalDataRecordsDO> queryByVinSet(@Param("vinSet") Set<String> vinSet);
}
