package com.jlr.ecp.subscription.enums.fufil;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务名枚举
 */
@AllArgsConstructor
@Getter
public enum ServiceNameEnum {
    TSDP("TSDP", "TSDP"),
    APPD("APPD", "APPD"),
    UNICOM("UNICOM", "UNICOM"),
    AMAP("AMAP", "AMAP");

    /**
     * 名称
     * */
    public final String serviceName;

    /**
     * 描述
     * */
    public final String desc;
}
