package com.jlr.ecp.subscription.dal.mysql.icroder;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【t_series_brand_mapping_data(t_series_brand_mapping_data)】的数据库操作Mapper
* @createDate 2024-01-16 09:42:36
* @Entity com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO
*/
@Mapper
public interface SeriesBrandMappingDataDOMapper extends BaseMapperX<SeriesBrandMappingDataDO> {

}




