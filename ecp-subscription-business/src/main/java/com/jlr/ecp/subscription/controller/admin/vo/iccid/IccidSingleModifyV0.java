package com.jlr.ecp.subscription.controller.admin.vo.iccid;

import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "ICCID单个修改 VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class IccidSingleModifyV0 {
    @Schema(description = "修改结果")
    private String renewalResult;

    @Schema(description = "调用iccid返回的结果UnicomRespVO")
    private UnicomRespVO unicomRespVO;

    //二次弹窗内容修改
    @Schema(description = "车架号 vin")
    private String carVin;

    @Schema(description = "旧ICCID")
    private String oldIccid;

    @Schema(description = "新ICCID（20位）")
    private String newIccid;
}
