package com.jlr.ecp.subscription.file.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.file.dao.FileDO;
import com.jlr.ecp.subscription.file.vo.FilePageReqVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文件操作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("ecp_system")
public interface FileMapper extends BaseMapperX<FileDO> {

    default PageResult<FileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileDO>()
                .likeIfPresent(FileDO::getPath, reqVO.getPath())
                .likeIfPresent(FileDO::getType, reqVO.getType())
                .betweenIfPresent(FileDO::getCreatedTime, reqVO.getCreatedTime())
                .orderByDesc(FileDO::getId));
    }
}

