package com.jlr.ecp.subscription.dal.dataobject.vehicle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_vehicle_config_group表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_vehicle_config_group")
public class VehicleConfigGroupDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;
    /**
    * 主键
    */
    @TableId
    private Long id;

    /**
     * 配置编码;配置编码
     */
    @TableField(value = "config_code")
    private String configCode;

    /**
     * 分组ID;分组ID
     * APPD_CANNOT_BUY:不能买APPD的分组
     */
    @TableField(value = "group_id")
    private String groupId;
}

