package com.jlr.ecp.subscription.service.pivi;

import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;

import java.util.List;

/**
 * PIVIPackageService
 */
public interface PIVIPackageService {
    /**
     * 批量插入PIVIPackage
     * @param packageDOList packageDOList
     */
    void insertBatch(List<PIVIPackageDO> packageDOList);

    /**
     * 根据vinList查询PIVIPackageList
     * @param vinList vinList
     * @return List<PIVIPackageDO>
     */
    List<PIVIPackageDO> queryByVinList(List<String> vinList);

    /**
     * 根据vin查询PIVIPackage
     * @param vin vin
     * @return PIVIPackageDO
     */
    PIVIPackageDO queryByVin(String vin);

    /**
     * 插入PIVIPackage
     * @param piviPackageDO piviPackageDO
     */
    void insert(PIVIPackageDO piviPackageDO);
}
