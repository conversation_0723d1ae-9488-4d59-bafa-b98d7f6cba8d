package com.jlr.ecp.subscription.service.iccid.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidModifyQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidModifyQueryPageVO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.iccid.ModifyStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.service.iccid.IccidModifyQueryService;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum.SYSTEM_ERROR;

@Service
@Slf4j
public class IccidModifyQueryServiceImpl implements IccidModifyQueryService {
    @Resource
    private IccidModifyRecordsDOMapper iccidModifyRecordsDOMapper;
    @Autowired
    private IccidModifyBatchRecordsDOMapper iccidModifyBatchRecordsDOMapper;

    @Override
    public PageResult<IccidModifyQueryPageVO> queryModifyResultPageList(IccidModifyQueryPageDTO pageDTO) {
        log.info("查询ICCID修改结果的分页列表, pageDTO:{}", pageDTO);
        Page<IccidModifyRecordsDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapper<IccidModifyRecordsDO> queryWrapper = buildIccidModifyQueryWrapper(pageDTO);
        Page<IccidModifyRecordsDO> iccidModifyQueryPage = iccidModifyRecordsDOMapper.selectPage(pageParam, queryWrapper);
        if (ObjectUtils.isEmpty(iccidModifyQueryPage) || CollUtil.isEmpty(iccidModifyQueryPage.getRecords())) {
            log.info("查询ICCID修改结果的分页列表结果为空, pageDTO:{}", pageDTO);
            return new PageResult<>();
        }
        List<IccidModifyQueryPageVO> pageVOList = buildIccidModifyQueryPageVOList(iccidModifyQueryPage.getRecords());

        return new PageResult<>(pageVOList, iccidModifyQueryPage.getTotal());
    }

    @Override
    public String queryModifyBatchNo(String batchNo) {
        log.info("根据批次号查询ICCID的批次号, batchNo:{}", batchNo);

        try {
            Long batchNoLong = Long.valueOf(batchNo);
            IccidModifyBatchRecordsDO iccidModifyBatchRecordsDO = queryModifyBatchByBatchNo(batchNoLong);
            if (Objects.isNull(iccidModifyBatchRecordsDO)) {
                //查询单表
                IccidModifyRecordsDO iccidModifyRecordsDO = queryModifyByBatchNo(batchNoLong);
                if (Objects.isNull(iccidModifyRecordsDO)) {
                    return null;
                }
                return String.valueOf(iccidModifyRecordsDO.getModifyNo());
            }
            return String.valueOf(iccidModifyBatchRecordsDO.getBatchNo());
        } catch (Exception e) {
            log.info("根据批次号获取ICCID查询批次信息异常：{}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 根据批次号查询ICCID IccidModifyRecordsDO 中的批次号
     *
     * @param batchNoLong
     * @return
     */
    private IccidModifyRecordsDO queryModifyByBatchNo(Long batchNoLong) {
        if (Objects.isNull(batchNoLong)) {
            return null;
        }
        LambdaQueryWrapper<IccidModifyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyRecordsDO::getModifyNo, batchNoLong)
                .eq(IccidModifyRecordsDO::getIsDeleted, false);
        List<IccidModifyRecordsDO> resp = new ArrayList<>();
        try {
            resp = iccidModifyRecordsDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据批次号查询ICCID记录异常：{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次号查询ICCID记录,数量大于1, batchNo:{}", batchNoLong);
        }
        return resp.get(0);
    }

    /**
     * 根据批次号查询ICCID IccidModifyBatchRecordsDO 中的批次号
     *
     * @param batchNo
     * @return
     */
    private IccidModifyBatchRecordsDO queryModifyBatchByBatchNo(Long batchNo) {
        if (Objects.isNull(batchNo)) {
            return null;
        }

        LambdaQueryWrapper<IccidModifyBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IccidModifyBatchRecordsDO::getBatchNo, batchNo)
                .eq(IccidModifyBatchRecordsDO::getIsDeleted, false);
        List<IccidModifyBatchRecordsDO> resp = new ArrayList<>();
        try {
            resp = iccidModifyBatchRecordsDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据批次号查询ICCID批次记录异常：{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据批次号查询ICCID批次记录,数量大于1, batchNo:{}", batchNo);
        }
        return resp.get(0);
    }

    /**
     * 构建ICCID修改查询页面VO列表
     *
     * @param recordList 记录列表，包含所有的修改记录
     * @return 返回一个列表，包含转换后的VO对象
     */
    private List<IccidModifyQueryPageVO> buildIccidModifyQueryPageVOList(List<IccidModifyRecordsDO> recordList) {
        List<IccidModifyQueryPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(recordList)) {
            return resp;
        }
        for (IccidModifyRecordsDO iccidModifyRecordsDO : recordList) {
            resp.add(buildIccidModifyQueryPageVO(iccidModifyRecordsDO));
        }
        return resp;
    }

    /**
     * 构建ICCID修改查询页面VO对象
     *
     * @param iccidModifyRecordsDO ICCID的修改记录实体，包含车辆VIN码、修改状态等信息
     * @return IccidModifyQueryPageVO 修改查询页面VO对象，包含车辆VIN码、批次号、修改状态等信息
     */
    private IccidModifyQueryPageVO buildIccidModifyQueryPageVO(IccidModifyRecordsDO iccidModifyRecordsDO) {
        if (Objects.isNull(iccidModifyRecordsDO)) {
            return null;
        }
        return IccidModifyQueryPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(iccidModifyRecordsDO.getCreatedTime(), SubscribeTimeFormatUtil.FORMAT_2))
                .operator(iccidModifyRecordsDO.getUpdatedBy())
                .batchNo(String.valueOf(iccidModifyRecordsDO.getModifyNo()))
                .carVin(iccidModifyRecordsDO.getCarVin())
                .modifyBeforeIccid(iccidModifyRecordsDO.getModifyBeforeIccid())
                .modifyAfterIccid(iccidModifyRecordsDO.getModifyAfterIccid())
                .modifyStatus(iccidModifyRecordsDO.getModifyStatus())
                .modifyStatusDesc(ModifyStatusEnum.getDescByStatus(iccidModifyRecordsDO.getModifyStatus()))
                .errorDesc(getIccidModifyErrorDesc(iccidModifyRecordsDO)).build();
    }

    /**
     * 获取ICCID修改失败的错误描述
     *
     * @param iccidModifyRecordsDO
     * @return
     */
    private String getIccidModifyErrorDesc(IccidModifyRecordsDO iccidModifyRecordsDO) {
        if (Objects.isNull(iccidModifyRecordsDO) || StrUtil.isBlank(iccidModifyRecordsDO.getErrorDesc())) {
            return "";
        }

        //直接返回原有提示信息
        if (iccidModifyRecordsDO.getErrorDesc().contains("在ECP数据库中未找到该VIN")) {
            return iccidModifyRecordsDO.getErrorDesc();
        }

        //如果是 ‘System error......’ or ‘系统异常’ 则返回‘系统异常’
        if (iccidModifyRecordsDO.getErrorDesc().contains(SYSTEM_ERROR.getDesc()) ||
                iccidModifyRecordsDO.getErrorDesc().contains(SYSTEM_ERROR.getErrorDescription())) {
            return SYSTEM_ERROR.getEcpDisplay();
        }

        return UnicomResultEnum.getEcpDisplayByErrorDescription(iccidModifyRecordsDO.getErrorDesc());
    }

    /**
     * 构建IccidModifyRecordsDO的查询参数
     *
     * @param pageDTO 包含查询参数的DTO对象
     * @return 返回构建的查询包装器对象，如果输入参数为空则返回null
     */
    private LambdaQueryWrapper<IccidModifyRecordsDO> buildIccidModifyQueryWrapper(IccidModifyQueryPageDTO pageDTO) {
        log.info("构建IccidModifyRecordsDO的查询参数, pageDTO:{}", pageDTO);
        if (Objects.isNull(pageDTO)) {
            return null;
        }
        LambdaQueryWrapper<IccidModifyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();

        // a.根据车辆编号构建查询条件
        if (StrUtil.isNotBlank(pageDTO.getCarVin())) {
            queryWrapper.eq(IccidModifyRecordsDO::getCarVin, pageDTO.getCarVin());
        }

        // b.根据批次号构建查询条件
        if (CollUtil.isNotEmpty(pageDTO.getBatchNoList())) {
            List<Long> batchNoList = new ArrayList<>();
            for (String batchNo : pageDTO.getBatchNoList()) {
                Long batchNoLong = Long.valueOf(batchNo);
                batchNoList.add(batchNoLong);
            }
            queryWrapper.in(IccidModifyRecordsDO::getModifyNo, batchNoList);
        }

        // c.根据修改状态构建查询条件
        if (pageDTO.getModifyStatus() != null) {
            queryWrapper.eq(IccidModifyRecordsDO::getModifyStatus, pageDTO.getModifyStatus());
        }

        // d.根据操作时间范围构建查询条件
        if (StrUtil.isNotBlank(pageDTO.getOperateStartTime()) && StrUtil.isNotBlank(pageDTO.getOperateEndTime())) {
            LocalDateTime startTime = SubscribeTimeFormatUtil.stringToTimeByFormat(pageDTO.getOperateStartTime(), SubscribeTimeFormatUtil.FORMAT_2);
            LocalDateTime endTime = SubscribeTimeFormatUtil.stringToTimeByFormat(pageDTO.getOperateEndTime(), SubscribeTimeFormatUtil.FORMAT_2);
            queryWrapper.ge(IccidModifyRecordsDO::getCreatedTime, startTime).le(IccidModifyRecordsDO::getCreatedTime, endTime);
        }

        // e.根据操作人员构建查询条件
        if (StrUtil.isNotBlank(pageDTO.getOperator())) {
            queryWrapper.eq(IccidModifyRecordsDO::getUpdatedBy, pageDTO.getOperator());
        }

        // f.根据操作时间排序
        if (StrUtil.isNotBlank(pageDTO.getOperateTimeSort())) {
            if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
                queryWrapper.orderByAsc(IccidModifyRecordsDO::getCreatedTime);
                queryWrapper.orderByAsc(IccidModifyRecordsDO::getId);
            } else {
                queryWrapper.orderByDesc(IccidModifyRecordsDO::getCreatedTime);
                queryWrapper.orderByDesc(IccidModifyRecordsDO::getId);
            }
        }

        return queryWrapper;
    }
}
