package com.jlr.ecp.subscription.dal.dataobject.unicom;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_unicom_todo_order
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_unicom_rnr_query_records")
public class UnicomRnrQueryRecordsDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;


    /**
     *批次号;批量查询批次号
     */
    private Long batchNo;

     /**
     * VIN;VIN
     */    
    private String carVin;

     /**
     * 查询状态;查询状态 1：查询中 2：查询成功 3：查询失败
     */    
    private Integer queryStatus;



    /**
     * ICCID;VIN对应ICCID
     */
    private String iccid;


    /**
     * 卡状态;open：开机 stop：停机 cancel：销户
     */
    private Integer realNameFlag;

    /**
     * 最新实名时间
     */
    private LocalDateTime realNameUpdateTime;

    /**
     * ICCID;VIN对应ICCID
     */
    private String cardState;


    /**
     * 查询失败原因;失败原因 1：VIN格式校验错误  2：ICCID查询失败  3：联通查询失败
     */
    private Integer failedType;


    /**
     * 失败原因描述
     */
    private String errorDesc;


}

