package com.jlr.ecp.subscription.enums.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VerifyResultEnum {
    FAIL(0, "校验不通过"),
    SUCCESS(1, "校验通过");

    private final Integer code;

    private final String desc;

    /**
     * 根据给定的代码获取对应的描述
     *
     * @param code 需要获取描述的代码值
     * @return 与给定代码匹配的描述，如果找不到匹配则返回空字符串
     */
    public static String getDescByCode(Integer code) {
        for (VerifyResultEnum verifyResultEnum : VerifyResultEnum.values()) {
            if (verifyResultEnum.getCode().equals(code)) {
                return verifyResultEnum.getDesc();
            }
        }
        return "";
    }
}
