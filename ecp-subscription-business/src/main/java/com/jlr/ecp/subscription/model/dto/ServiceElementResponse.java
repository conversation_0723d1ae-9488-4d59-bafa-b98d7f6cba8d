package com.jlr.ecp.subscription.model.dto;


import lombok.Data;

import java.time.LocalDateTime;

/** AppD续约服务响应
 */
@Data
public class ServiceElementResponse {
    /**
     * PROVISION ACTIVATE DEACTIVATE DEPROVISION
     */
    private String event;

    private LocalDateTime eventDateTime;

    private Long jlrServiceElementInstanceId;
    /**
     * example: Online Pack with Data Plan
     */
    private String name;

    private String shortCode;

    private String serviceElementInstanceId;
}
