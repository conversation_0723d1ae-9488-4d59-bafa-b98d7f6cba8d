package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DP查询结果枚举
 */
@AllArgsConstructor
@Getter
public enum DpResultEnum {

    SUCCESS(1, "是", null),

    SYSTEM_ERROR(2, "否，系统异常", "DP系统异常"),

    EMPTY_DATA(3, "否，对应的配置信息为空", "DP系统异常");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 错误信息
     * */
    public final String errorMessage;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (DpResultEnum status : DpResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    public static String getErrorMessage(Integer code){
        if (code == null) {
            return null;
        }
        for (DpResultEnum status : DpResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getErrorMessage();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
