package com.jlr.ecp.subscription.controller.admin.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP批量手上传Excel校验VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPBatchRenewalUploadVO {

    /**
     *  info: 普通提示语，success：成功提示语
     * */
    @Schema(description = "普通提示语，info: 普通提示语，success：成功提示语")
    private String type;

    /**
     *  消息
     * */
    @Schema(description = "消息")
    private String msg;
}
