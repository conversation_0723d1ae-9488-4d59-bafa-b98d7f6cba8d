package com.jlr.ecp.subscription.dal.dataobject.remote;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_remote_renew_detail_records")
public class RemoteRenewDetailRecords extends BaseDO {
    // 主键
    @TableId(value = "id")
    private Long id;

    // 批次号;批次编号，单条修改记录拥有唯一的雪花算法生成的id; 对于批量修改就是批次号
    @TableField("batch_no")
    private Long batchNo;

    // VIN
    @TableField("car_vin")
    private String carVin;

    // 续费前到期日
    @TableField("modify_before_date")
    private LocalDateTime modifyBeforeDate;

    // 续费后到期日
    @TableField("modify_after_date")
    private LocalDateTime modifyAfterDate;

    // 修改状态;修改状态：1：进行中 2：修改成功 3：修改失败
    @TableField("modify_status")
    private Integer modifyStatus;

    // 错误信息描述;错误信息描述
    @TableField("error_desc")
    private String errorDesc;

    // 数据来源 1单次续费 2批量续费
    @TableField("data_source")
    private Integer dataSource;

    // 操作人账号
    @TableField("operator")
    private String operator;

    // 租户号
    @TableField("tenant_id")
    private Integer tenantId;
}
