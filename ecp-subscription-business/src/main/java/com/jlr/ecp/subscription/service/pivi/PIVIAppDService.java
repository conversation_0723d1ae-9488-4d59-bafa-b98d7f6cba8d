package com.jlr.ecp.subscription.service.pivi;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;

import java.time.LocalDateTime;

/**
 * APPD订阅
 */
public interface PIVIAppDService {
    /**
     * 调用appd续期接口
     * @param message 消息
     * @param fulfilmentId 履约id
     * @return 续期是否成功
     */
    boolean callAppDService(BaseMessage message, String fulfilmentId, Long jlrSubscriptionId);

    /**
     * 手动续订APPD
     *
     * @param vin 车辆识别号，用于识别特定的车辆订阅
     * @param endDate 续订的结束日期，用于构建续订请求
     * @return 返回VcsOrderFufilmentCall对象，表示VCS订单履行调用的结果
     */
    CommonResult<VcsOrderFufilmentCall> appDManualRenewal(String vin, LocalDateTime endDate);

    /**
     * 调用appd查询接口
     * @param vin vin
     * @return 续期是否成功
     */
    AppDSubscriptionResp getVinSubscriptions(String vin);
}
