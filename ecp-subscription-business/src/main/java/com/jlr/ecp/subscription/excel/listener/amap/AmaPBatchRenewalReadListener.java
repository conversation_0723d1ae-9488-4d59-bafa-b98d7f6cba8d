package com.jlr.ecp.subscription.excel.listener.amap;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPBatchRenewalExcel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AmaPBatchRenewalReadListener implements ReadListener<AmaPBatchRenewalExcel> {
    private static final int BATCH_COUNT = 500;

    private List<AmaPBatchRenewalExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<AmaPBatchRenewalExcel>> allList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    @Override
    public void invoke(AmaPBatchRenewalExcel amaPBatchRenewalExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(amaPBatchRenewalExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allList.add(dataList);
                //重置空间
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("高德批量续费读取异常:{}", e.getMessage());
            //重置空间
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("高德批量续费读取最后剩余的手机号码");
        try {
            //调用批量处理逻辑
            allList.add(dataList);
        } catch (Exception e) {
            log.info("高德批量续费读取最后剩余的手机号码异常:{}", e.getMessage());
        }  finally {
            dataList = null;
        }
        log.info("高德批量续费读取最后剩余的手机号码，所有数据解析完成!");
    }
}
