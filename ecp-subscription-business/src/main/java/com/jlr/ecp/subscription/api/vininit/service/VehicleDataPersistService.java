package com.jlr.ecp.subscription.api.vininit.service;

import com.jlr.ecp.subscription.api.vininit.model.ProcessResult;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 车辆数据持久化服务接口
 *
 */
public interface VehicleDataPersistService {
    
    /**
     * 持久化批次数据
     *
     * @param insertVehicleList   车辆数据列表
     * @param serviceDOList 服务数据列表
     * @param customerSet   客户数据集合
     * @param vinIdMap      VIN与原始数据ID映射
     * @param failVinSet    失败的VIN集合
     * @return 处理结果
     */
    ProcessResult persistBatchData(List<IncontrolVehicleDO> insertVehicleList,
                                   List<IncontrolVehicleDO> updateVehicleList,
                                  List<SubscriptionServiceDO> serviceDOList,
                                  Set<IncontrolCustomerDO> customerSet,
                                  Map<String, Set<Long>> vinIdMap,
                                  Set<String> failVinSet);
    
    /**
     * 保存单个VIN的车辆和服务数据
     *
     * @param vin           VIN号
     * @param serviceDOList 服务数据列表
     * @param insertVehicleDO  新增车辆数据
     * @param updateVehicleDO 更新车辆数据
     * @return 是否成功
     */
    boolean saveVehicleAndServiceForVin(String vin, 
                                       List<SubscriptionServiceDO> serviceDOList, 
                                       IncontrolVehicleDO insertVehicleDO,
                                       IncontrolVehicleDO updateVehicleDO);
}
