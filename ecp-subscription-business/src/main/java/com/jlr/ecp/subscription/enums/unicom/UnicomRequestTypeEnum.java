package com.jlr.ecp.subscription.enums.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomRequestTypeEnum {

    ORDER("order", "订购"),

    CANCEL("cancel", "退订"),

    CHANGE("change", "变更");

    /**
     * 类型
     * */
    public final String type;

    /**
     * 描述
     * */
    public final String desc;


    public static String getDescriptionByCode(String code) {
        // 或者返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (UnicomCardStateEnum status : UnicomCardStateEnum.values()) {
            if (status.getType().equals(code)) {
                return status.getDesc();
            }
        }
        return code;
    }
}
