package com.jlr.ecp.subscription.api.vininit.service;

import com.jlr.ecp.subscription.api.vininit.model.ProcessResult;
import com.jlr.ecp.subscription.api.remoteservice.RemoteOriginalDataParseBO;

import java.util.List;

/**
 * 车辆数据处理服务接口
 *
 */
public interface VehicleDataProcessService {
    
    /**
     * 处理解析后的数据
     *
     * @param parsedDataList 解析后的数据列表
     * @return 处理结果
     */
    ProcessResult processData(List<RemoteOriginalDataParseBO> parsedDataList);
    
    /**
     * 处理单批数据
     *
     * @param batchData 批次数据
     * @return 处理结果
     */
    ProcessResult processBatchData(List<RemoteOriginalDataParseBO> batchData);
}
