package com.jlr.ecp.subscription.api.vininit;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.vininit.constants.VehicleProcessConstants;
import com.jlr.ecp.subscription.api.vininit.dto.parse.VinInitParseErrorDTO;
import com.jlr.ecp.subscription.api.vininit.model.ProcessResult;
import com.jlr.ecp.subscription.api.vininit.service.VehicleDataParseService;
import com.jlr.ecp.subscription.api.remoteservice.RemoteOriginalDataParseBO;
import com.jlr.ecp.subscription.api.vininit.service.VehicleDataProcessService;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import com.jlr.ecp.subscription.dal.repository.RemoteOriginalDataRepository;
import com.jlr.ecp.subscription.enums.remote.RemoteDataErrorType;
import com.jlr.ecp.subscription.service.remoteservice.RemoteOriginalDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


import static com.jlr.ecp.framework.common.pojo.CommonResult.success;

/**
 * 车辆初始化任务实现类（重构版）
 *
 */
@RestController
@Validated
@Slf4j
public class VehicleInitByTSDPJobImpl implements VehicleInitByTSDPJobAPI {

    @Resource
    private RemoteOriginalDataRepository remoteOriginalDataRepository;

    @Resource
    private RemoteOriginalDataService remoteOriginalDataService;

    @Resource
    private VehicleDataParseService vehicleDataParseService;

    @Resource
    private VehicleDataProcessService vehicleDataProcessService;

    @Override
    public CommonResult<Map<Integer, Integer>> processRemoteServiceData(String dataNo, Long pageNo, Long pageSize, Integer status) {
        log.info("开始处理远程服务数据，分页参数: pageNo={}, pageSize={}, dataNo={}, status={}",
            pageNo, pageSize, dataNo, status);
        // 1. 分页查询原始数据
        Page<RemoteOriginalDataDO> pageData = queryOriginalData(dataNo, pageNo, pageSize, status);
        List<RemoteOriginalDataDO> records = pageData.getRecords();
        if (CollUtil.isEmpty(records)) {
            log.info("本次批次无数据需要处理");
            return success(ProcessResult.empty().toMap());
        }
        log.info("本次定时任务要处理的原始数据条数: {} 条", CollUtil.size(records));

        // 2. 解析原始数据
        VehicleDataParseService.ParseResult parseResult = vehicleDataParseService.parseRawData(records);

        // 3. 处理解析错误
        handleParseErrors(parseResult.getErrorIds());

        // 4. 处理解析后的数据
        ProcessResult processResult = processData(parseResult.getParsedData());

        // 5. 合并结果
        ProcessResult finalResult = ProcessResult.of(
            processResult.getSuccessCount(),
            processResult.getFailCount() + parseResult.getErrorIds().size()
        );

        log.info("远程服务数据处理完成: {}", finalResult);
        return success(finalResult.toMap());
    }

    /**
     * 查询原始数据
     */
    private Page<RemoteOriginalDataDO> queryOriginalData(String dataNo, Long pageNo, Long pageSize, Integer status) {
        return remoteOriginalDataRepository.selectPage(
                new Page<>(pageNo, pageSize, false),
                dataNo,
                status,
                VehicleProcessConstants.MISS_COUNT_MAX
        );
    }

    /**
     * 处理解析错误
     */
    private void handleParseErrors(List<VinInitParseErrorDTO> parseErrorDTOList) {
        if (CollUtil.isEmpty(parseErrorDTOList)) {
            log.info("数据解析错误数量为空");
            return ;
        }
        log.info("数据解析错误，错误ID数量: {}", parseErrorDTOList.size());

        remoteOriginalDataService.updateDateFailByParse(
                parseErrorDTOList,
                RemoteDataErrorType.RAW_ERROR.getType()
        );
    }

    /**
     * 处理解析后的数据
     */
    private ProcessResult processData(List<RemoteOriginalDataParseBO> parsedDataList) {
        if (CollUtil.isEmpty(parsedDataList)) {
            log.info("无有效数据需要处理");
            return ProcessResult.empty();
        }

        log.info("开始处理解析后数据，数据量: {}", parsedDataList.size());
        return vehicleDataProcessService.processData(parsedDataList);
    }
}
