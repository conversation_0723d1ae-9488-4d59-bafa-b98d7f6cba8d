package com.jlr.ecp.subscription.file.service;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.file.dao.FileDO;
import com.jlr.ecp.subscription.file.vo.FilePageReqVO;

/**
 * 文件 Service 接口
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 获得文件分页
     *
     * @param pageReqVO 分页查询
     * @return 文件分页
     */
    PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO);

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content, String code);

    /**
     * 删除文件
     *
     * @param id 编号
     */
    void deleteFile(Long id) throws Exception;

    /**
     * 获得文件内容, 需要取file类型的文件的的后缀
     *
     * @param configCode 配置编号
     * @param path 文件路径
     * @return 文件内容
     */
    byte[] getFileContent(String configCode, String path) throws Exception;

    /**
     * 获得文件内容使用全路径
     *
     * @param configCode 配置编号
     * @param path 文件路径
     * @return 文件内容
     */
    byte[] getFileContentByAllPath(String configCode, String path) throws Exception;

    /**
     * 获取上传文件路径的键（file类型的后缀）
     *
     * @param uploadFilePath 上传文件的完整路径
     * @return 提取出来的路径键，如果输入的路径无效或不合法，则返回空字符串
     */
    String getFileUploadPathKey(String uploadFilePath);


    /**
     * 删除文件
     *
     * @param configCode 配置编号
     * @param path 文件路径
     */
    Boolean deleteFile(String configCode, String path);

}

