package com.jlr.ecp.subscription.service.vehicle;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.tenant.core.aop.TenantIgnore;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPExpireDateRequestDTO;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPExpireDateResponseDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.temp.AmaPTempDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.mysql.temp.AmaPTempDOMapper;
import com.jlr.ecp.subscription.enums.amap.AmaPErrorCode;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.AmaPUpdateExpireDateService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmaPUpdateExpireDateServiceImpl implements AmaPUpdateExpireDateService {
    @Resource
    private AmaPTempDOMapper amaPTempDOMapper;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource
    private AmaPProperties amapProperties;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    private static final Integer PAGE_SIZE = 10;


    /**
     * 更新高德地图的过期日期
     *
     * @param amaPExpireDateDTO 高德地图过期日期请求DTO，包含分页信息和更新所需的参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAmaPExpireDate(AmaPExpireDateRequestDTO amaPExpireDateDTO) {
        log.info("更新高德地图的过期日期, amaPExpireDateDTO:{}", amaPExpireDateDTO);
        if (Objects.isNull(amaPExpireDateDTO)) {
            log.info("更新高德地图的过期日期, amaPExpireDateDTO为空");
            return ;
        }
        for (int i = amaPExpireDateDTO.getPageNo(); i < amaPExpireDateDTO.getTotalPage(); i++) {
            Page<AmaPTempDO> amaPTempDOPage = getAmaPTempDOByPage(i);
            if (Objects.isNull(amaPTempDOPage) || CollUtil.isEmpty(amaPTempDOPage.getRecords())) {
                log.info("更新高德地图的过期日期结束, 页号:{}，总数:{}", i, amaPTempDOPage.getTotal());
                return ;
            }
            log.info("更新高德地图的过期日期, 当前读取第:{}页，当前页查询的数量:{} ", i, amaPTempDOPage.getRecords());
            long startTime = System.currentTimeMillis();
            List<CompletableFuture<AmaPExpireDateResponseDTO>> futures = getAmaPExpireDateAsync(amaPTempDOPage);
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            List<AmaPExpireDateResponseDTO> amapDataDOList = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
            updatePIVIPackageAmaPExpireDate(amapDataDOList);
            updateSubscribeDOAmaPExpireDate(amapDataDOList);
            redisTemplate.opsForValue().set(Constants.AMAP_EXPIRE_DATE_PAGE_KEY, i, 2, TimeUnit.DAYS);
            long endTime = System.currentTimeMillis();
            long finishTime = endTime - startTime;
            log.info("更新高德地图的过期日期，第:{}页，用时:{}毫秒", i, finishTime);
            if (finishTime < 1000) {
                try {
                    Thread.sleep(1010 - finishTime);
                } catch (InterruptedException e) {
                    log.error("更新高德地图的过期日期暂停失败:", e);
                }
            }
        }
    }

    /**
     * 更新PIVIPackage的到期日期
     *
     * @param amapDataDOList 高德地图到期日期信息列表
     */
    private void updatePIVIPackageAmaPExpireDate(List<AmaPExpireDateResponseDTO> amapDataDOList) {
        if (CollUtil.isEmpty(amapDataDOList)) {
            log.info("更新PIVIPackage的到期日期,amaPDataDOList为空");
            return ;
        }
        List<String> vinList = amapDataDOList.stream().map(AmaPExpireDateResponseDTO::getCarVin).collect(Collectors.toList());
        List<PIVIPackageDO> piviPackageDOList = queryPIVIPackageDOList(vinList);
        Map<String, AmaPExpireDateResponseDTO> map = getAmaPExpireDateMap(amapDataDOList);
        List<PIVIPackageDO> resp = new ArrayList<>();
        for (PIVIPackageDO piviPackageDO : piviPackageDOList) {
            AmaPExpireDateResponseDTO amaPResponse = map.get(piviPackageDO.getVin());
            if (Objects.isNull(amaPResponse)) {
                log.info("更新PIVIPackage的到期日期,vin:{}，未查询到高德地图到期日期", piviPackageDO.getVin());
                continue;
            }
            if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPResponse.getCode())) {
                log.info("更新PIVIPackage的到期日期,获取的结果不正确,amaPResponse:{}", amaPResponse);
                continue;
            }
            piviPackageDO.setAmaPExpireDate(amaPResponse.getAmaPExpireDate());
            piviPackageDO.setUpdatedTime(LocalDateTime.now());
            resp.add(piviPackageDO);
        }
        if (CollUtil.isEmpty(resp)) {
            log.info("更新PIVIPackage的到期日期,未查询到需要更新的数据");
            return ;
        }
        piviPackageDOMapper.updateBatch(resp);
    }

    /**
     * 将高德地图数据列表转换为以车辆识别码（VIN）为键的映射集合
     *
     * @param amapDataDOList 高德地图数据列表，包含多个高德地图到期日期响应对象
     * @return 返回一个映射集合，键为车辆识别码（VIN），值为对应的高德地图到期日期响应对象
     */
    private Map<String, AmaPExpireDateResponseDTO> getAmaPExpireDateMap(List<AmaPExpireDateResponseDTO> amapDataDOList) {
        Map<String, AmaPExpireDateResponseDTO> map = new HashMap<>();
        for (AmaPExpireDateResponseDTO amaPExpireDateResponseDTO : amapDataDOList) {
            map.put(amaPExpireDateResponseDTO.getCarVin(), amaPExpireDateResponseDTO);
        }
        return map;
    }

    /**
     * 根据车架号列表查询PIVIPackageDO信息列表
     *
     * @param carVinList 车架号列表，作为查询条件之一
     * @return 返回查询到的PIVIPackageDO列表，若无符合条件的记录或发生异常，则返回空列表
     */
    private List<PIVIPackageDO> queryPIVIPackageDOList(List<String> carVinList) {
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PIVIPackageDO::getVin, carVinList).eq(PIVIPackageDO::getIsDeleted, false);
        try {
            return piviPackageDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据车架号列表查询PIVIPackageDO信息列表异常:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 更新订阅服务的高德地图到期日期
     *
     * @param amapDataDOList 高德地图到期日期响应数据列表
     */
    private void updateSubscribeDOAmaPExpireDate(List<AmaPExpireDateResponseDTO> amapDataDOList) {
        if (CollUtil.isEmpty(amapDataDOList)) {
            log.info("更新订阅服务的高德地图到期日期, amapDataDOList为空");
            return ;
        }
        List<String> carVinList = amapDataDOList.stream().map(AmaPExpireDateResponseDTO::getCarVin)
                .collect(Collectors.toList());
        List<SubscriptionServiceDO> serviceDOList = queryAmaPSubscribeDOList(carVinList);
        log.info("更新订阅服务的高德地图到期日期, serviceDOList的数量：{}", serviceDOList.size());
        Map<String, AmaPExpireDateResponseDTO> map = getAmaPExpireDateMap(amapDataDOList);
        List<SubscriptionServiceDO> resp = new ArrayList<>();
        for (SubscriptionServiceDO subscriptionServiceDO : serviceDOList) {
            AmaPExpireDateResponseDTO amaPResponse = map.get(subscriptionServiceDO.getCarVin());
            if (Objects.isNull(amaPResponse)) {
                log.info("更新SubscriptionServiceDO的到期日期,vin:{}，未查询到高德地图到期日期", subscriptionServiceDO.getCarVin());
                continue;
            }
            if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPResponse.getCode())) {
                log.info("更新订阅服务的高德地图到期日期,获取的结果不正确,amaPResponse:{}", amaPResponse);
                continue;
            }
            subscriptionServiceDO.setExpiryDate(amaPResponse.getAmaPExpireDate());
            if (Objects.nonNull(amaPResponse.getAmaPExpireDate())) {
                subscriptionServiceDO.setExpireDateUtc0(amaPResponse.getAmaPExpireDate().minusHours(8));
            }
            subscriptionServiceDO.setUpdatedTime(LocalDateTime.now());
            resp.add(subscriptionServiceDO);
        }
        if (CollUtil.isEmpty(resp)) {
            log.info("更新订阅服务的高德地图到期日期,未查询到需要更新的数据");
            return ;
        }
        subscriptionServiceMapper.updateBatch(resp);
    }

    /**
     * 根据车架号列表查询amaP订阅服务信息列表
     *
     * @param carVinList 车架号列表，用于查询特定车辆的订阅服务信息
     * @return 返回查询到的订阅服务信息列表；如果没有查询到数据或查询异常，则返回一个空列表
     */
    private List<SubscriptionServiceDO> queryAmaPSubscribeDOList(List<String> carVinList) {
        log.info("根据车架号列表查询amaP订阅服务信息列表, carVinList:{}", carVinList);
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SubscriptionServiceDO::getCarVin, carVinList)
                .eq(SubscriptionServiceDO::getServicePackage, ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName())
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        List<SubscriptionServiceDO> resp = new ArrayList<>();
        try {
            resp = subscriptionServiceMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据车架号列表查询AmaP的SubscriptionServiceDO信息列表异常:{}", e.getMessage());
        }
        log.info("根据车架号列表查询amaP订阅服务信息列表, 查询的数量:{}", resp.size());
        return resp;
    }

    /**
     * 异步获取高德地图到期时间
     *
     * @param amaPTempDOPage 车辆临时信息分页数据，包含多条车辆记录
     * @return 返回一个包含未来结果的列表，每个结果对应一个车辆的高德地图到期时间信息
     */
    private List<CompletableFuture<AmaPExpireDateResponseDTO>> getAmaPExpireDateAsync(Page<AmaPTempDO> amaPTempDOPage ) {
        return amaPTempDOPage.getRecords().stream()
                .map(amaPTempDO -> CompletableFuture.supplyAsync(() -> {
                    AmaPCarInfoResponse response = piviAmaPService.getCarAmaPInfoBatch(amapProperties.getPid(), amaPTempDO.getVin());
                    if (Objects.isNull(response)) {
                        log.info("更新AmaP到期时间,查询到期时间为空,vin:{}", amaPTempDO.getVin());
                    }
                    if (AmaPErrorCode.SUCCESSFUL.getCode().equals(response.getCode())) {
                        return AmaPExpireDateResponseDTO.builder()
                                .carVin(amaPTempDO.getVin())
                                .amaPExpireDate(response.getData().getPermissions().get(0).getEndTime())
                                .code(response.getCode())
                                .errDetail(response.getErrDetail())
                                .build();
                    } else {
                        return AmaPExpireDateResponseDTO.builder()
                                .carVin(amaPTempDO.getVin())
                                .code(response.getCode())
                                .errDetail(response.getErrDetail())
                                .build();
                    }
                }, subscribeAsyncThreadPool))
                .collect(Collectors.toList());
    }


    /**
     * 根据分页条件获取AmaPTempDO对象列表
     *
     * @param pageNo 当前页码
     * @return 返回分页的AmaPTempDO对象列表
     */
    @TenantIgnore
    public Page<AmaPTempDO> getAmaPTempDOByPage(Integer pageNo) {
        log.info("根据分页条件获取AmaPTempDO对象列表, pageNo:{}", pageNo);
        Page<AmaPTempDO> pageParam = new Page<>(pageNo, PAGE_SIZE);
        return amaPTempDOMapper.selectPage(pageParam, null);
    }
}
