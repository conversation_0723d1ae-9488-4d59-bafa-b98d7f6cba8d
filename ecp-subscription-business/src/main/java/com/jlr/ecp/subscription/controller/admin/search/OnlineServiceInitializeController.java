package com.jlr.ecp.subscription.controller.admin.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordVO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.model.vo.OnlineServiceBatchQueryRecordVO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeLogVO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeStatusVO;
import com.jlr.ecp.subscription.service.oss.DmsOssFileRecordsService;
import com.jlr.ecp.subscription.service.oss.DmsOssOriginalDataService;
import com.jlr.ecp.subscription.service.oss.VinAdditionalRecordService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Tag(name = "查询中心 - VIN在线服务初始化")
@RestController
@RequestMapping("/search/center/onlineService")
@Validated
public class OnlineServiceInitializeController {

    @Resource
    private DmsOssOriginalDataService originalDataService;

    @Resource
    private DmsOssFileRecordsService fileRecordsService;

    @Resource
    private VinAdditionalRecordService vinAdditionalRecordService;

    @GetMapping("/getStatus")
    @Operation(summary = "单个VIN在线服务初始化状态查询")
    @PreAuthorize("@ss.hasPermission('query:single-vin-initialize-status:forms')")
    public CommonResult<OnlineServiceInitializeStatusVO> getStatus(@RequestParam("carVin") String carVin) {
        if (!CarVinUtil.checkVinFormat(carVin)) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        OnlineServiceInitializeStatusVO statusVO = originalDataService.getStatusByVin(carVin);
        return CommonResult.success(statusVO);
    }

    @GetMapping("/download/template")
    @Operation(summary = "下载批量VIN在线服务初始化状态查询模板")
    @PreAuthorize("@ss.hasPermission('query:multiple-vin-initialize-status:forms')")
    public CommonResult<String> downloadTemplate() {
        // 获取模板文件的URL
        return CommonResult.success(originalDataService.queryExcelUrl());
    }

    @PostMapping("/uploadExcel")
    @Operation(summary = "上传批量查询excel文件")
    @PreAuthorize("@ss.hasPermission('query:multiple-vin-initialize-status:forms')")
    public CommonResult<Boolean> uploadBatchSearch(@RequestBody MultipartFile multipartFile) {
        return originalDataService.uploadBatchSearch(multipartFile);
    }

    @PostMapping("/getBatchQueryRecords")
    @Operation(summary = "批量查询上传记录查询")
    @PreAuthorize("@ss.hasPermission('query:multiple-vin-initialize-status:forms')")
    public CommonResult<PageResult<OnlineServiceBatchQueryRecordVO>> getBatchQueryRecords(@RequestBody VinInitialLogPageDTO logPageDTO) {
        PageResult<OnlineServiceBatchQueryRecordVO> page = originalDataService.getBatchQueryRecords(logPageDTO);
        return CommonResult.success(page);
    }

    @PostMapping("/getLogs")
    @Operation(summary = "VIN在线服务初始化日志查询")
    @PreAuthorize("@ss.hasPermission('query:vin-initialize-log:forms')")
    public CommonResult<PageResult<OnlineServiceInitializeLogVO>> getLogListByPage(@RequestBody @Validated VinInitialLogPageDTO logPageDTO) {
        PageResult<OnlineServiceInitializeLogVO> page = fileRecordsService.getLogPageList(logPageDTO);
        return CommonResult.success(page);
    }

    @GetMapping("/generate")
    @Operation(summary = "生成未初始化原因文件")
    @PreAuthorize("@ss.hasPermission('query:vin-initialize-log:forms')")
    public CommonResult<Boolean> generateReasonFile(@RequestParam("bauJobId") Long bauJobId) {
        return originalDataService.generateReasonFile(bauJobId);
    }

    @PostMapping("additionalRecord")
    @Operation(summary = "补录vin")
    @PreAuthorize("@ss.hasPermission('query:additional-recording-vin:forms')")
    public CommonResult<String> additionalRecord(@RequestBody @Validated VinAdditionalRecordDTO additionalRecordDTO) {
        return vinAdditionalRecordService.additionalRecord(additionalRecordDTO);
    }

    @PostMapping("getAdditionalRecordPage")
    @Operation(summary = "分页查询补录vin记录")
    @PreAuthorize("@ss.hasPermission('query:additional-recording-vin:forms')")
    public CommonResult<PageResult<VinAdditionalRecordVO>> getAdditionalRecordPage(@RequestBody @Validated VinAdditionalRecordPageDTO pageDTO) {
        PageResult<VinAdditionalRecordVO> page = vinAdditionalRecordService.getAdditionalRecordPage(pageDTO);
        return CommonResult.success(page);
    }
}
