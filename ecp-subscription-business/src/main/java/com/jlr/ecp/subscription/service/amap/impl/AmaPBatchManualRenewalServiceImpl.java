package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.*;
import com.jlr.ecp.subscription.excel.listener.amap.AmaPBatchRenewalCheckListener;
import com.jlr.ecp.subscription.excel.listener.amap.AmaPBatchRenewalReadListener;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPBatchRenewalExcel;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPBatchRenewalResultExcel;
import com.jlr.ecp.subscription.excel.utils.AmaPExcelUtil;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.amap.AmaPBatchManualRenewalService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Service
@Slf4j
public class AmaPBatchManualRenewalServiceImpl implements AmaPBatchManualRenewalService {
    @Resource
    private AmaPRenewBatchRecordsDOMapper amaPRenewBatchRecordsDOMapper;

    @Resource
    private AmaPRenewRecordsDOMapper amaPRenewRecordsDOMapper;

    @Resource
    private Snowflake snowflake;

    @Resource
    private FileService fileService;

    @Resource
    private AmaPProperties amaPProperties;

    @Resource
    private PermissionApi permissionApi;

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024 ;

    private static final String FILE_CODE = "AWS_S3_FILE";

    private static final String EXCEL_FORMATTER = ".xlsx";

    /**
     * 查询批量续费页面列表
     *
     * @param amaPPageDto 查询参数对象，包含分页和筛选条件
     * @return 返回分页结果对象，包含批量续费页面视图对象列表和总记录数
     */
    @Override
    public PageResult<AmaPBatchRenewalPageVO> queryBatchRenewalPageList(AmaPBatchRenewalPageDTO amaPPageDto) {
        log.info("查询批量续费页面列表, amaPPageDto:{}", amaPPageDto);
        Page<AmaPRenewBatchRecordsDO> amaPPage = queryBatchAmaPRecords(amaPPageDto);
        if (Objects.isNull(amaPPage) || CollUtil.isEmpty(amaPPage.getRecords())) {
            return new PageResult<>();
        }
        List<AmaPRenewBatchRecordsDO> amaPRenewBatchRecordsDOList = amaPPage.getRecords();
        List<AmaPBatchRenewalPageVO> amaPPageVOList = buildAmaPBatchRenewalPageVOList(amaPRenewBatchRecordsDOList);
        return new PageResult<>(amaPPageVOList, amaPPage.getTotal());
    }

    /**
     * 上传高德批量续费的Excel文件
     *
     * @param multipartFile 多部分文件对象，包含上传的Excel文件
     * @return CommonResult<AmaPBatchRenewalUploadVO> 返回一个通用结果对象，包含上传结果和相关信息
     */
    @Override
    public CommonResult<AmaPBatchRenewalUploadVO> uploadAmaPExcelRenewal(MultipartFile multipartFile) {
        log.info("上传高德批量续费的excel文件:{}", multipartFile.toString());
        long startTime = System.currentTimeMillis();
        // 检查文件大小
        if (multipartFile.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        AmaPBatchRenewalCheckListener renewalCheckListener = new AmaPBatchRenewalCheckListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), AmaPBatchRenewalExcel.class,
                    renewalCheckListener).sheet().doRead();
        } catch (Exception e) {
            log.info("上传高德批量续费的excel文件异常:{}", e.getMessage());
        }
        AmaPBatchRenewalUploadVO renewalUploadVO = new AmaPBatchRenewalUploadVO();
        if(renewalCheckListener.getCheckResult() && renewalCheckListener.getVinRepeatable()) {
            renewalUploadVO.setType("info");
            renewalUploadVO.setMsg("当前文件包含重复VIN号,建议二次确认文件");
        } else {
            renewalUploadVO.setType("success");
            renewalUploadVO.setMsg("请关注续费文件校验结果");
        }
        long endTime = System.currentTimeMillis();
        log.info("上传高德批量续费的excel文件解析完成，花费时间:{}毫秒", (endTime-startTime));
        if (Boolean.TRUE.equals(renewalCheckListener.getFormatError())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(renewalCheckListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        addAmaPBatchRenewalRecords(renewalCheckListener);
        return CommonResult.success(renewalUploadVO);
    }

    /**
     * 批量发送高德续费
     *
     * @param amaPBatchSendDTO 批量发送AMA P续订的通知对象，包含文件的源路径和批次编号等信息
     */
    @Override
    public CommonResult<String> batchSendAmaPRenewal(AmaPBatchSendDTO amaPBatchSendDTO) {
        log.info("批量发送高德续费, amaPBatchSendDTO:{}", amaPBatchSendDTO);
        try {
            Long batchNo = Long.parseLong(amaPBatchSendDTO.getBatchNo());
            List<AmaPRenewBatchRecordsDO> progressRecordsDOList = queryAmaPRenewalByDealStatus(DealStatusEnum.PROGRESS.getStatus());
            if (CollUtil.isNotEmpty(progressRecordsDOList)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
            }
            AmaPRenewBatchRecordsDO batchRecordsDO = queryAmaPRenewalByBatchNo(batchNo);
            if (Objects.isNull(batchRecordsDO)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
            }
            String uploadPathKey = getUploadPathKey(batchRecordsDO.getUploadFile());
            byte[] content = fileService.getFileContent(FILE_CODE, uploadPathKey);
            List<List<AmaPBatchRenewalExcel>> batchRenewalExcelList = doReadAmaPRenewalExcel(content);
            for (List<AmaPBatchRenewalExcel> batchRenewalExcels : batchRenewalExcelList) {
                batchInsertAmaPBatchRenewalRecord(batchRenewalExcels, batchNo);
            }
            batchRecordsDO.setDealStatus(DealStatusEnum.PROGRESS.getStatus());
            amaPRenewBatchRecordsDOMapper.updateById(batchRecordsDO);
        } catch (Exception e) {
            log.info("批量发送高德续费异常:{}", e.getMessage());
            return CommonResult.error(new ErrorCode(1000001, e.getMessage()));
        }
        return CommonResult.success("请求已经发送，请前往续费记录查看结果");
    }

    /**
     * 获取模板URL
     *
     * @return 模板的URL地址
     */
    @Override
    public String getTemplateUrl() {
        return amaPProperties.getRenewalExcelUrl();
    }

    /**
     * 根据批次编号查询续费记录
     *
     * @param batchNo 批次编号，用于标识特定的续费批次
     * @return 返回符合批次编号且未被标记为已删除的续费记录如果不存在这样的记录，则返回null
     */
    private AmaPRenewBatchRecordsDO queryAmaPRenewalByBatchNo(Long batchNo) {
        LambdaQueryWrapper<AmaPRenewBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewBatchRecordsDO::getBatchNo, batchNo)
                .eq(AmaPRenewBatchRecordsDO::getIsDeleted, false);
        return amaPRenewBatchRecordsDOMapper.selectOne(queryWrapper);
    }

    /**
     * 根据批次编号查询续批记录
     *
     * @param dealStatus 处理中状态
     * @return 返回对应的续批记录对象（AmaPRenewBatchRecordsDO）如果不存在，则返回null
     */
    private List<AmaPRenewBatchRecordsDO> queryAmaPRenewalByDealStatus(Integer dealStatus) {
        LambdaQueryWrapper<AmaPRenewBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewBatchRecordsDO::getDealStatus, dealStatus)
                .eq(AmaPRenewBatchRecordsDO::getIsDeleted, false);
        return amaPRenewBatchRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 批量插入会员续费记录
     *
     * @param batchRenewalExcelList 会员续费Excel列表，包含多个会员的续费信息
     * @param batchNo 批次编号，用于标识当前批次的唯一性
     */
    public void batchInsertAmaPBatchRenewalRecord(List<AmaPBatchRenewalExcel> batchRenewalExcelList, Long batchNo) {
        if (CollUtil.isEmpty(batchRenewalExcelList)) {
            log.info("批量插入会员续费记录为空, batchNo:{}", batchNo);
            return ;
        }
        List<AmaPRenewRecordsDO> amaPRenewRecordsDOList = new ArrayList<>();
        for (AmaPBatchRenewalExcel amaPBatchRenewalExcel : batchRenewalExcelList) {
            AmaPRenewRecordsDO amaPRenewRecordsDO = buildAmaPBatchRenewalRecord(amaPBatchRenewalExcel, batchNo);
            amaPRenewRecordsDOList.add(amaPRenewRecordsDO);
        }
        amaPRenewRecordsDOMapper.insertBatch(amaPRenewRecordsDOList);
    }

    /**
     * 构建AMA P批次续订记录对象
     *
     * @param amaPBatchRenewalExcel Excel数据，包含车辆和续订信息
     * @param batchNo 批次编号
     * @return AmaPRenewRecordsDO 返回生成的续订记录对象
     */
    private AmaPRenewRecordsDO buildAmaPBatchRenewalRecord(AmaPBatchRenewalExcel amaPBatchRenewalExcel, Long batchNo) {
        if (Objects.isNull(amaPBatchRenewalExcel)) {
            return AmaPRenewRecordsDO.builder().build();
        }
        return AmaPRenewRecordsDO.builder()
                .carVin(CarVinUtil.carVinToUpperCase(amaPBatchRenewalExcel.getCarVin()))
                .renewYear(AmaPRenewalYearEnum.getCodeByDesc(amaPBatchRenewalExcel.getRenewalYear()))
                .renewNo(batchNo)
                .cusOrderId(snowflake.nextIdStr())
                .operator(getOperatorUser())
                .dataSource(AmaPDataSourceEnum.BATCH.getDateSource())
                .renewStatus(AmaPRenewStatusEnum.WAIT_RENEW.getStatus())
                .build();
    }

    /**
     * 读取续费Excel文件
     *
     * @param bytes Excel文件的字节内容
     * @return 解析后的Excel数据列表
     */
    public List<List<AmaPBatchRenewalExcel>> doReadAmaPRenewalExcel(byte[] bytes) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "AmaPBatchRenewalReadS3Excel" + System.currentTimeMillis() + EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("读取续费Excel文件, filePath:{}", filePath);
        AmaPBatchRenewalReadListener amaPReadListener =  new AmaPBatchRenewalReadListener();
        List<List<AmaPBatchRenewalExcel>> resp = new ArrayList<>();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(bytes);
            outputStream.flush();
            EasyExcel.read(fileName, AmaPBatchRenewalExcel.class, amaPReadListener).sheet().doRead();
            resp = amaPReadListener.getAllList();
            log.info("读取续费Excel文件, filePath:{}", filePath);
        } catch (Exception e) {
            log.info("读取续费Excel文件, 解析excel文件异常:{}", e.getMessage());
        } finally {
            FileUtil.del(fileName);
        }
        return resp;
    }

    /**
     * 获取上传文件路径的键
     *
     * @param uploadExcelPath 上传文件的完整路径
     * @return 提取出来的路径键，如果输入的路径无效或不合法，则返回空字符串
     */
    private String getUploadPathKey(String uploadExcelPath) {
        log.info("获取上传文件路径的键, uploadExcelPath:{}", uploadExcelPath);
        if (StringUtils.isBlank(uploadExcelPath)) {
            return "";
        }
        // 找到 "file/" 子串在 URL 中的起始位置
        int startIndex = uploadExcelPath.indexOf("file/") + "file/".length();
        if (startIndex < 0) {
            return "";
        }
        // 从找到的起始位置截取至字符串末尾
        return uploadExcelPath.substring(startIndex);
    }

    /**
     * 添加批量续费记录
     *
     * @param renewalCheckListener 批量续费检查监听器，包含所有待处理数据和检查结果
     */
    private void addAmaPBatchRenewalRecords(AmaPBatchRenewalCheckListener renewalCheckListener) {
        AmaPRenewBatchRecordsDO amaPRenewBatchRecordsDO = new AmaPRenewBatchRecordsDO();
        String uploadS3AmaPSourcePath = uploadAamPRenewalSourceToS3File(renewalCheckListener.getAllDataList());
        amaPRenewBatchRecordsDO.setBatchNo(snowflake.nextId());
        amaPRenewBatchRecordsDO.setUploadFile(uploadS3AmaPSourcePath);
        if(Boolean.FALSE.equals(renewalCheckListener.getCheckResult())) {
            String uploadAmaPResultPath = uploadAamPRenewalResultToS3File(renewalCheckListener.getResultExcelList());
            amaPRenewBatchRecordsDO.setVerifyResult(VerifyResultEnum.FAIL.getCode());
            amaPRenewBatchRecordsDO.setVerifyResultFile(uploadAmaPResultPath);
        } else {
            amaPRenewBatchRecordsDO.setVerifyResult(VerifyResultEnum.SUCCESS.getCode());
        }
        amaPRenewBatchRecordsDO.setDealStatus(DealStatusEnum.WAITED.getStatus());
        amaPRenewBatchRecordsDO.setOperator(getOperatorUser());
        amaPRenewBatchRecordsDOMapper.insert(amaPRenewBatchRecordsDO);
    }

    /**
     * 将批量续费结果Excel文件上传至S3存储
     *
     * @param batchRenewalResultExcels 批量续费结果的Excel对象列表
     * @return 上传至S3后的文件路径
     */
    private String uploadAamPRenewalResultToS3File(List<AmaPBatchRenewalResultExcel> batchRenewalResultExcels) {
        String uploadS3ResultFilePath = "";
        File uploadS3TempResultExcel = null;
        try {
            uploadS3TempResultExcel = AmaPExcelUtil.amaPBatchRenewalWriteResultFile(batchRenewalResultExcels);
            if (Objects.isNull(uploadS3TempResultExcel)) {
                log.info("将批量续费结果Excel文件上传至S3存储, 生成本地临时文件为空");
                return uploadS3ResultFilePath;
            }
            String fileName = "ECP_AMAP批量续费文件校验" +  System.currentTimeMillis();
            uploadS3ResultFilePath = fileService.createFile(null, "AmaPBatchRenewalResultExcel" + File.separator +
                     fileName + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempResultExcel), FILE_CODE);
            log.info("将批量续费结果Excel文件上传至S3存储, 本地文件原始文件路径:{}", uploadS3TempResultExcel.getPath());
        } catch (Throwable e) {
            log.info("将批量续费结果Excel文件上传至S3存储:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempResultExcel)) {
                FileUtil.del(uploadS3TempResultExcel);
                log.info("将批量续费结果Excel文件上传至S3存储, 成功删除本地临时文件");
            }
        }
        return uploadS3ResultFilePath;
    }

    /**
     * 将批量续订Excel源文件上传至S3
     *
     * @param batchRenewalExcelList 包含批量续订Excel信息的列表
     * @return 上传到S3的文件路径，如果上传失败则返回空字符串
     */
    private String uploadAamPRenewalSourceToS3File(List<AmaPBatchRenewalExcel> batchRenewalExcelList) {
        String uploadS3FilePath = "";
        File uploadS3TempSourceExcel = null;
        try {
            uploadS3TempSourceExcel = AmaPExcelUtil.amaPBatchRenewalWriteSourceFile(batchRenewalExcelList);
            if (Objects.isNull(uploadS3TempSourceExcel)) {
                log.info("将批量续订Excel源文件上传至S3, 生成本地临时文件为空");
                return uploadS3FilePath;
            }
            uploadS3FilePath = fileService.createFile(null, "AmaPBatchRenewalExcel" + File.separator
                    + System.currentTimeMillis() + EXCEL_FORMATTER, FileUtil.readBytes(uploadS3TempSourceExcel), FILE_CODE);
            log.info("将批量续订Excel源文件上传至S3, 本地文件原始文件路径:{}", uploadS3TempSourceExcel.getPath());
        } catch (Throwable e) {
            log.info("将批量续订Excel源文件上传至S3异常:{}", e.getMessage());
        } finally {
            if (Objects.nonNull(uploadS3TempSourceExcel)) {
                FileUtil.del(uploadS3TempSourceExcel);
                log.info("将批量续订Excel源文件上传至S3, 成功删除本地临时文件");
            }
        }
        return uploadS3FilePath;
    }

    /**
     * 获取当前操作用户
     *
     * @return 当前操作用户 如果登录用户名称为空或空白，则返回"System"，否则返回登录用户的名称
     */
    private String getOperatorUser() {
        if (StringUtils.isBlank(WebFrameworkUtils.getLoginUserName())) {
            return "System";
        }
        return WebFrameworkUtils.getLoginUserName();
    }

    /**
     * 构建高德批量续费页面的VO列表
     *
     * @param batchRecordsDOList 包含所有批量续费记录的数据库操作对象列表
     * @return 返回一个页面视图对象列表，其中每个对象代表一条批量续费记录
     */
    private List<AmaPBatchRenewalPageVO> buildAmaPBatchRenewalPageVOList(List<AmaPRenewBatchRecordsDO> batchRecordsDOList) {
        List<AmaPBatchRenewalPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(batchRecordsDOList)) {
            return resp;
        }
        for (AmaPRenewBatchRecordsDO batchRecordsDO : batchRecordsDOList) {
            resp.add(buildAmaPBatchRenewalPageVO(batchRecordsDO));
        }
        return resp;
    }

    /**
     * 构建高德批量续费页面的视图对象
     *
     * @param batchRecordsDO 批量续费记录数据对象，包含批量续费的相关信息
     * @return AmaPBatchRenewalPageVO 返回构建的高德批量续费页面视图对象
     */
    private AmaPBatchRenewalPageVO buildAmaPBatchRenewalPageVO(AmaPRenewBatchRecordsDO batchRecordsDO) {
        return AmaPBatchRenewalPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(batchRecordsDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .operator(batchRecordsDO.getOperator())
                .checkResultDesc(VerifyResultEnum.getDescByCode(batchRecordsDO.getVerifyResult()))
                .checkResultStatus(batchRecordsDO.getVerifyResult())
                .batchNo(batchRecordsDO.getBatchNo())
                .operateStatus(batchRecordsDO.getDealStatus())
                .errorDetailPath(batchRecordsDO.getVerifyResultFile())
                .build();
    }

    /**
     * 查询批量续费记录列表
     *
     * @param amaPPageDto 查询参数对象，包含分页信息和排序条件等
     * @return 返回分页查询结果，包含批量续费记录列表和分页信息
     */
    private Page<AmaPRenewBatchRecordsDO> queryBatchAmaPRecords(AmaPBatchRenewalPageDTO amaPPageDto) {
        if (Objects.isNull(amaPPageDto)) {
            return new Page<>();
        }
        Page<AmaPRenewBatchRecordsDO> pageParam = new Page<>(amaPPageDto.getPageNo(), amaPPageDto.getPageSize());
        LambdaQueryWrapper<AmaPRenewBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewBatchRecordsDO::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(amaPPageDto.getOperateTimeSort())) {
            queryWrapper.orderByAsc(AmaPRenewBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(AmaPRenewBatchRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(AmaPRenewBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(AmaPRenewBatchRecordsDO::getId);
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }

        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(AmaPRenewBatchRecordsDO::getOperator, userName);
            log.info("查询批量续费记录列表, 限制用户访问的数据，user:{}, amaPPageDto:{}", userName, amaPPageDto);
        } else {
            log.info("查询批量续费记录列表, 登录用户为空或用户是超管, amaPPageDto:{}", amaPPageDto);
        }
        return amaPRenewBatchRecordsDOMapper.selectPage(pageParam, queryWrapper);
    }
}
