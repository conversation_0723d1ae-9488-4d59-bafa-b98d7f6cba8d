package com.jlr.ecp.subscription.service.vehicle;


import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.api.model.vo.UserCarServiceListVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteQueryByVinRespDTO;

import java.util.List;
import java.util.Set;

/**
 * t_incontrol_vehicle服务接口
 *
 * <AUTHOR>
 * @since 2023-12-20 14:45:58
 * @description 由 Mybatisplus Code Generator 创建
 */
public interface IncontrolVehicleService {


    List<IncontrolVehicleDTO> getIncontrolVehicleByConsumerCode(String consumerCode,String brandCode);

    /**
     * @param carVinList
     * @return
     */
    List<IncontrolVehicleByCarDTO> getIncontrolVehicleByCarVinList(List<String> carVinList);

    /** 查询并存储车辆信息
     * @param inControlId
     * @return
     */
    void getAndStoreCarInfo(String inControlId);

    /** 查询并存储车辆信息ByIcr
     * @param inControlId inControlId
     */
    void getAndStoreCarInfoByICR(String inControlId, Set<String> vinSet);

    /** 根据icr账号从 TSDP查询车辆和服务信息
     * @param inControlId
     * @return
     */
    List<VinsAndServiceDTO> getVinsAndServiceByIncontronId(String inControlId);

    /**
     * 根据icr账号从 TSDP查询车辆和服务信息 并保存到表里
     * @param inControlId
     * @return
     */
    List<VinsAndServiceDTO> queryAndStoreVinsAndServiceInfo(String inControlId);

    /**
     * 获取
     * @param consumerCode 顾客号
     * @return List<UserCarServiceListVO>
     */
    List<UserCarServiceListVO> getUserCarServiceList(String consumerCode, String brandCode, String clientId);

    /** 查询并存储车辆信息ByVin
     * @param carVin carVin
     */
    boolean getAndStoreCarInfoByVin(String carVin);

    /**
     * 根据vin从 TSDP查询车辆和服务信息 并保存到表里
     * @param vin
     * @return
     */
    List<VinsAndServiceDTO> queryAndStoreByVin(String vin);
}
