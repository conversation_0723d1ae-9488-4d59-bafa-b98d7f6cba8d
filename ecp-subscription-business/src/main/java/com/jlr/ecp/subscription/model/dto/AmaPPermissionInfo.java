package com.jlr.ecp.subscription.model.dto;

import cn.hutool.core.annotation.Alias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPPermissionInfo {
    /**
     *  sid
     * */
    private String sid;

    /**
     *  状态
     * */
    private Integer status;

    /**
     *  amap到期时间
     * */
    @Alias("end_time")
    private LocalDateTime endTime;

    /**
     *  描述
     * */
    private String description;
}
