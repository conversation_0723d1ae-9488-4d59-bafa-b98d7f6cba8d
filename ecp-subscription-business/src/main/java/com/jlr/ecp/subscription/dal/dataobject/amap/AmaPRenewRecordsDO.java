package com.jlr.ecp.subscription.dal.dataobject.amap;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_amap_renew_records")
public class AmaPRenewRecordsDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * VIN;VIN
     */
    @TableField("car_vin")
    private String carVin;

    /**
     *  高德充值的订单号
     * */
    @TableField("cus_order_id")
    private String cusOrderId;

    /**
     * 续费时长;续费时长 1：1年 3：3年
     */
    @TableField("renew_year")
    private Integer renewYear;

    /**
     * 续费编号;续费编号，对于批量续费就是批次号
     */
    @TableField("renew_no")
    private Long renewNo;

    /**
     * 操作人账号;操作人账号
     */
    @TableField("operator")
    private String operator;

    /**
     * 数据来源;数据来源 1单次续费 2批量续费
     */
    @TableField("data_source")
    private Integer dataSource;

    /**
     * 续费状态;续费状态 0：待续费 1：续费中  2：续费成功 3：续费失败
     */
    @TableField("renew_status")
    private Integer renewStatus;

    /**
     * 续费前到期日;续费前到期日
     */
    @TableField("renew_before_expiry_date")
    private LocalDateTime renewBeforeExpiryDate;

    /**
     * 续费后到期日;续费后到期日
     */
    @TableField("renew_after_expiry_date")
    private LocalDateTime renewAfterExpiryDate;

    /**
     *  高德续费结果 1成功 其他值失败
     * */
    @TableField("order_result_code")
    private String orderResultCode;

    /**
     *  高德查询结果 1成功 其他值失败
     * */
    @TableField("query_result_code")
    private String queryResultCode;

    /**
     * 续费失败原因;续费失败原因
     */
    @TableField("error_desc")
    private String errorDesc;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;
}

