package com.jlr.ecp.subscription.controller.admin.dto.search;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "服务到期结果查询DTO")
public class ExpireResultQueryDTO extends PageParam {
    @Schema(description = "车架号")
    private String carVin;

    @Schema(description = "批次号")
    private List<String> batchNoList;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "查询状态")
    private Integer queryStatus;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "操作时间排序")
    private String operateTimeSort;
}
