package com.jlr.ecp.subscription.service.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.QueryAmaPExpireDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.QueryAmaPExpireVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPQueryRecordsDO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SearchAmaPExpireService {

    /**
     * 获取查询模板URL
     *
     * @return 查询模板的URL字符串
     */
    String getQueryTemplateUrl();

    /**
     * 查询批次页面列表
     *
     * @param queryPageDTO 页面查询参数对象，包含查询条件和分页信息
     * @return 返回批次页面数据封装对象，包含批次数据列表和总记录数
     */
    PageResult<AmaPBatchQueryPageVO> queryBatchPageList(AmaPBatchQueryPageDTO queryPageDTO);

    /**
     * 批量查询高德地图会员到期情况
     *
     * @param multipartFile 用户上传的包含会员信息的文件
     * @return CommonResult<String> 查询操作的结果封装
     */
    CommonResult<AmaPBatchRenewalUploadVO> batchSearchAmaPExpire(MultipartFile multipartFile);

    /**
     * 根据批次号获取支付宝查询批次信息
     *
     * @param batchNo 批次号字符串，用于标识查询的批次
     * @return 返回批次号字符串，如果未找到相关记录，则返回空字符串
     */
    String getAmaPQueryBatchNo(String batchNo);


    /**
     * 获取查询状态列表
     *
     * @return 包含所有高德查询状态的列表，每个状态都封装在AmaPQueryStatusVO对象中
     */
    List<AmaPQueryStatusVO> getAmaPQueryStatus();

    /**
     * 批量查询高德地图结果
     *
     * @param queryAmaPExpireDTO 查询参数对象，包含高德地图到期信息的查询条件
     * @return 返回分页结果对象，包含高德地图到期信息的列表和总记录数
     */
    PageResult<QueryAmaPExpireVO> amaPBatchQueryResult(QueryAmaPExpireDTO queryAmaPExpireDTO);

    /**
     * 批量查询AmaP到期日期
     *
     * @param amaPQueryRecordsDOList 包含AmaP查询记录的列表，用于批量查询凭证到期日期
     * @param batchNo 查询批次的唯一编号，用于标识和管理查询批次
     */
    void batchQueryAmaPExpireDate(List<AmaPQueryRecordsDO> amaPQueryRecordsDOList, Long batchNo);
}
