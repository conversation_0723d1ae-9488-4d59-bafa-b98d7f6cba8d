package com.jlr.ecp.subscription.service.search.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.dto.Vehicle5000InfoDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ExpireResultQueryDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ServiceBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.SearchExpireUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceExpireQueryResultVO;
import com.jlr.ecp.subscription.dal.dataobject.search.ExpireSearchBatchRecordDO;
import com.jlr.ecp.subscription.dal.dataobject.search.ExpireSearchDetailRecordDO;
import com.jlr.ecp.subscription.dal.mysql.search.ExpireSearchBatchRecordDOMapper;
import com.jlr.ecp.subscription.dal.mysql.search.ExpireSearchDetailRecordDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.enums.amap.QueryUploadResultEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.search.ServiceExpireKeyMapping;
import com.jlr.ecp.subscription.enums.search.ServiceExpireQueryStatusEnum;
import com.jlr.ecp.subscription.excel.listener.search.ExpireSearchCheckListener;
import com.jlr.ecp.subscription.excel.pojo.search.ExpireBatchSearchExcel;
import com.jlr.ecp.subscription.service.search.VehicleExpireBatchSearchService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class VehicleExpireBatchSearchServiceImpl implements VehicleExpireBatchSearchService {

    @Value("${search.templateUrl}")
    private String templateUrl;

    @Resource
    private ExpireSearchBatchRecordDOMapper serviceBatchRecordDOMapper;

    @Resource
    private ExpireSearchDetailRecordDOMapper serviceDetailRecordDOMapper;

    @Resource
    private SubscriptionService subscriptionService;

    @Resource
    private Snowflake snowflake;

    @Resource
    private PermissionApi permissionApi;

    /**
     * 获取车辆过期模板的URL。
     *
     * @return String 返回车辆过期模板的URL地址
     */
    @Override
    public String getVehicleExpireTemplateUrl() {
        log.info("获取车辆过期模板的URL");
        return templateUrl;
    }

    /**
     * 查询批量服务到期日分页列表
     *
     * @param queryPageDTO 查询条件对象，包含分页信息和查询参数
     * @return PageResult<ServiceBatchQueryPageVO> 返回分页结果，包含批量服务列表和总记录数
     */
    @Override
    public PageResult<ServiceBatchQueryPageVO> queryBatchServicePageList(ServiceBatchQueryPageDTO queryPageDTO) {
        log.info("查询批量服务到期日分页列表, queryPageDTO:{}", queryPageDTO);
        Page<ExpireSearchBatchRecordDO> pageRes = queryServiceBatchPageList(queryPageDTO);
        if (Objects.isNull(pageRes) || CollUtil.isEmpty(pageRes.getRecords())) {
            return new PageResult<>();
        }
        List<ExpireSearchBatchRecordDO> batchRecordDOList = pageRes.getRecords();
        List<ServiceBatchQueryPageVO> serviceBatchPageList =  buildServiceBatchQueryPageVOList(batchRecordDOList);
        return new PageResult<>(serviceBatchPageList, pageRes.getTotal());
    }

    /**
     * 批量查询服务过期信息
     *
     * @param multipartFile 上传的Excel文件，包含需要查询的过期信息。
     * @return CommonResult<SearchExpireUploadVO> 返回一个包含上传结果的对象，如果成功则返回成功信息，否则返回错误信息。
     */
    @Override
    public CommonResult<SearchExpireUploadVO> batchSearchExpire(MultipartFile multipartFile) {
        if (Objects.isNull(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        log.info("批量查询服务过期信息,multipartFile:{}", multipartFile);
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        List<ExpireSearchBatchRecordDO> queryBatchRecordsDOList = queryExpireBatchByStatus(DealStatusEnum.PROGRESS.getStatus());
        if (CollUtil.isNotEmpty(queryBatchRecordsDOList)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
        }
        ExpireSearchCheckListener searchExpireListener = new ExpireSearchCheckListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), ExpireBatchSearchExcel.class,
                    searchExpireListener).sheet().doRead();
        } catch (Exception e) {
            log.info("批量查询服务过期信息,异常:{}", e.getMessage());
        }
        if (Boolean.TRUE.equals(searchExpireListener.getIsExcelFormatError())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(searchExpireListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        if (searchExpireListener.getTotalSize() > 1000) {
            return CommonResult.error(1000001, "上传内容条数大于1000条");
        }
        log.info("批量查询服务过期信息, searchExpireListener:{}", searchExpireListener);
        String batchNo = snowflake.nextIdStr();
        insertBatchExpireQueryRecords(batchNo);
        insertExpireDetailRecords(searchExpireListener, batchNo);
        searchExpireListener.setAllDataList(null);
        //查询服务到期日并更新record和batch的状态
        CompletableFuture.runAsync(() -> queryServiceAndUpdateExpireResult(batchNo));
        SearchExpireUploadVO uploadVO = SearchExpireUploadVO.builder()
                .type("success")
                .msg("上传成功，请关注批量查询结果")
                .build();
        return CommonResult.success(uploadVO);
    }

    /**
     * 根据批次号获取过期查询的批次号是否存在
     *
     * @param batchNo 要查询的批次号
     * @return 查询到的批次号，如果未找到则返回空字符串
     */
    @Override
    public String getExpireQueryBatchNo(String batchNo) {
        ExpireSearchBatchRecordDO batchRecordDO = getExpireBatchRecordsByBatchNo(batchNo);
        if (Objects.isNull(batchRecordDO)) {
            return "";
        }
        return batchRecordDO.getBatchNo();
    }

    /**
     * 获取所有过期的查询状态
     *
     * @return List<AmaPQueryStatusVO> 包含所有过期查询状态的列表，每个状态以 `AmaPQueryStatusVO` 对象表示。
     */
    @Override
    public List<AmaPQueryStatusVO> getExpireQueryStatus() {
        ServiceExpireQueryStatusEnum[] queryStatusEnums = ServiceExpireQueryStatusEnum.values();
        List<AmaPQueryStatusVO> resp = new ArrayList<>();
        for (ServiceExpireQueryStatusEnum queryStatusEnum : queryStatusEnums) {
            AmaPQueryStatusVO amaPQueryStatusVO = AmaPQueryStatusVO.builder()
                    .queryStatusCode(queryStatusEnum.getQueryStatus())
                    .queryStatusDesc(queryStatusEnum.getQueryDesc())
                    .build();
            resp.add(amaPQueryStatusVO);
        }
        return resp;
    }

    /**
     * 服务到期批量查询结果
     *
     * @param expireResultQueryDTO 查询条件对象，包含查询所需的各种参数
     * @return PageResult<ServiceExpireQueryResultVO> 分页查询结果，包含查询到的服务到期结果列表及总记录数
     */
    @Override
    public PageResult<ServiceExpireQueryResultVO> expireBatchQueryResult(ExpireResultQueryDTO expireResultQueryDTO) {
        log.info("服务到期批量查询结果, expireResultQueryDTO:{}", expireResultQueryDTO);
        if (Objects.isNull(expireResultQueryDTO)) {
            return new PageResult<>();
        }
        Page<ExpireSearchDetailRecordDO> queryRecordsPage = getExpirePageByQueryDTO(expireResultQueryDTO);
        if (Objects.isNull(queryRecordsPage) || CollUtil.isEmpty(queryRecordsPage.getRecords())) {
            log.info("服务到期批量查询结果为空, queryRecordsPage:{}", queryRecordsPage);
            return new PageResult<>();
        }
        log.info("服务到期批量查询结果, 查询的数量:{}", queryRecordsPage.getTotal());
        List<ExpireSearchDetailRecordDO> queryList = queryRecordsPage.getRecords();
        List<ServiceExpireQueryResultVO> resp = buildServiceExpireVoList(queryList);
        return new PageResult<>(resp, queryRecordsPage.getTotal());
    }

    /**
     * 构建服务到期查询结果VO列表
     *
     * @param queryList 包含服务到期详细信息的 `ExpireSearchDetailRecordDO` 列表
     * @return 转换后的 `ServiceExpireQueryResultVO` 列表，如果输入列表为空，则返回空列表
     */
    private List<ServiceExpireQueryResultVO> buildServiceExpireVoList(List<ExpireSearchDetailRecordDO> queryList) {
        List<ServiceExpireQueryResultVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(queryList)) {
            log.info("构建服务到期查询结果VO列表, queryList为空");
            return resp;
        }
        log.info("构建服务到期查询结果VO列表, queryList的数量:{}", queryList.size());
        for (ExpireSearchDetailRecordDO searchDetailRecordDO : queryList) {
            resp.add(buildServiceExpireVo(searchDetailRecordDO));
        }
        return resp;
    }

    /**
     * 构建服务过期查询结果VO对象
     *
     * @param searchDetailRecordDO 包含查询详情的记录对象，用于构建 `ServiceExpireQueryResultVO`
     * @return 构建完成的服务过期查询结果VO对象
     */
    private ServiceExpireQueryResultVO buildServiceExpireVo(ExpireSearchDetailRecordDO searchDetailRecordDO) {
        if (Objects.isNull(searchDetailRecordDO)) {
            log.info("构建服务过期查询结果VO对象, searchDetailRecordDO为空");
            return ServiceExpireQueryResultVO.builder().build();
        }
        // 使用 Builder 构建基础对象
        return ServiceExpireQueryResultVO.builder()
                .batchNo(searchDetailRecordDO.getBatchNo())
                .queryStatus(ServiceExpireQueryStatusEnum.getDescByStatus(searchDetailRecordDO.getQueryStatus()))
                .carVin(searchDetailRecordDO.getCarVin())
                .brandName(searchDetailRecordDO.getBrandName())
                .seriesName(searchDetailRecordDO.getSeriesName())
                .seriesCode(searchDetailRecordDO.getSeriesCode())
                .carSystemModel(getCarSystemModelDesc(searchDetailRecordDO.getCarSystemModel()))
                .hasInfoEntertain(getHasInfoEntertainDesc(searchDetailRecordDO.getHasInfoEntertain()))
                .modelYear(searchDetailRecordDO.getModelYear())
                .configCode(searchDetailRecordDO.getConfigCode())
                .configName(searchDetailRecordDO.getConfigName())
                .brandCode(searchDetailRecordDO.getBrandCode())
                .remoteServiceDate(searchDetailRecordDO.getRemoteServiceDate())
                .piviServiceDate(searchDetailRecordDO.getPiviServiceDate())
                .appDServiceDate(searchDetailRecordDO.getAppDServiceDate())
                .amaPServiceDate(searchDetailRecordDO.getAmaPServiceDate())
                .unicomServiceDate(searchDetailRecordDO.getUnicomServiceDate())
                .invoiceServiceDate(searchDetailRecordDO.getInvoiceServiceDate())
                .operateTime(TimeFormatUtil.timeToStringByFormat(searchDetailRecordDO.getOperateTime(), TimeFormatUtil.formatter_6))
                .operator(searchDetailRecordDO.getOperator())
                .errorReason(searchDetailRecordDO.getErrorReason())
                .build();
    }

    /**
     * 根据车辆系统模型描述返回对应的描述信息。
     *
     * @param carSystemModel 车辆系统模型字符串，用于判断系统类型。
     * @return 如果车辆系统模型为 "PIVI"，则返回 "是"，否则返回 "否"。
     */
    private String getCarSystemModelDesc(String carSystemModel) {
        if (CarSystemModelEnum.PIVI.getCode().equals(carSystemModel)) {
            return "是";
        }
        if ("-".equals(carSystemModel)) {
            return carSystemModel;
        }
        return "否";
    }

    /**
     * 根据是否有信息娱乐系统的标识符返回对应的描述信息。
     *
     * @param hasInfoEntertain 表示是否有信息娱乐系统的标识符，通常为 "Y" 或 "N"。
     * @return 返回 "是" 表示有信息娱乐系统，返回 "否" 表示没有信息娱乐系统。
     */
    private String getHasInfoEntertainDesc(String hasInfoEntertain) {
        if ("Y".equals(hasInfoEntertain)) {
            return "是";
        } else if ("N".equals(hasInfoEntertain)) {
            return "否";
        }
        return "-";
    }


    /**
     * 根据查询条件获取过期记录的分页数据
     *
     * @param expireQueryDTO 包含查询条件的DTO对象，不能为null
     * @return 返回包含过期记录的分页数据，如果expireQueryDTO为null，则返回null
     */
    private Page<ExpireSearchDetailRecordDO> getExpirePageByQueryDTO(ExpireResultQueryDTO expireQueryDTO) {
        if (Objects.isNull(expireQueryDTO)) {
            return null;
        }
        Page<ExpireSearchDetailRecordDO> pageParam = new Page<>(expireQueryDTO.getPageNo(), expireQueryDTO.getPageSize());
        LambdaQueryWrapper<ExpireSearchDetailRecordDO> queryWrapper = buildExpireResultQueryWrapper(expireQueryDTO);
        return serviceDetailRecordDOMapper.selectPage(pageParam, queryWrapper);
    }


    /**
     * 构建过期结果查询的LambdaQueryWrapper对象。
     *
     * @param expireQueryDTO 包含查询条件的DTO对象，可能包含车辆VIN、操作员、查询状态、时间范围、批次号列表等条件。
     * @return 返回构建好的LambdaQueryWrapper对象，如果传入的expireQueryDTO为null，则返回null。
     */
    private LambdaQueryWrapper<ExpireSearchDetailRecordDO> buildExpireResultQueryWrapper(ExpireResultQueryDTO expireQueryDTO) {
        log.info("构建过期结果查询的LambdaQueryWrapper对象, expireQueryDTO:{}", expireQueryDTO);
        if (Objects.isNull(expireQueryDTO)) {
            return null;
        }
        LambdaQueryWrapper<ExpireSearchDetailRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpireSearchDetailRecordDO::getIsDeleted, false);
        queryWrapper.eq(StringUtils.isNotBlank(expireQueryDTO.getCarVin()), ExpireSearchDetailRecordDO::getCarVin,
                        expireQueryDTO.getCarVin())
                .eq(StringUtils.isNotBlank(expireQueryDTO.getOperator()), ExpireSearchDetailRecordDO::getOperator,
                        expireQueryDTO.getOperator())
                .eq(Objects.nonNull(expireQueryDTO.getQueryStatus()), ExpireSearchDetailRecordDO::getQueryStatus,
                        expireQueryDTO.getQueryStatus());
        if (StringUtils.isNotBlank(expireQueryDTO.getStartTime())
                && StringUtils.isNotBlank(expireQueryDTO.getEndTime())) {
            queryWrapper.ge(ExpireSearchDetailRecordDO::getCreatedTime, expireQueryDTO.getStartTime())
                    .le(ExpireSearchDetailRecordDO::getCreatedTime, expireQueryDTO.getEndTime());
        }
        if (CollUtil.isNotEmpty(expireQueryDTO.getBatchNoList())) {
            queryWrapper.in(ExpireSearchDetailRecordDO::getBatchNo, expireQueryDTO.getBatchNoList());
        }
        if (SortTypeEnum.ASC.getSortType().equals(expireQueryDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(ExpireSearchDetailRecordDO::getCreatedTime);
            queryWrapper.orderByAsc(ExpireSearchDetailRecordDO::getId);
        } else {
            queryWrapper.orderByDesc(ExpireSearchDetailRecordDO::getCreatedTime);
            queryWrapper.orderByDesc(ExpireSearchDetailRecordDO::getId);
        }
        log.info("构建过期结果查询的LambdaQueryWrapper对象, queryWrapper:{}", queryWrapper.getSqlSegment());
        return queryWrapper;
    }



    /**
     * 查询服务到期信息并更新查询结果。
     *
     * @param batchNo 批次号，用于标识一组查询记录
     * @return Integer 查询的数量
     */
    public Integer queryServiceAndUpdateExpireResult(String batchNo) {
        if (StringUtils.isBlank(batchNo)) {
            return 0;
        }
        List<ExpireSearchDetailRecordDO> queryRecordList = getExpireQueryRecordsByBatchNo(batchNo, ServiceExpireQueryStatusEnum.PROGRESS.getQueryStatus());
        if (CollUtil.isEmpty(queryRecordList)) {
            log.info("查询服务到期并更新结果, 查询结果为空，batchNo：{}", batchNo);
            //更新batch的状态
            updateBatchQueryStatus(batchNo);
            return 0;
        }
        queryServiceExpireDate(queryRecordList);
        //更新batch的状态
        updateBatchQueryStatus(batchNo);
        return queryRecordList.size();
    }

    /**
     * 查询服务到期日期并更新相关记录。
     *
     * @param expireDetailList 包含过期搜索详细记录的列表，如果列表为空，则函数直接返回。
     */
    public void queryServiceExpireDate(List<ExpireSearchDetailRecordDO> expireDetailList) {
        if (CollUtil.isEmpty(expireDetailList)) {
            return ;
        }
        for (ExpireSearchDetailRecordDO detailRecordDO : expireDetailList) {
            long startTime = System.currentTimeMillis();
            SubscriptionSearchResultDTO serviceSearchDTO = subscriptionService.getExpireDateByVin(detailRecordDO.getCarVin());
            long endTime = System.currentTimeMillis();
            updateExpireDetailRecordsDO(detailRecordDO, serviceSearchDTO, endTime-startTime);
        }
        serviceDetailRecordDOMapper.updateBatch(expireDetailList);
    }

    /**
     * 更新过期详情记录DO对象。
     *
     * @param detailRecordDO 需要更新的过期详情记录DO对象
     * @param serviceSearchDTO 订阅查询结果DTO对象，包含查询结果和车辆信息
     */
    private void updateExpireDetailRecordsDO(ExpireSearchDetailRecordDO detailRecordDO,
                                             SubscriptionSearchResultDTO serviceSearchDTO, long costTime) {
        log.info("更新过期详情记录DO对象, detailRecordDO:{}, serviceSearchDTO:{}, costTime:{}", detailRecordDO,
                serviceSearchDTO, costTime);
        detailRecordDO.setOperateTime(LocalDateTime.now());
        detailRecordDO.setOperator(WebFrameworkUtils.getLoginUserName());
        detailRecordDO.setUpdatedBy(WebFrameworkUtils.getLoginUserName());
        detailRecordDO.setUpdatedTime(LocalDateTime.now());
        if (!CarVinUtil.checkVinFormat(detailRecordDO.getCarVin())) {
            detailRecordDO.setQueryStatus(ServiceExpireQueryStatusEnum.FAIL.getQueryStatus());
            detailRecordDO.setErrorReason("VIN需由17位数字及字母组成");
            detailRecordDO.setCarSystemModel("-");
            return ;
        }
        if (costTime > 60000) {
            detailRecordDO.setQueryStatus(ServiceExpireQueryStatusEnum.FAIL.getQueryStatus());
            detailRecordDO.setErrorReason("查询超时，请重试");
            detailRecordDO.setCarSystemModel("-");
            return ;
        }
        if (Objects.isNull(serviceSearchDTO)) {
            detailRecordDO.setQueryStatus(ServiceExpireQueryStatusEnum.FAIL.getQueryStatus());
            detailRecordDO.setCarSystemModel("-");
            detailRecordDO.setErrorReason("该VIN在ECP不存在");
            return ;
        }
        detailRecordDO.setQueryStatus(ServiceExpireQueryStatusEnum.SUCCESS.getQueryStatus());
        Vehicle5000InfoDTO vehicleInfoDTO = serviceSearchDTO.getVehicle5000Info();
        if (Objects.nonNull(vehicleInfoDTO)) {
            detailRecordDO.setBrandName(vehicleInfoDTO.getBrandName());
            detailRecordDO.setConfigName(vehicleInfoDTO.getConfigName());
            detailRecordDO.setModelYear(vehicleInfoDTO.getModelYear());
            detailRecordDO.setConfigCode(vehicleInfoDTO.getConfigCode());
            detailRecordDO.setSeriesCode(vehicleInfoDTO.getSeriesCode());
            detailRecordDO.setBrandCode(vehicleInfoDTO.getBrandCode());
            detailRecordDO.setSeriesName(vehicleInfoDTO.getSeriesName());
            detailRecordDO.setCarSystemModel(vehicleInfoDTO.getCarSystemModel());
            detailRecordDO.setHasInfoEntertain(vehicleInfoDTO.getHasInfoEntertain());
        }
        if (StringUtils.isBlank(serviceSearchDTO.getCarSystemModel())) {
            detailRecordDO.setCarSystemModel("否");
        } else {
            detailRecordDO.setCarSystemModel(serviceSearchDTO.getCarSystemModel());
        }
        Map<String, String> expireInfoMap = serviceExpireConvertToMapBFS(serviceSearchDTO.getServiceList());
        addServiceExpireDate(expireInfoMap, detailRecordDO);
    }

    /**
     * 根据传入的过期信息映射表，设置对应的服务过期日期到详细记录对象中。
     *
     * @param expireInfoMap 包含服务过期信息的映射表，键为服务类型，值为过期日期。
     * @param detailRecordDO 需要设置服务过期日期的详细记录对象。
     */
    private void addServiceExpireDate(Map<String, String> expireInfoMap, ExpireSearchDetailRecordDO detailRecordDO) {
        if (CollUtil.isEmpty(expireInfoMap)) {
            return ;
        }
        for (Map.Entry<String, String> entry : expireInfoMap.entrySet()) {
            if (ServiceExpireKeyMapping.REMOTE_SERVICE.getKey().equals(entry.getKey())) {
                detailRecordDO.setRemoteServiceDate(entry.getValue());
            } else if (ServiceExpireKeyMapping.ONLINE_SERVICE.getKey().equals(entry.getKey())) {
                detailRecordDO.setPiviServiceDate(entry.getValue());
            } else if (ServiceExpireKeyMapping.APPD_INFO.getKey().equals(entry.getKey())) {
                detailRecordDO.setAppDServiceDate(entry.getValue());
            } else if (ServiceExpireKeyMapping.AMAP_INFO.getKey().equals(entry.getKey())) {
                detailRecordDO.setAmaPServiceDate(entry.getValue());
            } else if (ServiceExpireKeyMapping.NETWORK_FLOW.getKey().equals(entry.getKey())) {
                detailRecordDO.setUnicomServiceDate(entry.getValue());
            } else if (ServiceExpireKeyMapping.INVOICE_DATE.getKey().equals(entry.getKey())) {
                detailRecordDO.setInvoiceServiceDate(getInvoiceDateDecrypt(entry.getValue()));
            }
        }
    }

    /**
     * 获取解密后的发票日期。

     *
     * @param invoiceServiceDateEncrypt 需要加密的发票服务日期字符串。
     * @return 返回加密后的发票日期字符串。
     */
    private String getInvoiceDateDecrypt(String invoiceServiceDateEncrypt) {
        log.info("获取解密后的发票日期, invoiceServiceDateEncrypt:{}", invoiceServiceDateEncrypt);
        if (StrUtil.isBlank(invoiceServiceDateEncrypt)){
            return CharSequenceUtil.EMPTY;
        }
        try {
            JSONObject json = new JSONObject();
            json.put("encryptTex", invoiceServiceDateEncrypt);
            CommonResult<String>  commonResult = permissionApi.getDecryptText(json);
            log.info("获取解密后的发票日期, commonResult:{}", commonResult);
            if (Objects.nonNull(commonResult) && StringUtils.isNotBlank(commonResult.getData())) {
                return commonResult.getData();
            }
        } catch (Exception e) {
            log.error("获取解密后的发票日期异常：{}", e.getMessage());
        }
        return "";
    }


    /**
     * 将服务过期信息列表转换为Map，使用广度优先搜索（BFS）遍历树形结构。
     *
     * @param infoVoList 服务过期信息列表，每个元素包含服务名称、过期日期和子节点信息。
     * @return 返回一个Map，其中键为服务名称，值为对应的过期日期。如果输入列表为空，则返回空Map。
     */
    public Map<String, String> serviceExpireConvertToMapBFS(List<ServiceExpireInfoVO> infoVoList) {
        Map<String, String> resultMap = new HashMap<>();
        if (CollUtil.isEmpty(infoVoList)) {
            return resultMap;
        }
        Queue<ServiceExpireInfoVO> queue = new LinkedList<>(infoVoList);
        while (!queue.isEmpty()) {
            ServiceExpireInfoVO current = queue.poll();
            if (Objects.isNull(current)) {
                continue;
            }
            // 处理当前节点
            if (StringUtils.isNotBlank(current.getServiceName())) {
                resultMap.put(current.getServiceName(), current.getExpireDate());
            }
            // 子节点入队
            List<ServiceExpireInfoVO> children = current.getChild();
            if (CollUtil.isNotEmpty(children)) {
                queue.addAll(children);
            }
        }
        return resultMap;
    }

    /**
     * 更新指定批次号的查询状态为已完成。
     *
     * @param batchNo 批次号，用于标识需要更新的记录
     */
    public void updateBatchQueryStatus(String batchNo) {
        //更新batch的状态
        ExpireSearchBatchRecordDO queryBatchRecordsDO = new ExpireSearchBatchRecordDO();
        queryBatchRecordsDO.setBatchNo(batchNo);
        queryBatchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        queryBatchRecordsDO.setUpdatedTime(LocalDateTime.now());
        LambdaUpdateWrapper<ExpireSearchBatchRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExpireSearchBatchRecordDO::getBatchNo, batchNo)
                .eq(ExpireSearchBatchRecordDO::getIsDeleted, false);
        serviceBatchRecordDOMapper.update(queryBatchRecordsDO, updateWrapper);
    }

    /**
     * 根据批次号和查询状态获取符合条件的过期搜索详情记录列表
     *
     * @param batchNo     批次号，用于筛选记录
     * @param queryStatus 查询状态，用于筛选记录
     * @return 返回符合条件的过期搜索详情记录列表，如果未找到则返回空列表
     */
    private List<ExpireSearchDetailRecordDO> getExpireQueryRecordsByBatchNo(String batchNo, Integer queryStatus) {
        LambdaQueryWrapper<ExpireSearchDetailRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpireSearchDetailRecordDO::getBatchNo, batchNo)
                .eq(ExpireSearchDetailRecordDO::getQueryStatus, queryStatus)
                .eq(ExpireSearchDetailRecordDO::getIsDeleted, false);
        return serviceDetailRecordDOMapper.selectList(queryWrapper);
    }

    /**
     * 批量插入查询记录
     *
     * @param searchExpireListener 查询到期检查的监听器，包含所有待处理的数据
     * @param batchNo 批次编号，用于标识当前批次的数据
     */
    public void insertExpireDetailRecords(ExpireSearchCheckListener searchExpireListener, String batchNo) {
        if (Objects.isNull(searchExpireListener) || CollUtil.isEmpty(searchExpireListener.getAllDataList())) {
            log.info("服务到期查询的监听器为空，不进行每条数据的插入操作, batchNo:{}", batchNo);
            return ;
        }
        List<List<ExpireBatchSearchExcel>> batchExcelGroup = searchExpireListener.getAllDataList();
        for (List<ExpireBatchSearchExcel> batchExcelList : batchExcelGroup) {
            List<ExpireSearchDetailRecordDO> queryRecordsList = new ArrayList<>();
            for (ExpireBatchSearchExcel expireBatchExcel : batchExcelList) {
                queryRecordsList.add(buildExpireDetailRecordsDO(expireBatchExcel, batchNo));
            }
            serviceDetailRecordDOMapper.insertBatch(queryRecordsList);
        }
    }

    /**
     * 构建服务到期查询记录
     *
     * @param searchExpireExcel Excel数据，用于查询高德平台的到期记录
     * @param batchNo 批处理编号，用于标识查询记录所属的批次
     * @return 返回构建的高德查询记录数据对象(AmaPQueryRecordsDO)
     */
    public ExpireSearchDetailRecordDO buildExpireDetailRecordsDO(ExpireBatchSearchExcel searchExpireExcel, String batchNo) {
        if (Objects.isNull(searchExpireExcel)) {
            return ExpireSearchDetailRecordDO.builder()
                    .batchNo(batchNo)
                    .build();
        }
        return ExpireSearchDetailRecordDO.builder()
                .carVin(CarVinUtil.carVinToUpperCase(searchExpireExcel.getCarVin()))
                .batchNo(batchNo)
                .queryStatus(ServiceExpireQueryStatusEnum.PROGRESS.getQueryStatus())
                .operator(getOperator())
                .operateTime(LocalDateTime.now())
                .carSystemModel("-")
                .build();

    }

    /**
     * 批量插入服务过期查询记录
     *
     * @param batchNo 批次号，用于标识当前处理的批次
     */
    public void insertBatchExpireQueryRecords(String  batchNo) {
        ExpireSearchBatchRecordDO queryRecordsDO = ExpireSearchBatchRecordDO.builder()
                .batchNo(batchNo)
                .verifyResult(1)
                .dealStatus(DealStatusEnum.PROGRESS.getStatus())
                .operator(getOperator())
                .build();
        serviceBatchRecordDOMapper.insert(queryRecordsDO);
    }

    /**
     * 获取当前登录用户的操作者名称。
     *
     * @return 当前登录用户的用户名，如果用户名为空或空白则返回空字符串。
     */
    private String getOperator() {
        if (StringUtils.isBlank(WebFrameworkUtils.getLoginUserName())) {
            return "";
        }
        return WebFrameworkUtils.getLoginUserName();
    }


    /**
     * 构建服务批次查询页面VO列表
     *
     * @param batchRecordDOList 包含批次记录数据的 `ExpireSearchBatchRecordDO` 对象列表
     * @return 转换后的 `ServiceBatchQueryPageVO` 对象列表，如果输入列表为空，则返回空列表
     */
    private  List<ServiceBatchQueryPageVO>  buildServiceBatchQueryPageVOList(List<ExpireSearchBatchRecordDO> batchRecordDOList) {
        List<ServiceBatchQueryPageVO>  resp = new ArrayList<>();
        if (CollUtil.isEmpty(batchRecordDOList)) {
            return resp;
        }
        for (ExpireSearchBatchRecordDO batchRecordDO : batchRecordDOList) {
            resp.add(buildServiceBatchQueryPageVO(batchRecordDO));
        }
        return resp;
    }

    /**
     * 构建服务批次查询页面视图对象（ServiceBatchQueryPageVO）。
     *
     * @param batchRecordDO 过期搜索批次记录对象，包含批次的相关信息。
     * @return ServiceBatchQueryPageVO 服务批次查询页面视图对象
     */
    private ServiceBatchQueryPageVO buildServiceBatchQueryPageVO(ExpireSearchBatchRecordDO batchRecordDO) {
        if (Objects.isNull(batchRecordDO)) {
            return ServiceBatchQueryPageVO.builder().build();
        }
        return ServiceBatchQueryPageVO.builder()
                .operateTime(TimeFormatUtil.timeToStringByFormat(batchRecordDO.getCreatedTime(), TimeFormatUtil.formatter_6))
                .checkResultStatus(batchRecordDO.getVerifyResult())
                .checkResultDesc(QueryUploadResultEnum.getDescByCode(batchRecordDO.getVerifyResult()))
                .batchNo(batchRecordDO.getBatchNo())
                .errorDetail(batchRecordDO.getVerifyReason())
                .build();
    }

    /**
     * 根据处理状态查询未删除的过期批次记录
     *
     * @param dealStatus 处理状态，用于筛选符合条件的记录
     * @return 返回符合条件的过期批次记录列表，若未找到则返回空列表
     */
    private List<ExpireSearchBatchRecordDO> queryExpireBatchByStatus(Integer dealStatus) {
        LambdaQueryWrapper<ExpireSearchBatchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpireSearchBatchRecordDO::getDealStatus, dealStatus)
                .eq(ExpireSearchBatchRecordDO::getIsDeleted, false);
        return serviceBatchRecordDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据批次号获取未删除的过期搜索批次记录
     *
     * @param batchNo 批次号，用于查询对应的过期搜索批次记录
     * @return 返回与批次号匹配且未删除的过期搜索批次记录，如果未找到则返回null
     */
    private ExpireSearchBatchRecordDO getExpireBatchRecordsByBatchNo(String batchNo) {
        LambdaQueryWrapper<ExpireSearchBatchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpireSearchBatchRecordDO::getBatchNo, batchNo)
                .eq(ExpireSearchBatchRecordDO::getIsDeleted, false);
        return serviceBatchRecordDOMapper.selectOne(queryWrapper);
    }

    /**
     * 查询服务批次分页列表
     *
     * @param queryPageDTO 查询条件对象，包含分页信息、排序方式等
     * @return 分页查询结果，包含服务批次记录列表
     */
    private Page<ExpireSearchBatchRecordDO> queryServiceBatchPageList(ServiceBatchQueryPageDTO queryPageDTO) {
        log.info("查询服务批次分页列表, queryPageDTO:{}", queryPageDTO);
        if (Objects.isNull(queryPageDTO)) {
            return new Page<>();
        }
        Page <ExpireSearchBatchRecordDO> page = new Page<>(queryPageDTO.getPageNo(), queryPageDTO.getPageSize());
        LambdaQueryWrapper<ExpireSearchBatchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpireSearchBatchRecordDO::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(queryPageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(ExpireSearchBatchRecordDO::getCreatedTime);
            queryWrapper.orderByAsc(ExpireSearchBatchRecordDO::getId);
        } else if (SortTypeEnum.DESC.getSortType().equals(queryPageDTO.getOperateTimeSort())) {
            queryWrapper.orderByDesc(ExpireSearchBatchRecordDO::getCreatedTime);
            queryWrapper.orderByDesc(ExpireSearchBatchRecordDO::getId);
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }
        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(ExpireSearchBatchRecordDO::getOperator, userName);
        } else {
            log.info("服务到期日分页查询, 登录用户为空, queryPageDTO:{}", queryPageDTO);
        }
        return serviceBatchRecordDOMapper.selectPage(page, queryWrapper);
    }
}
