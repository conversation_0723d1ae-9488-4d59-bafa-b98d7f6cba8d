package com.jlr.ecp.subscription.service.appd;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AppDCuBatchRenewalService {

    /**
     * 获取AppDUC批量续费的模板URL
     *
     * @return 模板的URL地址
     */
    String getAppDUcTemplateUrl();

    /**
     *  AppDUC 批量续费分页查询列表
     *
     * @param pageDTO 分页查询参数
     * @return 续费页面的数据列表和总记录数
     */
    PageResult<AppDUcBatchRenewalPageVO> queryAppDUcBatchRenewalPageList(AppDUcBatchRenewalPageDTO pageDTO);

    /**
     * 上传AppDUc批量续费的Excel文件
     *
     * @param multipartFile 多部分文件对象，包含上传的Excel文件
     * @return CommonResult<AppDUcBatchRenewalUploadVO> 返回一个通用结果对象，包含上传结果和相关信息
     */
    CommonResult<AppDUcBatchRenewalUploadVO> uploadAppDUcExcelRenewal(MultipartFile multipartFile);

    /**
     * 批量发送AppDCu续费通知
     *
     * @param appDUcBatchSendDTO 批量发送AppDUc续费的参数对象，包含批次号等信息
     * @return CommonResult<String> 返回操作结果的封装对象，包含成功、错误等状态及相应消息
     */
    CommonResult<String> batchSendAppDCuRenewal(AppDUcBatchSendDTO appDUcBatchSendDTO);

    /**
     * APPD批量发送续订请求并更新续订记录
     *
     * @param appdList 待处理的续订记录列表
     * @param renewStatus 更新状态
     */
    void appDBatchSendRenewalAndUpdate(List<AppDCuRenewRecords> appdList, Integer renewStatus);

    /**
     * 批量发送联通续约请求，并更新续约记录
     *
     * @param unicomList 待处理的联通续约记录列表
     */
    void unicomBatchSendRenewalAndUpdate(List<AppDCuRenewRecords> unicomList, Integer renewStatus);
}
