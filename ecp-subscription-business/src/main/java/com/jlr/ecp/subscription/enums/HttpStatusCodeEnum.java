package com.jlr.ecp.subscription.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum HttpStatusCodeEnum {
    SUCCESS(200, 299),
    REDIRECTION(300, 399),
    CLIENT_ERROR(400, 499),
    SERVER_ERROR(500, 599);

    private final int minCode;
    private final int maxCode;

    public boolean contains(int statusCode) {
        return statusCode >= this.minCode && statusCode <= this.maxCode;
    }
}