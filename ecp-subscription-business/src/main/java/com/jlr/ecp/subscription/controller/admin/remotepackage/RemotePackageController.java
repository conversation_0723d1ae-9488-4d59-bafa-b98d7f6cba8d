package com.jlr.ecp.subscription.controller.admin.remotepackage;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageCreateDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageDelReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackagePageReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.BatchUploadRespVO;
import com.jlr.ecp.subscription.api.remotepackage.vo.RemotePackageListRespVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.Locale;

@Slf4j
@Tag(name = "平台管理 - 服务包配置")
@RestController
@RequestMapping("v1/subscription/remote/package")
@Validated
public class RemotePackageController {
    @Resource
    private RemotePackageDOService remotePackageDOService;


    /**
     * 创建服务包API接口
     *
     * @param createDTO 入参
     * @return CommonResult<String>
     */
    @PostMapping("/create")
    @Operation(summary = "添加服务包")
    @PreAuthorize("@ss.hasPermission('platform:remote:create')")
    CommonResult<String> createRemotePackage(@Validated @RequestBody RemotePackageCreateDTO createDTO) {
        Boolean success = remotePackageDOService.createRemotePackage(createDTO);
        if (success) {
            return CommonResult.success(Constants.PACKAGE_CREATE_SUCCESS_MESSAGE);
        }

        return CommonResult.error(ErrorCodeConstants.PACKAGE_CREATE_FAIL);
    }

    /**
     * 服务包列表API
     *
     * @param dto
     * @return CommonResult<List < RemotePackageListRespVO>>
     */
    @GetMapping("/page")
    @Operation(summary = "服务包列表查询接口")
    @PreAuthorize("@ss.hasPermission('platform:remote:list')")
    CommonResult<PageResult<RemotePackageListRespVO>> page(RemotePackagePageReqDTO dto) {
        // 为了兼容前端传入的大小写，这里统一转换为小写
        if (dto.getCreatedTimeSort() != null) {
            dto.setCreatedTimeSort(dto.getCreatedTimeSort().toLowerCase(Locale.ROOT));
        }

        return CommonResult.success(remotePackageDOService.getPage(dto));
    }

    /**
     * 删除服务包
     *
     * @param dto
     * @return CommonResult<String>
     */
    @PostMapping("/delete")
    @Operation(summary = "删除服务包")
    @PreAuthorize("@ss.hasPermission('platform:remote:delete')")
//    @PermitAll
    CommonResult<String> deleteByPackageCode(@RequestBody RemotePackageDelReqDTO dto) {
        String packageCode = dto.getPackageCode();

        Boolean success = remotePackageDOService.deleteByPackageCode(packageCode);
        if (success) {
            return CommonResult.success(Constants.PACKAGE_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.PACKAGE_DELETE_FAIL);
    }

    /**
     * 2MB
     */
    private static final long MAX_FILE_SIZE = 2L * 1024 * 1024;

    /**
     * 批量上传服务包编号API接口
     *
     * @param file Excel文件
     * @return CommonResult<BatchUploadRespVO>
     */
    @PostMapping("/batch/upload")
    @Operation(summary = "批量上传服务包编号")
    @PreAuthorize("@ss.hasPermission('platform:remote:upload')")
    CommonResult<BatchUploadRespVO> batchUpload(@RequestParam("file") MultipartFile file, @NotBlank(message = "车机不能为空") @RequestParam("carSystemModel") String carSystemModel) {
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.FILE_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(file)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        return remotePackageDOService.processBatchUpload(file, carSystemModel);
    }

    /**
     * 下载服务包编号模板API接口
     *
     * @return CommonResult<String>
     */
    @GetMapping("/download/template")
    @Operation(summary = "下载服务包编号模板")
    @PreAuthorize("@ss.hasPermission('platform:remote:download')")
    CommonResult<String> downloadTemplateUrl() {
        try {
            // 获取模板文件的URL
            String templateUrl = remotePackageDOService.getTemplateUrl();

            return CommonResult.success(templateUrl);
        } catch (Exception e) {
            log.error("Error retrieving template URL", e);
            return CommonResult.error(ErrorCodeConstants.SERVER_ERROR);
        }
    }


}
