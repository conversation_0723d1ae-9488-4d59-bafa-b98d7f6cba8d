package com.jlr.ecp.subscription.controller.admin.remote;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalUploadVO;
import com.jlr.ecp.subscription.service.remote.RemoteBatchRenewalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "批量续费-远程车控")
@RequestMapping("/remote/batch/manual/renewal")
@Validated
public class RemoteBatchRenewalController {

    @Resource
    private RemoteBatchRenewalService remoteBatchRenewalService;

    @GetMapping("/download/template")
    @Operation(summary = "下载Remote批量Excel发送模板")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-multiple-renewal:forms')")
    public CommonResult<String> downloadTemplateUrl() {
        // 获取模板文件的URL
        String templateUrl = remoteBatchRenewalService.getRemoteTemplateUrl();
        return CommonResult.success(templateUrl);
    }

    @PostMapping("/queryBatchPageList")
    @Operation(summary = "查询Remote续费页面列表")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-multiple-renewal:forms')")
    public CommonResult<PageResult<RemoteBatchRenewalPageVO>> queryBatchRenewalPageList(@RequestBody @Valid RemoteBatchRenewalPageDTO pageDto) {
        return CommonResult.success(remoteBatchRenewalService.queryRemoteBatchRenewalPageList(pageDto));
    }

    @PostMapping("/uploadExcel")
    @Operation(summary = "上传excel文件")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-multiple-renewal:forms')")
    public CommonResult<RemoteBatchRenewalUploadVO> uploadExcelRenewal(@RequestBody MultipartFile multipartFile) {
        return remoteBatchRenewalService.uploadRemoteExcelRenewal(multipartFile);
    }

    @PostMapping("/batchSendRemote")
    @Operation(summary = "remote批量续费发送")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-multiple-renewal:forms')")
    public CommonResult<String> batchSendRenewal(@RequestBody @Valid RemoteBatchSendDTO remoteBatchSendDTO) {
        return remoteBatchRenewalService.batchSendRemoteRenewal(remoteBatchSendDTO);
    }
}
