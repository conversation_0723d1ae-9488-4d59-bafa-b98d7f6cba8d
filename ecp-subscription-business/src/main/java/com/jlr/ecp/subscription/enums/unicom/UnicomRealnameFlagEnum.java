package com.jlr.ecp.subscription.enums.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomRealnameFlagEnum {

    FALSE("0", "未实名"),

    TRUE("1", "已实名"),

    FAIL("-1", "查询失败");

    /**
     * 类型
     * */
    public final String code;

    /**
     * 描述
     * */
    public final String desc;

    public static String getDescriptionByCode(Integer code) {
        // 或者返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (UnicomRealnameFlagEnum status : UnicomRealnameFlagEnum.values()) {
            if (status.getCode().equals(String.valueOf(code))) {
                return status.getDesc();
            }
        }
        return String.valueOf(code);
    }
}
