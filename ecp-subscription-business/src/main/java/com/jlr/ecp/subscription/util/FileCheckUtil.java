package com.jlr.ecp.subscription.util;

import cn.hutool.core.io.IoUtil;
import com.jlr.ecp.framework.file.core.utils.FileTypeUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
public class FileCheckUtil {

    private static final List<String> SUPPORTED_TYPES = Arrays.asList(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "application/x-tika-ooxml"
    );

    public static boolean isExcelFile(byte[] readBytes) {
        String mediaType = FileTypeUtils.getMineType(readBytes);
        log.info("文件类型:{}", mediaType);
        return SUPPORTED_TYPES.contains(mediaType);
    }

    /**
     * 检查上传的文件是否为Excel文件
     *
     * @param multipartFile 文件对象，用于获取文件的输入流
     * @return 如果文件是Excel文件，则返回true；否则返回false
     */
    public static boolean isExcelFile(MultipartFile multipartFile ) {
        // 检查文件是否为excel
        byte[] readBytes;
        try {
            readBytes = IoUtil.readBytes(multipartFile.getInputStream());
            return isExcelFile(readBytes);
        } catch (IOException e) {
            log.info("上传Excel文件读取异常:{}", e.getMessage());
        }
        return false;
    }
}
