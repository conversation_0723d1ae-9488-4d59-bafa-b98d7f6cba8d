package com.jlr.ecp.subscription.config;

import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * cdp sdk sign
 * <AUTHOR>
 */
public class Sign {

    // 创建一个 Logger 对象
    private static final Logger logger = Logger.getLogger(Sign.class.getName());


    private static final BitSet URLENCODER = new BitSet(256);

    private static final String CONST_ENCODE = "0123456789ABCDEF";
    public static final Charset UTF_8 = StandardCharsets.UTF_8;

    private String region = "";
    private String service = "";
    private String schema = "";
    private String host = "";
    private String path = "";
    private String ak = "";
    private String sk = "";

    static {
        int i;
        for (i = 97; i <= 122; ++i) {
            URLENCODER.set(i);
        }

        for (i = 65; i <= 90; ++i) {
            URLENCODER.set(i);
        }

        for (i = 48; i <= 57; ++i) {
            URLENCODER.set(i);
        }
        URLENCODER.set('-');
        URLENCODER.set('_');
        URLENCODER.set('.');
        URLENCODER.set('~');
    }

    public Sign(String region, String service, String schema, String host, String path, String ak, String sk) {
        this.region = region;
        this.service = service;
        this.host = host;
        this.schema = schema;
        this.path = path;
        this.ak = ak;
        this.sk = sk;
    }

    public Sign(String domain, String ak, String sk) {
        this.region = "cn";
        this.service = "open_platform";
        this.host = domain.split("://")[1];
        this.schema = domain.split("://")[0];
        this.path = "/open_platform/openapi";
        this.ak = ak;
        this.sk = sk;
    }


    public String doRequest(String method, Map<String, String> queryList, String body,
                            String action, String version, int project_id) throws Exception {
        byte[] body1 = null;
        if (body == null) {
            body1 = new byte[0];
        } else {
            body1 = body.getBytes(StandardCharsets.UTF_8);
        }
        String xContentSha256 = hashSHA256(body1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String xDate = sdf.format(new Date());
        String shortXDate = xDate.substring(0, 8);
//        String contentType = "application/x-www-form-urlencoded";
        String contentType = "application/json";

        String signHeader = "x-date;x-content-sha256;content-type";


        SortedMap<String, String> realQueryList = new TreeMap<>(queryList);
        realQueryList.put("Action", action);
        realQueryList.put("Version", version);
        StringBuilder querySB = new StringBuilder();
        for (String key : realQueryList.keySet()) {
            querySB.append(signStringEncoder(key)).append("=").append(signStringEncoder(realQueryList.get(key))).append("&");
        }
        querySB.deleteCharAt(querySB.length() - 1);

        String canonicalStringBuilder = method + "\n" + path + "\n" + querySB + "\n" +
//                "host:" + host + "\n" +
                "x-date:" + xDate + "\n" +
                "x-content-sha256:" + xContentSha256 + "\n" +
                "content-type:" + contentType + "\n" +
                "\n" +
                signHeader + "\n" +
                xContentSha256;

//        logger.log(Level.INFO, canonicalStringBuilder);

        String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
        String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
        String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

        byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, region, service);
//        String signature = HexFormat.of().formatHex(hmacSHA256(signKey, signString));
        String signature = byteArrayToHexString(hmacSHA256(signKey, signString));

        URL url = new URL(schema + "://" + host + path + "?" + querySB);


        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod(method);
        // 设置连接超时（以毫秒为单位）
        conn.setConnectTimeout(10000);
        // 设置读取超时（以毫秒为单位）
        conn.setReadTimeout(10000);

//        conn.setRequestProperty("Host", host);
        conn.setRequestProperty("x-Tenant", String.valueOf(project_id));
        conn.setRequestProperty("X-Date", xDate);
        conn.setRequestProperty("X-Content-Sha256", xContentSha256);
        conn.setRequestProperty("Content-Type", contentType);
        String authorization = "HMAC-SHA256" +
                " Credential=" + ak + "/" + credentialScope +
                ", SignedHeaders=" + signHeader +
                ", Signature=" + signature;
        conn.setRequestProperty("Authorization", authorization);
        if (!Objects.equals(conn.getRequestMethod(), "GET")) {
            conn.setDoOutput(true);
            OutputStream os = conn.getOutputStream();
            os.write(body1);
            os.flush();
            os.close();
        }

        // 打印 curl 命令
        String curl = String.format("curl --location --request  %s '%s' \\\n" +
                        "--header 'Content-Type: %s' \\\n" +
                        "--header 'X-Tenant: %s' \\\n" +
                        "--header 'x-trace-id: %s' \\\n" +
//                "--header 'Host: %s' \\\n" +
                        "--header 'X-Date: %s' \\\n" +
                        "--header 'X-Content-Sha256: %s' \\\n" +
                        "--header 'Authorization: %s' "
                , method, url, contentType, project_id,
                "openapi_" + queryList.get("ApiAction")+ "_" + System.currentTimeMillis(), xDate,
                xContentSha256, authorization);
        if ("POST".equalsIgnoreCase(method) && body != null) {
            curl += String.format("\\\n--data-raw '%s'", body.toString());
        }
        logger.log(Level.INFO,curl);

        conn.connect();
        int responseCode = conn.getResponseCode();

        InputStream is;
        if (responseCode == 200) {
            is = conn.getInputStream();
        } else {
            is = conn.getErrorStream();
        }
        String responseBody = convertInputStreamToString(is);
        is.close();


        logger.log(Level.INFO,String.valueOf(responseCode));
        logger.log(Level.INFO,responseBody);
        return responseBody;
    }


    public static String convertInputStreamToString(InputStream inputStream) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            StringBuilder sb = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }

            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    private String signStringEncoder(String source) {
        if (source == null) {
            return null;
        }
        StringBuilder buf = new StringBuilder(source.length());
        ByteBuffer bb = UTF_8.encode(source);
        while (bb.hasRemaining()) {
            int b = bb.get() & 255;
            if (URLENCODER.get(b)) {
                buf.append((char) b);
            } else if (b == 32) {
                buf.append("%20");
            } else {
                buf.append("%");
                char hex1 = CONST_ENCODE.charAt(b >> 4);
                char hex2 = CONST_ENCODE.charAt(b & 15);
                buf.append(hex1);
                buf.append(hex2);
            }
        }

        return buf.toString();
    }

    public static String hashSHA256(byte[] content) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            //return HexFormat.of().formatHex(md.digest(content));//cdp original
            return byteArrayToHexString(md.digest(content));


        } catch (Exception e) {

            throw new Exception(
                    "Unable to compute hash while signing request: "
                            + e.getMessage(), e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception(
                    "Unable to calculate a request signature: "
                            + e.getMessage(), e);
        }
    }

    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }

    public static String byteArrayToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < bytes.length; i++) {
            int value = bytes[i] & 0xFF; // 确保每个字节都被正确地表示为无符号整数

            if (value <= 0xf) {
                sb.append("0"); // 补零操作，确保结果始终有两位十六进制值
            }

            sb.append(Integer.toHexString(value));
        }

        return sb.toString().toLowerCase(); // 返回大写形式的十六进制字符串
    }

}
