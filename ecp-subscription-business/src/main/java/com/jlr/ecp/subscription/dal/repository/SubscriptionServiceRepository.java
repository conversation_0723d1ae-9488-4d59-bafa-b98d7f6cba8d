package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireVo;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 订阅服务Repository接口
 *
 */
public interface SubscriptionServiceRepository extends IService<SubscriptionServiceDO> {

    /**
     * 查询过期服务
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 过期服务列表
     */
    List<SubscriptionServiceDO> queryExpireService(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据车辆VIN和服务类型查询
     *
     * @param carVinAndServiceTypeList 车辆VIN和服务类型列表
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> queryByCarVinAndServiceType(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    /**
     * 获取最新过期日期
     *
     * @param carVinSet 车辆VIN集合
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> getLatestExpireDate(Set<String> carVinSet);

    /**
     * 获取最新联通过期日期
     *
     * @param carVinSet 车辆VIN集合
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> getLatestUnicomExpireDate(Set<String> carVinSet);

    /**
     * 根据车辆VIN查找ICCID
     *
     * @param carVin 车辆VIN
     * @return 订阅服务
     */
    SubscriptionServiceDO findICCIDByCarVin(String carVin);

    /**
     * 根据车辆VIN和包名查询服务
     *
     * @param carVin 车辆VIN
     * @param packageName 包名
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> queryServiceDOByCarVinAndPackage(String carVin, String packageName);

    /**
     * 查询组合过期列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param serviceType 服务类型
     * @param servicePackage 服务包
     * @param pageSize 页面大小
     * @param offset 偏移量
     * @return 服务过期VO列表
     */
    List<ServiceExpireVo> queryCombinedExpireList(LocalDateTime startTime, LocalDateTime endTime,
                                                  Integer serviceType, String servicePackage,
                                                  Integer pageSize, Integer offset);

    /**
     * 查询组合过期数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param serviceType 服务类型
     * @param servicePackage 服务包
     * @return 数量
     */
    Integer queryCombinedExpireCount(LocalDateTime startTime, LocalDateTime endTime,
                                     Integer serviceType, String servicePackage);

    /**
     * 批量保存或更新
     *
     * @param entities 实体集合
     * @return 是否成功
     */
    boolean saveOrUpdateBatch(Collection<SubscriptionServiceDO> entities);

    /**
     * 根据车辆VIN列表查询订阅服务
     *
     * @param carVinList 车辆VIN列表
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> selectByCarVinList(List<String> carVinList);

    /**
     * 根据车辆VIN查询订阅服务
     *
     * @param carVin 车辆VIN
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> selectByCarVin(String carVin);

    /**
     * 根据VIN集合和服务类型查询订阅服务
     *
     * @param vinSet      VIN集合
     * @param serviceType 服务类型
     * @return 订阅服务列表
     */
    List<SubscriptionServiceDO> findByVinSetAndServiceType(Set<String> vinSet, Integer serviceType);
}
