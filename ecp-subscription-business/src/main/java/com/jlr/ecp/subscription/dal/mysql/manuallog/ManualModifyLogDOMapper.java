package com.jlr.ecp.subscription.dal.mysql.manuallog;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.manuallog.ManualModifyLogDO;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

import static com.jlr.ecp.subscription.util.TimeFormatUtil.formatter_7;

/**
* <AUTHOR>
* @description 针对表【t_manual_modify_log(t_manual_modify_log)】的数据库操作Mapper
* @createDate 2024-11-19 19:44:40
* @Entity generator.domain.ManualModifyLogDO
*/
@Mapper
public interface ManualModifyLogDOMapper extends BaseMapperX<ManualModifyLogDO> {


    default int insertLog(String vin,String modifyBeforeValue,
                          String modifyAfterValue,String operator,
                          LocalDateTime operateTime,
                          Integer modifyType) {
        ManualModifyLogDO modifyLogDO = new ManualModifyLogDO();
        modifyLogDO.setCarVin(vin);
        modifyLogDO.setModifyBeforeValue(modifyBeforeValue);
        modifyLogDO.setModifyAfterValue(modifyAfterValue);
        modifyLogDO.setOperator(operator);
        modifyLogDO.setOperateTime(operateTime);
        modifyLogDO.setModifyType(modifyType);
        return insert(modifyLogDO);
    }
}




