package com.jlr.ecp.subscription.constant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public final class Constants {

    private Constants() {
        throw new IllegalStateException("Utility class");
    }

    /***
     *  ========车型年款相关=======
     */
    public static final String SERIES_CREATE_SUCCESS_MESSAGE = "车型年款配置创建成功";
    public static final String SERIES_UPDATE_SUCCESS_MESSAGE = "车型年款配置编辑成功";
    public static final String SERIES_DELETE_SUCCESS_MESSAGE = "车型年款配置刪除成功";

    /**
     * ========== 车型展示名称配置 ==========
     */
    public static final String SERIES_MAPPING_CREATE_SUCCESS_MESSAGE = "车型展示名称配置创建成功";
    public static final String SERIES_MAPPING_UPDATE_SUCCESS_MESSAGE = "车型展示名称配置编辑成功";
    public static final String SERIES_MAPPING_DELETE_SUCCESS_MESSAGE = "车型展示名称配置刪除成功";
    /***
     *  ========服务包相关=======
     */
    public static final String PACKAGE_CREATE_SUCCESS_MESSAGE = "服务包配置创建成功";
    public static final String PACKAGE_DELETE_SUCCESS_MESSAGE = "服务包配置刪除成功";
    public static final String PACKAGE_BATCH_UPLOAD_SUCCESS_MESSAGE = "批量上传服务包编号完成";
    public static final int PACKAGE_CODE_MAX_LENGTH = 30;
    public static final String LIMIT_ONE = "limit 1";
    /**
     * 服务过期数据，幂等校验前缀
     * */
    public static final String SERVICE_EXPIRE_KEY = "service:expire:key:";

    /**
     * 业务线缓存key
     */
    public static final String BUSINESS_CACHE_KEY = "global:business:info";

    /**
     * redis中幂等校验前缀
     * */
    public static final String FUFILMENT_IDEMPOTENT_KEY = "fufilment:idempotent:key:";

    public static final String AMAP_EXPIRE_DATE_PAGE_KEY = "amap:expire:date:page:key";


    /**
     * api限流key
     * */
    public static final String API_LIMITATION_KEY = "api:limitation:key:";

    /**
     * 订阅过期
     * */
    public static final String SUBSCRIPTION_EXPIRED = "订阅过期";

    /**
     * 订阅中
     * */
    public static final String SUBSCRIBING = "订阅中";

    /**
     * ========== policy相关 ==========
     */
    public static final String POLICY_CREATE_SUCCESS_MESSAGE = "添加信息配置成功";
    public static final String POLICY_UPDATE_SUCCESS_MESSAGE = "编辑信息配置成功";
    public static final String POLICY_DELETE_SUCCESS_MESSAGE = "删除信息配置成功";

    /**
     * ========== faq相关 ==========
     */
    public static final String FAQ_CREATE_SUCCESS_MESSAGE = "添加问答配置成功";
    public static final String FAQ_UPDATE_SUCCESS_MESSAGE = "编辑问答配置成功";
    public static final String FAQ_DELETE_SUCCESS_MESSAGE = "删除问答配置成功";

    /**
     * ========== faq type 相关 ==========
     */
    public static final String FAQ_TYPE_CREATE_SUCCESS_MESSAGE = "问题类别创建成功";
    public static final String FAQ_TYPE_EDIT_SUCCESS_MESSAGE = "问题类别编辑成功";
    public static final String FAQ_TYPE_DELETE_SUCCESS_MESSAGE = "问题类别刪除成功";


    /***
     *  ========销售单位相关=======
     */
    public static final String UNIT_CREATE_SUCCESS_MESSAGE = "商品销售单位创建成功";
    public static final String UNIT_UPDATE_SUCCESS_MESSAGE = "商品销售单位编辑成功";
    public static final String UNIT_DELETE_SUCCESS_MESSAGE = "商品销售单位刪除成功";



    /***
     *  ========商品类别相关=======
     */
    public static final String CATEGORY_CREATE_SUCCESS_MESSAGE = "商品类别创建成功";
    public static final String CATEGORY_UPDATE_SUCCESS_MESSAGE = "商品类别编辑成功";
    public static final String CATEGORY_DELETE_SUCCESS_MESSAGE = "商品类别刪除成功";


    /***
     *  ========商品属性相关=======
     */
    public static final String ATTRIBUTE_CREATE_SUCCESS_MESSAGE = "商品属性创建成功";
    public static final String ATTRIBUTE_UPDATE_SUCCESS_MESSAGE = "商品属性编辑成功";
    public static final String ATTRIBUTE_DELETE_SUCCESS_MESSAGE = "商品属性刪除成功";

    /***
     *  ========商品品牌相关=======
     */
    public static final String BRAND_CREATE_SUCCESS_MESSAGE = "商品品牌创建成功";
    public static final String BRAND_UPDATE_SUCCESS_MESSAGE = "商品品牌编辑成功";
    public static final String BRAND_DELETE_SUCCESS_MESSAGE = "商品品牌刪除成功";

    public static final class BindStatus {
        /**
         * 0：已解绑
         */
        public static final int UN_BIND = 0;
        /**
         * 1已绑定
         */
        public static final int DO_BIND = 1;
    }
    public static final class BUSINESS_LOCK_KEY {

        public static final String INCONTROL_KEY = "{}:vehicle_info_deal";


        public static final String ICR_TSDP_QRY_FAIL = "icr_tsdp_qry:fail_{}";

    }
    public static final class CODE_PREFIX {

        public static final String MEMBER_CODE = "MEMBER:CODE:";

        public static final String ECP_CAR = "ECP:CAR:";

    }
    public static final class SERVICE_TYPE {

        /**
         * 1：remote vcs服务
         */
        public static final Integer REMOTE = 1;
        /**
         * 2：非remote服务
         */
        public static final Integer NOT_REMOTE = 2;

        /**
         *  Pivi服务
         * */
        public static final Integer PIVI = 3;
    }

    /**
     * 1主动登录 2过期服务查询
     */

        public static final int PROACTIVE = 1;

        public static final int EXPIRE_QRY = 2;


    /**
     * remote过期数据处理状态 1待处理 2处理成功 3处理失败
     */
    public static final int PENDING = 1;

    public static final int SUCCESS = 2;

    public static final int FAIL = 3;


    /**
     *  redis key
     */
    public static final class REDIS_KEY {
        /**
         * 车型编码映射缓存
         */
        public static final String SERIES_CACHE_KEY = "global:series:mapping";

        public static final String APPDCU_BATCH_RENEWAL_KEY = "appdcu:batch:renewal:key";

        //发送 ICCID批量修改 ICCID_BATCH_MODIFY_KEY
        public static final String ICCID_BATCH_MODIFY_KEY = "iccid:batch:modify:key";

        public static final String APPD_ASYNC_RENEWAL_KEY = "appd:async:renewal:key";

        public static final String UNICOM_ASYNC_RENEWAL_KEY = "unicom:async:renewal:key";

        public static final String RNR_BATCH_QUERY_KEY = "rnr:batch:query:key";

        public static final String SOTA_QUERY_KEY = "sota:query:key";

        public static final String REMOTE_BATCH_RENEWAL_KEY = "remote:batch:renewal:key";

        public static final String REMOTE_ASYNC_RENEWAL_KEY = "remote:async:renewal:key";

        public static final String CONCURRENT_AMAP_RENEWAL_KEY = "concurrent:amap:renewal:key:";

        public static final String CONCURRENT_APPD_RENEWAL_KEY = "concurrent:appd:renewal:key:";

        public static final String CONCURRENT_UNICOM_RENEWAL_KEY = "concurrent:unicom:renewal:key:";

        public static final String CONCURRENT_REMOTE_RENEWAL_KEY = "concurrent:remote:renewal:key:";

        public static final String MOCK_DP_KEY = "TEST_VIN_MAP";

    }

    public static final String REMOTE_SERVICE_NAME = "InControl远程车控";

    public static final String PIVI_SERVICE_NAME = "InControl在线服务";
    public static final String CAN_NOT_BUY = "您的爱车无需购买";
    public static final String I_KNOW = "我知道了";
    public static final String CAN_NOT_BUY_CONTACT_CONSUMER = "您的爱车无需购买，如有问题请联系客服。";
    public static final String CONTACT_CONSUMER = "联系客服";

    public static final String DEFAULT_CONCAT_STR = "-";

    public static final String APPD_CANNOT_BUY_PACKAGE_CODE = "DP";

    public static final String APPD_CANNOT_BUY_SERVICE_NAME = "DP";

    public static final String FILE_CODE = "AWS_S3_FILE";
    public static final String EXCEL_FORMATTER = ".xlsx";
    public static final String ATTENTION_RESULT  = "请关注续费校验结果";

    /**
     * ICCID 请求接口成功的响应信息，后期可能做中英文提示msg 埋点
     */
    public static final String SINFLE_MODIFY_SUCCESS_MESSAGE = "请求已发送，请前往修改记录查看结果";

    public static final String UPLOAD_ICCID_EXCEL_SUCCESS_MSG = "请关注修改文件校验结果";

    public static final String RENEWAL_INFO_NOT_MATCH = "续费信息无法匹配";
    public static final String ICCID_NOT_EXIST = "ICCID不存在";

    public static final String PROFILE_ACTIVE = "spring.profiles.active";
    public static final String APPD_SYSTEM_ERROR = "APPD系统异常";

    /**
     * 高德续费年限
     */
    public static final class AMAP_RENEWAL_YEAR {


        public static final int ONE_YEAR = 1;

        public static final int THREE_YEAR = 3;

        public static final int SIX_YEAR = 6;
    }
}
