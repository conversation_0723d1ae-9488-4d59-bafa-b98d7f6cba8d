package com.jlr.ecp.subscription.controller.admin.appd;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuSingleRenewalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleExpireVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSinglePageListV0;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleRenewalV0;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Tag(name = "单个续费-APPD-联通")
@RequestMapping("/appd/uicom/single/manual/renewal")
@Validated
public class AppDCuSingleRenewalController {
    @Resource
    private AppDCuSingleRenewalService appDCuSingleRenewalService;

    @PostMapping("/pageList")
    @Operation(summary = "APPD和联通单个续费分页查询")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-single-renewal:forms')")
    public CommonResult<PageResult<AppDCuSinglePageListV0>> getAppDCuSinglePageList(@RequestBody @Valid AppDUcSingleRenewalPageDTO pageDTO) {
        return CommonResult.success(appDCuSingleRenewalService.getAppDCuSinglePageList(pageDTO));
    }

    @GetMapping("/expireList")
    @Operation(summary = "信息娱乐及网络流量单个续费二次弹窗")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-single-renewal:forms')")
    public CommonResult<List<AppDCuSingleExpireVO>> getAppDCuSingleExpireList(@RequestParam(value = "carVin")String carVin) {
        return CommonResult.success(appDCuSingleRenewalService.getAppDCuSingleExpireList(carVin));
    }

    @PostMapping("/operate")
    @Operation(summary = "APPD和联通单个续费操作")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-single-renewal:forms')")
    public CommonResult<AppDCuSingleRenewalV0> appDOperateRenewal(@RequestBody @Valid AppDCuSingleRenewalDTO appDCuSingleRenewalDTO) {
        return appDCuSingleRenewalService.appDCuSingleOperateRenewal(appDCuSingleRenewalDTO);
    }


}
