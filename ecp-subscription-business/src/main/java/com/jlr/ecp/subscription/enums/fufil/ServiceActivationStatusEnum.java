package com.jlr.ecp.subscription.enums.fufil;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServiceActivationStatusEnum {
    ACTIVATION_NO(-1, "无需激活"),
    ACTIVATION_UNKNOWN(1, "激活中"),
    ACTIVATION_SUCCESS(2, "已激活"),
    ACTIVATION_FAILED(3, "激活失败"),
    BUSINESS_ACTIVATION_FAILED(4, "业务激活失败");

    private final Integer status;

    private final String dec;
}
