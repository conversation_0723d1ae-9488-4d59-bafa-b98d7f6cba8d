package com.jlr.ecp.subscription.service.iccid;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidModifyQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidModifyQueryPageVO;

public interface IccidModifyQueryService {

    /**
     * 修改结果 分页查询
     * @param pageDTO
     * @return
     */
    PageResult<IccidModifyQueryPageVO> queryModifyResultPageList(IccidModifyQueryPageDTO pageDTO);

    /**
     * 根据批次号查询ICCID的批次号
     * @param batchNo
     * @return
     */
    String queryModifyBatchNo(String batchNo);
}
