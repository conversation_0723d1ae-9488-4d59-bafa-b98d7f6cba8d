package com.jlr.ecp.subscription.dal.dataobject.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_vehicle_model_master_data(VehicleModelMasterData)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_vehicle_model_master_data")
public class VehicleModelMasterDataDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;

     /**
     * 品牌名
     */    
    private String brandName;

     /**
     * 品牌CODE
     */    
    private String brandCode;

    /**
     * 车机型号;车机型号 PIVI
     */
    private String carSystemModel;

    /**
     *车型编码
     */
    private String seriesCode;

    /**
     * 车型名称
     */
    private String seriesName;

    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 型号年款
     */
    private String modelYear;

    /**
     * 产品名称EN
     */
    private String productionEn;

    /**
     * House of Brand 英文描述描述
     */
    private String hobEn;



}

