package com.jlr.ecp.subscription.controller.admin.dto.iccid;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "ICCID单个修改操作记录分页查询DTO")
public class IccidSingleModifyPageDTO extends PageParam {

    @Schema(description = "操作时间排序, 正序:asc, 倒叙:desc")
    @NotEmpty
    private String operateTimeSort;
}