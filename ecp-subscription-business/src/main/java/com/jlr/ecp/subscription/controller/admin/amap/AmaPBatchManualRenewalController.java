package com.jlr.ecp.subscription.controller.admin.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.service.amap.AmaPBatchManualRenewalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/amap/batch/manual/renewal")
@Validated
@Tag(name = "AMAP手动续费- 批量续费")
public class AmaPBatchManualRenewalController {
    @Resource
    private AmaPBatchManualRenewalService amaPBatchManualRenewalService;

    @PostMapping("/queryBatchPageList")
    @Operation(summary = "查询批量续费页面列表")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-multiple-renewal:forms')")
    public CommonResult<PageResult<AmaPBatchRenewalPageVO>> queryBatchRenewalPageList(@RequestBody @Valid AmaPBatchRenewalPageDTO amaPPageDto) {
        return CommonResult.success(amaPBatchManualRenewalService.queryBatchRenewalPageList(amaPPageDto));
    }

    @PostMapping("/uploadExcel")
    @Operation(summary = "上传excel文件")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-multiple-renewal:forms')")
    public CommonResult<AmaPBatchRenewalUploadVO> uploadExcelRenewal(@RequestBody MultipartFile multipartFile) {
        return amaPBatchManualRenewalService.uploadAmaPExcelRenewal(multipartFile);
    }

    @PostMapping("/batchSendAmaP")
    @Operation(summary = "高德批量续费发送")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-multiple-renewal:forms')")
    public CommonResult<String> batchSendRenewal(@RequestBody @Valid AmaPBatchSendDTO amaPBatchSendDTO) {
        return amaPBatchManualRenewalService.batchSendAmaPRenewal(amaPBatchSendDTO);
    }

    @GetMapping("/download/template")
    @Operation(summary = "下载高德批量Excel发送模板")
    @PreAuthorize("@ss.hasPermission('subscribe:navigation-manual-multiple-renewal:forms')")
    public CommonResult<String> downloadTemplateUrl() {
        // 获取模板文件的URL
        String templateUrl = amaPBatchManualRenewalService.getTemplateUrl();
        return CommonResult.success(templateUrl);
    }
}
