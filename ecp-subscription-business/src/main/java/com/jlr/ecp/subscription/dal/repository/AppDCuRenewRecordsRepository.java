package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;

import java.util.List;

/**
 * 手动续费Repository接口
 *
 */
public interface AppDCuRenewRecordsRepository extends IService<AppDCuRenewRecords> {

    /**
     * 查询批次下的所有车辆续保记录
     *
     */
    List<AppDCuRenewRecords> getByVinListAndStatus(List<String> vinList, Integer status, Long renewNo, Integer renewServiceType);

    /**
     * 查询批次下的所有车辆续保记录
     *
     */
    List<AppDCuRenewRecords> getByVinAndStatus(String vinList, Integer status);
}
