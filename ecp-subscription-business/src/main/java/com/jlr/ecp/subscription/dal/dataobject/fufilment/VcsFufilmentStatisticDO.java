package com.jlr.ecp.subscription.dal.dataobject.fufilment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_vcs_fufilment_statistic
 *
 * @TableName t_vcs_fufilment_statistic
 */
@TableName(value = "t_vcs_fufilment_statistic")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsFufilmentStatisticDO extends BaseDO {

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;


    /**
     * 订阅服务数量;订阅服务数量
     */
    @TableField(value = "vcs_service_count")
    private Integer vcsServiceCount;

    /**
     *车型编码
     */
    private String seriesCode;

    /**
     *品牌
     */
    private String brandCode;

    /**
     * 车型名称
     */
    private String seriesName;

    /**
     * 用户incontrol账号
     */
    private String incontrolId;
}