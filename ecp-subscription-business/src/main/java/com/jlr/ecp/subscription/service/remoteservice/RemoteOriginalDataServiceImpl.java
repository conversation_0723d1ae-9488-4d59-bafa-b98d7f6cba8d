package com.jlr.ecp.subscription.service.remoteservice;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.subscription.api.vininit.dto.parse.VinInitParseErrorDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import com.jlr.ecp.subscription.dal.mysql.remoteservice.RemoteOriginalDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RemoteOriginalDataServiceImpl implements RemoteOriginalDataService {

    @Resource
    RemoteOriginalDataMapper remoteOriginalDataMapper;

    @Override
    public void updateDataSuccess(Set<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            remoteOriginalDataMapper.updateBatchToSuccess(ids);
        }
    }

    @Override
    public void updateDataFail(Set<Long> ids, String failType, String failDesc) {
        if (CollUtil.isNotEmpty(ids)) {
            ArrayList<RemoteOriginalDataDO> updateList = new ArrayList<>(ids.size());
            List<RemoteOriginalDataDO> oldDataList = remoteOriginalDataMapper.selectList(RemoteOriginalDataDO::getId, ids);
            for (RemoteOriginalDataDO oldDataDO : oldDataList) {
                oldDataDO.setStatus(Constants.FAIL);
                oldDataDO.setFailType(failType);
                oldDataDO.setFailDesc(failDesc);
                oldDataDO.setMissCount(oldDataDO.getMissCount() + 1);
                oldDataDO.setUpdatedTime(LocalDateTime.now());
                updateList.add(oldDataDO);
            }
            // ids.removeIf(id -> oldDataList.stream().anyMatch(oldDataDO -> oldDataDO.getId().equals(id)));
            // for (Long id : ids) {
            //     RemoteOriginalDataDO dataDO = new RemoteOriginalDataDO();
            //     dataDO.setId(id);
            //     dataDO.setStatus(Constants.FAIL);
            //     dataDO.setFailType(failType);
            //     dataDO.setFailDesc(failDesc);
            //     saveOrUpdateList.add(dataDO);
            // }
            remoteOriginalDataMapper.updateBatch(updateList);
        }
    }

    /**
     * 根据解析错误信息更新日期失败的记录
     *
     * @param list 解析错误的DTO列表，包含需要更新的记录信息
     * @param failType 失败类型，描述解析失败的原因
     */
    @Override
    public void updateDateFailByParse(List<VinInitParseErrorDTO> list, String failType) {
        if (CollUtil.isEmpty(list) || StringUtils.isBlank(failType)) {
            return ;
        }
        log.info("根据解析错误信息更新日期失败的记录, 原始数量:{}, failType:{}", list.size(), failType);
        Map<Long, VinInitParseErrorDTO> map = list.stream().collect(Collectors.toMap(VinInitParseErrorDTO::getId, v -> v, (v1, v2) -> v1));
        Set<Long> ids = map.keySet();
        List<RemoteOriginalDataDO> oldDataList = remoteOriginalDataMapper.selectList(RemoteOriginalDataDO::getId, ids);
        ArrayList<RemoteOriginalDataDO> updateList = new ArrayList<>(ids.size());
        for (RemoteOriginalDataDO oldDataDO : oldDataList) {
            oldDataDO.setStatus(Constants.FAIL);
            oldDataDO.setFailType(failType);
            oldDataDO.setFailDesc(map.getOrDefault(oldDataDO.getId(), new VinInitParseErrorDTO()).getErrorMessage());
            oldDataDO.setMissCount(oldDataDO.getMissCount() + 1);
            oldDataDO.setUpdatedTime(LocalDateTime.now());
            updateList.add(oldDataDO);
        }
        log.info("根据解析错误信息更新日期失败的记录, 去重后的数量:{}, 待更新数量:{}", ids.size(), updateList.size());
        remoteOriginalDataMapper.updateBatch(updateList);
    }
}
