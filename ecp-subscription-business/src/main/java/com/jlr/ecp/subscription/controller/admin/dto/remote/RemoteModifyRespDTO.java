package com.jlr.ecp.subscription.controller.admin.dto.remote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "remote服务续费结果")
public class RemoteModifyRespDTO {

    @Schema(description = "续费是否成功")
    private boolean success;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "Too Many Requests")
    private boolean tooManyRequests;

    @Schema(description = "响应信息")
    private String response;
}
