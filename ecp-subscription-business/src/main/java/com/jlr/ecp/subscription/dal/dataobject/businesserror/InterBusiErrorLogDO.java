package com.jlr.ecp.subscription.dal.dataobject.businesserror;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 业务异常日志表(t_inter_busi_error_log)实体类
 *
 * <AUTHOR>
 * @since 2024-02-05 13:46:20
 * @description 由 Mybatisplus Code Generator 创建
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_inter_busi_error_log")
public class InterBusiErrorLogDO extends TenantBaseDO {

    /**
     * 编号
     */
    @TableId
	private Integer id;
    /**
     * 链路追踪编号
     */
    private String traceId;
    /**
     * 业务单据id
     */
    private String businessId;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务参数
     */
    private String businesParams;
    /**
     * 失败次数
     */
    private Integer errorCount;
    /**
     * 异常导致的消息
     */
    private String errorMessage;
}