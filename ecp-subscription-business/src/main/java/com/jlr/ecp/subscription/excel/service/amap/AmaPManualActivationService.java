package com.jlr.ecp.subscription.excel.service.amap;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.excel.vo.amap.AmaPManualActivationImportVO;
import com.jlr.ecp.subscription.excel.vo.amap.AmaPManualUploadResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AmaPManualActivationService {

    /**
     * 下载AMAP没有激活车辆VIN码。
     *
     * @return CommonResult<String> 返回一个包含文件下载路径的结果对象，如果下载为空，则返回相应的提示信息。
     */
    CommonResult<String> downloadCarVin();

    /**
     * 处理AMAP手动激活Excel文件上传。
     *
     * @param file 上传的Excel文件，包含AMAP手动激活的信息。
     * @return CommonResult<AmaPManualUploadResultVO> 返回一个处理结果
     */
    CommonResult<AmaPManualUploadResultVO> uploadAmaPActivationExcel(MultipartFile file);

    /**
     * 获取手动激活记录的导入视图对象列表。
     *
     * @return List<AmaPManualActivationImportVO> - 手动激活记录的导入视图对象列表。
     */
    List<AmaPManualActivationImportVO> getAmaPManualActivationImportVO();
}
