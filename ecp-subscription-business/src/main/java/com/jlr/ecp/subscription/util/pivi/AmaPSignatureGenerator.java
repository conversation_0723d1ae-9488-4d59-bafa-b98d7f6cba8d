package com.jlr.ecp.subscription.util.pivi;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * amap签名util
 *
 * <AUTHOR>
 * @date 2024/05/24
 */
@RequiredArgsConstructor
@Component
public class AmaPSignatureGenerator {


    /**
     * 构建签名
     *
     * @param accessKeyId 访问密钥id
     * @param secretKey   密钥
     * @param httpMethod  http方法
     * @param parameters  参数
     * @return {@link Map }<{@link String }, {@link String }>
     */
    public static UriComponents buildSignature(String accessKeyId, String secretKey, String httpMethod,
                                               String endPoint, Map<String, Object> parameters) {
        String SignatureMethod = "HMAC-SHA1";
        String SignatureVersion = "1.0";
        String SignatureNonce = IdUtil.getSnowflakeNextIdStr();
        String timestamp = ZonedDateTime.now(ZoneId.of("UTC"))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        parameters.put("SignatureMethod", SignatureMethod);
        parameters.put("SignatureNonce", SignatureNonce);
        parameters.put("SignatureVersion", SignatureVersion);
        parameters.put("AccessKeyId", accessKeyId);
        parameters.put("Timestamp", timestamp);
        // Step 1: Generate signString
        String signString = generateSignString(httpMethod, parameters);

        // Step 2: Calculate the signature
        String signature = calculateSignature(signString, secretKey);

        // Add the signature to the parameters
        parameters.put("Signature", encode(signature));
        return createUrlEncodedQuery(endPoint, parameters);
    }

    private static String generateSignString(String httpMethod, Map<String, Object> parameters) {
        String encodedParams = parameters.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(param -> encode(param.getKey()) + "=" + encode(param.getValue().toString()))
                .collect(Collectors.joining("&"));
        // Step 1.3: Combine them
        return httpMethod + "&" + encode("/") + "&" + encode(encodedParams);
    }

    private static String calculateSignature(String signString, String accessKeySecret) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec((accessKeySecret + "&").getBytes(StandardCharsets.UTF_8), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(signString.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("Error while calculating signature", e);
        }
    }

    /**
     * 编码
     *
     * @param value 值
     * @return {@link String }
     */
    private static String encode(String value) {
        return value != null ? URLEncoder.encode(value, StandardCharsets.UTF_8)
                .replace("+", "%20")
                .replace("*", "%2A")
                .replace("%7E", "~") : null;
    }

    private static UriComponents createUrlEncodedQuery(String endPoint, Map<String, Object> parameters) {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUri(URI.create(endPoint));
        parameters.forEach(uriComponentsBuilder::queryParam);
        return uriComponentsBuilder.build();
    }

//    public static void main(String[] args) {
//        String secretKey = "testsecret";
//        Map<String, Object> parameters = new LinkedHashMap<>() {{
//            put("Action", "AuthenticateSig");
//            put("SessionId", "13211111111");
//            put("Scene", "xxx");
//            put("Token", "xxx");
//            put("Sig", "1");
//            put("AppKey", "xxx");
//            put("RemoteIp", "xxx");
//            put("Format", "xml");
//            put("Version", "2018-01-12");
//            put("SignatureMethod", "HMAC-SHA1");
//            put("SignatureNonce", "15215528852396");
//            put("SignatureVersion", "1.0");
//            put("AccessKeyId", "testid");
//            put("Timestamp", "2014-10-10T12:00:00Z");
//        }};
//        String signString = generateSignString(HttpMethod.GET.name(), parameters);
//
//        String url = "GET&%2F&AccessKeyId%3Dtestid%26Action%3DAuthenticateSig%26AppKey%3Dxxx%26Format%3Dxml%26RemoteIp%3Dxxx%26Scene%3Dxxx%26SessionId%3D13211111111%26Sig%3D1%26SignatureMethod%3DHMAC-SHA1%26SignatureNonce%3D15215528852396%26SignatureVersion%3D1.0%26Timestamp%3D2014-10-10T12%253A00%253A00Z%26Token%3Dxxx%26Version%3D2018-01-12";
//
//        System.out.println(url.equals(signString));
//        System.out.println(signString);
//
//        // Step 2: Calculate the signature
//        String signature = calculateSignature(signString, secretKey);
//
//        String sing = "lWV2CWB7camHHDmaUmMOlbgQc9c=";
//        System.out.println(sing.equals(signature));
//        System.out.println(signature);
//
//        String requestUrl = "https://afs.aliyuncs.com/?Action=AuthenticateSig" +
//                "&SessionId=13211111111" +
//                "&Scene=xxx" +
//                "&Token=xxx" +
//                "&Sig=1" +
//                "&AppKey=xxx" +
//                "&RemoteIp=xxx" +
//                "&Format=xml" +
//                "&Version=2018-01-12" +
//                "&SignatureMethod=HMAC-SHA1" +
//                "&SignatureNonce=15215528852396" +
//                "&SignatureVersion=1.0" +
//                "&AccessKeyId=testid" +
//                "&Timestamp=2014-10-10T12:00:00Z" +
//                "&Signature=lWV2CWB7camHHDmaUmMOlbgQc9c%3D";
//
//        parameters.put("Signature", encode(signature));
//        UriComponents uriComponents = createUrlEncodedQuery("https://afs.aliyuncs.com/", parameters);
//        System.out.println(uriComponents.toUriString().equals(requestUrl));
//        System.out.println(uriComponents.toUriString());
//    }

}

