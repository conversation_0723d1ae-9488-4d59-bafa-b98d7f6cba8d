package com.jlr.ecp.subscription.service.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.cart.CartApi;
import com.jlr.ecp.subscription.api.consumer.ConsumerServiceApi;
import com.jlr.ecp.subscription.api.consumer.dto.ConsumerIncontrolDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerIncontrolDO;
import com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerIncontrolMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.jlr.ecp.framework.common.pojo.CommonResult.success;

@RestController
@Slf4j
public class ConsumerServiceImpl implements ConsumerServiceApi {

    @Resource
    ConsumerIncontrolMapper consumerIncontrolMapper;

    @Resource
    private CartApi cartApi;


    @Override
    public CommonResult<ConsumerIncontrolDTO> bindIRC(String consumerCode, String inControlID, String clientId) {
        log.info("bindIRC方法入参, consumerCode:{}, inControlID:{}, clientId:{}", consumerCode, inControlID, clientId);

        //查询ICR已绑定的微信用户
        ConsumerIncontrolDO consumerIncontrol = consumerIncontrolMapper.selectByIncontrolId(inControlID,clientId);
        log.info("查询ICR已绑定的微信用户，consumerIncontrol:{}", consumerIncontrol);
        if (consumerIncontrol != null){
            // 其他微信用户绑定的ICR账号解绑
            consumerIncontrolMapper.unBindICR(inControlID,clientId);
            log.info("绑定的ICR账号解绑,inControlID:{},clientId:{}", inControlID, clientId);
            // consumerCode不同的情况下，清空上次登录用户的购物车
            if (!consumerCode.equals(consumerIncontrol.getConsumerCode())) {
                cartApi.deleteByConsumerCodeAndICR(consumerIncontrol.getConsumerCode(), inControlID, clientId);
                log.info("清空上次登录用户的购物车, old consumerCode={}", consumerIncontrol.getConsumerCode());
            }
        }
        // 之前有绑定ICR的解绑
        ConsumerIncontrolDO bindInControl = consumerIncontrolMapper.selectByConsumerCode(consumerCode,clientId);
        log.info("之前有绑定ICR的解绑，bindInControl:{}", bindInControl);
        if (bindInControl != null) {
            if (!inControlID.equals(bindInControl.getIncontrolId())) {
                log.info("执行解绑");
                bindInControl.setBindStatus(Constants.BindStatus.UN_BIND);
                consumerIncontrolMapper.updateById(bindInControl);
                consumerIncontrol = insertIncontrolConsumer(inControlID, consumerCode,clientId);
            }
        } else {
            consumerIncontrol = insertIncontrolConsumer(inControlID, consumerCode,clientId);
        }

        log.info("bindIRC方法返回结果, consumerIncontrol:{}", consumerIncontrol);
        return success(BeanUtil.copyProperties(consumerIncontrol,ConsumerIncontrolDTO.class));
    }

    @Override
    public CommonResult<ConsumerIncontrolDTO> unBindIRC(String consumerCode, String inControlID, String clientId) {
        // 之前有绑定ICR的解绑
        ConsumerIncontrolDO bindInControl = consumerIncontrolMapper.selectByConsumerCode(consumerCode,clientId);
        if (bindInControl != null) {
            if (inControlID.equals(bindInControl.getIncontrolId())) {
                bindInControl.setBindStatus(Constants.BindStatus.UN_BIND);
                consumerIncontrolMapper.updateById(bindInControl);
            }
        }
        return success(BeanUtil.copyProperties(bindInControl,ConsumerIncontrolDTO.class));
    }

    @Override
    public CommonResult<ConsumerIncontrolDTO> getConsumerByCodeOrICR(String consumerCode, String inControlID,String clientId) {
        if (StrUtil.isNotBlank(consumerCode)) {
            ConsumerIncontrolDO bindInControl = consumerIncontrolMapper.selectByConsumerCode(consumerCode,clientId);
            return success(BeanUtil.copyProperties(bindInControl,ConsumerIncontrolDTO.class));
        } else if (StrUtil.isBlank(inControlID)) {
            ConsumerIncontrolDO consumerIncontrol = consumerIncontrolMapper.selectByIncontrolId(inControlID,clientId);
            return success(BeanUtil.copyProperties(consumerIncontrol,ConsumerIncontrolDTO.class));
        }
        return success(null);
    }

    private ConsumerIncontrolDO insertIncontrolConsumer(String inControlID, String consumerCode,String clientId) {
        ConsumerIncontrolDO consumerIncontrol = new ConsumerIncontrolDO();
        consumerIncontrol.setConsumerCode(consumerCode);
        consumerIncontrol.setIncontrolId(inControlID);
        consumerIncontrol.setBindChannel(clientId);
        consumerIncontrol.setBindTime(LocalDateTime.now());
        consumerIncontrol.setBindStatus(Constants.BindStatus.DO_BIND);
        consumerIncontrolMapper.insert(consumerIncontrol);
        return consumerIncontrol;
    }
}
