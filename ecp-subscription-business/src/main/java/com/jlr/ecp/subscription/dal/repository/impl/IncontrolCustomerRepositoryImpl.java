package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.mysql.incontrol.IncontrolCustomerMapper;
import com.jlr.ecp.subscription.dal.repository.IncontrolCustomerRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * 客户信息Repository实现类
 *
 */
@Component
@Slf4j
public class IncontrolCustomerRepositoryImpl extends ServiceImpl<IncontrolCustomerMapper, IncontrolCustomerDO> implements IncontrolCustomerRepository {

    @Override
    public boolean saveOrUpdateBatch(Collection<IncontrolCustomerDO> entities) {
        log.info("批量保存或更新客户信息, size: {}", entities.size());
        try {
            baseMapper.saveOrUpdateBatch(entities);
        } catch (Exception e) {
            log.info("批量保存或更新客户信息异常：", e);
            return false;
        }
        return true;
    }

    @Override
    public IncontrolCustomerDO findByIncontrolId(String incontrolId) {
        log.info("根据InControl ID查询客户信息, incontrolId: {}", incontrolId);
        return getOne(new LambdaQueryWrapperX<IncontrolCustomerDO>()
                .eq(IncontrolCustomerDO::getIncontrolId, incontrolId)
                .eq(IncontrolCustomerDO::getIsDeleted, false)
                .orderByDesc(IncontrolCustomerDO::getId)
                .last("LIMIT 1"));
    }

    @Override
    public List<IncontrolCustomerDO> findByIncontrolIdList(List<String> incontrolIdList) {
        log.info("根据InControl ID列表查询客户信息, incontrolIdList size: {}", incontrolIdList.size());
        return list(new LambdaQueryWrapperX<IncontrolCustomerDO>()
                .in(IncontrolCustomerDO::getIncontrolId, incontrolIdList)
                .eq(IncontrolCustomerDO::getIsDeleted, false));
    }

    @Override
    public int insert(IncontrolCustomerDO customerDO) {
        log.info("插入单个客户信息, incontrolId: {}", customerDO.getIncontrolId());
        return baseMapper.insert(customerDO);
    }

    @Override
    public boolean updateById(IncontrolCustomerDO customerDO) {
        log.info("更新客户信息, id: {}, incontrolId: {}", customerDO.getId(), customerDO.getIncontrolId());
        return baseMapper.updateById(customerDO) > 0;
    }
}
