package com.jlr.ecp.subscription.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.repository.PIVIPackageRepository;
import com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PIVI包信息Repository实现类
 *
 */
@Component
@Slf4j
public class PIVIPackageRepositoryImpl extends ServiceImpl<PIVIPackageDOMapper, PIVIPackageDO> implements PIVIPackageRepository {

    @Override
    public PIVIPackageDO findICCIDByCarVin(String carVin) {
        log.info("根据车辆VIN查找ICCID, carVin: {}", carVin);
        return getOne(new LambdaQueryWrapperX<PIVIPackageDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(PIVIPackageDO::getVin, carVin)
                .isNotNull(PIVIPackageDO::getIccid)
                .orderByDesc(BaseDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));
    }

    @Override
    public List<PIVIPackageDO> selectPIVIPackageDOByCarVinList(Collection<String> carVinCollection) {
        log.info("根据车辆VIN列表查询PIVI包信息, carVinCollection数量: {}", CollUtil.size(carVinCollection));

        // 添加空集合检查，避免无效查询
        if (CollUtil.isEmpty(carVinCollection)) {
            log.info("车辆VIN列表为空，返回空结果");
            return new ArrayList<>();
        }

        try {
            return list(new LambdaQueryWrapperX<PIVIPackageDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .in(PIVIPackageDO::getVin, carVinCollection));
        } catch (Exception e) {
            log.info("根据车辆VIN列表查询PIVI包信息异常, carVinCollection数量: {}",
                    CollUtil.size(carVinCollection), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, PIVIPackageDO> queryPIVIPackageDOMap(Collection<String> carVinCollection) {
        log.info("根据车辆VIN列表查询PIVI包信息, carVinCollection数量: {}", CollUtil.size(carVinCollection));

        // 添加空集合检查
        if (CollUtil.isEmpty(carVinCollection)) {
            log.info("车辆VIN列表为空，返回空Map");
            return new HashMap<>();
        }

        List<PIVIPackageDO> piviPackageDOS = selectPIVIPackageDOByCarVinList(carVinCollection);
        if (CollUtil.isEmpty(piviPackageDOS)) {
            log.info("根据车辆VIN列表查询PIVI包信息, 查询结果为空, carVinCollection数量: {}", CollUtil.size(carVinCollection));
            return new HashMap<>();
        }

        try {
            return piviPackageDOS.stream()
                    .filter(piviPackageDO -> piviPackageDO.getVin() != null)
                    .collect(Collectors.toMap(PIVIPackageDO::getVin, Function.identity(), (v1, v2) -> v1));
        } catch (Exception e) {
            log.info("构建PIVI包信息Map异常, piviPackageDOS数量: {}", piviPackageDOS.size(), e);
            return new HashMap<>();
        }
    }

    @Override
    public List<VinExpireServiceDTO> getVinExpireServiceByDataRange(Page<VinExpireServiceDTO> page,
                                                                    LocalDateTime startTime,
                                                                    LocalDateTime endTime) {
        log.info("分页查询VIN过期服务, startTime: {}, endTime: {}, page: {}", startTime, endTime, page.getCurrent());
        return baseMapper.getVinExpireServiceByDataRangeForAll(page, startTime, endTime);
    }

    @Override
    public List<VinExpireServiceDTO> getVinExpireServiceByDataRangeForAllNoPage(LocalDateTime startTime,
                                                                                LocalDateTime endTime) {
        log.info("查询VIN过期服务（不分页）, startTime: {}, endTime: {}", startTime, endTime);
        return baseMapper.getVinExpireServiceByDataRangeForAllNoPage(startTime, endTime);
    }

    @Override
    public PIVIPackageDO selectOnePiviDataByVin(String vin) {
        log.info("根据VIN获取指定的PIVI数据, vin: {}", vin);
        return getOne(new LambdaQueryWrapperX<PIVIPackageDO>()
                .eq(PIVIPackageDO::getVin, vin)
                .eq(PIVIPackageDO::getIsDeleted, false)
                .orderByDesc(PIVIPackageDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    @Override
    public PIVIPackageDO selectOneByCarVin(String carVin) {
        log.info("根据车辆VIN查询单个PIVI包信息, carVin: {}", carVin);
        return getOne(new LambdaQueryWrapperX<PIVIPackageDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(PIVIPackageDO::getVin, carVin)
                .orderByDesc(BaseDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));
    }
}
