package com.jlr.ecp.subscription.excel.listener.initialize;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.initialize.InitializeStatusQueryExcel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class InitializeStatusQueryListener extends AnalysisEventListener<InitializeStatusQueryExcel> {


    private static final int BATCH_COUNT = 500;

    private static final String VIN_TITLE = "VIN";

    private List<InitializeStatusQueryExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<InitializeStatusQueryExcel> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Boolean isFormatError = false;

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(InitializeStatusQueryExcel initializeStatusQueryExcel, AnalysisContext analysisContext) {
        try {
            dataList.add(initializeStatusQueryExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                checkUploadExcel(dataList);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("批量VIN在线服务初始化状态查询解析excel文件异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("批量VIN在线服务初始化状态查询处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            checkUploadExcel(dataList);
        } catch (Exception e) {
            log.info("批量VIN在线服务初始化状态查询处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("批量VIN在线服务初始化状态查询所有数据解析完成！");
    }


    /**
     * 检查上传的Excel文件中的VIN
     *
     * @param initializeStatusQueryExcelList 包含多个VIN的Excel文件列表
     */
    public void checkUploadExcel(List<InitializeStatusQueryExcel> initializeStatusQueryExcelList) {
        if (headers.isEmpty() || !VIN_TITLE.equals(headers.get(0).get(0))) {
            isFormatError = true;
        }
    }

}
