package com.jlr.ecp.subscription.dal.mysql.oss;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.model.dto.VinMatchCountDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
 * t_dms_oss_original_data表数据库访问层
 * <AUTHOR>
 */
@Mapper
public interface DmsOssOriginalDataMapper extends BaseMapperX<DmsOssOriginalDataDO> {
    List<VinMatchCountDto> selectCountByJobId(@Param("jobIdSet") Set<Long> jobIdSet);
}
