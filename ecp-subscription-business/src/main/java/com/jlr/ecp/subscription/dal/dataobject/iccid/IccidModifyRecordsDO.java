package com.jlr.ecp.subscription.dal.dataobject.iccid;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_iccid_modify_records
 * @TableName t_iccid_modify_records
 */
@TableName(value ="t_iccid_modify_records")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class IccidModifyRecordsDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 批次编号;批次编号，单条修改记录拥有唯一的雪花算法生成的id; 对于批量修改就是批次号
     */
    @TableField(value = "modify_no")
    private Long modifyNo;

    /**
     * VIN
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 修改前联通ICCID;修改前联通ICCID
     */
    @TableField(value = "modify_before_iccid")
    private String modifyBeforeIccid;

    /**
     * 修改后联通ICCID;修改后联通ICCID
     */
    @TableField(value = "modify_after_iccid")
    private String modifyAfterIccid;

    /**
     * 修改状态;修改状态：1：进行中 2：修改成功 3：修改失败
     */
    @TableField(value = "modify_status")
    private Integer modifyStatus;

    /**
     * 数据来源 1单次修改 2批量修改
     */
    @TableField(value = "data_source")
    private Integer dataSource;

    /**
     * 错误信息;修改失败原因:1：ICCID在联通不存在 2.联通接口调用失败 3.系统异常
     */
    @TableField(value = "error_desc")
    private String errorDesc;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}