package com.jlr.ecp.subscription.service.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.search.ExpireResultQueryDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ServiceBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.SearchExpireUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceExpireQueryResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface VehicleExpireBatchSearchService {

    /**
     * 获取车辆过期模板的URL。
     *
     * @return String 返回车辆过期模板的URL地址
     */
    String getVehicleExpireTemplateUrl();

    /**
     * 查询批量服务到期日分页列表
     *
     * @param queryPageDTO 查询条件对象，包含分页信息和查询参数
     * @return PageResult<ServiceBatchQueryPageVO> 返回分页结果，包含批量服务列表和总记录数
     */
    PageResult<ServiceBatchQueryPageVO>  queryBatchServicePageList(ServiceBatchQueryPageDTO queryPageDTO);

    /**
     * 批量查询过期信息的函数。
     *
     * @param multipartFile 上传的Excel文件，包含需要查询的过期信息。
     * @return CommonResult<SearchExpireUploadVO> 返回一个包含上传结果的对象，如果成功则返回成功信息，否则返回错误信息。
     */
    CommonResult<SearchExpireUploadVO>  batchSearchExpire(MultipartFile multipartFile);

    /**
     * 查询服务到期信息并更新查询结果。
     *
     * @param batchNo 批次号，用于标识一组查询记录
     * @return Integer 查询的数量
     */
    Integer queryServiceAndUpdateExpireResult(String batchNo);

    /**
     * 根据批次号获取过期查询的批次号是否存在
     *
     * @param batchNo 要查询的批次号
     * @return 查询到的批次号，如果未找到则返回空字符串
     */
    String getExpireQueryBatchNo(String batchNo);

    /**
     * 获取所有过期的查询状态
     *
     * @return List<AmaPQueryStatusVO> 包含所有过期查询状态的列表，每个状态以 `AmaPQueryStatusVO` 对象表示。
     */
    List<AmaPQueryStatusVO>  getExpireQueryStatus();


    /**
     * 服务到期批量查询结果
     *
     * @param expireResultQueryDTO 查询条件对象，包含查询所需的各种参数
     * @return PageResult<ServiceExpireQueryResultVO> 分页查询结果，包含查询到的服务到期结果列表及总记录数
     */
    PageResult<ServiceExpireQueryResultVO> expireBatchQueryResult(ExpireResultQueryDTO expireResultQueryDTO);
}
