package com.jlr.ecp.subscription.dal.dataobject.fufilment;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "t_vcs_order_rollback_records")
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsOrderRollbackRecords extends BaseDO {
    @TableId
    private Long id;

    /**
     *  履约号ID
     * */
    @TableField(value = "fufilment_id")
    private String fufilmentId;

    /**
     *  回滚履约号ID
     * */
    @TableField(value = "rollback_fufilment_id")
    private String rollbackFufilmentId;

    /**
     *  服务包编码
     * */
    @TableField(value = "service_package")
    private String servicePackage;

    /**
     *  服务名
     * */
    @TableField(value = "service_name")
    private String serviceName;

    /**
     *  激活状态; 1：激活关闭中；2：激活关闭成功  3:激活关闭失败 4:业务激活关闭失败
     *  @link com.jlr.ecp.subscription.enums.fufil.CancelServiceStatusEnum
     * */
    @TableField(value = "activation_status")
    private Integer activationStatus;

    /**
     *  重试次数
     * */
    @TableField(value = "retry_times")
    private Integer retryTimes;

    /***
     *   激活方式;激活方式 1：接口激活
     * */
    @TableField(value = "activation_method")
    private Integer activationMethod;

    /**
     *  服务过期时间
     * */
    @TableField(value = "expire_date")
    private LocalDateTime expireDate;

    /**
     *  租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
