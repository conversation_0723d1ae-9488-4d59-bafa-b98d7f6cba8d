package com.jlr.ecp.subscription.config;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisService {
    @Autowired
    public RedisTemplate redisTemplate;

    private static final int SCAN_BATCH_SIZE = 500;  // 每次扫描获取的键数量
    private static final int PIPELINE_BATCH_SIZE = 1000; // 每批删除的键数量

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 获取计数key的自增最新值
     *
     * @param counterKey 计数key
     * @return 自增最新值
     */
    public Long increment(String counterKey) {
        return redisTemplate.opsForValue().increment(counterKey);
    }

    /**
     * 获取计数key的自增定长最新值
     *
     * @param counterKey 计数key
     * @param delta 增长的长度
     * @return 自增最新值
     */
    public Long increment(String counterKey, long delta) {
        return redisTemplate.opsForValue().increment(counterKey, delta);
    }

    /**
     * 使用Pipeline批量设置Hash中的数据（优化版本）
     *
     * @param key Redis键
     * @param dataMap 要批量设置的Hash数据
     * @param batchSize 每个Pipeline批次的大小
     */
    public <T> void batchSetCacheMapValue(String key, Map <String, T> dataMap, int batchSize) {
        if (CollUtil.isEmpty(dataMap)) {
            return;
        }
        // 获取序列化器
        org.springframework.data.redis.serializer.RedisSerializer<String> keySerializer = redisTemplate.getStringSerializer();
        org.springframework.data.redis.serializer.RedisSerializer<String> hashKeySerializer = redisTemplate.getHashKeySerializer();
        org.springframework.data.redis.serializer.RedisSerializer<T> hashValueSerializer = redisTemplate.getHashValueSerializer();
        // 序列化主键
        byte[] redisKey = keySerializer.serialize(key);

        // 过滤掉空值并分批处理
        List<Map.Entry<String, T>> nonNullEntries = dataMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toList());

        // 使用Guava的Lists.partition进行分批
        Lists.partition(nonNullEntries, batchSize).forEach(batch -> {
            redisTemplate.executePipelined((org.springframework.data.redis.core.RedisCallback<Object>) connection -> {
                for (Map.Entry<String, T> entry : batch) {
                    // 序列化字段和值
                    byte[] field = hashKeySerializer.serialize(entry.getKey());
                    byte[] valBytes = hashValueSerializer.serialize(entry.getValue());
                    if (field != null && valBytes != null) {
                        connection.hSet(redisKey, field, valBytes);
                    }
                }
                return null;
            });
        });
    }

    /**
     * 使用Pipeline批量设置Hash中的数据（默认批次大小1000）
     *
     * @param key Redis键
     * @param dataMap 要批量设置的Hash数据
     */
    public <T> void batchSetCacheMapValue(final String key, final Map<String, T> dataMap) {
        batchSetCacheMapValue(key, dataMap, 1000);
    }

    /**
     * 使用Pipeline批量设置Hash中的数据并设置过期时间
     *
     * @param key Redis键
     * @param dataMap 要批量设置的Hash数据
     * @param batchSize 每个Pipeline批次的大小
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     */
    public <T> void batchSetCacheMapValue(String key, Map<String, T> dataMap, int batchSize, long timeout, TimeUnit timeUnit) {
        if (dataMap == null || dataMap.isEmpty()) {
            return;
        }

        // 先执行批量插入
        batchSetCacheMapValue(key, dataMap, batchSize);

        // 设置过期时间
        expire(key, timeout, timeUnit);
    }

    /**
     * 使用Pipeline批量设置Hash中的数据并设置过期时间（默认批次大小1000）
     *
     * @param key Redis键
     * @param dataMap 要批量设置的Hash数据
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     */
    public <T> void batchSetCacheMapValue(final String key, final Map<String, T> dataMap, long timeout, TimeUnit timeUnit) {
        batchSetCacheMapValue(key, dataMap, 1000, timeout, timeUnit);
    }

    /**
     * 获取Hash中的字段数量（使用HLEN命令，性能更好）
     *
     * @param key Redis键
     * @return Hash中的字段数量
     */
    public Long getCacheMapSize(final String key) {
        return redisTemplate.opsForHash().size(key);
    }

    /**
     * 执行Pipeline操作，减少网络往返次数
     *
     * @param callback Pipeline回调函数
     * @return Pipeline执行结果列表
     */
    public <T> List<T> executePipelined(org.springframework.data.redis.core.RedisCallback<T> callback) {
        return redisTemplate.executePipelined(callback);
    }

    /**
     * 批量获取Hash值（带分批处理）- 优化版本
     *
     * @param key Redis键
     * @param fields 查询字段列表
     * @param batchSize 每批处理数量
     * @return 与输入字段顺序对应的值列表
     */
    public List<Object> batchGetHashValues(String key, List<String> fields, int batchSize) {
        if (CollUtil.isEmpty(fields)) {
            return Collections.emptyList();
        }

        // 获取序列化器（确保一致性）
        org.springframework.data.redis.serializer.RedisSerializer<String> keySerializer = redisTemplate.getStringSerializer();
        org.springframework.data.redis.serializer.RedisSerializer<String> hashKeySerializer =
            (org.springframework.data.redis.serializer.RedisSerializer<String>) redisTemplate.getHashKeySerializer();
        org.springframework.data.redis.serializer.RedisSerializer<Object> hashValueSerializer =
            (org.springframework.data.redis.serializer.RedisSerializer<Object>) redisTemplate.getHashValueSerializer();

        // 序列化主键（一次完成）
        byte[] serializedKey = keySerializer.serialize(key);
        if (serializedKey == null) {
            return createNullResults(fields.size());
        }

        // 预序列化所有字段（提升性能）
        List<byte[]> serializedFields = fields.stream()
            .map(hashKeySerializer::serialize)
            .collect(Collectors.toList());

        // 分批处理
        List<Object> results = new ArrayList<>(fields.size());
        int total = fields.size();

        for (int i = 0; i < total; i += batchSize) {
            int end = Math.min(i + batchSize, total);
            List<byte[]> batchFields = serializedFields.subList(i, end);

            List<Object> batchResults = executeHashPipeline(
                serializedKey,
                batchFields,
                hashValueSerializer
            );

            results.addAll(batchResults);
        }

        return results;
    }

    /**
     * 批量获取Hash值（默认批次大小500）
     *
     * @param key Redis键
     * @param fields 查询字段列表
     * @return 与输入字段顺序对应的值列表
     */
    public List<Object> batchGetHashValues(String key, List<String> fields) {
        return batchGetHashValues(key, fields, 500);
    }

    /**
     * 执行Hash Pipeline查询
     */
    private List<Object> executeHashPipeline(
        byte[] serializedKey,
        List<byte[]> serializedFields,
        org.springframework.data.redis.serializer.RedisSerializer<Object> valueSerializer
    ) {
        // 执行Pipeline查询
        List<Object> rawResults = redisTemplate.executePipelined(
            (org.springframework.data.redis.core.RedisCallback<Object>) connection -> {
                for (byte[] field : serializedFields) {
                    if (field != null) {
                        connection.hGet(serializedKey, field);
                    } else {
                        // 添加占位符命令保持顺序
                        connection.ping();
                    }
                }
                return null;
            }
        );

        // 处理结果并反序列化
        List<Object> results = new ArrayList<>(serializedFields.size());
        for (int i = 0; i < serializedFields.size(); i++) {
            byte[] field = serializedFields.get(i);

            if (field == null) {
                results.add(null); // 无效字段
                continue;
            }

            Object rawValue = rawResults.get(i);
            if (rawValue instanceof byte[]) {
                results.add(valueSerializer.deserialize((byte[]) rawValue));
            } else {
                results.add(rawValue); // ping命令返回"PONG"或null
            }
        }

        return results;
    }

    /**
     * 创建空结果列表（占位用）
     */
    private List<Object> createNullResults(int size) {
        return Collections.nCopies(size, null);
    }

    /**
     * 兼容性方法：保持原有接口
     *
     * @deprecated 建议使用 batchGetHashValues 方法
     */
    @Deprecated
    public <T> List<T> batchGetCacheMapValue(final String key, final List<String> fields) {
        List<Object> results = batchGetHashValues(key, fields);
        return (List<T>) results;
    }


    /**
     * 删除 Hash 中指定前缀的所有字段
     *
     * @param key Redis 键名
     * @param prefix 要删除的字段前缀
     * @return 实际删除的字段数量
     */
    public long deleteFieldsByPrefix(String key, String prefix) {
        if (key == null || prefix == null) {
            return 0;
        }

        long totalDeleted = 0;
        String pattern = prefix + "*";  // 匹配前缀的模式

        try (Cursor<Map.Entry<Object, Object>> cursor = redisTemplate.opsForHash()
                .scan(key, ScanOptions.scanOptions()
                        .match(pattern)
                        .count(SCAN_BATCH_SIZE)
                        .build())) {

            List<Object> fieldsToDelete = new ArrayList<>(PIPELINE_BATCH_SIZE);

            while (cursor.hasNext()) {
                Map.Entry<Object, Object> entry = cursor.next();
                fieldsToDelete.add(entry.getKey());

                // 当达到批处理大小时执行删除
                if (fieldsToDelete.size() >= PIPELINE_BATCH_SIZE) {
                    totalDeleted += executeBatchDelete(key, fieldsToDelete);
                    fieldsToDelete.clear();
                }
            }

            // 删除剩余字段
            if (!fieldsToDelete.isEmpty()) {
                totalDeleted += executeBatchDelete(key, fieldsToDelete);
            }
        }

        return totalDeleted;
    }

    /**
     * 使用 Pipeline 批量删除字段
     */
    public long executeBatchDelete(String key, List<Object> fields) {
        if (CollUtil.isEmpty(fields)) {
            return 0;
        }
        // 使用 Pipeline 执行批量删除
        List<Object> results = redisTemplate.executePipelined(
                (RedisCallback<Object>) connection -> {
                    byte[] keyBytes = redisTemplate.getKeySerializer().serialize(key);
                    if (keyBytes == null) return 0L;

                    for (Object field : fields) {
                        byte[] fieldBytes = redisTemplate.getHashKeySerializer().serialize(field);
                        if (fieldBytes != null) {
                            connection.hDel(keyBytes, fieldBytes);
                        }
                    }
                    return null;
                }
        );
        // 计算实际删除数量（每个删除命令返回1表示成功）
        return results.stream()
                .filter(result -> result instanceof Long)
                .mapToLong(result -> (Long) result)
                .sum();
    }

}
