package com.jlr.ecp.subscription.service.listener;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.service.listener.dto.VinBindNotifyDto;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import com.jlr.ecp.subscription.util.SqsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.Message;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 监听SQS消息发生变化的监听器
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "spring.cloud.aws.sqs.enabled", havingValue = "true", matchIfMissing = true)
public class VinBindListener {

    final int EXCEPTION_DELAY_TIME = 20 * 1000; //异常延迟时间,单位毫秒
    final int PULL_TIME_OUT = 20; //异常延迟时间,单位秒
    final int MAX_RECEIVE_COUNT = 1;
    final Long TENANT_ID = 1L;

    final String MESSAGE_IN_BODY = "Message";

    private final AtomicBoolean keepRunning = new AtomicBoolean(true);

    @Value("${sqs.bind.queue.host}${sqs.bind.queue.path}")
    String sqlQueueUrl;

    @Resource
    VinInitializeService vinInitializeService;

    @Resource
    SqsUtil sqsUtil;

    private SqsClient sqsClient;

    @PostConstruct
    public void init() {
        try {
            sqsClient = sqsUtil.getSqsClient();
            log.info("Bind Vin Listening: 已初始化，开始监听 SQS 消息队列:{}", sqlQueueUrl);
        } catch (Exception e) {
            log.info("Bind Vin Listening:初始化SQS客户端失败", e);
            return;
        }
        new Thread(this::loopReceiveMessage).start();
    }

    public void start() {
        log.info("Bind Vin Listening:重新启动车辆绑定监听功能");
        try {
            sqsClient = sqsUtil.getSqsClient();
        } catch (Exception e) {
            log.info("Bind Vin Listening: 初始化SQS客户端失败", e);
            throw new RuntimeException(e);
        }
        loopReceiveMessage();
    }

    private void loopReceiveMessage(){
        while (keepRunning.get()){
            List<Message> messages = null;
            log.info("Bind Vin Listening: 开始尝试拉取SQS消息队列数据,拉取地址：{}，拉取数量:{},超时时间(秒){}", sqlQueueUrl, MAX_RECEIVE_COUNT, PULL_TIME_OUT);
            try{
                messages = sqsUtil.receiveMessage(sqlQueueUrl, MAX_RECEIVE_COUNT, PULL_TIME_OUT, sqsClient);
            }
            catch (Exception exception){
                log.warn("Bind Vin Listening:监听SQS消息队列异常,将在等待{}毫秒后重试", EXCEPTION_DELAY_TIME, exception);
                try {
                    Thread.sleep(EXCEPTION_DELAY_TIME);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            try {
                if (CollUtil.isNotEmpty(messages)){
                    for (Message message : messages) {//这里的循环虽然只会跑一次，但还是循环处理
                        parseAndDealWithMessage(message);
                    }
                }
            } catch (Exception e) {
                //防止业务处理的异常，导致线程挂掉
                log.info("Bind Vin Listening:处理消息异常");
            }
        }
    }

    public void parseAndDealWithMessage(Message message) {
        String bodyStr = message.body();
        log.info("Bind Vin Listening:接收到消息：{}", bodyStr);


        VinBindNotifyDto vinBindNotifyDto;
        try {
            JSONObject messageBodyJson = JSONObject.parseObject(bodyStr);
            if (messageBodyJson == null || messageBodyJson.get(MESSAGE_IN_BODY) == null){
                log.info("Bind Vin Listening:消息中的Message为空");
                return;
            }
            vinBindNotifyDto = JSONObject.parseObject(messageBodyJson.get(MESSAGE_IN_BODY).toString(), VinBindNotifyDto.class);
        } catch (Exception e) {
            log.info("Bind Vin Listening:消息转对象失败", e);
            throw new RuntimeException(e);
        }


        try {
            TenantContextHolder.setTenantId(TENANT_ID);//目前租户只有一个，所以这里直接写死
            if(vinInitializeService.dealWithMessage(message, vinBindNotifyDto)){
                sqsUtil.deleteMessage(sqlQueueUrl, message, sqsClient);
            }
        } finally {
            TenantContextHolder.clear();
        }
    }

    public void stopListener() {
        keepRunning.set(false);
    }

    public void startListener(){
        if (keepRunning.get()){
            log.info("Bind Vin Listening:车辆绑定监听功能执行中");
            return;
        }
        keepRunning.set(true);
        start();
    }
}
