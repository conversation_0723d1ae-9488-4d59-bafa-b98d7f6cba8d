package com.jlr.ecp.subscription.dal.mysql.oss;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.VinInitialQueryDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * t_vin_initial_query表数据库访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface VinInitialQueryMapper extends BaseMapperX<VinInitialQueryDO> {
}
