package com.jlr.ecp.subscription.enums.consumer;

import com.jlr.ecp.subscription.enums.brand.BrandCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ConsumerBindEnum {
    BIND("bind", 1),
    UNBIND("unbind", 0);

    /**
     *  操作类型
     * */
    private final String operateType;

    /**
     *  绑定状态
     * */
    private final Integer bindStatus;

    public static Integer getStatusByOperateType(String operateType) {
        for (ConsumerBindEnum consumerBindEnum : ConsumerBindEnum.values()) {
            if (consumerBindEnum.getOperateType().contains(operateType)) {
                return consumerBindEnum.getBindStatus();
            }
        }
        throw new IllegalArgumentException("Invalid operateType: " + operateType);
    }
}
