package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.order.api.order.VcsOrderApi;
import com.jlr.ecp.order.api.order.dto.VCSOrderInfoDTO;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualPageParam;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.vo.PIVIInvoiceDateUpdateVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageLogDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIInvoiceDateManualUpdateService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service
@Slf4j
public class PIVIInvoiceDateManualUpdateServiceImpl implements PIVIInvoiceDateManualUpdateService {
    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private VcsOrderApi vcsOrderApi;

    @Resource
    private PIVIPackageLogDOMapper piviPackageLogDOMapper;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private PIVIAppDService piviAppDService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    Snowflake ecpIdUtil;

    private static final String MODIFY_BY_INVOICE_DATE= "modify_by_invoice_date";

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;

    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    /**
     * 根据车辆VIN在ECP查询相关信息。
     *
     * @param carVin 车辆的VIN码，用于查询车辆信息。
     * @return 返回查询结果，如果查询失败，则包含失败的原因和错误码。
     */
    @Override
    public CommonResult<String> queryCarVinFromECP(String carVin) {
        log.info("根据车辆VIN在ECP查询相关信息, carVin:{}", carVin);
        CommonResult<Boolean> verifyCarVin = verifyCarVinIsLegal(carVin);
        if (!verifyCarVin.isSuccess()) {
            return CommonResult.error(verifyCarVin.getCode(), verifyCarVin.getMsg());
        }
        boolean verifyCarVinOrderIsExist = verifyCarVinOrderIsExist(carVin);
        if (verifyCarVinOrderIsExist) {
            return CommonResult.error(ErrorCodeConstants.ECP_ORDER_FOUND);
        }
        return CommonResult.success(carVin);
    }

    /**
     * 根据车辆VIN码查询手动录入的发票信息页面参数。
     *
     * @param pageParam 页面请求参数，包含分页和查询条件信息。
     * @return 返回手动录入发票日期的查询结果，包含分页信息和转换后的业务对象列表。
     */
    @Override
    public CommonResult<PageResult<PIVIInvoiceDateUpdateVO>> queryManualInvoiceDateByCarVin(InvoiceDateManualPageParam pageParam) {
        log.info("根据车辆VIN码查询手动录入的发票信息页面参数, pageParam:{}", pageParam);
        Page<PIVIPackageLogDO> packageLogDOPage = selectPIVIPackageLogDOByPage(pageParam);
        List<PIVIInvoiceDateUpdateVO> piviInvoiceDateUpdateVOList = buildPIVIInvoiceDateUpdateVOList(packageLogDOPage.getRecords());
        PageResult<PIVIInvoiceDateUpdateVO> pageResult = new PageResult<>(piviInvoiceDateUpdateVOList, packageLogDOPage.getTotal());
        log.info("根据车辆VIN码查询手动录入的发票信息页面参数, 分页结果pageResult:{}", pageResult);
        return CommonResult.success(pageResult);
    }

    /**
     * PIVI手动更新发票日期。
     *
     * @param manualUpdateDTO 手动更新发票日期的请求参数。
     * @return 返回更新结果。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<String> manualUpdateInvoiceDate(InvoiceDateManualUpdateDTO manualUpdateDTO) {
        log.info("PIVI手动更新发票日期, manualUpdateDTO:{}", manualUpdateDTO);
        boolean verifyCarVinOrderIsExist = verifyCarVinOrderIsExist(manualUpdateDTO.getCarVin());
        if (verifyCarVinOrderIsExist) {
            return CommonResult.error(ErrorCodeConstants.ECP_ORDER_FOUND);
        }
        List<SubscriptionServiceDO> serviceDOList = selectServiceDOByCarVin(manualUpdateDTO.getCarVin());
        List<PIVIPackageDO> packageDOS = selectPIVIPackageDOByCarVin(manualUpdateDTO.getCarVin());
        if (CollUtil.isEmpty(packageDOS)) {
            log.info("PIVI手动更新发票日期, PIVIPackageDO查询结果为空");
            return CommonResult.error(ErrorCodeConstants.ECP_CAR_VIN_NOT_FOUND);
        }
        LocalDateTime expireDate = calculateExpireDate(manualUpdateDTO.getModifyInvoiceDate());
        if (Objects.isNull(expireDate)) {
            log.info("PIVI手动更新发票日期, 计算到期时间为空");
            return CommonResult.error(ErrorCodeConstants.INVOICE_DATE_FORMAT_ERROR);
        }
        // sprint47:校验是否存在appd或cu手动续费的订单
        CommonResult<String> processRecord = checkProcessRecord(manualUpdateDTO.getCarVin());
        if (!processRecord.isSuccess()) {
            return processRecord;
        }
        log.info("PIVI手动更新发票日期, 计算新的到期时间expireDate:{}", expireDate);
        insertPIVIPackageLogDO(packageDOS.get(0), manualUpdateDTO);
        if (!CollUtil.isEmpty(serviceDOList)) {
            updateServiceDOExpireDate(serviceDOList, expireDate);
            updatePIVIPackageDODate(packageDOS, manualUpdateDTO.getModifyInvoiceDate());
        } else {
            updatePIVIPackageDODate(packageDOS, manualUpdateDTO.getModifyInvoiceDate());
        }
        renewalAppDExpireDate(packageDOS.get(0), manualUpdateDTO);
        renewalUnicomExpireDate(packageDOS.get(0), manualUpdateDTO);
        return CommonResult.success("发票日期修改成功");
    }

    /**
     * 更新APPD的到期日期。
     *
     * @param packageDO 包含订阅ID和其他相关数据的VIP包数据对象。
     * @param manualUpdateDTO 包含手动更新信息的数据传输对象，用于构建基础消息。
     */
    private void renewalAppDExpireDate(PIVIPackageDO packageDO, InvoiceDateManualUpdateDTO manualUpdateDTO) {
        log.info("更新APPD的到期日期, packageDO:{}, manualUpdateDTO:{}", packageDO, manualUpdateDTO);
        if (Objects.isNull(packageDO) || Objects.isNull(packageDO.getJlrSubscriptionId())) {
            log.warn("JlrSubscriptionId为空, packageDO:{}, manualUpdateDTO:{}", packageDO, manualUpdateDTO);
            return ;
        }
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setVcsOrderCode(UUID.randomUUID().toString().replace("-", ""));
        baseMessage.setVin(manualUpdateDTO.getCarVin());
        baseMessage.setServiceEndDate(calculateExpireDate(manualUpdateDTO.getModifyInvoiceDate()));
        piviAppDService.callAppDService(baseMessage, "", packageDO.getJlrSubscriptionId());

    }

    /**
     * 更新联通到期日期。
     *
     * @param piviPackageDO 套餐信息数据对象，包含套餐的发票日期等信息。
     * @param manualUpdateDTO 手动更新的数据对象，包含需要更新的车辆VIN信息和修改后的发票日期。
     */
    private void renewalUnicomExpireDate(PIVIPackageDO piviPackageDO, InvoiceDateManualUpdateDTO manualUpdateDTO) {
        log.info("更新联通到期日期, piviPackageDO:{}, manualUpdateDTO:{}", piviPackageDO, manualUpdateDTO);
        LocalDateTime invoiceDate = SubscribeTimeFormatUtil.stringToTimeByFormat(manualUpdateDTO.getModifyInvoiceDate(),
                SubscribeTimeFormatUtil.FORMAT_2);
        FufilmentMessage fufilmentMessage = new FufilmentMessage();
        if (Objects.nonNull(invoiceDate)) {
            fufilmentMessage.setServiceBeginDate(invoiceDate.minusYears(1));
        }
        fufilmentMessage.setVcsOrderCode(ecpIdUtil.nextIdStr());
        fufilmentMessage.setVin(manualUpdateDTO.getCarVin());
        fufilmentMessage.setTenantId(TenantContextHolder.getTenantId());
        fufilmentMessage.setServiceEndDate(calculateExpireDate(manualUpdateDTO.getModifyInvoiceDate()));
        piviUnicomService.callUnicomService(fufilmentMessage,MODIFY_BY_INVOICE_DATE);
    }


    /**
     * 计算发票到期日期。
     *
     * @param manualInvoiceDate 手动输入的发票日期，字符串格式。
     * @return 返回计算得到的发票到期日期，如果输入日期格式不正确，则返回null。
     */
    private LocalDateTime calculateExpireDate(String manualInvoiceDate) {
        LocalDateTime invoiceDate = SubscribeTimeFormatUtil.stringToTimeByFormat(manualInvoiceDate,
                SubscribeTimeFormatUtil.FORMAT_2);
        if (Objects.isNull(invoiceDate)) {
            return null;
        }
        return invoiceDate.plusYears(3);
    }

    /**
     * 更新订阅服务的过期日期。
     *
     * @param serviceDOList 订阅服务数据对象列表，待更新过期日期的订阅服务在此列表中。
     * @param expireDate 更新后的过期日期。
     */
    private void updateServiceDOExpireDate(List<SubscriptionServiceDO> serviceDOList, LocalDateTime expireDate) {
        log.info("更新订阅服务的过期日期, serviceDOList:{}, expireDate:{}", serviceDOList, expireDate);
        if (CollUtil.isEmpty(serviceDOList) || Objects.isNull(expireDate)) {
            return ;
        }
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            serviceDO.setExpiryDate(expireDate);
            serviceDO.setExpireDateUtc0(expireDate.minusHours(8));
            serviceDO.setUpdatedBy(getModifyUser());
            serviceDO.setUpdatedTime(LocalDateTime.now());
        }
        subscriptionServiceMapper.updateBatch(serviceDOList);
    }

    /**
     * 更新PIVI套餐DO列表中的到期日期和发票日期。
     *
     * @param packageDOList 套餐DO列表，包含需要更新的套餐信息。
     * @param invoiceDate 发票日期，用于计算套餐的到期日期。
     */
    private void updatePIVIPackageDODate(List<PIVIPackageDO> packageDOList, String invoiceDate) {
        log.info("更新PIVI套餐DO列表中的到期日期和发票日期, packageDOList:{}, invoiceDate:{}", packageDOList, invoiceDate);
        if (CollUtil.isEmpty(packageDOList) || StringUtils.isBlank(invoiceDate)) {
            return ;
        }
        for (PIVIPackageDO piviPackageDO : packageDOList) {
            piviPackageDO.setExpiryDate(calculateExpireDate(invoiceDate));
            if (Objects.nonNull(calculateExpireDate(invoiceDate))) {
                piviPackageDO.setExpireDateUtc0(calculateExpireDate(invoiceDate).minusHours(8));
            }
            piviPackageDO.setDmsInvoiceDate(SubscribeTimeFormatUtil.stringToTimeByFormat(invoiceDate,
                    SubscribeTimeFormatUtil.FORMAT_2));
            piviPackageDO.setUpdatedBy(getModifyUser());
            piviPackageDO.setUpdatedTime(LocalDateTime.now());
        }
        piviPackageDOMapper.updateBatch(packageDOList);
    }

    /**
     * 插入VIP套餐包更新日志。
     *
     * @param piviPackageDO VIP套餐包数据对象，包含当前的套餐包信息，尤其是发票日期。
     * @param manualUpdateDTO 手动更新的数据传输对象，包含待更新的发票日期和关联的车辆信息。
     */
    private void insertPIVIPackageLogDO(PIVIPackageDO piviPackageDO, InvoiceDateManualUpdateDTO manualUpdateDTO) {
        log.info("插入VIP套餐包更新日志, piviPackageDO:{}, manualUpdateDTO:{}", piviPackageDO, manualUpdateDTO);
        PIVIPackageLogDO piviPackageLogDO = PIVIPackageLogDO.builder()
                .modifyTime(LocalDateTime.now())
                .modifyUser(getModifyUser())
                .carVin(manualUpdateDTO.getCarVin())
                .modifyInvoiceDateBefore(piviPackageDO.getDmsInvoiceDate())
                .modifyInvoiceDateAfter(SubscribeTimeFormatUtil.stringToTimeByFormat(manualUpdateDTO.getModifyInvoiceDate(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .build();
        piviPackageLogDOMapper.insert(piviPackageLogDO);

        //操作日志记录 -- 修改发票日期
        manualModifyLogDOService.recordLog(piviPackageLogDO);
    }


    /**
     * 获取修改操作的用户名称。
     *
     * @return 修改操作用户的名称。如果当前没有登录用户，则返回管理员名称"admin"。
     */
    private String getModifyUser() {
        if (StringUtils.isBlank(WebFrameworkUtils.getLoginUserName())) {
            return "admin";
        }
        return WebFrameworkUtils.getLoginUserName();
    }

    /**
     * 根据PIVIPackageLogDO列表构建PIVIInvoiceDateUpdateVO列表。
     *
     * @param piviPackageLogDOS PIVIPackageLogDO对象的列表，代表原始数据。
     * @return PIVIInvoiceDateUpdateVO对象的列表，代表转换后的数据。
     */
    private List<PIVIInvoiceDateUpdateVO> buildPIVIInvoiceDateUpdateVOList(List<PIVIPackageLogDO> piviPackageLogDOS) {
        if (CollUtil.isEmpty(piviPackageLogDOS)) {
            return new ArrayList<>();
        }
        List<PIVIInvoiceDateUpdateVO> resp = new ArrayList<>();
        for (PIVIPackageLogDO piviPackageLogDO : piviPackageLogDOS) {
            resp.add(buildPIVIInvoiceDateUpdateVO(piviPackageLogDO));
        }
        return resp;
    }

    /**
     * 根据PIVIPackageLogDO对象构建PIVIInvoiceDateUpdateVO对象。
     *
     * @param piviPackageLogDO PIVIPackageLogDO对象，包含日志信息。
     * @return 返回构建好的PIVIInvoiceDateUpdateVO对象，包含更新后的发票日期和其他相关信息。
     */
    private PIVIInvoiceDateUpdateVO buildPIVIInvoiceDateUpdateVO(PIVIPackageLogDO piviPackageLogDO) {
        return PIVIInvoiceDateUpdateVO.builder()
                .modifyTime(SubscribeTimeFormatUtil.timeToStringByFormat(piviPackageLogDO.getModifyTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .modifyUser(piviPackageLogDO.getModifyUser())
                .carVin(piviPackageLogDO.getCarVin())
                .invoiceNewDate(SubscribeTimeFormatUtil.timeToStringByFormat(piviPackageLogDO.getModifyInvoiceDateAfter(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .build();
    }

    /**
     * 根据车辆VIN码验证PIVI订单是否存在。
     *
     * @param carVin 车辆的VIN码，用于查询订单。
     * @return 如果订单存在，则返回true；否则返回false。
     */
    private boolean verifyCarVinOrderIsExist(String carVin) {
        log.info("根据车辆VIN码验证PIVI订单是否存在, 入参carVin：{}", carVin);
        try {
            CommonResult<List<VCSOrderInfoDTO>> vcsOrderInfoDTOResp = vcsOrderApi.queryPIVIVcsOrderByCarVin(carVin);
            if (vcsOrderInfoDTOResp.isSuccess() && CollUtil.isEmpty(vcsOrderInfoDTOResp.getData())) {
                return false;
            }
            log.info("根据车辆VIN码验证PIVI订单是否存在, 查询结果vcsOrderInfoDTOResp：{}", vcsOrderInfoDTOResp.getData());
        } catch (Exception e) {
            log.error("根据车辆VIN码验证PIVI订单是否存在异常: ", e);
        }
        return true;
    }

    /**
     * 验证车辆VIN码是否合法。
     *
     * @param carVin 车辆的VIN码。
     * @return 返回验证结果，如果VIN码合法且在系统中存在对应的车辆信息，则返回成功结果；否则返回错误代码。
     */
    private CommonResult<Boolean> verifyCarVinIsLegal(String carVin) {
        if (!CarVinUtil.checkVinFormat(carVin)) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(selectPIVIPackageDOByCarVin(carVin))) {
            return CommonResult.error(ErrorCodeConstants.ECP_CAR_VIN_NOT_FOUND);
        }
        return CommonResult.success(true);
    }

    /**
     * 根据车辆VIN码查询订阅服务信息。
     *
     * @param carVin 车辆的唯一标识VIN码。
     * @return 返回匹配的订阅服务信息列表。如果VIN码为空或查询出现异常，则返回空列表。
     */
    private List<SubscriptionServiceDO> selectServiceDOByCarVin(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionServiceDO::getCarVin, carVin)
                .in(SubscriptionServiceDO::getServicePackage, List.of(ServicePackageEnum.ONLINE_PACK.getPackageName(),
                                ServicePackageEnum.DATA_PLAN.getPackageName()))
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        try {
            return subscriptionServiceMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据车辆VIN码查询订阅服务信息异常: ", e);
        }
        return new ArrayList<>();
    }

    /**
     * 根据车辆识别码查询PIVI订单发票修改日志信息。
     *
     * @param pageParam 分页参数，用于控制查询结果数量和偏移量。
     * @return 返回匹配的PIVI套餐日志列表。如果车辆识别码为空或查询过程中发生异常，则返回空列表。
     */
    private Page<PIVIPackageLogDO> selectPIVIPackageLogDOByPage(InvoiceDateManualPageParam pageParam) {
        if (Objects.isNull(pageParam)) {
            return new Page<>();
        }
        try {
            Page<PIVIPackageLogDO> page = new Page<>(pageParam.getPageNo(), pageParam.getPageSize());
            LambdaQueryWrapper<PIVIPackageLogDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PIVIPackageLogDO::getIsDeleted, false)
                    .orderByDesc(PIVIPackageLogDO::getModifyTime);
            Page<PIVIPackageLogDO> packageLogDOPage = piviPackageLogDOMapper.selectPage(page, queryWrapper);
            if (Objects.isNull(packageLogDOPage) || CollUtil.isEmpty(packageLogDOPage.getRecords())) {
                return new Page<>();
            }
            return packageLogDOPage;
        } catch (Exception e) {
           log.error("根据车辆VIN码查询相应的PIVI订单发票修改日志信息异常:", e);
        }
        return new Page<>();
    }

    /**
     * 根据车辆VIN码查询相应的PIVIPackage套餐信息。
     *
     * @param carVin 车辆的VIN码，用于精确查询VIP套餐信息。
     * @return 返回匹配的VIP套餐列表。如果VIN码为空或查询过程中出现异常，则返回空列表。
     */
    private List<PIVIPackageDO> selectPIVIPackageDOByCarVin(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PIVIPackageDO::getVin, carVin)
                .eq(PIVIPackageDO::getIsDeleted, false);
        try {
            return piviPackageDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据车辆VIN码查询相应的PIVIPackage套餐信息异常:", e);
        }
        return new ArrayList<>();
    }

    /**
     * 根据车架号检查是否存在续费中的记录
     * 此方法用于验证给定车架号是否有正在进行的续费记录，以防止重复提交或处理
     *
     * @param carVin 车架号，用于查询续费记录
     * @return 如果存在续费中的记录，则返回错误结果；否则返回成功结果
     */
    private CommonResult<String> checkProcessRecord(String carVin) {
        List<AppDCuRenewRecords> processRecordList = appDCuRenewRecordsMapper.getByVinAndStatus(carVin, AppDRenewStatusEnum.RENEW_PROGRESS.getStatus());
        if(CollUtil.isNotEmpty(processRecordList)){
            log.info("vin:{}存在续费中的appd或cu的手动续费记录, {}", carVin, processRecordList);
            return CommonResult.error(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR);
        }
        return CommonResult.success("");
    }
}
