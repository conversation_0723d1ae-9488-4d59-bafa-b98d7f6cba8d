package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 初始化状态枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum InitializeStatusEnum {

    PENDING(0, "否，未刷新", null),

    SUCCESS(1, "是", null),

    APPD_FAIL(2, "否，APPD到期日刷新失败", "APPD到期日刷新失败"),

    CU_FAIL(3, "否，联通到期日刷新失败", "联通到期日刷新失败"),

    ALL_FAIL(4, "否，APPD和联通到期日刷新失败", "APPD到期日刷新失败;联通到期日刷新失败");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 错误信息
     * */
    public final String errorMessage;

    public static Integer getCodeByResult(Boolean appD, Boolean unicom){
        if (appD && unicom) {
            return InitializeStatusEnum.SUCCESS.getCode();
        }
        if (!appD && !unicom) {
            return InitializeStatusEnum.ALL_FAIL.getCode();
        }
        if (!appD) {
            return InitializeStatusEnum.APPD_FAIL.getCode();
        }
        return InitializeStatusEnum.CU_FAIL.getCode();
    }

    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (InitializeStatusEnum status : InitializeStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    public static String getErrorMessage(Integer code){
        if (code == null) {
            return null;
        }
        for (InitializeStatusEnum status : InitializeStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getErrorMessage();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
