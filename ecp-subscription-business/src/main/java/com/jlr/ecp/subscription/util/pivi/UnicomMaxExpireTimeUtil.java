package com.jlr.ecp.subscription.util.pivi;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.enums.unicom.UnicomBookStatusEnum;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class UnicomMaxExpireTimeUtil {
    /**
     * 获取联通服务有效的最大到期日期
     *
     * @param unicomRespVO 联通响应对象，包含用户的服务信息
     * @return 返回服务到期的日期时间对象，如果没有有效服务或发生异常则返回null
     */
    public static LocalDateTime getUnicomMaxExpireDate(UnicomRespVO unicomRespVO) {
        log.info("获取联通服务的到期日期, unicomRespVO:{}", unicomRespVO);
        if (Objects.isNull(unicomRespVO) || Objects.isNull(unicomRespVO.getUnicomRespData())
                || CollUtil.isEmpty(unicomRespVO.getUnicomRespData().getProductBookInfoList())) {
            log.info("获取联通服务的到期日期, productBookInfoList为空");
            return null;
        }
        try {
            List<ProductBookInfo> productBookInfoList = unicomRespVO.getUnicomRespData().getProductBookInfoList();
            // 获取bookStatus为active的所有记录的expireTime的最大值
            Optional<String> maxOptionalExpireTime = productBookInfoList.stream()
                    .filter(bookInfo -> UnicomBookStatusEnum.ACTIVE.getType().equals(bookInfo.getBookStatus()))
                    .map(ProductBookInfo::getExpireTime)
                    .max(Comparator.naturalOrder());
            String maxExpireTime = maxOptionalExpireTime.orElseGet(() -> null);
            if (StringUtils.isBlank(maxExpireTime)) {
                log.info("获取联通服务的到期日期, maxExpireTime为空, unicomRespVO:{}", unicomRespVO);
            }
            return SubscribeTimeFormatUtil.stringToTimeByFormat(maxExpireTime, SubscribeTimeFormatUtil.FORMAT_3);
        } catch (Exception e) {
            log.info("获取联通服务有效的最大到期日期异常:{}", e.getMessage());
        }
        return null;
    }
}
