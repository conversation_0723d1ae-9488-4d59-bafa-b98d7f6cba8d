package com.jlr.ecp.subscription.controller.admin.appd;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuRenewalQueryPageVO;
import com.jlr.ecp.subscription.service.appd.AppDCuRenewalQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "批量查询-APPD-联通续费结果")
@RequestMapping("/appd/uicom/renewal/query")
@Validated
public class AppDCuRenewalQueryController {
    @Resource
    private AppDCuRenewalQueryService appDCuRenewalQueryService;
    @GetMapping("/queryBatchNo")
    @Operation(summary = "查询续费编号")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-renewal-record:forms')")
    public CommonResult<String> queryAppDCuRenewalBatchNo(@RequestParam("batchNo") String batchNo) {
        String resp = appDCuRenewalQueryService.queryAppDCuRenewalBatchNo(batchNo);
        return CommonResult.success(resp);
    }

    @GetMapping("/queryRenewalService")
    @Operation(summary = "查询续费服务")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-renewal-record:forms')")
    public CommonResult<List<AppDCuEnumQueryVO>> queryRenewalService() {
        List<AppDCuEnumQueryVO> resp = appDCuRenewalQueryService.queryAppDCuRenewalService();
        return CommonResult.success(resp);
    }
    @GetMapping("/queryRenewalStatus")
    @Operation(summary = "查询续费状态")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-renewal-record:forms')")
    public CommonResult<List<AppDCuEnumQueryVO>> queryRenewalStatus() {
        List<AppDCuEnumQueryVO> resp = appDCuRenewalQueryService.queryAppDCuRenewalStatus();
        return CommonResult.success(resp);
    }

    @PostMapping("/queryRenewalRecord/pageList")
    @Operation(summary = "分页查询续费记录")
    @PreAuthorize("@ss.hasPermission('subscribe:appd-renewal-record:forms')")
    public CommonResult<PageResult<AppDCuRenewalQueryPageVO>> query(@RequestBody AppDCuRenewalQueryPageDTO queryPageDTO) {
        PageResult<AppDCuRenewalQueryPageVO> resp = appDCuRenewalQueryService.queryAppDCuRenewalPageList(queryPageDTO);
        return CommonResult.success(resp);
    }
}
