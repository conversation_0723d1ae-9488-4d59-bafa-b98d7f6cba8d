package com.jlr.ecp.subscription.service.appd;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuSingleRenewalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleExpireVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSinglePageListV0;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleRenewalV0;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;

import java.util.List;

public interface AppDCuSingleRenewalService {

    /**
     * APPD单个续费操作
     *
     * @param appDCuSingleRenewalDTO 续费请求对象，包含续费所需的各项参数
     * @return 返回一个表示操作结果的CommonResult对象，此处返回的是一个字符串类型的CommonResult对象
     */
    CommonResult<AppDCuSingleRenewalV0> appDCuSingleOperateRenewal(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO);

    /**
     * 更新AppD续订记录
     * 根据VcsOrderFufilmentCall中的激活状态，更新AppDCuRenewRecords表中的续订状态及相关信息
     *
     * @param appDCuRenewRecords AppD续订记录对象，包含车辆VIN号和续订状态等信息
     * @param vcsOrderFufilmentCallResp VCS订单履行调用对象，包含激活状态和失败原因等信息
     */
    void updateAppDRenewRecords(AppDCuRenewRecords appDCuRenewRecords,
                                CommonResult<VcsOrderFufilmentCall> vcsOrderFufilmentCallResp);

    /**
     * 根据车辆识别号（VIN）获取AppDCu单次过期列表
     *
     * @param carVin 车辆识别号，用于查询过期信息
     * @return 包含AppDCu单次过期信息的列表
     */
    List<AppDCuSingleExpireVO>  getAppDCuSingleExpireList(String carVin);
    /**
     * 更新联通续费记录
     *
     * @param appDCuRenewRecords 应用续费记录对象，包含续费相关数据
     * @param unicomRespVOResult 联通接口返回的数据对象，包含续费结果信息
     */
    void updateUnicomRenewRecords(AppDCuRenewRecords appDCuRenewRecords, CommonResult<UnicomRespVO> unicomRespVOResult);

    /**
     * 查询单个APPD的分页列表信息
     *
     * @param pageDTO 分页查询参数对象，包含分页及筛选条件
     * @return 返回分页结果对象，包含单页列表和总记录数
     */
    PageResult<AppDCuSinglePageListV0> getAppDCuSinglePageList(AppDUcSingleRenewalPageDTO pageDTO);

    /**
     * 检查是否存在同一车辆的续费进行中记录
     * 此方法旨在确保对于特定车辆，没有重复的续费操作正在进行中
     * 它主要针对两种服务类型（APPD和CU）进行检查，以避免续费操作的冲突
     *
     * @param appDCuSingleRenewalDTO 包含车辆VIN和续费相关信息的DTO对象
     * @return Boolean 返回是否存在记录的布尔值
     */
    boolean checkProcessRecord(AppDCuSingleRenewalDTO appDCuSingleRenewalDTO);
}
