package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.QueryAmaPExpireDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.QueryAmaPExpireVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPQueryBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPQueryRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPQueryBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPQueryRecordsDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.*;
import com.jlr.ecp.subscription.excel.listener.amap.SearchAmaPExpireCheckListener;
import com.jlr.ecp.subscription.excel.pojo.amap.SearchAmaPExpireBatchExcel;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.amap.SearchAmaPExpireService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class SearchAmaPExpireServiceImpl implements SearchAmaPExpireService {

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024;

    @Resource
    private Snowflake snowflake;

    @Resource
    private AmaPQueryBatchRecordsDOMapper amaPQueryBatchRecordsDOMapper;

    @Resource
    private AmaPQueryRecordsDOMapper amaPQueryRecordsDOMapper;

    @Resource
    private AmaPProperties amaPProperties;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource
    private ThreadPoolTaskScheduler subscribeScheduledThreadPool;

    private static final Integer GROUP_SIZE = 5;

    private static final String BLANK = " ";

    private static final String TODAY_MAX_TIME = "23:59:59";

    @Resource
    private PermissionApi permissionApi;

    /**
     * 获取查询模板URL
     *
     * @return 查询模板的URL字符串
     */
    @Override
    public String getQueryTemplateUrl() {
        return amaPProperties.getQueryExcelUrl();
    }

    /**
     * 查询批次页面列表
     *
     * @param queryPageDTO 页面查询参数对象，包含查询条件和分页信息
     * @return 返回批次页面数据封装对象，包含批次数据列表和总记录数
     */
    @Override
    public PageResult<AmaPBatchQueryPageVO> queryBatchPageList(AmaPBatchQueryPageDTO queryPageDTO) {
        Page<AmaPQueryBatchRecordsDO> pageRes = queryBatchRecordsDOPage(queryPageDTO);
        if (Objects.isNull(pageRes) || CollUtil.isEmpty(pageRes.getRecords())) {
            return new PageResult<>();
        }
        List<AmaPQueryBatchRecordsDO> queryBatchRecordsList = pageRes.getRecords();
        List<AmaPBatchQueryPageVO> batchQueryPageVOList = buildBatchQueryPageVOList(queryBatchRecordsList);
        return new PageResult<>(batchQueryPageVOList, pageRes.getTotal());
    }

    /**
     * 批量查询高德地图到期情况
     *
     * @param multipartFile 用户上传的包含会员信息的文件
     * @return CommonResult<AmaPBatchRenewalUploadVO> 查询操作的结果封装
     */
    @Override
    public CommonResult<AmaPBatchRenewalUploadVO> batchSearchAmaPExpire(MultipartFile multipartFile) {
        if (Objects.isNull(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        log.info("批量查询高德地图会员到期情况,multipartFile:{}", multipartFile);
        if (multipartFile.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.AMAP_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        List<AmaPQueryBatchRecordsDO> queryBatchRecordsDOList = queryAmaPQueryBatchByStatus(DealStatusEnum.PROGRESS.getStatus());
        if (CollUtil.isNotEmpty(queryBatchRecordsDOList)) {
            return CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
        }
        SearchAmaPExpireCheckListener searchExpireListener = new SearchAmaPExpireCheckListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), SearchAmaPExpireBatchExcel.class,
                    searchExpireListener).sheet().doRead();
        } catch (Exception e) {
            log.info("批量查询高德地图会员到期情况读取excel文件异常:{}", e.getMessage());
        }
        if (Boolean.TRUE.equals(searchExpireListener.getIsExcelFormatError())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(searchExpireListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        }
        log.info("批量查询高德地图会员到期情况, searchExpireListener:{}", searchExpireListener);
        Long batchNo = snowflake.nextId();
        insertBatchAmaPQueryRecords(batchNo);
        insertAmaPQueryRecords(searchExpireListener, batchNo);
        searchExpireListener.setAllDataList(null);
        //查询高德并更新record和batch的状态
        CompletableFuture.runAsync(() -> queryAmaPAndUpdateExpireResult(batchNo));
        AmaPBatchRenewalUploadVO uploadVO = AmaPBatchRenewalUploadVO.builder()
                .type("success")
                .msg("上传成功，请关注批量查询结果")
                .build();
        return CommonResult.success(uploadVO);
    }

    /**
     * 根据批次号获取AMAP查询批次信息
     *
     * @param batchNo 批次号字符串，用于标识查询的批次
     * @return 返回批次号字符串，如果未找到相关记录，则返回空字符串
     */
    @Override
    public String getAmaPQueryBatchNo(String batchNo) {
        AmaPQueryBatchRecordsDO queryBatchRecordsDO;
        try {
            log.info("根据批次号获取AMAP查询批次信息,batchNo:{}", batchNo);
            Long batchNoLong = Long.valueOf(batchNo);
            queryBatchRecordsDO = getAmaPQueryRecordsByBatchNo(batchNoLong);
            if (Objects.isNull(queryBatchRecordsDO)) {
                log.info("根据批次号获取AMAP查询批次信息，查询结果为空，batchNo:{}", batchNo);
                return "";
            }
        } catch (NumberFormatException e) {
            log.info("根据批次号获取AMAP查询批次信息异常：{}", e.getMessage());
            return "";
        }
        return String.valueOf(queryBatchRecordsDO.getBatchNo());
    }

    /**
     * 获取查询状态列表
     *
     * @return 包含所有高德查询状态的列表，每个状态都封装在AmaPQueryStatusVO对象中
     */
    @Override
    public List<AmaPQueryStatusVO> getAmaPQueryStatus() {
        AmaPQueryStatusEnum[] queryStatusEnums = AmaPQueryStatusEnum.values();
        List<AmaPQueryStatusVO> resp = new ArrayList<>();
        for (AmaPQueryStatusEnum queryStatusEnum : queryStatusEnums) {
            AmaPQueryStatusVO amaPQueryStatusVO = AmaPQueryStatusVO.builder()
                    .queryStatusCode(queryStatusEnum.getQueryStatus())
                    .queryStatusDesc(queryStatusEnum.getQueryDesc())
                    .build();
            resp.add(amaPQueryStatusVO);
        }
        return resp;
    }

    /**
     * 批量查询高德地图结果
     *
     * @param queryAmaPExpireDTO 查询参数对象，包含高德地图到期信息的查询条件
     * @return 返回分页结果对象，包含高德地图到期信息的列表和总记录数
     */
    @Override
    public PageResult<QueryAmaPExpireVO> amaPBatchQueryResult(QueryAmaPExpireDTO queryAmaPExpireDTO) {
        log.info("批量查询高德地图结果, queryAmaPExpireDTO:{}", queryAmaPExpireDTO);
        if (Objects.isNull(queryAmaPExpireDTO)) {
            return new PageResult<>();
        }
        Page<AmaPQueryRecordsDO> amaPQueryRecordsPage = getRecordsDOPageByQueryDTO(queryAmaPExpireDTO);
        if (Objects.isNull(amaPQueryRecordsPage) || CollUtil.isEmpty(amaPQueryRecordsPage.getRecords())) {
            return new PageResult<>();
        }
        List<AmaPQueryRecordsDO> queryList = amaPQueryRecordsPage.getRecords();
        List<QueryAmaPExpireVO> resp = buildQueryAmaPExpireVoList(queryList);
        return new PageResult<>(resp, amaPQueryRecordsPage.getTotal());
    }

    /**
     * 批量查询AmaP到期日期
     *
     * @param amaPQueryRecordsDOList 包含AmaP查询记录的列表，用于批量查询凭证到期日期
     * @param batchNo 查询批次的唯一编号，用于标识和管理查询批次
     */
    @Override
    public void batchQueryAmaPExpireDate(List<AmaPQueryRecordsDO> amaPQueryRecordsDOList, Long batchNo) {
        log.info("批量查询AmaP到期日期, amaPQueryRecordsDOList的数量：{}, batchNo:{}", amaPQueryRecordsDOList.size(), batchNo);
        if (CollUtil.isEmpty(amaPQueryRecordsDOList) || Objects.isNull(batchNo)) {
            return ;
        }
        queryAmaPExpireDate(amaPQueryRecordsDOList, batchNo);
        //更新batch的状态
        AmaPQueryBatchRecordsDO amaPQueryBatchRecordsDO = getAmaPQueryRecordsByBatchNo(batchNo);
        log.info("批量查询AmaP到期日期, amaPQueryBatchRecordsDO:{}", amaPQueryBatchRecordsDO);
        if (!DealStatusEnum.COMPLETED.getStatus().equals(amaPQueryBatchRecordsDO.getDealStatus())) {
            amaPQueryBatchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
            amaPQueryBatchRecordsDO.setUpdatedTime(LocalDateTime.now());
            amaPQueryBatchRecordsDOMapper.updateById(amaPQueryBatchRecordsDO);
        }
    }

    /**
     * 构建高德地图查询记录的过期状态信息的列表
     *
     * @param queryList 高德地图查询记录的DO对象列表
     * @return 转换后的VO对象列表，如果输入列表为空，则返回空列表
     */
    private List<QueryAmaPExpireVO> buildQueryAmaPExpireVoList(List<AmaPQueryRecordsDO> queryList) {
        List<QueryAmaPExpireVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(queryList)) {
            return resp;
        }
        for (AmaPQueryRecordsDO amaPQueryRecordsDO : queryList) {
            resp.add(buildQueryAmaPExpireVo(amaPQueryRecordsDO));
        }
        return resp;
    }

    /**
     * 构建查询高德服务到期的VO对象
     *
     * @param amaPQueryRecordsDO 高德服务查询记录实体，包含从数据库查询到的记录信息
     * @return 返回转换后的QueryAmaPExpireVO对象，如果输入参数为空，则返回一个空的QueryAmaPExpireVO对象
     */
    private QueryAmaPExpireVO buildQueryAmaPExpireVo(AmaPQueryRecordsDO amaPQueryRecordsDO) {
        if (Objects.isNull(amaPQueryRecordsDO)) {
            return QueryAmaPExpireVO.builder().build();
        }
        return QueryAmaPExpireVO.builder()
                .batchNo(String.valueOf(amaPQueryRecordsDO.getRenewNo()))
                .carVin(amaPQueryRecordsDO.getCarVin())
                .queryStatus(amaPQueryRecordsDO.getQueryStatus())
                .queryStatusDesc(AmaPQueryStatusEnum.getDescByStatus(amaPQueryRecordsDO.getQueryStatus()))
                .amaPExpireDate(SubscribeTimeFormatUtil.timeToStringByFormat(amaPQueryRecordsDO.getAmaPExpiryDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .ecpExpireDate(SubscribeTimeFormatUtil.timeToStringByFormat(amaPQueryRecordsDO.getEcpExpiryDate(),
                        SubscribeTimeFormatUtil.FORMAT_1))
                .serviceStatus(amaPQueryRecordsDO.getServiceStatus())
                .serviceStatusDesc(AmaPServiceStatusEnum.getDescByStatus(amaPQueryRecordsDO.getServiceStatus()))
                .operator(amaPQueryRecordsDO.getOperator())
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(amaPQueryRecordsDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .errorInfo(getQueryErrorInfo(amaPQueryRecordsDO))
                .build();
    }

    /**
     * 获取查询记录错误信息
     *
     * @param amaPQueryRecordsDO 查询记录对象，包含操作结果码等信息
     * @return 错误信息字符串，当参数为null或操作成功时返回空字符串，否则返回对应的错误描述
     */
    private String getQueryErrorInfo(AmaPQueryRecordsDO amaPQueryRecordsDO) {
        if (Objects.isNull(amaPQueryRecordsDO)) {
            return "";
        }
        if (AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPQueryRecordsDO.getResultCode())) {
            return "";
        }
        return AmaPErrorCode.getManualRenewalDescByCode(amaPQueryRecordsDO.getResultCode());
    }

    /**
     * 根据查询参数获取分页记录
     *
     * @param queryPageDTO 查询参数对象，包含分页信息和查询条件
     * @return 返回分页查询结果，包含查询记录列表和总记录数
     */
    private Page<AmaPQueryRecordsDO> getRecordsDOPageByQueryDTO(QueryAmaPExpireDTO queryPageDTO) {
        if (Objects.isNull(queryPageDTO)) {
            return null;
        }
        Page<AmaPQueryRecordsDO> pageParam = new Page<>(queryPageDTO.getPageNo(), queryPageDTO.getPageSize());
        LambdaQueryWrapper<AmaPQueryRecordsDO> queryWrapper = buildQueryRecordsWrapper(queryPageDTO);
        return amaPQueryRecordsDOMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 构建查询记录的条件封装器
     *
     * @param queryAmaPExpireDTO 查询参数对象，包含车辆识别号（VIN）、批次号、操作员和查询状态等信息
     * @return 返回构建的 LambdaQueryWrapper 对象，如果查询参数对象为空，则返回 null
     */
    private LambdaQueryWrapper<AmaPQueryRecordsDO> buildQueryRecordsWrapper(QueryAmaPExpireDTO queryAmaPExpireDTO) {
        if (Objects.isNull(queryAmaPExpireDTO)) {
            return null;
        }
        LambdaQueryWrapper<AmaPQueryRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPQueryRecordsDO::getIsDeleted, false);
        queryWrapper.eq(StringUtils.isNotBlank(queryAmaPExpireDTO.getCarVin()), AmaPQueryRecordsDO::getCarVin,
                        queryAmaPExpireDTO.getCarVin())
                .eq(StringUtils.isNotBlank(queryAmaPExpireDTO.getOperator()), AmaPQueryRecordsDO::getOperator,
                        queryAmaPExpireDTO.getOperator())
                .eq(Objects.nonNull(queryAmaPExpireDTO.getQueryStatus()), AmaPQueryRecordsDO::getQueryStatus,
                        queryAmaPExpireDTO.getQueryStatus());
        if (StringUtils.isNotBlank(queryAmaPExpireDTO.getStartTime())
                && StringUtils.isNotBlank(queryAmaPExpireDTO.getEndTime())) {
            queryWrapper.ge(AmaPQueryRecordsDO::getCreatedTime, queryAmaPExpireDTO.getStartTime())
                    .le(AmaPQueryRecordsDO::getCreatedTime, queryAmaPExpireDTO.getEndTime());
        }
        if (CollUtil.isNotEmpty(queryAmaPExpireDTO.getBatchNoList())) {
            List<Long> batchNoList = new ArrayList<>();
            for (String batchNo : queryAmaPExpireDTO.getBatchNoList()) {
                try {
                    batchNoList.add(Long.valueOf(batchNo));
                } catch (Exception e) {
                    log.info("构建查询记录的条件封装器, batchNo转化为数字异常:{}", e.getMessage());
                }
            }
            queryWrapper.in(AmaPQueryRecordsDO::getRenewNo, batchNoList);
        }
        if (SortTypeEnum.ASC.getSortType().equals(queryAmaPExpireDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(AmaPQueryRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(AmaPQueryRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(AmaPQueryRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(AmaPQueryRecordsDO::getId);
        }
        return queryWrapper;
    }

    /**
     * 查询高德地图并更新过期结果
     *
     * @param batchNo 任务批次编号，用于标识和关联一批查询记录
     */
    public void queryAmaPAndUpdateExpireResult(Long batchNo) {
        if (Objects.isNull(batchNo)) {
            return ;
        }
        List<AmaPQueryRecordsDO> queryRecordList = getAmaPQueryRecordsByBatchNoAndStatus(batchNo, AmaPQueryStatusEnum.PROGRESS.getQueryStatus());
        if (CollUtil.isEmpty(queryRecordList)) {
            log.info("查询高德地图并更新过期结果, 查询结果为空，batchNo：{}", batchNo);
            //更新batch的状态
            updateBatchQueryStatus(batchNo);
            return ;
        }
        queryAmaPExpireDate(queryRecordList, batchNo);
        //更新batch的状态
        updateBatchQueryStatus(batchNo);
    }

    /**
     * 查询高德地图的过期日期
     *
     * @param batchAmaPList 包含多个查询记录的列表
     * @param batchNo 批次编号，用于标识查询批次
     */
    public void queryAmaPExpireDate(List<AmaPQueryRecordsDO> batchAmaPList, Long batchNo) {
        if (CollUtil.isEmpty(batchAmaPList)) {
            return ;
        }
        List<List<AmaPQueryRecordsDO>> searchGroup = searchAmaPExcelByGroup(batchAmaPList);
        for (List<AmaPQueryRecordsDO> queryList : searchGroup) {
            long startTime = System.currentTimeMillis();
            Map<String, CompletableFuture<AmaPSearchCenterVO>> futureMap = asyncQueryAmaPExpireDateMap(queryList, batchNo);
            for (AmaPQueryRecordsDO searchAmaP : queryList) {
                String key = getAmaPQueryKey(batchNo, searchAmaP.getCarVin());
                if (!futureMap.containsKey(key)) {
                    continue;
                }
                AmaPSearchCenterVO response = futureMap.get(key).join();
                buildSearchAmaP(searchAmaP, response);
                searchAmaP.setUpdatedTime(LocalDateTime.now());
            }
            amaPQueryRecordsDOMapper.updateBatch(queryList);
            long endTime = System.currentTimeMillis();
            long finishTime = endTime - startTime;
            if (finishTime < 1000) {
                try {
                    Thread.sleep(1010 - finishTime);
                } catch (InterruptedException e) {
                    log.error("更新高德地图的过期日期暂停失败:", e);
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 根据地图查询结果更新查询对象
     * 此方法主要用于将搜索高德的查询结果从响应对象转移到查询记录对象中
     * 它处理成功或失败的查询，并相应地更新查询状态和错误描述
     *
     * @param searchAmaP 查询记录对象，用于存储查询相关信息和结果
     * @param response 搜索中心响应对象，包含查询结果和状态信息
     */
    private static void buildSearchAmaP(AmaPQueryRecordsDO searchAmaP, AmaPSearchCenterVO response) {
        searchAmaP.setResultCode(response.getResultCode());
        if (AmaPErrorCode.SUCCESSFUL.getCode().equals(response.getResultCode())) {
            searchAmaP.setServiceStatus(response.getAmaPServiceStatus());
            String amaPExpireDate = response.getAmaPExpireDate() + BLANK + TODAY_MAX_TIME;
            searchAmaP.setAmaPExpiryDate(SubscribeTimeFormatUtil.stringToTimeByFormat(amaPExpireDate,
                    SubscribeTimeFormatUtil.FORMAT_2));
            String ecpExpireDate = response.getEcpExpireDate() + BLANK + TODAY_MAX_TIME;
            searchAmaP.setEcpExpiryDate(SubscribeTimeFormatUtil.stringToTimeByFormat(ecpExpireDate,
                    SubscribeTimeFormatUtil.FORMAT_2));
            searchAmaP.setQueryStatus(AmaPQueryStatusEnum.SUCCESS.getQueryStatus());
        } else {
            if(StringUtils.isNotBlank(response.getEcpExpireDate())) {
                String ecpExpireDate = response.getEcpExpireDate() + BLANK + TODAY_MAX_TIME;
                searchAmaP.setEcpExpiryDate(SubscribeTimeFormatUtil.stringToTimeByFormat(ecpExpireDate,
                        SubscribeTimeFormatUtil.FORMAT_2));
            }
            searchAmaP.setQueryStatus(AmaPQueryStatusEnum.FAIL.getQueryStatus());
            searchAmaP.setErrorDesc(response.getErrorDesc());
        }
    }

    /**
     * 根据分组大小，将一批高德查询记录分隔成多个子列表
     *
     * @param batchAmaPList 一批高德查询记录的列表
     * @return 返回一个包含分组后的高德查询记录子列表的列表
     */
    private List<List<AmaPQueryRecordsDO>> searchAmaPExcelByGroup(List<AmaPQueryRecordsDO> batchAmaPList) {
        List<List<AmaPQueryRecordsDO>> searchGroup = new ArrayList<>();
        if (CollUtil.isEmpty(batchAmaPList)) {
            return searchGroup;
        }
        for (int i = 0; i < batchAmaPList.size(); i += GROUP_SIZE) {
            int endIdx = Math.min(i+GROUP_SIZE, batchAmaPList.size());
            searchGroup.add(batchAmaPList.subList(i, endIdx));
        }
        return searchGroup;
    }


    /**
     * 异步查询高德车辆信息并以特定键值对形式存储
     *
     * @param amaPQueryList 高德查询记录列表，用于异步查询车辆信息
     * @param batchNo 批次编号，用于区分不同的查询批次
     * @return 返回一个Map，其中包含车辆的异步查询结果，
     *         键为组合键（批次编号+车辆VIN码），值为包含车辆信息的CompletableFuture
     */
    private Map<String, CompletableFuture<AmaPSearchCenterVO>> asyncQueryAmaPExpireDateMap(
                                                        List<AmaPQueryRecordsDO> amaPQueryList, Long batchNo) {
        Map<String, CompletableFuture<AmaPSearchCenterVO>> map = new HashMap<>();
        if (CollUtil.isEmpty(amaPQueryList)) {
            return map;
        }
        for (AmaPQueryRecordsDO queryAmaP : amaPQueryList) {
            CompletableFuture<AmaPSearchCenterVO> future = CompletableFuture.supplyAsync(() ->
                            piviAmaPService.queryAmaPInfo(queryAmaP.getCarVin()),
                    subscribeScheduledThreadPool);
            String key = getAmaPQueryKey(batchNo, queryAmaP.getCarVin());
            map.put(key, future);
        }
        return map;
    }


    /**
     * 构建高德地图查询参数字符串
     *
     * @param batchNo 批次编号，用于区分不同的数据批次
     * @param carVin 车辆识别码，用于唯一标识一辆车
     * @return 返回拼接好的查询参数字符串，格式为“批次编号,车辆识别码”
     */
    private String getAmaPQueryKey(Long batchNo, String carVin) {
        return  batchNo + "," + carVin;
    }

    /**
     * 更新批量查询状态
     *
     * @param batchNo 批次编号
     */
    public void updateBatchQueryStatus(Long batchNo) {
        //更新batch的状态
        AmaPQueryBatchRecordsDO amaPQueryBatchRecordsDO = new AmaPQueryBatchRecordsDO();
        amaPQueryBatchRecordsDO.setBatchNo(batchNo);
        amaPQueryBatchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
        amaPQueryBatchRecordsDO.setUpdatedTime(LocalDateTime.now());
        LambdaUpdateWrapper<AmaPQueryBatchRecordsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AmaPQueryBatchRecordsDO::getBatchNo, batchNo)
                .eq(AmaPQueryBatchRecordsDO::getIsDeleted, false);
        amaPQueryBatchRecordsDOMapper.update(amaPQueryBatchRecordsDO, updateWrapper);
    }

    /**
     * 根据处理状态查询批处理查询记录
     *
     * @param dealStatus 处理状态，用于筛选记录
     * @return 返回符合处理状态且未被标记为删除的批处理查询记录列表
     */
    private List<AmaPQueryBatchRecordsDO> queryAmaPQueryBatchByStatus(Integer dealStatus) {
        LambdaQueryWrapper<AmaPQueryBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPQueryBatchRecordsDO::getDealStatus, dealStatus)
                .eq(AmaPQueryBatchRecordsDO::getIsDeleted, false);
        return amaPQueryBatchRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 构建分页查询结果列表
     *
     * @param queryBatchRecordList 一批查询记录列表
     * @return 分页查询结果列表
     */
    private List<AmaPBatchQueryPageVO> buildBatchQueryPageVOList(List<AmaPQueryBatchRecordsDO> queryBatchRecordList) {
        List<AmaPBatchQueryPageVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(queryBatchRecordList)) {
            return resp;
        }
        for (AmaPQueryBatchRecordsDO amaPQueryBatchRecordsDO : queryBatchRecordList) {
            resp.add(buildBatchQueryPageVO(amaPQueryBatchRecordsDO));
        }
        return resp;
    }

    /**
     * 根据查询批次记录构建批次查询页面VO
     *
     * @param amaPQueryBatchRecordsDO 批次查询记录实体，可能为null
     * @return AmaPBatchQueryPageVO 返回批次查询的页面VO对象，如果输入参数为null，则返回一个空的VO
     */
    private AmaPBatchQueryPageVO buildBatchQueryPageVO(AmaPQueryBatchRecordsDO amaPQueryBatchRecordsDO) {
        if (Objects.isNull(amaPQueryBatchRecordsDO))  {
            return AmaPBatchQueryPageVO.builder().build();
        }
        return AmaPBatchQueryPageVO.builder()
                .operateTime(SubscribeTimeFormatUtil.timeToStringByFormat(amaPQueryBatchRecordsDO.getCreatedTime(),
                        SubscribeTimeFormatUtil.FORMAT_2))
                .checkResultStatus(amaPQueryBatchRecordsDO.getVerifyResult())
                .checkResultDesc(QueryUploadResultEnum.getDescByCode(amaPQueryBatchRecordsDO.getVerifyResult()))
                .batchNo(String.valueOf(amaPQueryBatchRecordsDO.getBatchNo()))
                .errorDetail(amaPQueryBatchRecordsDO.getVerifyReason())
                .build();
    }

    /**
     * AMAP查询分页记录的页面
     *
     * @param queryPageDTO 查询参数对象，包含分页和排序等信息
     * @return 返回分页查询结果，包含查询记录列表和总记录数
     */
    public Page<AmaPQueryBatchRecordsDO> queryBatchRecordsDOPage(AmaPBatchQueryPageDTO queryPageDTO) {
        log.info("查询分页记录的页面, queryPageDTO:{}", queryPageDTO);
        if (Objects.isNull(queryPageDTO)) {
            return null;
        }
        Page<AmaPQueryBatchRecordsDO> pageParam = new Page<>(queryPageDTO.getPageNo(), queryPageDTO.getPageSize());
        LambdaQueryWrapper<AmaPQueryBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPQueryBatchRecordsDO::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(queryPageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(AmaPQueryBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(AmaPQueryBatchRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(AmaPQueryBatchRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(AmaPQueryBatchRecordsDO::getId);
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        String userName = WebFrameworkUtils.getLoginUserName();
        boolean isSuperAdmin = false;
        if (Objects.nonNull(userId)) {
            try {
                CommonResult<Boolean> superAdminResult = permissionApi.currentUserRoleIsSuperAdmin(userId);
                isSuperAdmin = Boolean.TRUE.equals(Optional.ofNullable(superAdminResult)
                        .map(CommonResult::getData)
                        .orElse(false));
            } catch (Exception e) {
                log.error("调用system服务判断是否超管异常", e);
            }
        }
        if (StringUtils.isNotBlank(userName) && !isSuperAdmin) {
            queryWrapper.eq(AmaPQueryBatchRecordsDO::getOperator, userName);
        } else {
            log.info("AMAP查询分页记录的页面, 登录用户为空或用户是超管, queryPageDTO:{}", queryPageDTO);
        }
        return amaPQueryBatchRecordsDOMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 批量插入查询记录
     *
     * @param searchExpireListener 查询到期检查的监听器，包含所有待处理的数据
     * @param batchNo 批次编号，用于标识当前批次的数据
     */
    public void insertAmaPQueryRecords(SearchAmaPExpireCheckListener searchExpireListener, Long batchNo) {
        if (Objects.isNull(searchExpireListener) || CollUtil.isEmpty(searchExpireListener.getAllDataList())) {
            log.info("查询到期检查的监听器为空，不进行每条数据的插入操作, batchNo:{}", batchNo);
            return ;
        }
        List<List<SearchAmaPExpireBatchExcel>> batchExcelGroup = searchExpireListener.getAllDataList();
        for (List<SearchAmaPExpireBatchExcel> batchExcelList : batchExcelGroup) {
            List<AmaPQueryRecordsDO> amaPQueryRecordsList = new ArrayList<>();
            for (SearchAmaPExpireBatchExcel expireBatchExcel : batchExcelList) {
                amaPQueryRecordsList.add(buildAmaPQueryRecordsDO(expireBatchExcel, batchNo));
            }
            amaPQueryRecordsDOMapper.insertBatch(amaPQueryRecordsList);
        }
    }

    /**
     * 构建高德查询记录对象
     *
     * @param searchExpireExcel Excel数据，用于查询高德平台的到期记录
     * @param batchNo 批处理编号，用于标识查询记录所属的批次
     * @return 返回构建的高德查询记录数据对象(AmaPQueryRecordsDO)
     */
    public AmaPQueryRecordsDO buildAmaPQueryRecordsDO(SearchAmaPExpireBatchExcel searchExpireExcel,
                                                            Long batchNo) {
        if (Objects.isNull(searchExpireExcel)) {
            return AmaPQueryRecordsDO.builder()
                    .renewNo(batchNo)
                    .build();
        }
        return AmaPQueryRecordsDO.builder()
                .carVin(CarVinUtil.carVinToUpperCase(searchExpireExcel.getCarVin()))
                .renewNo(batchNo)
                .queryStatus(AmaPQueryStatusEnum.PROGRESS.getQueryStatus())
                .operator(getOperator())
                .build();

    }

    /**
     * 根据批次编号获取查询记录
     *
     * @param batchNo 批次编号，用于唯一标识一个批次的查询记录
     * @return 返回对应的查询记录对象
     */
    private AmaPQueryBatchRecordsDO getAmaPQueryRecordsByBatchNo(Long batchNo) {
        LambdaQueryWrapper<AmaPQueryBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPQueryBatchRecordsDO::getBatchNo, batchNo)
                .eq(AmaPQueryBatchRecordsDO::getIsDeleted, false);
        return amaPQueryBatchRecordsDOMapper.selectOne(queryWrapper);
    }

    /**
     * 根据批次号和查询状态获取查询记录
     *
     * @param batchNo 批次号，用于标识一批查询记录的共同特征
     * @param queryStatus 查询状态，用于筛选特定状态的查询记录
     * @return 返回符合条件的查询记录列表
     */
    private List<AmaPQueryRecordsDO> getAmaPQueryRecordsByBatchNoAndStatus(Long batchNo, Integer queryStatus) {
        LambdaQueryWrapper<AmaPQueryRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPQueryRecordsDO::getRenewNo, batchNo)
                .eq(AmaPQueryRecordsDO::getQueryStatus, queryStatus)
                .eq(AmaPQueryRecordsDO::getIsDeleted, false);
        return amaPQueryRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 批量插入高德地图查询记录
     *
     * @param batchNo 批次编号，用于标识此次批量插入的记录所属的批次
     */
    public void insertBatchAmaPQueryRecords(Long batchNo) {
        AmaPQueryBatchRecordsDO amaPQueryRecordsDO = AmaPQueryBatchRecordsDO.builder()
                .batchNo(batchNo)
                .verifyResult(1)
                .dealStatus(DealStatusEnum.PROGRESS.getStatus())
                .operator(getOperator())
                .build();
        amaPQueryBatchRecordsDOMapper.insert(amaPQueryRecordsDO);
    }

    /**
     * 获取当前操作员的用户名
     *
     * @return 当前操作员的用户名，如果未登录或用户名为空，则返回空字符串
     */
    private String getOperator() {
        if (StringUtils.isBlank(WebFrameworkUtils.getLoginUserName())) {
            return "";
        }
        return WebFrameworkUtils.getLoginUserName();
    }

}
