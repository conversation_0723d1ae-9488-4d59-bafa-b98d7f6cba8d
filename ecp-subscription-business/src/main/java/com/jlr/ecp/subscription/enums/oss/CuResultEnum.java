package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * CU查询结果枚举
 */
@AllArgsConstructor
@Getter
public enum CuResultEnum {

    SUCCESS(1, "是", null),

    SYSTEM_ERROR(2, "否，系统异常", "联通系统异常"),

    REQUEST_ERROR(3, "否，请求异常", "联通系统异常"),

    VIN_NOT_FOUND(4, "否，未找到该VIN的ICCID", "联通中无该VIN的ICCID");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 错误信息
     * */
    public final String errorMessage;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (CuResultEnum status : CuResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    public static String getErrorMessage(Integer code){
        if (code == null) {
            return null;
        }
        for (CuResultEnum status : CuResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getErrorMessage();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
