package com.jlr.ecp.subscription.controller.admin.iccid;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyPageV0;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyV0;
import com.jlr.ecp.subscription.service.iccid.IccidSingleModifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "ICCID-单个修改")
@RequestMapping("/iccid/single/modify")
@Validated
public class IccidSingleModifyController {

    @Resource
    private IccidSingleModifyService iccidSingleModifyService;

    @PostMapping("/operate")
    @Operation(summary = "ICCID单个修改操作")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-single-modify:forms')")
    public CommonResult<IccidSingleModifyV0> iccidOperateModify(@RequestBody @Valid IccidSingleModifyDTO iccidSingleModifyDTO) {
        return iccidSingleModifyService.iccidSingleOperateModify(iccidSingleModifyDTO);
    }

    @PostMapping("/preCheck")
    @Operation(summary = "ICCID单个修改 前置校验")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-single-modify:forms')")
    public CommonResult<IccidSingleModifyV0> iccidSingleModifyPreCheck(@RequestBody @Valid IccidSingleModifyDTO iccidSingleModifyDTO) {
        return iccidSingleModifyService.iccidSingleModifyPreCheck(iccidSingleModifyDTO);
    }

    @PostMapping("/pageList")
    @Operation(summary = "ICCID单个修改 操作记录分页查询")
    @PreAuthorize("@ss.hasPermission('subscribe:iccid-single-modify:forms')")
    public CommonResult<PageResult<IccidSingleModifyPageV0>> getIccidSinglePageList(@RequestBody @Valid IccidSingleModifyPageDTO pageDTO) {
        return CommonResult.success(iccidSingleModifyService.getIccidSinglePageList(pageDTO));
    }

}
