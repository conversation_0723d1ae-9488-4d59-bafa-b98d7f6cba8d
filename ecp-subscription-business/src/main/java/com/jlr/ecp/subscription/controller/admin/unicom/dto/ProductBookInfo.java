package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订购记录信息
 * <AUTHOR>
 */
@Data
public class ProductBookInfo implements Serializable {

    /**
     * 订购ID 必传
     * 捷豹路虎传入的订购ID，由捷豹路虎侧内部管理，退订时需要传入同一个订购ID。
     */
    @JsonProperty("ext_book_id")
    private String externalBookId;

    /**
     * 产品ID 必传
     * 捷豹路虎订购时传入的产品ID，产品ID由上海联通分配和提供。
     */
    @JsonProperty("product_id")
    private String productId;

    /**
     * 产品名称
     * 产品名称由上海联通分配和提供。
     */
    @JsonProperty("product_name")
    private String productName;

    /**
     * 订购状态 必传
     * active：生效
     * cancel：作废
     */
    @JsonProperty("book_status")
    private String bookStatus;

    /**
     * 激活时间 yyyyMMddHHmmSS
     */
    @JsonProperty("active_time")
    private String activeTime;

    /**
     * 到期时间
     */
    @JsonProperty("expire_time")
    private String expireTime;

    /**
     * 订购时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 退订时间
     */
    @JsonProperty("unsubscribe_time")
    private String unsubscribeTime;
}