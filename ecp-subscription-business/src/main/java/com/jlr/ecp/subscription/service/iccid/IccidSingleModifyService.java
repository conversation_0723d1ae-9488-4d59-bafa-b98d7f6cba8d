package com.jlr.ecp.subscription.service.iccid;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyPageV0;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyV0;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;

public interface IccidSingleModifyService {
    /**
     * ICCID 单个修改
     * @param iccidSingleModifyDTO
     * @return
     */
    CommonResult<IccidSingleModifyV0> iccidSingleOperateModify(IccidSingleModifyDTO iccidSingleModifyDTO);

    PageResult<IccidSingleModifyPageV0> getIccidSinglePageList(IccidSingleModifyPageDTO pageDTO);

    /**
     * ICCID 单个修改前置校验
     * @param iccidSingleModifyDTO
     * @return
     */
    CommonResult<IccidSingleModifyV0> iccidSingleModifyPreCheck(IccidSingleModifyDTO iccidSingleModifyDTO);

    CommonResult<UnicomRespVO> updateCuExpireDate(PIVIPackageDO piviPackageDO);
}
