package com.jlr.ecp.subscription.controller.app.model;


import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.model.vo.UserCarServiceListVO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.Arrays;
import java.util.List;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 */
@Tag(name = "小程序端购物车 - 车机年款")
@RestController
@RequestMapping("v1/vehicleModel")
@Validated
@Slf4j
public class AppVehicleModelMasterDataController {


    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;

    @Resource
    private IncontrolVehicleService incontrolVehicleService;

    @Resource
    private SOTAService sotaService;

    /**
     * 车型年款详情API
     * @param consumerCode 父级品牌 非必传
     * @return
     */
    @GetMapping( "/getUserCarList")
    @Operation(summary = "查询指定用户车辆信息列表")
    @Parameter(name = "consumerCode", description = "客户编码")
    // @PermitAll
    CommonResult<List<UserCarServiceListVO>> getUserCarList(@RequestParam(value = "consumerCode")String consumerCode,@RequestParam(value = "brandCode",required = false) String brandCode,
                                                            @RequestHeader(value = "client-id", required = false) String clientId,
                                                            @RequestHeader(value = "jlrId") String jlrId){
        log.info("请求头clientId:{}, jlrId:{}", clientId, jlrId);
        if (StrUtil.isBlank(clientId)) {
            throw exception(ErrorCodeConstants.CLIENT_ID_HEADER_NEEDED);
        }
        List<UserCarServiceListVO> list = incontrolVehicleService.getUserCarServiceList(jlrId,brandCode,clientId);
        return CommonResult.success(list);
    }

    @Deprecated
    @GetMapping( "/testFindDPList")
    @Operation(summary = "DP查询接口 废弃")
    @Parameter(name = "consumerCode", description = "客户编码")
    @PermitAll
    CommonResult<List<UserDPResultVO>> testFindDPList(@RequestParam String vin){

        List<UserDPResultVO> findDpList = vehicleModelMasterDataService.findDpList(Arrays.asList(vin));
        return CommonResult.success(findDpList);
    }


    @Deprecated
    @GetMapping( "/testFindSOAT")
    @Operation(summary = "SOTA查询 废弃")
    @PermitAll
    CommonResult<SOTAResultVO> testFindSOAT(@RequestParam String vin){

        SOTAResultVO sotaResultVO = sotaService.getSOTAInfoByVin(vin);
        return CommonResult.success(sotaResultVO);
    }


}
