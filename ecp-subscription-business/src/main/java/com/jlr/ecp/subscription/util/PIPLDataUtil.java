package com.jlr.ecp.subscription.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class PIPLDataUtil {

    @Resource
    private PermissionApi permissionApi;

    /**
     * 获取解密的字符串
     * @param encodeText 加密的密文
     * @return String
     * */
    public String getDecodeText(String encodeText) {
        String decodeText =  CharSequenceUtil.EMPTY;
        if (StrUtil.isBlank(encodeText)){
            return decodeText;
        }
        CommonResult<String> commonResult = null;
        try {
            JSONObject json = new JSONObject();
            json.put("encryptTex", encodeText);
            commonResult = permissionApi.getDecryptText(json);
        } catch (Exception e) {
            log.error("调用system解密服务异常：", e);
        }
        if (commonResult != null && commonResult.isSuccess() && commonResult.getData() != null) {
            decodeText = commonResult.getData();
        }
        return decodeText;
    }
    /**
     * 获取解密的字符串
     * @param encodeTexts 加密的密文
     * @return String
     * */
    public Map<String,String> getDecodeListText(List<String> encodeTexts) {
        Map<String, String> decryptTextBatch = new HashMap<>();
        if (CollectionUtil.isEmpty(encodeTexts)){
            return decryptTextBatch;
        }
        CommonResult<Map<String, String>> commonResult = null;
        try {
            commonResult = permissionApi.getDecryptTextBatch(encodeTexts);
        } catch (Exception e) {
            log.error("调用system解密服务异常：", e);
        }
        if (commonResult != null && commonResult.isSuccess() && commonResult.getData() != null) {
            decryptTextBatch = commonResult.getData();
        }
        return decryptTextBatch;
    }
    /**
     * 获取加密后的的字符串
     * @param originStr 原始字符串
     * @return String  加密字符串
     * */
    public String getEncryptText(String originStr) {
        String resultStr = CharSequenceUtil.EMPTY;
        if (StrUtil.isBlank(originStr)) {
            return resultStr;
        }
        CommonResult<String> commonResult = null;
        try {
            commonResult = permissionApi.getEncryptText(originStr);
        } catch (Exception e) {
            log.error("调用system加密服务异常：", e);
        }
        if (commonResult != null && commonResult.isSuccess() && StrUtil.isNotBlank(commonResult.getData())) {
            resultStr = commonResult.getData();
        }
        return resultStr;
    }
    /**
     * 批量获取加密后的的字符串
     * @param originStrs 原始字符串
     * @return String  加密字符串
     * */
    public Map<String, String> getEncryptListText(Set<String> originStrs) {
        Map<String, String> encryptTextList = new HashMap<>();
        if (CollectionUtil.isEmpty(originStrs)) {
            return Collections.emptyMap();
        }
        CommonResult<Map<String, String>> commonResult = null;
        try {
            commonResult = permissionApi.getEncryptTextBatch(new ArrayList<>(originStrs));
        } catch (Exception e) {
            log.error("调用system加密服务异常：", e);
        }
        if (commonResult != null && commonResult.isSuccess()) {
            encryptTextList = commonResult.getData();
        }
        return encryptTextList;
    }

    /**
     * 获取指定icr账号的半隐藏数据  要求只看到@后面的字符 前面的隐藏
     * */
    public static String getIncontrolIdMix(String incontrolId) {
        if (StrUtil.isBlank(incontrolId)) {
            return "-";
        }
        return StrUtil.replace(incontrolId, 0, incontrolId.indexOf("@"), '*');
    }

    /**
     * 获取指定vin号的半隐藏数据  要求只看到前6位 后面的隐藏
     * */
    public static String getVinMix(String vin) {
        return StrUtil.replace(vin, 6, vin.length(), '*');
    }

}
