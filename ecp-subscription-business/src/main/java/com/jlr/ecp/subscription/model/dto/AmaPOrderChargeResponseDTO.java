package com.jlr.ecp.subscription.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class AmaPOrderChargeResponseDTO {

     /**
      *  版本号
      * */
     private String version;

     /**
      *  错误码
      * */
     private String code;

     /**
      * 错误信息
      * */
     private String message;

     /**
      *  错误详情
      * */
     @JsonProperty("errdetail")
     private String errDetail;

     /**
      *  unix时间戳，秒
      * */
     private Long timestamp;

     /**
      *  请求结果，true表示成功，false表示失败
      * */
     private Boolean result;

     /**
      *  返回h数据结果列表
      * */
     @JsonProperty("data")
     List<OrderChargeResponseData> data;
}
