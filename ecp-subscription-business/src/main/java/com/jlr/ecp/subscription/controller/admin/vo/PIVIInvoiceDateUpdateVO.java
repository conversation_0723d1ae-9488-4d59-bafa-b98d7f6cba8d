package com.jlr.ecp.subscription.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "PIVI手动更新发票时间历史编辑记录VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PIVIInvoiceDateUpdateVO {
    @Schema(description = "更新时间")
    private String modifyTime;

    @Schema(description = "更新人")
    private String modifyUser;

    @Schema(description = "车俩VIN")
    private String carVin;

    @Schema(description = "发票新时间")
    private String invoiceNewDate;
}
