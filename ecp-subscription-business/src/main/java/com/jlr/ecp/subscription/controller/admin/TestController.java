 package com.jlr.ecp.subscription.controller.admin;

 import cn.hutool.core.collection.ListUtil;
 import cn.hutool.core.lang.Snowflake;
 import cn.hutool.core.util.StrUtil;
 import com.alibaba.fastjson.JSON;
 import com.alibaba.fastjson.JSONArray;
 import com.alibaba.fastjson.JSONObject;
 import com.jlr.ecp.framework.common.pojo.CommonResult;
 import com.jlr.ecp.framework.kafka.producer.ProducerTool;
 import com.jlr.ecp.framework.tenant.core.aop.TenantIgnore;
 import com.jlr.ecp.subscription.annotation.ApiLimitation;
 import com.jlr.ecp.subscription.api.model.vo.DPResponseVO;
 import com.jlr.ecp.subscription.api.model.vo.DPResultVO;
 import com.jlr.ecp.subscription.api.subscription.SubscriptionServiceApiImpl;
 import com.jlr.ecp.subscription.api.unicom.UnicomTodoOrderApi;
 import com.jlr.ecp.subscription.api.unicom.dto.GenerateReportRequestV2;
 import com.jlr.ecp.subscription.api.unicom.vo.InitResponse;
 import com.jlr.ecp.subscription.config.DPService;
 import com.jlr.ecp.subscription.config.RedisService;
 import com.jlr.ecp.subscription.constant.Constants;
 import com.jlr.ecp.subscription.enums.fufil.AppDSubscriptionEventEnum;
 import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
 import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
 import com.jlr.ecp.subscription.model.dto.AmaPOrderChargeResponseDTO;
 import com.jlr.ecp.subscription.model.dto.AmaPOrderChargerRequestDTO;
 import com.jlr.ecp.subscription.model.dto.AppDSubscriptionReq;
 import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
 import com.jlr.ecp.subscription.util.PIPLDataUtil;
 import com.jlr.ecp.subscription.util.TimeFormatUtil;
 import io.swagger.v3.oas.annotations.Operation;
 import lombok.extern.slf4j.Slf4j;
 import org.redisson.api.RateIntervalUnit;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.http.*;
 import org.springframework.util.LinkedMultiValueMap;
 import org.springframework.util.MultiValueMap;
 import org.springframework.web.bind.annotation.*;
 import org.springframework.web.client.HttpClientErrorException;
 import org.springframework.web.client.RestClientException;
 import org.springframework.web.client.RestTemplate;

 import javax.annotation.Resource;
 import javax.annotation.security.PermitAll;
 import java.time.LocalDateTime;
 import java.util.*;
 import java.util.concurrent.CompletableFuture;
 import java.util.concurrent.ExecutionException;
 import java.util.concurrent.Future;
 import java.util.concurrent.TimeUnit;
 import java.util.concurrent.atomic.AtomicInteger;

 @RestController
 @Slf4j
 public class TestController {

     @Autowired
     ProducerTool producerTool;

     @Resource
     Snowflake ecpIdUtil;

     @Resource
     RedisService redisService;

     @Resource
     SubscriptionServiceApiImpl service;

     @Resource
     RestTemplate restTemplate;

     @Resource
     RestTemplate restTemplateAPPD;

     @Resource
     private PIVIAmaPService piviAmaPService;

     @Resource
     private DPService dpService;

     @Resource
     private PIPLDataUtil piplDataUtil;

     private static final String AUTHORIZATION = "Authorization";

     @Resource
     private UnicomTodoOrderApi unicomTodoOrderApi;

     @PermitAll
     @PostMapping(value = "/test/initializeRecords")
     @Operation(summary = "测试 初始化VIN月到期记录（含主表和明细表）")
     public String testInitializeRecords(@RequestBody GenerateReportRequestV2 request){
         CommonResult<InitResponse> initResponseCommonResult = unicomTodoOrderApi.initializeRecords(request);
         return JSON.toJSONString(initResponseCommonResult);
     }

     @PermitAll
     @GetMapping(value = "/test/getInitializedCount")
     @Operation(summary = "测试 根据JobID获取已初始化的明细记录数")
//     @TenantIgnore 无效
     public String getInitializedCount(@RequestParam("jobId") Long jobId){
         CommonResult<Integer> integerCommonResult = unicomTodoOrderApi.getInitializedCount(jobId);
         return JSON.toJSONString(integerCommonResult);
     }


     @PermitAll
     @GetMapping(value = "/testmsg")
     public String testmsg()
     {
         String str = String.format("data is %s",new Date().toString());
         producerTool.sendMsg("test-topic","",
                 str);
         return str;
     }

     @PermitAll
     @PostMapping(value = "/testDistributionIcrCar")
     public String testDistributionIcrCar(@RequestBody JSONObject json) {
         log.info("ICR分配入参：{}", json);
         redisService.setCacheMap("TEST_ICR_MAP", json);
         Map<String, Object> testIcrMap = redisService.getCacheMap("TEST_ICR_MAP");
         String icrVin = (String) testIcrMap.get("<EMAIL>");
         System.out.println(icrVin);
         String icrVin2 = (String) testIcrMap.get("ro");
         System.out.println(icrVin2);
         return "分配成功，下次登录生效";
     }

     @PermitAll
     @PostMapping(value = "/testDistributionVin")
     public String testDistributionVin(@RequestBody JSONArray jsonArray) {
         log.info("VIN号分配入参：{}", jsonArray);
         for (Object o : jsonArray) {
             Map obj = (Map) o;
             DPResultVO vo = JSON.parseObject(JSON.toJSONString(obj),DPResultVO.class);
             if (StrUtil.isBlank(vo.getVin())){
                 return "入参错误，需要指定vin号";
             }
             redisService.setCacheMapValue(Constants.REDIS_KEY.MOCK_DP_KEY, vo.getVin(),vo);
         }
         Map<String, DPResultVO> testVinMap = redisService.getCacheMap(Constants.REDIS_KEY.MOCK_DP_KEY);
         System.out.println(JSON.toJSONString(testVinMap));
         return "分配成功，下次登录生效";
     }

     @PermitAll
     @GetMapping(value = "/testSendOrdMQ")
     public String testSendOrdMQ(){
         FufilmentMessage fulfilmentMessage = new FufilmentMessage();
         fulfilmentMessage.setMessageId(ecpIdUtil.nextIdStr());
         fulfilmentMessage.setOrderCode("test:orderCode");
         fulfilmentMessage.setOrderItemCode("test:orderItemCode");
         fulfilmentMessage.setVcsOrderCode("test:vscOrdCode");
         fulfilmentMessage.setVin("test:vinCode");
         fulfilmentMessage.setTenantId(1L);
         producerTool.sendMsg("order-fufilment-topic","order-fufilment-group-local", JSON.toJSONString(fulfilmentMessage));
         return "testSendOrdMQSuccess";
     }

     @PermitAll
     @GetMapping(value = "/testSendCancelMQ")
     public String testSendCancelMQ(@RequestBody CancelMessage cancelMessage){
         // fulfilmentMessage.setVin("TBPRECD30SMF40101");
         cancelMessage.setServiceEndDate(LocalDateTime.now().plusMonths(3));
         cancelMessage.setTenantId(1L);
         cancelMessage.setMessageId(ecpIdUtil.nextIdStr());
         producerTool.sendMsg("order-fufilment-topic","order-cancel-group-local", JSON.toJSONString(cancelMessage));
         return "testSendCancelMQSuccess";
     }


     @PermitAll
     @GetMapping(value = "/testJob")
     @Deprecated
     public String testJOB(){
 //        service.checkFulfilmentStatus();
         return "testJobSuccess";
     }

     @PermitAll
     @GetMapping(value = "/test/apiLimitation")
     @ApiLimitation(limitCount = 1, time = 30, timeUnit = RateIntervalUnit.SECONDS, tokenBucketName = "testApiLimitation")
     public String testApiLimitation(@RequestParam String str){
         log.info("testApiLimitation, str:{}", str);
         return str;
     }

     @PermitAll
     @TenantIgnore
     @GetMapping(value = "/testmtls")
     public ResponseEntity<JSONObject> testRequest() throws Exception {
         String url = "https://ifecom.prodfeu-chn.jlrmotor.com/subscriptions/users/";
         // 设置header数据
         HttpHeaders headers = new HttpHeaders();
         headers.add("Accept","application/json");
         headers.add(AUTHORIZATION,"Basic dGVzdHVzZXI6dGVzdHBhc3N3b3Jk");
         // 创建HttpEntity对象，包含header和body数据
         HttpEntity<String> entity = new HttpEntity<>(headers);
         url = url + "<EMAIL>";
         // 发送GET请求
         ResponseEntity<JSONObject> response = null;
         try {
             response = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
         } catch (RestClientException e) {
             log.info("TSDP查询账号下订阅失败：",e);
             if (e instanceof HttpClientErrorException.Forbidden){
                 throw new Exception("forbidden");
             }
             if (e instanceof HttpClientErrorException.TooManyRequests){
                 throw new Exception("too fast");
             }
             throw e;
         }
         log.info("TSDP查询账号下订阅成功");
         return response;
     }

     @PermitAll
     @TenantIgnore
     @GetMapping(value = "/testAppd")
     public ResponseEntity<JSONObject> testAppd() throws Exception {
         String url = "https://pgws.qa1-chn-ci.jlrmotor.com/oauth2/b2b/token";
         // 设置header数据
         HttpHeaders headers = new HttpHeaders();
         // 参数类型
         headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
         // 设置参数
         MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
         map.add("grant_type", "client_credentials");
         map.add("client_id", "JLR.ECP");
         map.add("client_secret", "b1ff5960-c86d-4fce-8209-c78764929476");

         HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(map, headers);

         // 发送请求
         ResponseEntity<JSONObject> response;
         try {
             response = restTemplateAPPD.exchange(url, HttpMethod.POST, requestEntity, JSONObject.class);
         } catch (RestClientException e) {
             log.info("testAppd接口调用失败：", e);

             throw e;
         }
         log.info("testAppd接口调用成功");
         return response;
     }

     @GetMapping("/test/appd/get")
     @PermitAll
     @TenantIgnore
     public ResponseEntity<JSONArray> testAppdGetSubscription(@RequestParam String accessToken, @RequestParam String vin) {
         HttpHeaders headers = new HttpHeaders();
         headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
         headers.set(AUTHORIZATION, "Bearer " + accessToken);
         HttpEntity<Object> entity = new HttpEntity<>(headers);
         String url = "https://pgws.qa1-chn-ci.jlrmotor.com/b2b/SCP/IF_ServiceProvider/1.0.0/serviceProvider/vehicles/{vin}/subscriptions";
         // 发送请求
         ResponseEntity<JSONArray> response = restTemplateAPPD.exchange(url, HttpMethod.GET, entity, JSONArray.class, vin);
         if(Objects.isNull(response.getBody())){
             throw new RestClientException("response body为空");
         }
         System.out.println(JSON.toJSONString(response));
         return response;
     }

     @PostMapping("/test/appd/renew")
     @PermitAll
     @TenantIgnore
     public ResponseEntity<JSONObject> testAppdRenew(@RequestParam String accessToken, @RequestParam Long jlrSubscriptionId, @RequestParam String date) {
         AppDSubscriptionReq request = new AppDSubscriptionReq();
         request.setSubscriptionEvent(AppDSubscriptionEventEnum.ACTIVATE.getEvent());
         request.setSubscriptionEventDateTime(LocalDateTime.now());
         request.setExpiresDate(TimeFormatUtil.stringToLocalDate(date));
         request.setJlrSubscriptionId(jlrSubscriptionId);
         MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
         headerMap.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
         headerMap.set(AUTHORIZATION, "Bearer " + accessToken);
         HttpEntity<AppDSubscriptionReq> entity = new HttpEntity<>(request, headerMap);
         // 发送请求
         ResponseEntity<JSONObject> response = restTemplateAPPD.exchange("https://pgws.prod-chn-ci.jlrmotor.com/b2b/SCP/IF_ServiceProvider/1.0.0/serviceProvider/subscriptionRequests", HttpMethod.PUT, entity, JSONObject.class);
         if(Objects.isNull(response.getBody())){
             throw new RestClientException("response body为空");
         }
         System.out.println(JSON.toJSONString(response));
         return response;
     }

     @PostMapping("/test/amap/charge")
     @PermitAll
     @TenantIgnore
     public void testAmaPCharge(@RequestBody AmaPOrderChargerRequestDTO requestDTO) {
         log.info("testAmaPCharge, requestDTO:{}", requestDTO);
         AmaPOrderChargeResponseDTO responseDTO = piviAmaPService.chargeAmaPOrder(requestDTO);
         log.info("responseDTO:{}", responseDTO);
     }

     @PostMapping("/test/encryptTextBatch")
     @PermitAll
     @TenantIgnore
     public Map<String, String> encryptTextBatch() {
         Set<String> set = new HashSet<>();
         for (int i = 0; i < 1000; i++) {
             set.add("phone_"+i);
         }
         Map<String, String> result = piplDataUtil.getEncryptListText(set);
         System.out.println("获取到结果："+result.size()+"个");
         return result;
     }

     @PostMapping("/test/dpService")
     @PermitAll
     @TenantIgnore
     public List dpService() throws ExecutionException, InterruptedException {
         List<String> list = new ArrayList<>();
         for (int i = 0; i < 500; i++) {
             // list.add("vin_" + i);
             list.add("SALEA7RU2N2084182");
             list.add("SALEA7RUXN2085614");
         }
         List<List<String>> split = ListUtil.split(list, 100);
         AtomicInteger count = new AtomicInteger(0);
         List<Future<DPResponseVO>> result = new ArrayList<>();
         for (List<String> strings : split) {
             CompletableFuture<DPResponseVO> future = CompletableFuture.supplyAsync(() -> {
                 count.getAndAdd(1);
                 return dpService.callApi(strings);
             });
             result.add(future);
         }
         CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
         System.out.println("获取到结果：" +count.get() + "个");
         List totalResult = new ArrayList<>();
         for (Future<DPResponseVO> future : result) {
             List<DPResultVO> resultVOS = future.get().getResult();
             totalResult.add(resultVOS);
         }
         return totalResult;
     }


     @PermitAll
     @PostMapping("/test/cacheMockData")
     @Operation(summary = "将 iccid 和 mockData 缓存到 Redis，并从 Redis 查询返回")
     public CommonResult<String> cacheMockData(
             @RequestParam String iccid,
             @RequestBody String mockData) {
         // 定义 Redis 缓存 key
         String redisKey = "mock_simcard_info:" + iccid;

         // 将 mockData 存入 Redis
         redisService.setCacheObject(redisKey, mockData);

         return CommonResult.success(mockData);
     }

     @PermitAll
     @PostMapping("/test/batchInsertTestVinData")
     @Operation(summary = "批量插入TEST_VIN_MAP测试数据")
     @TenantIgnore
     public CommonResult<String> batchInsertTestVinData(@RequestParam(required = true) String vinPrefix,
                                                        @RequestParam(required = true) Integer startIndex,
                                                        @RequestParam(required = true) Integer totalCount) {
         log.info("开始批量插入TEST_VIN_MAP测试数据，vinPrefix: {}, startIndex: {}, totalCount: {}", vinPrefix, startIndex, totalCount);
         try {
             Map<String, DPResultVO> batchData = new HashMap<>();
             // 分批处理数据
             for (int vinNumber = startIndex; vinNumber < totalCount+startIndex; vinNumber++) {
                 String vin = vinPrefix + vinNumber;
                 DPResultVO dpResultVO = new DPResultVO();
                 dpResultVO.setVin(vin);
                 dpResultVO.setBrandCode("JAG");
                 dpResultVO.setBrandName("捷豹");
                 dpResultVO.setModelYear("2024.01");
                 dpResultVO.setConfigCode("配置code");
                 dpResultVO.setConfigName("配置Name");
                 dpResultVO.setSeriesCode("X761");
                 dpResultVO.setSeriesName("X761款");
                 dpResultVO.setProductionEn("产品说明");
                 dpResultVO.setHobEn("说明");
                 batchData.put(vin, dpResultVO);
             }
             // 使用Pipeline批量插入到Redis，使用较小的Pipeline批次
             redisService.batchSetCacheMapValue(Constants.REDIS_KEY.MOCK_DP_KEY, batchData, 1000);

             // 使用HLEN命令验证插入结果（避免HGETALL超时）
             Long actualCount = redisService.getCacheMapSize(Constants.REDIS_KEY.MOCK_DP_KEY);
             log.info("批量插入完成，现在实际数据量：{}", actualCount);
             return CommonResult.success("批量插入完成，现在实际数据量：" + actualCount);
         } catch (Exception e) {
             log.error("批量插入TEST_VIN_MAP数据失败", e);
             return CommonResult.error(10001, "批量插入失败：" + e.getMessage());
         }
     }


     @PermitAll
     @PostMapping("/test/clearTestVinMap")
     @Operation(summary = "清空Redis-Hash数据结构指定key数据")
     @TenantIgnore
     public CommonResult<String> clearTestVinMap(@RequestParam(required = true) String hashKey,
                                                 @RequestParam(required = true) String fieldKeyPrefix) {
         try {
             long res = redisService.deleteFieldsByPrefix(hashKey, fieldKeyPrefix);
             log.info("已清空TEST_VIN_MAP数据");
             return CommonResult.success("已删除TEST_VIN_MAP指定数据, 数量" + res);
         } catch (Exception e) {
             log.error("清空TEST_VIN_MAP数据失败", e);
             return CommonResult.error(10001, "清空数据失败：" + e.getMessage());
         }
     }

 }
