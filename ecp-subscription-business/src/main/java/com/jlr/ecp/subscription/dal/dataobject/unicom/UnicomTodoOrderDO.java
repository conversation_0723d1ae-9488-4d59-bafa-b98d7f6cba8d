package com.jlr.ecp.subscription.dal.dataobject.unicom;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_unicom_todo_order
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_unicom_todo_order")
public class UnicomTodoOrderDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;

     /**
     * 订阅服务ID;订阅服务ID
     */    
    private String iccid;

     /**
     * 请求类型;order：订购 cancel：退订 change：变更
     */    
    private String requestType;

    /**
     * 订购ID;订购ID
     */
    @TableField(value = "ext_book_id")
    private String extBookId;

    /**
     * 产品ID
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 订购状态
     */
    @TableField(value = "book_status")
    private String bookStatus;

    /**
     * 激活时间;激活时间yyyyMMddHHmmSS
     */
    @TableField(value = "active_time")
    private String activeTime;


    /**
     * 到期时间;到期时间yyyyMMddHHmmSS
     */
    @TableField(value = "expire_time")
    private String expireTime;


    /**
     * 当前处理状态;状态 0：待处理 1：处理完成 2：处理失败
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 处理次数;处理次数，默认0，大于3次不再处理，日志告警
     */
    @TableField(value = "request_count")
    private Integer requestCount;

    /**
     * 待校验订购ID;待校验订购ID
     */
    @TableField(value = "valid_ext_book_id")
    private String validExtBookId;

    /**
     * 需要校验的订购状态;active：生效 cancel：作废
     */
    @TableField(value = "valid_book_status")
    private String validBookStatus;

}

