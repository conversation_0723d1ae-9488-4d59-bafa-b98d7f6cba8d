package com.jlr.ecp.subscription.kafka.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsFufilmentStatisticDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderAmapRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentRecords;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsFufilmentStatisticMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderAmapRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.*;
import com.jlr.ecp.subscription.enums.subscribeservice.notify.ExpirationServiceEnum;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIFulfilmentService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderFufilmentListener {

    @Autowired
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Autowired
    private VcsFufilmentStatisticMapper vcsFufilmentStatisticMapper;

    @Autowired
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private VcsOrderFufilmentRecordsMapper vcsOrderFufilmentRecordsMapper;

    @Resource
    private PIVIAppDService piviAppDService;

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private OrderFufilmentListener orderFulfilmentListener;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource
    private PIVIFulfilmentService piviFulfilmentService;

    @Resource(name = "subscribeScheduledThreadPool")
    private ThreadPoolTaskScheduler subscribeScheduledThreadPool;

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private Environment environment;

    @Resource
    private VcsOrderAmapRecordsMapper vcsOrderAmapRecordsMapper;

    @KafkaListener(topics = "order-fufilment-topic", groupId = "order-fufilment-group", properties = "max.poll.records:1")
    public void onMessage(String message) {
        log.info("order fifulment 消费消息, message:{}", message);
        try {
            FufilmentMessage fufilmentMessage = JSON.parseObject(message, FufilmentMessage.class);
            TenantContextHolder.setTenantId(fufilmentMessage.getTenantId());

            // 校验vin码是否存在，以及是否拥有车辆完整配置信息和订阅服务
            IncontrolVehicleDO incontrolVehicleDO = incontrolVehicleDOMapper.selectOne(
                    new LambdaQueryWrapperX<IncontrolVehicleDO>()
                            .eq(IncontrolVehicleDO::getCarVin, fufilmentMessage.getVin())
                            .eq(BaseDO::getIsDeleted, false)
                            .orderByDesc(IncontrolVehicleDO::getId)
                            .last("limit 1"));
            if (incontrolVehicleDO == null) {
                log.warn("续费消费时车辆：{}未查询到", fufilmentMessage.getVin());
                return;
            }

            Integer subscriptionServiceType = fufilmentMessage.getServiceType();
            // 查询对应订阅服务，筛选remote类型和PIVI得到要续费的的服务
            if (Constants.SERVICE_TYPE.NOT_REMOTE.equals(fufilmentMessage.getServiceType())) {
                subscriptionServiceType = Constants.SERVICE_TYPE.PIVI;
            }
            List<SubscriptionServiceDO> services = subscriptionServiceMapper.selectList(
                    new LambdaQueryWrapperX<SubscriptionServiceDO>()
                            .eq(SubscriptionServiceDO::getCarVin, fufilmentMessage.getVin())
                            .eq(SubscriptionServiceDO::getServiceType, subscriptionServiceType)
            );
            if (CollUtil.isEmpty(services)) {
                log.warn("续费消费时车辆：{}未查询到可供续费的服务", fufilmentMessage.getVin());
                return;
            }

            if (Constants.SERVICE_TYPE.REMOTE.equals(subscriptionServiceType)) {
                log.info("进行remote履约消费处理");
                handleRemoteFulfilment(fufilmentMessage, incontrolVehicleDO, services);
            } else if (Constants.SERVICE_TYPE.PIVI.equals(subscriptionServiceType)) {
                log.info("进行PIVI履约消费处理");
                // 判断是否是不含APPD的车辆
                SubscriptionServiceDO appDService = services.stream().filter(serviceDO ->
                                ServicePackageEnum.ONLINE_PACK.getPackageName().equals(serviceDO.getServicePackage()))
                        .findFirst().orElse(new SubscriptionServiceDO());
                handlePiviFulfilment(fufilmentMessage, incontrolVehicleDO, appDService);
            }
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 处理Pivi履行订单
     * @param fufilmentMessage 履行消息，包含订单信息。
     * @param incontrolVehicleDO 车辆信息，用于关联订单和车辆。
     * @param service 订阅的服务，用于处理服务的激活和回滚。
     */
    private void handlePiviFulfilment(FufilmentMessage fufilmentMessage, IncontrolVehicleDO incontrolVehicleDO, SubscriptionServiceDO service) {
        Long jlrSubscriptionId = service.getJlrSubscriptionId();
        Integer appDInitStatus = ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus();
        if (Objects.isNull(jlrSubscriptionId)) {
            log.info("无需履约APPD");
            appDInitStatus = ServiceActivationStatusEnum.ACTIVATION_NO.getStatus();
        }
        Pair<Boolean, String> pair = orderFulfilmentListener.insertPIVIOrderFulfilment(fufilmentMessage, incontrolVehicleDO, appDInitStatus);
        // 判断履约记录是否已经存在, 证明为重复消费
        if (!pair.getLeft()) {
            log.info("Pivi履约记录已经存在, fulfilmentId:{}", pair.getRight());
            return;
        }
        String fulfilmentId = pair.getRight();
        // APPD
        CompletableFuture<Boolean> appDFuture = CompletableFuture.supplyAsync(
                        () -> piviAppDService.callAppDService(fufilmentMessage, fulfilmentId, jlrSubscriptionId), subscribeAsyncThreadPool)
                .exceptionally(ex -> {
                    log.info("调用APPD接口失败", ex);
                    return false;
                });

        CompletableFuture<Boolean> unionFuture;
        // 根据环境变量调用联通接口
        String env = environment.getProperty(Constants.PROFILE_ACTIVE);
        if ("dev".equals(env) || "test".equals(env)) {
            // 联通mock返回成功
            unionFuture = CompletableFuture.supplyAsync(() -> true, subscribeAsyncThreadPool)
                    .exceptionally(ex -> {
                        log.info("调用联通接口失败", ex);
                        return false;
                    });
        } else {
            //uat 和prd实际直接调用
            unionFuture = CompletableFuture.supplyAsync(
                            () -> piviUnicomService.callUnicomService(fufilmentMessage, fulfilmentId), subscribeAsyncThreadPool)
                    .exceptionally(ex -> {
                        log.info("调用联通接口失败", ex);
                        return false;
                    });
        }
        //AMAP
        CompletableFuture<Boolean> amaPFuture = CompletableFuture.supplyAsync(
                        () -> piviAmaPService.callAmaPService(fufilmentMessage, fulfilmentId), subscribeAsyncThreadPool)
                .exceptionally(ex -> {
                    log.info("调用AMAP接口失败", ex);
                    return false;
                });
        //等待appd、unicom、都已经续费完成
        CompletableFuture.allOf(appDFuture, unionFuture);
        log.info("激活服务结果-------APPD:{},联通:{}", appDFuture.join(), unionFuture.join());
        OrderFufilmentListener fufilmentListener = applicationContext.getBean(getClass());
        fufilmentListener.updateFulfilmentRecord(appDFuture.join(), unionFuture.join(), fulfilmentId, jlrSubscriptionId);
        // 根据高德续费结果处理
        handleAmaPResult(amaPFuture.join(), fufilmentMessage, fulfilmentId);
    }

    /**
     * 处理AMAP结果
     * 根据AMAP返回的结果决定PIVI履约的更新方式
     *
     * @param isSuccess 是否成功
     * @param fufilmentMessage 履约消息
     * @param fulfilmentId 履约ID
     */
    private void handleAmaPResult(boolean isSuccess, FufilmentMessage fufilmentMessage, String fulfilmentId) {
        // 续费时长
        int year = Math.abs(
                fufilmentMessage.getServiceEndDate().getYear() -
                        fufilmentMessage.getServiceBeginDate().getYear()
        );
        if (isSuccess) {
            if (year == Constants.AMAP_RENEWAL_YEAR.SIX_YEAR) {
                updatePIVIOrderFulfilmentBySixYear(fufilmentMessage.getVcsOrderCode(), fulfilmentId);
            } else {
                updatePIVIFulfilmentByAmaP(fufilmentMessage.getVcsOrderCode(), fulfilmentId);
            }
        } else {
            if (year == Constants.AMAP_RENEWAL_YEAR.SIX_YEAR) {
                // 查询是否存在一个订单续费成功
                LambdaQueryWrapper<VcsOrderAmapRecords> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VcsOrderAmapRecords::getFufilmentId, fulfilmentId)
                        .eq(VcsOrderAmapRecords::getChargeOrderStatus, AMapChargeOrderStatusEnum.SUCCESS.getStatus())
                        .eq(VcsOrderAmapRecords::getIsDeleted, false);
                List<VcsOrderAmapRecords> vcsOrderAmapRecords = vcsOrderAmapRecordsMapper.selectList(queryWrapper);
                if (CollUtil.isNotEmpty(vcsOrderAmapRecords)) {
                    updateOrderAmapRecords(vcsOrderAmapRecords.get(0).getVcsOrderCode(), fulfilmentId);
                }
            }
            log.info("AMAP充值返回结果没有成功，无法进行PIVI履约的更新, fulfilmentId:{}", fulfilmentId);
            updateAMapFulfilmentRecord(fulfilmentId);
        }
    }

    /**
     * 通过AMAP更新PIVI订单履行状态
     *
     * @param vcsOrderCode 订单代码，用于识别特定的订单
     * @param fulfilmentId 履行ID，与订单履行状态相关联
     */
    private void updatePIVIFulfilmentByAmaP(String vcsOrderCode, String fulfilmentId) {
        Date twoMinutesLater = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
        Runnable task = () -> piviFulfilmentService.updatePIVIOrderFulfilment(vcsOrderCode, fulfilmentId);
        subscribeScheduledThreadPool.schedule(task, twoMinutesLater);
    }

    /**
     * 通过AMAP更新PIVI订单履行状态(续费六年)
     *
     * @param vcsOrderCode 订单代码，用于识别特定的订单
     * @param fulfilmentId 履行ID，与订单履行状态相关联
     */
    private void updatePIVIOrderFulfilmentBySixYear(String vcsOrderCode, String fulfilmentId) {
        Date twoMinutesLater = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
        Runnable task = () -> piviFulfilmentService.updatePIVIOrderFulfilmentBySixYear(vcsOrderCode, fulfilmentId);
        subscribeScheduledThreadPool.schedule(task, twoMinutesLater);
    }

    /**
     * 更新续费过程记录(续费六年，一次成功一次失败)
     *
     * @param vcsOrderCode 订单代码，用于识别特定的订单
     * @param fulfilmentId 履行ID，与订单履行状态相关联
     */
    private void updateOrderAmapRecords(String vcsOrderCode, String fulfilmentId) {
        Date twoMinutesLater = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
        Runnable task = () -> piviFulfilmentService.updateOrderAmapRecords(vcsOrderCode, fulfilmentId);
        subscribeScheduledThreadPool.schedule(task, twoMinutesLater);
    }

    /**
     * 更新履约记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFulfilmentRecord(Boolean appd, Boolean unicom, String fulfilmentId, Long jlrSubscriptionId) {
        // jlrSubscriptionId不为空才更新APPD状态
        if (Objects.nonNull(jlrSubscriptionId)) {
            // 记录APPD激活状态
            VcsOrderFufilmentRecords appDRecord = new VcsOrderFufilmentRecords();
            appDRecord.setActivationStatus(appd ? ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus()
                    : ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus());
            vcsOrderFufilmentRecordsMapper.update(appDRecord, new LambdaUpdateWrapper<VcsOrderFufilmentRecords>()
                    .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                    .eq(VcsOrderFufilmentRecords::getServicePackage, ServicePackageEnum.ONLINE_PACK.getPackageName())
                    .eq(BaseDO::getIsDeleted, false));
        }

        // 记录UNICOM激活状态
        VcsOrderFufilmentRecords uniComRecord = new VcsOrderFufilmentRecords();
        uniComRecord.setActivationStatus(unicom ? ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus()
                : ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus());
        vcsOrderFufilmentRecordsMapper.update(uniComRecord, new LambdaUpdateWrapper<VcsOrderFufilmentRecords>()
                .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getServicePackage, ServicePackageEnum.DATA_PLAN.getPackageName())
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 更新履约记录
     */
    public void updateRemoteFulfilmentRecord(String fulfilmentId, boolean result) {
        VcsOrderFufilmentRecords records = new VcsOrderFufilmentRecords();
        records.setActivationStatus(result ? ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus()
                : ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus());
        vcsOrderFufilmentRecordsMapper.update(records, new LambdaUpdateWrapper<VcsOrderFufilmentRecords>()
                .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getServicePackage, ExpirationServiceEnum.REMOTE.getType())
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 更新高德履约失败记录
     */
    public void updateAMapFulfilmentRecord(String fulfilmentId) {
        VcsOrderFufilmentRecords aMapRecord = new VcsOrderFufilmentRecords();
        aMapRecord.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus());
        vcsOrderFufilmentRecordsMapper.update(aMapRecord, new LambdaUpdateWrapper<VcsOrderFufilmentRecords>()
                .eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getServicePackage, ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName())
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 插入Pivi订单履行记录。
     *
     * @param fufilmentMessage 履行消息，包含订单履行的详细信息。
     * @param incontrolVehicleDO 车辆信息，与订单履行相关联的车辆数据对象。
     */
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, String> insertPIVIOrderFulfilment(FufilmentMessage fufilmentMessage, IncontrolVehicleDO incontrolVehicleDO, Integer appDInitStatus) {
        Pair<Boolean, String> pair = orderFulfilmentListener.insertOrderFulfilment(fufilmentMessage, incontrolVehicleDO);
        // 只有OrderFulfilment不存在时，才新增records记录
        if (pair.getLeft()) {
            insertPIVIOrderFulfilmentRecords(pair.getRight(), appDInitStatus);
        }
        return pair;
    }

    /**
     * 插入Remote订单履行记录。
     *
     * @param fufilmentMessage 履行消息，包含订单履行的详细信息。
     * @param incontrolVehicleDO 车辆信息，与订单履行相关联的车辆数据对象。
     */
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, String> insertRemoteOrderFulfilment(FufilmentMessage fufilmentMessage, IncontrolVehicleDO incontrolVehicleDO) {
        Pair<Boolean, String> pair = orderFulfilmentListener.insertOrderFulfilment(fufilmentMessage, incontrolVehicleDO);
        // 只有OrderFulfilment不存在时，才新增records记录
        if (pair.getLeft()) {
            VcsOrderFufilmentRecords fulfilmentRecord = new VcsOrderFufilmentRecords();
            fulfilmentRecord.setFufilmentId(pair.getRight());
            fulfilmentRecord.setServicePackage(ExpirationServiceEnum.REMOTE.getType());
            fulfilmentRecord.setServiceName(ExpirationServiceEnum.REMOTE.getType());
            fulfilmentRecord.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus());
            fulfilmentRecord.setActivationMethod(ActivationMethod.MULTIPLE_ACTIVATION.getMethod());
            vcsOrderFufilmentRecordsMapper.insert(fulfilmentRecord);
        }
        return pair;
    }

    /**
     * 插入订单履行记录
     *
     * @param fulfilmentId 履行ID，用于标识订单的履行记录。
     */
    private void insertPIVIOrderFulfilmentRecords(String fulfilmentId, Integer appDInitStatus) {
        List<VcsOrderFufilmentRecords> fulfilmentRecords = new ArrayList<>();
        buildAppDFulfilmentRecords(fulfilmentId, fulfilmentRecords, appDInitStatus);
        buildUnicomFulfilmentRecords(fulfilmentId, fulfilmentRecords);
        buildAmaPulFulfilmentRecords(fulfilmentId, fulfilmentRecords);
        vcsOrderFufilmentRecordsMapper.insertBatch(fulfilmentRecords);
    }

    /**
     * 根据履行ID构建应用D的履行记录。
     *
     * @param fulfilmentId 履行ID，用于标识履行记录。
     * @param fulfilmentRecordList 履行记录列表，将在此方法中为每个服务名称枚举添加一个新的履行记录。
     */
    private void buildAppDFulfilmentRecords(String fulfilmentId, List<VcsOrderFufilmentRecords> fulfilmentRecordList, Integer appDInitStatus) {
        AppDServiceNameEnum[] serviceNameEnums = AppDServiceNameEnum.values();
        for (AppDServiceNameEnum serviceNameEnum : serviceNameEnums) {
            VcsOrderFufilmentRecords fulfilmentRecord = new VcsOrderFufilmentRecords();
            fulfilmentRecord.setFufilmentId(fulfilmentId);
            fulfilmentRecord.setServicePackage(ServicePackageEnum.ONLINE_PACK.getPackageName());
            fulfilmentRecord.setServiceName(serviceNameEnum.getServiceName());
            fulfilmentRecord.setActivationStatus(appDInitStatus);
            fulfilmentRecord.setActivationMethod(ActivationMethod.MULTIPLE_ACTIVATION.getMethod());
            fulfilmentRecordList.add(fulfilmentRecord);
        }
    }

    /**
     * 构建联通订单履行记录。
     *
     * @param fulfilmentId 履行ID，用于标识唯一的订单履行记录。
     * @param fulfilmentRecordList 履行记录列表，添加构建的履行记录对象到此列表中。
     */
    private void buildUnicomFulfilmentRecords(String fulfilmentId, List<VcsOrderFufilmentRecords> fulfilmentRecordList) {
        VcsOrderFufilmentRecords fulfilmentRecord = new VcsOrderFufilmentRecords();
        fulfilmentRecord.setFufilmentId(fulfilmentId);
        fulfilmentRecord.setServicePackage(ServicePackageEnum.DATA_PLAN.getPackageName());
        fulfilmentRecord.setServiceName(ServiceNameEnum.UNICOM.getServiceName());
        fulfilmentRecord.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus());
        fulfilmentRecord.setActivationMethod(ActivationMethod.MULTIPLE_ACTIVATION.getMethod());
        fulfilmentRecordList.add(fulfilmentRecord);
    }

    /**
     * 构建高德地图服务的履行记录。
     *
     * @param fulfilmentId 履行ID，用于唯一标识履行记录。
     * @param fulfilmentRecordList 履行记录列表，添加构建的履行记录到此列表中。
     */
    private void buildAmaPulFulfilmentRecords(String fulfilmentId, List<VcsOrderFufilmentRecords> fulfilmentRecordList) {
        VcsOrderFufilmentRecords fulfilmentRecord = new VcsOrderFufilmentRecords();
        fulfilmentRecord.setFufilmentId(fulfilmentId);
        fulfilmentRecord.setServicePackage(ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName());
        fulfilmentRecord.setServiceName(ServiceNameEnum.AMAP.getServiceName());
        fulfilmentRecord.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus());
        fulfilmentRecord.setActivationMethod(ActivationMethod.MULTIPLE_ACTIVATION.getMethod());
        fulfilmentRecordList.add(fulfilmentRecord);
    }

    /**
     * 添加一条fulfilment记录
     * @param fufilmentMessage fufilment消息
     * */
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, String> insertOrderFulfilment(FufilmentMessage fufilmentMessage, IncontrolVehicleDO incontrolVehicleDO) {
        VcsOrderFufilmentDO exist = vcsOrderFufilmentDOMapper.selectOne(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                .select(VcsOrderFufilmentDO::getFufilmentId)
                .eq(VcsOrderFufilmentDO::getOrderItemCode, fufilmentMessage.getOrderItemCode())
                .eq(VcsOrderFufilmentDO::getCarVin, fufilmentMessage.getVin())
                .eq(VcsOrderFufilmentDO::getServiceType, fufilmentMessage.getServiceType())
        );
        log.info(" 添加一条fulfilment记录, fulfilmentMessage:{}, incontrolVehicleDO:{}, exist:{}", fufilmentMessage,
                incontrolVehicleDO, exist);
        String fulfilmentId = Objects.isNull(exist) ? ecpIdUtil.nextIdStr() : exist.getFufilmentId();
        if (exist != null) {
            return new ImmutablePair<>(false, fulfilmentId);
        }
        VcsOrderFufilmentDO vcsOrderFufilmentDO = VcsOrderFufilmentDO.builder()
                .fufilmentId(fulfilmentId)
                .tenantId(fufilmentMessage.getTenantId())
                .orderCode(fufilmentMessage.getOrderCode())
                .vcsOrderCode(fufilmentMessage.getVcsOrderCode())
                .orderItemCode(fufilmentMessage.getOrderItemCode())
                .brandCode(incontrolVehicleDO.getBrandCode())
                .carVin(fufilmentMessage.getVin())
                .serviceBeginDate(fufilmentMessage.getServiceBeginDate())
                .serviceEndDate(fufilmentMessage.getServiceEndDate())
                .serviceType(fufilmentMessage.getServiceType())
                .serviceStatus(FufilmentServiceStatusEnum.UNACTIVATED.getStatus())
                .build();
        vcsOrderFufilmentDOMapper.insert(vcsOrderFufilmentDO);
        insertStatistic(incontrolVehicleDO);
        return new ImmutablePair<>(true, fulfilmentId);
    }

    private void insertStatistic(IncontrolVehicleDO incontrolVehicleDO) {
        // sprint52 incontrolId为空不做记录
        if (Objects.isNull(incontrolVehicleDO) || CharSequenceUtil.isBlank(incontrolVehicleDO.getIncontrolId())) {
            return;
        }
        //1.根据vin 查询incontrolId seriesCode 、seriesName
        //2.根据incontrolId  seriesCode 查询对应的car vinList
        Set<String> carVins = incontrolVehicleDOMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                        .eq(IncontrolVehicleDO::getIncontrolId, incontrolVehicleDO.getIncontrolId())
                        .eq(IncontrolVehicleDO::getSeriesCode, incontrolVehicleDO.getSeriesCode())
                        .eq(BaseDO::getIsDeleted, false))
                .stream()
                .map(IncontrolVehicleDO::getCarVin)
                .collect(Collectors.toSet());
        //3.根据incontrolId 和 seriesCode 去Fufilment表里面统计
        Long count = vcsOrderFufilmentDOMapper.selectCount(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                .in(VcsOrderFufilmentDO::getCarVin, carVins)
                .eq(BaseDO::getIsDeleted, false));
        //4.最后入库
        VcsFufilmentStatisticDO vcsFufilmentStatistic = vcsFufilmentStatisticMapper.selectOne(new LambdaQueryWrapperX<VcsFufilmentStatisticDO>()
                .eq(VcsFufilmentStatisticDO::getIncontrolId, incontrolVehicleDO.getIncontrolId())
                .eq(VcsFufilmentStatisticDO::getSeriesCode, incontrolVehicleDO.getSeriesCode())
                .orderByDesc(VcsFufilmentStatisticDO::getId)
                .last("limit 1"));
        //存在数据就更新
        if (vcsFufilmentStatistic == null) {
            vcsFufilmentStatistic = new VcsFufilmentStatisticDO();
            vcsFufilmentStatistic.setBrandCode(incontrolVehicleDO.getBrandCode());
            vcsFufilmentStatistic.setSeriesCode(incontrolVehicleDO.getSeriesCode());
            vcsFufilmentStatistic.setSeriesName(incontrolVehicleDO.getSeriesName());
            vcsFufilmentStatistic.setIncontrolId(incontrolVehicleDO.getIncontrolId());
            vcsFufilmentStatistic.setVcsServiceCount(count.intValue());
            vcsFufilmentStatisticMapper.insert(vcsFufilmentStatistic);
        } else {
            vcsFufilmentStatistic.setVcsServiceCount(count.intValue());
            vcsFufilmentStatisticMapper.updateById(vcsFufilmentStatistic);
        }
    }

    /**
     * 处理Remote履行订单
     * @param fufilmentMessage 履行消息，包含订单信息。
     * @param incontrolVehicleDO 车辆信息，用于关联订单和车辆。
     * @param services 订阅的服务，用于处理服务的激活和回滚。
     */
    private void handleRemoteFulfilment(FufilmentMessage fufilmentMessage, IncontrolVehicleDO incontrolVehicleDO, List<SubscriptionServiceDO> services) {
        //插入履约表信息
        Pair<Boolean, String> pair = orderFulfilmentListener.insertRemoteOrderFulfilment(fufilmentMessage, incontrolVehicleDO);
        // 判断履约记录是否已经存在, 证明为重复消费
        if (!pair.getLeft()) {
            log.info("Remote履约记录已经存在, fulfilmentId:{}", pair.getRight());
            return;
        }
        String fulfilmentId = pair.getRight();
        // 根据履约订单消息，获得车辆信息，服务订阅结束时间，组装数据向TSDP发起服务续约请求
        // 发起调用
        RemoteModifyRespDTO respDTO = remoteCallService.listenerCallTSDPRenew(services, fulfilmentId, fufilmentMessage, incontrolVehicleDO.getIncontrolId());
        updateRemoteFulfilmentRecord(fulfilmentId, respDTO.isSuccess());
    }

}
