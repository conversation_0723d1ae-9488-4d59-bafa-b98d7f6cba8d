package com.jlr.ecp.subscription.excel.listener.search;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.subscription.excel.pojo.search.ExpireBatchSearchExcel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class ExpireSearchCheckListener extends AnalysisEventListener<ExpireBatchSearchExcel> {
    private static final int BATCH_COUNT = 500;

    private static final String VIN_TITLE = "VIN";

    private List<ExpireBatchSearchExcel> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<ExpireBatchSearchExcel>> allDataList = ListUtils.newArrayListWithExpectedSize(0);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private Boolean isExcelFormatError = false;

    private Integer totalSize = 0;

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    @Override
    public void invoke(ExpireBatchSearchExcel expireBatchSearchExcel, AnalysisContext analysisContext) {
        try {
            if (Boolean.FALSE.equals(isExcelFormatError)) {
                checkUploadExcelTitle();
            }
            dataList.add(expireBatchSearchExcel);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.add(dataList);
                totalSize += dataList.size();
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.info("服务到期批量查询解析Excel异常:{}", e.getMessage());
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("服务到期批量查询处理最后剩余的数据");
        if (Boolean.FALSE.equals(isExcelFormatError)) {
            checkUploadExcelTitle();
        }
        try {
            if (CollUtil.isNotEmpty(dataList)) {
                allDataList.add(dataList);
                totalSize += dataList.size();
            }
        } catch (Exception e) {
            log.info("服务到期批量查询处理最后剩余的数据异常:{}", e.getMessage());
        }
        dataList = null;
        log.info("服务到期批量查询所有数据解析完成！");
    }

    public void checkUploadExcelTitle() {
        log.info("检查上传的Excel文件中的, headers:{}", headers);
        if (headers.isEmpty() || !VIN_TITLE.equals(headers.get(0).get(0))) {
            isExcelFormatError = true;
        }
    }
}
