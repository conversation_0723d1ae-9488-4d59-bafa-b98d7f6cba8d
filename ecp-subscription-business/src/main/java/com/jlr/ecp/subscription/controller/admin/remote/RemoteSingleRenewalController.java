package com.jlr.ecp.subscription.controller.admin.remote;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSingleRenewalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteVerifyDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteSinglePageListV0;
import com.jlr.ecp.subscription.service.remote.RemoteSingleRenewalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "单个续费-远程车控")
@RequestMapping("/remote/single/manual/renewal")
@Validated
public class RemoteSingleRenewalController {
    @Resource
    private RemoteSingleRenewalService remoteSingleRenewalService;

    @PostMapping("/pageList")
    @Operation(summary = "远程车控单个续费分页查询")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-single-renewal:forms')")
    public CommonResult<PageResult<RemoteSinglePageListV0>> getRemoteSinglePageList(@RequestBody @Valid RemoteSingleRenewalPageDTO pageDTO) {
        return CommonResult.success(remoteSingleRenewalService.getRemoteSinglePageList(pageDTO));
    }

    @PostMapping("/operate")
    @Operation(summary = "远程车控单个续费操作")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-single-renewal:forms')")
    public CommonResult<String> remoteOperateRenewal(@RequestBody RemoteSingleRenewalDTO remoteSingleRenewalDTO) {
        return remoteSingleRenewalService.remoteSingleOperateRenewal(remoteSingleRenewalDTO);
    }

    @PostMapping("/verify")
    @Operation(summary = "远程车控单个续费参数校验")
    @PreAuthorize("@ss.hasPermission('subscribe:remote-single-renewal:forms')")
    public CommonResult<RemoteVerifyDTO> verify(@RequestBody RemoteSingleRenewalDTO remoteSingleRenewalDTO) {
        return remoteSingleRenewalService.verifyParameters(remoteSingleRenewalDTO);
    }
}
