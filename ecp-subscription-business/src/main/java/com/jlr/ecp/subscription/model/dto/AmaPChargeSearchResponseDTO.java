package com.jlr.ecp.subscription.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class AmaPChargeSearchResponseDTO {
    private String version;

    private String code;

    private String message;

    @JsonProperty("errdetail")
    private String errDetail;

    private long timestamp;

    private boolean result;

    @JsonProperty("data")
    private AmaPChargeSearchDataDTO data;
}
