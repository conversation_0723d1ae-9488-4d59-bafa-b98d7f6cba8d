package com.jlr.ecp.subscription.dal.mysql.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataPageVO;
import com.jlr.ecp.subscription.dal.dataobject.model.VehicleModelMasterDataDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * t_vehicle_model_master_data(VehicleModelMasterData)表数据库访问层
 * <AUTHOR>
 */
@Mapper
public interface VehicleModelMasterDataMapper extends BaseMapperX<VehicleModelMasterDataDO> {

    /**
     * 分页列表
     * @param page
     * @return
     */
    Page<VehicleModelMasterDataPageVO> getPage(Page<VehicleModelMasterDataDO> page);

}
