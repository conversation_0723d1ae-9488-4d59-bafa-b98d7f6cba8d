package com.jlr.ecp.subscription.enums.unicom;

import com.jlr.ecp.subscription.enums.oss.CuResultEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.jlr.ecp.subscription.constant.Constants.ICCID_NOT_EXIST;
import static com.jlr.ecp.subscription.constant.Constants.RENEWAL_INFO_NOT_MATCH;

/**
 * 请求类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomResultEnum {

    SUCCESS("0000", "SUCCESS", CuResultEnum.SUCCESS.getCode(), "成功", ""),
    USER_NOT_EXISTS_OR_CARD_NOT_ACTIVATED("1001", "号卡信息在系统中不存在", CuResultEnum.VIN_NOT_FOUND.getCode(), RENEWAL_INFO_NOT_MATCH, "User is not exists in System or this card is not activated......"),
    SYSTEM_ERROR("1006", "系统异常", CuResultEnum.SYSTEM_ERROR.getCode(), "系统异常", "System error......"),
    REQUEST_ID_MISSING("1013", "请求流水号为空", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "RequestId is missing......"),
    USER_PROCESSING_ORDERS("1013", "该卡存在在途订单", CuResultEnum.SYSTEM_ERROR.getCode(), "已有在途订单，请稍后再试", "The current user is processing orders......"),
    ICCID_LENGTH_INVALID("1016", "iccid长度必须为19位或20位", CuResultEnum.REQUEST_ERROR.getCode(), ICCID_NOT_EXIST, "IccId length must be between 19 bits and 20 bits......"),
    ICCID_MISSING("1017", "iccid不能为空", CuResultEnum.REQUEST_ERROR.getCode(), ICCID_NOT_EXIST, "SimCard or Iccid is missing......"),
    ICCID_NOT_EXISTS("1066", "号卡信息在系统中不存在", CuResultEnum.VIN_NOT_FOUND.getCode(), ICCID_NOT_EXIST, "iccid is not exists......"),
    REQUEST_TYPE_INVALID("1078", "请求类型不合法", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "the request_type is invalid"),
    ICCID_MSISDN_NULL("1077", "iccId和msisdn不能都为空", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "iccId and msisdn can not be null ......"),
    PRODUCT_BOOK_INFO_EMPTY("1079", "产品订购信息为空", CuResultEnum.REQUEST_ERROR.getCode(), RENEWAL_INFO_NOT_MATCH, "the product_book_info is empty"),
    DATE_FORMAT_INVALID("1080", "日期格式不合法", CuResultEnum.REQUEST_ERROR.getCode(), RENEWAL_INFO_NOT_MATCH, "date format is invalid"),
    PRODUCT_ID_INVALID("1081", "产品ID不合法", CuResultEnum.REQUEST_ERROR.getCode(), RENEWAL_INFO_NOT_MATCH, "product_id is invalid"),
    EX_BOOK_ID_ICCID_MISMATCH("1082", "订购ID和卡号不匹配", CuResultEnum.SYSTEM_ERROR.getCode(), RENEWAL_INFO_NOT_MATCH, "ex_book_id not match the iccid"),
    BOOK_STATUS_INVALID("1083", "订购状态不合法", CuResultEnum.SYSTEM_ERROR.getCode(), RENEWAL_INFO_NOT_MATCH, "book_status is invalid"),
    CARD_NOT_EPSIM("1084", "该卡是非EPSIM卡", CuResultEnum.SYSTEM_ERROR.getCode(), "该卡是非EPSIM卡", "the card is not epsim, can not do this business"),
    INDUSTRY_CODE_NULL("0001", "parameter error: industry_code is null", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: industry_code is null"),
    INDUSTRY_CODE_INVALID("0001", "parameter error: industry_code is invalid", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: industry_code is invalid"),
    METHOD_NULL("0001", "parameter error: method is null", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: method is null"),
    TIMESTAMP_NULL("0001", "parameter error: timestamp is null", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: timestamp is null"),
    REQUEST_DATA_NULL("0001", "parameter error: request_data is null", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: request_data is null"),
    SIGN_DATA_NULL("0001", "parameter error: sign_data is null", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: sign_data is null"),
    SIGN_DATA_MISMATCH("0001", "parameter error: sign_data is not match, please check your appkey", CuResultEnum.REQUEST_ERROR.getCode(), "请求异常", "parameter error: sign_data is not match, please check your appkey");
    /**
     * 根据错误描述获取对应的EcpDisplay值
     *
     * @param errorDescription 错误描述，用于在UnicomResultEnum中查找对应的枚举项
     * @return 如果找到匹配的枚举项，则返回该枚举项的EcpDisplay值；否则返回空字符串
     */
    public static String getEcpDisplayByErrorDescription(String errorDescription) {
        UnicomResultEnum[] resultEnums = UnicomResultEnum.values();
        for (UnicomResultEnum resultEnum : resultEnums) {
            if (resultEnum.getErrorDescription().equals(errorDescription)) {
                return resultEnum.getEcpDisplay();
            }
        }
        return "";
    }

    /**
     * 类型 对应联通返回的 1.Error code
     */
    public final String code;

    /**
     * 描述 对应联通返回的 3.Explanation
     */
    public final String desc;

    /**
     * CU查询结果枚举
     */
    public final Integer resultCode;

    /**
     * ecp续费展示描述 对应联通返回的 4.ECP Display
     */
    public final String ecpDisplay;

    /**
     * 错误描述 对应联通返回的 2.Error description
     */
    public final String errorDescription;



    /**
     * 通过错误码获取联通校验结果
     */
    public static Integer getCuResultByCode(String code) {
        UnicomResultEnum[] resultEnums = UnicomResultEnum.values();
        for (UnicomResultEnum resultEnum : resultEnums) {
            if (resultEnum.getCode().equals(code)) {
                return resultEnum.getResultCode();
            }
        }
        return null;
    }

    /**
     * 根据代码获取对应的EcpDisplay值
     *
     * @param code 枚举代码，用于在UnicomResultEnum中查找对应的枚举项
     * @return 如果找到匹配的枚举项，则返回该枚举项的EcpDisplay值；否则返回空字符串
     */
    public static String getEcpDisplayByCode(String code) {
        UnicomResultEnum[] resultEnums = UnicomResultEnum.values();
        for (UnicomResultEnum resultEnum : resultEnums) {
            if (resultEnum.getCode().equals(code)) {
                return resultEnum.getEcpDisplay();
            }
        }
        return "";
    }

    /**
     * 根据描述获取对应的EcpDisplay值
     *
     * @param desc 枚举描述，用于在UnicomResultEnum中查找对应的枚举项
     * @return 如果找到匹配的枚举项，则返回该枚举项的EcpDisplay值；否则返回空字符串
     */
    public static String getEcpDisplayByDesc(String desc) {
        UnicomResultEnum[] resultEnums = UnicomResultEnum.values();
        for (UnicomResultEnum resultEnum : resultEnums) {
            if (resultEnum.getDesc().equals(desc)) {
                return resultEnum.getEcpDisplay();
            }
        }
        return "";
    }

    public static void main(String[] args) {
        String errorDescription = "User is not exists in System or this card is not activated......";
        String ecpDisplay = UnicomResultEnum.getEcpDisplayByErrorDescription(errorDescription);
        System.out.println(ecpDisplay); // 输出: 续费信息无法匹配
    }
}
