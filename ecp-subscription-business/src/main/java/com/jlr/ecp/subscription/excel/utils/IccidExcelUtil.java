package com.jlr.ecp.subscription.excel.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyExcel;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyResultExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class IccidExcelUtil {

    private static final String EXCEL_FORMATTER = ".xlsx";
    private static final int BATCH_COUNT = 500;

    /**
     * 批量写入ICCID批量修改的源文件
     *
     * @param iccidBatchModifyExcelList 包含ICCID批量修改数据的列表
     * @return 如果结果列表不为空，则返回写入后的文件；否则返回null
     */
    public static File iccidBatchModifyWriteSourceFile(List<IccidBatchModifyExcel> iccidBatchModifyExcelList) {
        if (CollectionUtils.isEmpty(iccidBatchModifyExcelList)) {
            log.info("批量写入ICCID批量修改源文件为空");
            return null;
        }
        List<List<IccidBatchModifyExcel>> groupList = new ArrayList<>();
        for (int i = 0; i < iccidBatchModifyExcelList.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, iccidBatchModifyExcelList.size());
            List<IccidBatchModifyExcel> group = iccidBatchModifyExcelList.subList(i, endIdx);
            groupList.add(group);
        }
        return iccidBatchWriteSourceFileByGroup(groupList);
    }

    /**
     * 按组批量写入ICCID批量修改的源文件
     *
     * @param iccidBatchModifyExcelList 包含多个ICCID批量修改Excel列表的集合，每个列表代表一个组
     * @return 返回生成的临时文件，如果输入列表为空或发生异常，则返回null
     */
    public static File iccidBatchWriteSourceFileByGroup(List<List<IccidBatchModifyExcel>> iccidBatchModifyExcelList) {
        if (CollectionUtils.isEmpty(iccidBatchModifyExcelList)) {
            return null;
        }
        String fileName = "IccidBatchModifyExcel" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, IccidBatchModifyExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("IccidBatchModifyExcel").build();
                for (List<IccidBatchModifyExcel> data : iccidBatchModifyExcelList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入ICCID批量修改原始Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime - startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("写入ICCID批量修改原始Excel文件，数据异常:", e);
        }
        return null;
    }

    /**
     * 批量写入ICCID批量修改的结果文件
     *
     * @param resultExcels 包含ICCID批量修改结果的列表
     * @return 返回写入结果的文件，如果输入列表为空，则返回null
     */
    public static File iccidBatchModifyWriteResultFile(List<IccidBatchModifyResultExcel> resultExcels) {
        if (CollectionUtils.isEmpty(resultExcels)) {
            log.info("批量写入ICCID批量修改结果文件为空");
            return null;
        }
        List<List<IccidBatchModifyResultExcel>> groupList = new ArrayList<>();
        for (int i = 0; i < resultExcels.size(); i += BATCH_COUNT) {
            int endIdx = Math.min(i + BATCH_COUNT, resultExcels.size());
            List<IccidBatchModifyResultExcel> group = resultExcels.subList(i, endIdx);
            groupList.add(group);
        }
        return iccidBatchWriteResultFileByGroup(groupList);
    }

    /**
     * 按组批量写入ICCID批量修改的结果到Excel文件
     *
     * @param iccidBatchModifyExcelList 包含多个批次ICCID批量修改结果的列表，如果列表为空或null，则返回null
     * @return 成功创建的临时Excel文件，如果写入过程中发生异常或输入列表为空，则返回null
     */
    public static File iccidBatchWriteResultFileByGroup(List<List<IccidBatchModifyResultExcel>> iccidBatchModifyExcelList) {
        if (CollectionUtils.isEmpty(iccidBatchModifyExcelList)) {
            return null;
        }
        String fileName = "IccidBatchModifyResultExcel" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, IccidBatchModifyResultExcel.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("IccidBatchModifyResultExcel").build();
                for (List<IccidBatchModifyResultExcel> data : iccidBatchModifyExcelList) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入ICCID批量修改结果Excel文件，花费时间:{}毫秒, tempFile:{}", (endTime - startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("写入ICCID批量修改结果Excel文件，数据异常:", e);
        }
        return null;
    }
}