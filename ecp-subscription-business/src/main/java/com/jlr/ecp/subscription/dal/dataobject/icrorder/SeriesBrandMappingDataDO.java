package com.jlr.ecp.subscription.dal.dataobject.icrorder;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_series_brand_mapping_data
 *
 * <AUTHOR>
 * @TableName t_series_brand_mapping_data
 */
@TableName(value = "t_series_brand_mapping_data")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SeriesBrandMappingDataDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 车型系列编码;车型系列编码
     */
    @TableField(value = "series_code")
    private String seriesCode;

    /**
     * 车型系列中文名;车型系列中文名
     */
    @TableField(value = "dp_series_name")
    private String dpSeriesName;

    /**
     * 车型系列中文名;车型系列中文名
     */
    @TableField(value = "series_name")
    private String seriesName;

    /**
     * 品牌显示名;品牌显示名
     */
    @TableField(value = "brand_name_view")
    private String brandNameView;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

}