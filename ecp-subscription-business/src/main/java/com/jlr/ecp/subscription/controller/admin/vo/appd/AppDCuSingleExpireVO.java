package com.jlr.ecp.subscription.controller.admin.vo.appd;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "信息娱乐及网络流量单个续费二次弹窗VO")
public class AppDCuSingleExpireVO {
    @Schema(description = "车辆编号")
    private String vin;

    @Schema(description = "服务名称")
    private String service;

    @Schema(description = "到期时间")
    private String oldDate;
}
