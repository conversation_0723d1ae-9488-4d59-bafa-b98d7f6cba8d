package com.jlr.ecp.subscription.controller.admin.vehicle;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.controller.admin.dto.vehicle.ConsumerInfoVO;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.service.icrorder.IncontrolVehicleDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * icr vehicle 信息
 *
 * <AUTHOR>
 */
@Tag(name = "订单管理 - 代客下单")
@RestController
@RequestMapping("v1/icr/vehicle")
@Validated
@Slf4j
public class IcrVehicleController {
    @Resource
    private IncontrolVehicleDOService incontrolVehicleDOService;
    @Resource
    private VcsOrderFufilmentDOService vcsOrderFufilmentDOService;


    @GetMapping("/searchVinInfo")
    @Operation(summary = "通过vin or ICR 搜索 客户、车辆、服务信息")
    @PreAuthorize("@ss.hasPermission('trade:order:create')")
    CommonResult<ConsumerInfoVO> searchVinInfo(@RequestParam("carVin") String carVin,
                                               @RequestParam("icr") String icr) {

        return incontrolVehicleDOService.searchVinInfo(carVin,icr);
    }

    @PostMapping("/checkCanBuyServiceForPC")
    @Operation(summary = "代客下单校验")
    @PreAuthorize("@ss.hasPermission('trade:order:create')")
    CommonResult<Boolean> checkCanBuyServiceForPC(@RequestBody List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList){
        return vcsOrderFufilmentDOService.checkCanBuyServiceForPC(carVinAndServiceTypeList);
    }

}
