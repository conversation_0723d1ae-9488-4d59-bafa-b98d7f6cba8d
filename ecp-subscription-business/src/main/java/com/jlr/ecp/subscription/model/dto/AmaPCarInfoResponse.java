package com.jlr.ecp.subscription.model.dto;

import cn.hutool.core.annotation.Alias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AmaPCarInfoResponse {

    /**
     * 编码
     */
    private String code;

    /**
     * err详细信息
     */
    @Alias("errdetail")
    private String errDetail;

    /**
     * 消息
     * */
    private String message;

    /**
     * 结果
     */
    private  Boolean result;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 版本
     */
    private String version;

    /**
     * 数据
     */
    private AmaPCarData data;

    /**
     * oss查询结果
     */
    private String queryResult;
}
