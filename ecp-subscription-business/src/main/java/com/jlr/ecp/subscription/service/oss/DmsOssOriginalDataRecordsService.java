package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;

import java.util.List;
import java.util.Set;

/**
 * OSS原本数据处理记录(DmsOssOriginalDataRecords)表服务接口
 *
 * <AUTHOR>
 */
public interface DmsOssOriginalDataRecordsService {

    void insertBatch(List<DmsOssOriginalDataRecordsDO> recordsDOList);


    /**
     * 查询过期同步结果
     * @param vinSet vinSet
     */
    List<DmsOssOriginalDataRecordsDO> queryByVinSet(Set<String> vinSet);

    void updateBatch(List<DmsOssOriginalDataRecordsDO> recordsDOList);

    void update(DmsOssOriginalDataRecordsDO dataRecordsDO);

    void insert(DmsOssOriginalDataRecordsDO dataRecordsDO);
}

