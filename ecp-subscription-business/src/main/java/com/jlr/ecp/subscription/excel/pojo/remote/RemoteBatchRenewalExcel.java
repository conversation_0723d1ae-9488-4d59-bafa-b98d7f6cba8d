package com.jlr.ecp.subscription.excel.pojo.remote;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = false)
public class RemoteBatchRenewalExcel {
    @ExcelProperty("VIN")
    private String carVin;

    @ExcelProperty("远程车控服务续期日期(yyyy/mm/dd)")
    private String renewalDate;
}
