package com.jlr.ecp.subscription.api.vininit.dto.parse;

import lombok.Data;

/**
 * 绑定客户信息DTO
 */
@Data
public class BoundToCustomerDTO {
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 用户ID
     */
    private String userid;
    
    /**
     * 地址信息
     */
    private AddressDTO address;
    
    /**
     * 姓氏
     */
    private String surname;
    
    /**
     * 语言
     */
    private String language;
    
    /**
     * 名字
     */
    private String firstName;
}
