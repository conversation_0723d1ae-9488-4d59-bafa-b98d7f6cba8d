package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;

import java.util.Collection;
import java.util.List;

/**
 * 客户信息Repository接口
 *
 */
public interface IncontrolCustomerRepository extends IService<IncontrolCustomerDO> {

    /**
     * 批量保存或更新客户信息
     *
     * @param entities 客户信息实体集合
     * @return 是否成功
     */
    boolean saveOrUpdateBatch(Collection<IncontrolCustomerDO> entities);

    /**
     * 根据InControl ID查询客户信息
     *
     * @param incontrolId InControl ID
     * @return 客户信息
     */
    IncontrolCustomerDO findByIncontrolId(String incontrolId);

    /**
     * 根据InControl ID列表查询客户信息
     *
     * @param incontrolIdList InControl ID列表
     * @return 客户信息列表
     */
    List<IncontrolCustomerDO> findByIncontrolIdList(List<String> incontrolIdList);

    /**
     * 插入单个客户信息
     *
     * @param customerDO 客户信息
     * @return 插入数量
     */
    int insert(IncontrolCustomerDO customerDO);

    /**
     * 更新客户信息
     *
     * @param customerDO 客户信息
     * @return 更新数量
     */
    boolean updateById(IncontrolCustomerDO customerDO);
}
