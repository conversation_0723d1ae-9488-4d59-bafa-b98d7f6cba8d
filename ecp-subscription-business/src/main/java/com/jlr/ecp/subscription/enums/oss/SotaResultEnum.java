package com.jlr.ecp.subscription.enums.oss;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SOTA查询结果枚举
 */
@AllArgsConstructor
@Getter
public enum SotaResultEnum {

    SUCCESS(1, "是"),

    EMPTY_DATA(2, "否，无数据返回"),

    EMPTY_ICCID(3, "否，ICCID为空");

    /**
     * 类型
     * */
    public final Integer code;

    /**
     * 描述
     * */
    public final String desc;


    public static String getDescByCode(Integer code){
        if (code == null) {
            return "-";
        }
        for (SotaResultEnum status : SotaResultEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }
}
