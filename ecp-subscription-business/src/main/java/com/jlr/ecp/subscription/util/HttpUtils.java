package com.jlr.ecp.subscription.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.ssl.SSLSocketFactoryBuilder;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jlr.ecp.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.oss.RecordResultEnum;
import com.jlr.ecp.subscription.util.token.AccessTokenResponse;
import com.jlr.ecp.subscription.util.token.AccessTokenUtil;
import com.jlr.ecp.subscription.util.token.JEEWeiXinX509TrustManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.net.ssl.SSLSocketFactory;
import java.time.Instant;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;


@Component
@Slf4j
public class HttpUtils {

    public static final String APIM_AUTH_TOKEN = "apimAuthToken";



    @Value("${apimService.apiKey}")
    private String apimServiceApiKey;



    @Resource
    private AccessTokenUtil accessTokenUtil;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    private static final Integer ACCESS_TOKEN_EXPIRE_TIME = 290;

    private static final Integer TIME_OUT = 30 * 1000;



    /**
     * post调用apim
     * @param url
     * @param reqObject
     * @param headerMap
     * @return
     */
    public JSONObject callApimServiceForPost(String url, Object reqObject, Map<String, String> headerMap, String subffixUrl) {
        log.info("调用apim转发请求入参,url = {},reqObject={}", url, reqObject);
        try {
            String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000);
            //String sign = SignatureUtil.generateSignature("POST", subffixUrl, timestamp, apimServiceApiSecret);
            headerMap.put("auth-token","Bearer " + getAccessToken());
            headerMap.put("Timestamp", timestamp);
            headerMap.put("X-API-Key", apimServiceApiKey);
            //headerMap.put("X-Signature", sign);
            SSLSocketFactory build = SSLSocketFactoryBuilder.create()
                    .setTrustManagers(new JEEWeiXinX509TrustManager()) //设置信任证书
                    .build();
            log.info("调用apim转发请求开始,url={},headerMap={},reqObject={}",url,headerMap,reqObject);
            HttpRequest httpRequest = HttpRequest.post(url).timeout(TIME_OUT).setSSLSocketFactory(build).body(reqObject.toString());
            HttpResponse response = httpRequest.headerMap(headerMap, false).execute();
            int status = response.getStatus();
            if (HttpStatus.HTTP_OK != status) {
                log.error("调用apim转发请求返回http status不为200,status = {}, body = {}", status, response.body());
                return null;
            }
            String body = response.body();
            log.info("调用apim转发请求出参为,body={}", body);
            JSONObject bodyJsonObject = JSONUtil.parseObj(body);
            // ecp成功code=0，cdt成功code=200
            if (!Objects.equals(GlobalErrorCodeConstants.SUCCESS.getCode(),bodyJsonObject.getInt("code"))
                    && !Objects.equals(GlobalErrorCodeConstants.SUCCESS.getCode(),bodyJsonObject.getInt("status"))) {
                log.error("调用apim转发请求返回的body里的状态是失败,body={}", body);
                return bodyJsonObject;
            }
            return bodyJsonObject;

        } catch (Exception e) {
            log.error("调用apim转发请求异常", e);
            return null;
        }
    }

    /**
     * 调用APIM转发
     * @param url
     * @param headerMap
     * @param subffixUrl
     * @return
     */
    public SOTAResultVO callApimServiceForGet(String url, Map<String, String> headerMap, String subffixUrl) {
        log.info("调用APIM转发请求入参,url = {},headerMap={},subffixUrl={}",url,headerMap,subffixUrl);
        SOTAResultVO resultVO = new SOTAResultVO();
        try {
            String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000);
            //String sign = SignatureUtil.generateSignature("GET", subffixUrl, timestamp, apimServiceApiSecret);
            headerMap.put("auth-token","Bearer " + getAccessToken());
            headerMap.put("Timestamp", timestamp);
            headerMap.put("X-API-Key", apimServiceApiKey);
            //headerMap.put("X-Signature", sign);
            SSLSocketFactory build = SSLSocketFactoryBuilder.create()
                    .setTrustManagers(new JEEWeiXinX509TrustManager()) //设置信任证书
                    .build();
            log.info("调用apim转发请求开始,url={},headerMap={}",url,headerMap);
            HttpRequest httpRequest = HttpRequest.get(url).setSSLSocketFactory(build).timeout(TIME_OUT);
            HttpResponse response = httpRequest.headerMap(headerMap, false).execute();
            int status = response.getStatus();
            if (HttpStatus.HTTP_OK != status) {
                log.error("调用apim转发请求返回http status不为200,status = {}, body = {}", status, response.body());
                resultVO.setQueryResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), response.body()));
                return resultVO;
            }
            String body = response.body();
            log.info("调用APIM转发请求出参为,body={}", body);
            JSONObject bodyJsonObject = JSONUtil.parseObj(body);

            return JSONUtil.toBean(bodyJsonObject, SOTAResultVO.class);

        } catch (Exception e) {
            log.error("调用APIM转发请求异常", e);
            resultVO.setQueryResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), e.getMessage()));
            return resultVO;
        }
    }


    /**
     * 获取accessToken
     * @return
     */
    private String getAccessToken(){
        ValueOperations<String, String> valueOperations = stringRedisTemplate.opsForValue();
        Boolean exists = stringRedisTemplate.hasKey(APIM_AUTH_TOKEN);
        String accessToken = "";
        if (exists != null && !exists) {
            // 不存在则设值并设置过期时间
            accessToken = getToken();
            log.info("apim提供的token, {}", accessToken);
            if(StringUtils.isBlank(accessToken)){
                throw exception(ErrorCodeConstants.GET_TOKEN_EXCEPTION);
            }
            valueOperations.set(APIM_AUTH_TOKEN,accessToken,ACCESS_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
        } else {
            // 存在则直接取值
            accessToken = valueOperations.get(APIM_AUTH_TOKEN);
            log.info("redis取的token, {}", accessToken);
        }
        return accessToken;
    }

    /**
     * 获取token
     */
    public String getToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.error("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }


    public String makeSignature(String key, String timestamp, String nonce) {
        String str = generateStr(key, timestamp, nonce);
        return HmacUtils.hmacSha256Hex(key, str.replaceAll("\\s+", ""));
    }

    /**
     * 签名待处理的字符串拼接
     */
    public static String generateStr(String key, String timestamp, String nonce) {
        String[] array = new String[]{key, timestamp, nonce};
        StringBuffer sb = new StringBuffer();
        // 字符串排序
        Arrays.sort(array);
        for (int i = 0; i < 3; i++) {
            sb.append(array[i]);
        }
        return sb.toString();
    }
}

