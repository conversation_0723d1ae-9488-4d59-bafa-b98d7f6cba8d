package com.jlr.ecp.subscription.enums.fufil;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * 服务包名枚举
 */
@AllArgsConstructor
@Getter
public enum ServicePackageEnum {
    ONLINE_PACK("ONLINE-PACK", "ONLINE-PACK","信息娱乐服务"),
    CONNECTED_NAVIGATION("CONNECTED-NAVIGATION", "CONNECTED-NAVIGATION","实时交通信息"),
    DATA_PLAN("DATA-PLAN", "DATA-PLAN","网络流量");

    /**
     * 名称
     * */
    public final String packageName;

    /**
     * 描述
     * */
    public final String desc;

    /**
     * 中文描述
     */
    public final String descCN;

    /**
     * 获取中文描述
     * @param servicePkgName
     * @return
     */
    public static String getDescCN(String servicePkgName){
        if (StrUtil.isEmpty(servicePkgName)){
            return null;
        }
        for (ServicePackageEnum value : values()) {
            if (value.getPackageName().equals(servicePkgName)){
                return value.getDescCN();
            }
        }
        return null;
    }
}
