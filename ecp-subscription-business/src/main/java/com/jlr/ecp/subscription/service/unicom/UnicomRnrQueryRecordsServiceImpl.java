package com.jlr.ecp.subscription.service.unicom;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.unicom.dto.UnicomBatchQueryPageDTO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomRnrBatchQueryListVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRealNameOrder;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomRnrQueryRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomRnrQueryRecordsMapper;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.unicom.*;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnicomRnrQueryRecordsServiceImpl implements UnicomRnrQueryRecordsService {


    @Resource
    private UnicomRnrQueryRecordsMapper unicomRnrQueryRecordsMapper;

    @Override
    public List<Long> selectQueryRecordsByBatchNo(Long batchNo){
        List<UnicomRnrQueryRecordsDO> list = unicomRnrQueryRecordsMapper.selectList(new LambdaQueryWrapperX<UnicomRnrQueryRecordsDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(UnicomRnrQueryRecordsDO::getQueryStatus,UnicomRnrQueryStatusEnum.QUERYING.getType())
                .eq(UnicomRnrQueryRecordsDO::getBatchNo, batchNo));
        return list.stream().map(UnicomRnrQueryRecordsDO::getId).collect(Collectors.toList());
    }

    @Override
    public PageResult<UnicomRnrBatchQueryListVO> getBatchFilePage(UnicomBatchQueryPageDTO pageDTO) {
        log.info("UnicomBatchQueryPageDTO :{}",pageDTO);
        Page<UnicomRnrQueryRecordsDO> pageParam = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LambdaQueryWrapperX<UnicomRnrQueryRecordsDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UnicomRnrQueryRecordsDO::getIsDeleted, false);

        PageResult<UnicomRnrBatchQueryListVO> newPageResult = new PageResult<>();

        //查询条件
        queryWrapper.inIfPresent(UnicomRnrQueryRecordsDO::getBatchNo,pageDTO.getBatchNoList());
        if (SortTypeEnum.ASC.getSortType().equals(pageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(UnicomRnrQueryRecordsDO::getCreatedTime);
            queryWrapper.orderByAsc(UnicomRnrQueryRecordsDO::getId);
        } else {
            queryWrapper.orderByDesc(UnicomRnrQueryRecordsDO::getCreatedTime);
            queryWrapper.orderByDesc(UnicomRnrQueryRecordsDO::getId);
        }
        if (StringUtils.isNotBlank(pageDTO.getStartTime()) && StringUtils.isNotBlank(pageDTO.getStartTime())) {
            LocalDateTime startTime = SubscribeTimeFormatUtil.stringToTimeByFormat(pageDTO.getStartTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            LocalDateTime endTime = SubscribeTimeFormatUtil.stringToTimeByFormat(pageDTO.getEndTime(),
                    SubscribeTimeFormatUtil.FORMAT_2);
            queryWrapper.ge(UnicomRnrQueryRecordsDO::getCreatedTime, startTime)
                    .le(UnicomRnrQueryRecordsDO::getCreatedTime, endTime);
        }
        queryWrapper.eqIfPresent(BaseDO::getCreatedBy,pageDTO.getOperator());
        queryWrapper.eqIfPresent(UnicomRnrQueryRecordsDO::getQueryStatus,pageDTO.getQueryStatus());
        queryWrapper.eqIfPresent(UnicomRnrQueryRecordsDO::getCarVin,pageDTO.getCarVin());
        Page<UnicomRnrQueryRecordsDO> pageResult = unicomRnrQueryRecordsMapper.selectPage(pageParam, queryWrapper);


        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageResult<>();
        }
        //转化返回数据
        List<UnicomRnrQueryRecordsDO> records = pageResult.getRecords();
        List<UnicomRnrBatchQueryListVO> pageListV0List = records.stream().map(vo->{
            UnicomRnrBatchQueryListVO unicomRnrBatchQueryListVO = new UnicomRnrBatchQueryListVO();
            BeanUtils.copyProperties(vo,unicomRnrBatchQueryListVO);
            if(!UnicomRnrQueryStatusEnum.SUCCESS.getType().equals(vo.getQueryStatus())){
                unicomRnrBatchQueryListVO.setIccid(null);
            }
            unicomRnrBatchQueryListVO.setQueryStatusTxt(UnicomRnrQueryStatusEnum.getDescriptionByCode(vo.getQueryStatus()));
            unicomRnrBatchQueryListVO.setOperator(vo.getCreatedBy());
            unicomRnrBatchQueryListVO.setCardStateTxt(UnicomCardStateEnum.getDescriptionByCode(vo.getCardState()));
            unicomRnrBatchQueryListVO.setFailedTypeTxt(UnicomRnrQueryFailedTypeEnum.getDescriptionByCode(vo.getFailedType()));
            unicomRnrBatchQueryListVO.setRealNameFlagTxt(UnicomRealnameFlagEnum.getDescriptionByCode(vo.getRealNameFlag()));
            unicomRnrBatchQueryListVO.setRealNameUpdateTime("-");
            if(UnicomRealnameFlagEnum.TRUE.getCode().equals(String.valueOf(vo.getRealNameFlag())) ){
                //过滤realNameOrderList时间倒叙排，取值状态为active的第一条
                if(vo.getRealNameUpdateTime() == null){
                    unicomRnrBatchQueryListVO.setRealNameUpdateTime(UnicomRealnameFlagEnum.FAIL.getDesc());
                }else {
                    //将unicomRealNameOrder.getOrderTime()时间戳字符串转换成yyyy/MM/dd HH:mm:ss格式的字符串
                    unicomRnrBatchQueryListVO.setRealNameUpdateTime(TimeFormatUtil.timeToStringByFormat(vo.getRealNameUpdateTime(), TimeFormatUtil.formatter_6));
                }
            }
            unicomRnrBatchQueryListVO.setOperateTime(
                    SubscribeTimeFormatUtil.timeToStringByFormat(vo.getCreatedTime(),
                            SubscribeTimeFormatUtil.FORMAT_2));
            return unicomRnrBatchQueryListVO;
        }).collect(Collectors.toList());
        newPageResult.setTotal(pageResult.getTotal());
        newPageResult.setList(pageListV0List);
        return newPageResult;
    }
}