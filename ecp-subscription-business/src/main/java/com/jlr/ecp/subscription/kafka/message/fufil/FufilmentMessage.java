package com.jlr.ecp.subscription.kafka.message.fufil;

import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 服务通知消息
 *
 * <AUTHOR>
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FufilmentMessage extends BaseMessage {

    /**
     * 订单item编码;订单item编码
     */
    private String orderItemCode;

    /**
     * 服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；
     */
    private Integer serviceType;

    /**
     * 服务起始时间;服务起始时间
     */
    private LocalDateTime serviceBeginDate;

    /**
     * 高德服务结束时间;高德服务结束时间
     */
    private LocalDateTime aMapServiceEndDate;
}
