package com.jlr.ecp.subscription.enums.redis;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * RedisDelayQueueEnum
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum RedisDelayQueueEnum {

    /**
     * 添加一个新的枚举项来表示商品定时上架的任务。
     */
    // ECP_SUBSCRIPTION_SCHEDULED_LAUNCH("ECP-SUBSCRIPTION-SCHEDULED-LAUNCH", "ECP-SUBSCRIPTION定时任务", "scheduledLaunchBean"),
    ECP_ICR_TSDP_QRY_RETRY_TASK("ECP_ICR_TSDP_QRY_RETRY_TASK", "ECP-icr订阅重试任务", "ecpIcrTsdpQryRetryTask");

    /**
     * 延迟队列 Redis Key
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String name;

    /**
     * 延迟队列具体业务实现的 Bean
     * 可通过 Spring 的上下文获取
     */
    private String beanId;

}
