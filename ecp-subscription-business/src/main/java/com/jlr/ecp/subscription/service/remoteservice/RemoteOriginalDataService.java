package com.jlr.ecp.subscription.service.remoteservice;

import com.jlr.ecp.subscription.api.vininit.dto.parse.VinInitParseErrorDTO;

import java.util.List;
import java.util.Set;

public interface RemoteOriginalDataService {

    /**
     * 批量更新指定id的数据为成功
     * @param ids
     */
    void updateDataSuccess(Set<Long> ids);


    /**
     * 批量更新指定id的数据为失败
     * @param ids
     */
    void updateDataFail(Set<Long> ids,String failType,String failDesc);

    /**
     * 批量更新指定id的数据为失败
     */
    void updateDateFailByParse(List<VinInitParseErrorDTO> list, String failType);
}
