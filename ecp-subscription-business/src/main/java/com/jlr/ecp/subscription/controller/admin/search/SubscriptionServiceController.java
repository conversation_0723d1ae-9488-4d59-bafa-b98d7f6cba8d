package com.jlr.ecp.subscription.controller.admin.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ExpireResultQueryDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ServiceBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.SearchExpireUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceExpireQueryResultVO;
import com.jlr.ecp.subscription.service.search.VehicleExpireBatchSearchService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "平台管理 - 查询中心")
@RestController
@RequestMapping("/search/center")
@Validated
@Slf4j
public class SubscriptionServiceController {

    @Resource
    SubscriptionService subscriptionService;

    @Resource
    VehicleExpireBatchSearchService expireBatchSearchService;

    @GetMapping("/getExpireDateByVin")
    @Operation(summary = "到期服务日期查询")
    @Parameter(name = "carVin", description = "vin号")
    @PreAuthorize("@ss.hasPermission('query:serviceenddate:forms')")
    public CommonResult<SubscriptionSearchResultDTO> getExpireDateByVin(@RequestParam String carVin) {
        log.info("getExpireDateByVin request incoming ~~~~~");
        return CommonResult.success(subscriptionService.getExpireDateByVin(carVin));
    }

    @GetMapping("/getExpireDateByVinDeeply")
    @Operation(summary = "到期服务日深度查询")
    @Parameter(name = "carVin", description = "vin号")
    @PreAuthorize("@ss.hasPermission('query:serviceenddate:forms')")
    public CommonResult<SubscriptionSearchResultDTO> getExpireDateByVinDeeply(@RequestParam String carVin) {
        log.info("getExpireDateByVinDeeply request incoming ~~~~~");
        return CommonResult.success(subscriptionService.getExpireDateByVinDeeply(carVin));
    }

    @GetMapping("/expire/download/template")
    @Operation(summary = "下载服务到期日批量查询的模板")
    @PreAuthorize("@ss.hasPermission('query:multiple-service-enddate:forms')")
    public CommonResult<String> downloadTemplateUrl() {
        log.info("获取车辆到期日批量查询的模板");
        String templateUrl = expireBatchSearchService.getVehicleExpireTemplateUrl();
        return CommonResult.success(templateUrl);
    }

    @PostMapping("/expire/queryBatchPageList")
    @Operation(summary = "批量查询续分页列表")
    @PreAuthorize("@ss.hasPermission('query:multiple-service-enddate:forms')")
    public CommonResult<PageResult<ServiceBatchQueryPageVO>> queryBatchServicePageList(@RequestBody @Valid ServiceBatchQueryPageDTO batchQueryPageDTO) {
        return CommonResult.success(expireBatchSearchService.queryBatchServicePageList(batchQueryPageDTO));
    }

    @PostMapping("/expire/batch/query")
    @Operation(summary = "上传批量查询Excel文件并查询")
    @PreAuthorize("@ss.hasPermission('query:multiple-service-enddate:forms')")
    public CommonResult<SearchExpireUploadVO> uploadAndBatchSearchAmaPInfo(@RequestBody MultipartFile multipartFile) {
        return expireBatchSearchService.batchSearchExpire(multipartFile);
    }

    @GetMapping("/expire/queryBatchNo")
    @Operation(summary = "服务到期查询批量编号")
    @PreAuthorize("@ss.hasPermission('query:multiple-service-enddate-result:forms')")
    public CommonResult<String> queryServiceExpireBatchNo(@RequestParam("batchNo") String batchNo) {
        String resp = expireBatchSearchService.getExpireQueryBatchNo(batchNo);
        return CommonResult.success(resp);
    }

    @GetMapping("/expire/queryStatus")
    @Operation(summary = "服务到期查询状态")
    @PreAuthorize("@ss.hasPermission('query:multiple-service-enddate-result:forms')")
    public CommonResult<List<AmaPQueryStatusVO>> queryStatus() {
        List<AmaPQueryStatusVO> resp = expireBatchSearchService.getExpireQueryStatus();
        return CommonResult.success(resp);
    }

    @PostMapping("/expire/query/result")
    @Operation(summary = "服务到期批量查询结果")
    @PreAuthorize("@ss.hasPermission('query:multiple-service-enddate-result:forms')")
    public CommonResult<PageResult<ServiceExpireQueryResultVO>> batchQueryExpireResult(
            @RequestBody ExpireResultQueryDTO expireResultQueryDTO) {
        return CommonResult.success(expireBatchSearchService.expireBatchQueryResult(expireResultQueryDTO));
    }
}
