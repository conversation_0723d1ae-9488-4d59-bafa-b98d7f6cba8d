package com.jlr.ecp.subscription.service.vin.expiry.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class VinExpiryMonthlyDetailDTO {
    private Long jobId;

    private LocalDateTime jobDate;

    private String carVin;

    private Integer queryStatus;

    private String iccid;

    @ExcelProperty("Service Type")
    private String serviceType;

    @ExcelProperty("ExpiryDate")
    private String expiryDate;

    @ExcelProperty("产地")
    private String productionPlace;

    @ExcelProperty("Brand")
    private String brandName;

    @ExcelProperty("车型名称")
    private String seriesName = "-";
}