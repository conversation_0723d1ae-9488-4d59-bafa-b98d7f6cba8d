package com.jlr.ecp.subscription.controller.admin.dto.bau;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

@Data
@Schema(description = "VIN补录记录VO")
@Validated
@Builder
public class VinAdditionalRecordVO {

    @Schema(description = "操作时间")
    private String operateTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "vin")
    private String vin;

    @Schema(description = "初始化结果")
    private String initialResult;

    @Schema(description = "失败原因")
    private String failReason;
}
