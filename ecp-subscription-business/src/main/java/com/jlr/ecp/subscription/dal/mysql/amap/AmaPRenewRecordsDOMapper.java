package com.jlr.ecp.subscription.dal.mysql.amap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.enums.amap.AmaPRenewStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;


@Mapper
public interface AmaPRenewRecordsDOMapper extends BaseMapperX<AmaPRenewRecordsDO> {
    /**
     * 根据vin获取续费中的记录
     *
     * @param vin vin，用于筛选特定的记录
     * @return 符合条件的AmaPRenewRecordsDO记录列表
     */
    default List<AmaPRenewRecordsDO> getProcessRecordByVin(String vin) {
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewRecordsDO::getCarVin, vin)
                .eq(AmaPRenewRecordsDO::getRenewStatus, AmaPRenewStatusEnum.RENEW_PROCESS.getStatus())
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        return selectList(queryWrapper);
    }

    /**
     * 根据vinSet和batchNo获取进行中的续费记录
     *
     * @param vinSet vinSet，用于筛选特定的记录
     * @return 符合条件的AmaPRenewRecordsDO记录列表
     */
    default List<AmaPRenewRecordsDO> getProcessRecordsByVinSetAndRenewNo(Set<String> vinSet, Long renewNo) {
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AmaPRenewRecordsDO::getCarVin, vinSet)
                .ne(AmaPRenewRecordsDO::getRenewNo, renewNo)
                .eq(AmaPRenewRecordsDO::getRenewStatus, AmaPRenewStatusEnum.RENEW_PROCESS.getStatus())
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        return selectList(queryWrapper);
    }
}
