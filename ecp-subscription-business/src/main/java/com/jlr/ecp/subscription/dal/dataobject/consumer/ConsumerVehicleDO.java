package com.jlr.ecp.subscription.dal.dataobject.consumer;

import com.baomidou.mybatisplus.annotation.*;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 消费者车辆实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_consumer_vehicle")
public class ConsumerVehicleDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户JLRID
     */
    private String consumerCode;

    /**
     * 车架号
     */
    private String carVin;

    /**
     * 绑定时间
     */
    private LocalDateTime bindTime;

    /**
     * 绑定状态: 0-已解绑 1-已绑定
     */
    private Integer bindStatus;

    /**
     * 绑定流水号
     */
    private String bindNo;

    /**
     * 解绑时间
     */
    private LocalDateTime unbindTime;

    /**
     * 数据来源: 1-小程序绑车 2-ICR登录
     */
    private Integer source;

    /**
     * ICR账号
     */
    private String incontrolId;

    /**
     * 租户号
     */
    private Long tenantId;
}
