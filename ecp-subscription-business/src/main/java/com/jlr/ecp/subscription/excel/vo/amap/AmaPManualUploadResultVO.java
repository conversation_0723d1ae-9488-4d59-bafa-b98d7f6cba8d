package com.jlr.ecp.subscription.excel.vo.amap;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "AMAP手动激活导入的VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPManualUploadResultVO {
    /**
     * AMAP手动激活源文件上传s3地址
     * */
    @Schema(description = "AMAP手动激活源文件上传s3地址")
    private String sourceUploadS3File;

    /**
     * AMAP处理结果上传s3地址
     * */
    @Schema(description = "AMAP处理结果上传s3地址")
    private String resultUploadS3File;

    /**
     *  原始文件名称
     * */
    @Schema(description = "原始文件名称")
    private String sourceUploadFileName;

    /**
     * 校验结果, 成功、部分成功、失败
     * */
    @Schema(description = "校验结果, 1:通过、2:部分成功、3：失败")
    private Integer checkResult;

    /**
     *  执行状态
     * */
    @Schema(description = "状态，检验完成")
    private String statusDesc;

    /**
     *  成功数量
     * */
    @Schema(description = "成功数量")
    private Integer successCount;

    /**
     *  失败数量
     * */
    @Schema(description = "失败数量")
    private Integer failedCount;
}
