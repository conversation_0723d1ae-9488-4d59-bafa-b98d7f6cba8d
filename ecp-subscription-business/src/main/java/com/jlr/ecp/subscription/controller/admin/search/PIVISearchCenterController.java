package com.jlr.ecp.subscription.controller.admin.search;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.QueryAmaPExpireDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.QueryAmaPExpireVO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;
import com.jlr.ecp.subscription.service.amap.SearchAmaPExpireService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.util.CarVinUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "平台管理 - 在线导航到期日查询")
@RestController
@RequestMapping("/search/center")
@Validated
public class PIVISearchCenterController {

    @Resource
    private PIVIAmaPService amaPService;

    @Resource
    private SearchAmaPExpireService searchAmaPExpireService;

    @GetMapping("/amap/getCarInfo")
    @Operation(summary = "单个AMAP到期日查询")
    @PreAuthorize("@ss.hasPermission('query:single-amap-enddate:forms')")
    public CommonResult<AmaPSearchCenterVO> searchAmaPInfo(@RequestParam("carVin") String carVin) {
        if (!CarVinUtil.checkVinFormat(carVin)) {
            return CommonResult.error(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR);
        }
        AmaPSearchCenterVO amaPSearchCenterVO = amaPService.queryAmaPInfo(carVin);
        return CommonResult.success(amaPSearchCenterVO);
    }

    @GetMapping("/download/template")
    @Operation(summary = "下载高德批量Excel发送模板")
    @PreAuthorize("@ss.hasPermission('query:multiple-amap-enddate:forms')")
    public CommonResult<String> downloadTemplateUrl() {
        // 获取模板文件的URL
        String templateUrl = searchAmaPExpireService.getQueryTemplateUrl();
        return CommonResult.success(templateUrl);
    }

    @PostMapping("/queryBatchPageList")
    @Operation(summary = "批量查询续分页列表")
    @PreAuthorize("@ss.hasPermission('query:multiple-amap-enddate:forms')")
    public CommonResult<PageResult<AmaPBatchQueryPageVO>> queryBatchRenewalPageList(@RequestBody @Valid AmaPBatchQueryPageDTO amaPPageDto) {
        return CommonResult.success(searchAmaPExpireService.queryBatchPageList(amaPPageDto));
    }

    @PostMapping("/amap/batch/query")
    @Operation(summary = "上传批量查询Excel文件并查询")
    @PreAuthorize("@ss.hasPermission('query:multiple-amap-enddate:forms')")
    public CommonResult<AmaPBatchRenewalUploadVO> uploadAndBatchSearchAmaPInfo(@RequestBody MultipartFile multipartFile) {
        return searchAmaPExpireService.batchSearchAmaPExpire(multipartFile);
    }

    @GetMapping("/queryRenewalNo")
    @Operation(summary = "查询续费编号")
    @PreAuthorize("@ss.hasPermission('query:multiple-amap-enddate-result:forms')")
    public CommonResult<String> queryRenewalBatchNo(@RequestParam("batchNo") String batchNo) {
        String resp = searchAmaPExpireService.getAmaPQueryBatchNo(batchNo);
        return CommonResult.success(resp);
    }

    @GetMapping("/queryStatus")
    @Operation(summary = "查询状态")
    @PreAuthorize("@ss.hasPermission('query:multiple-amap-enddate-result:forms')")
    public CommonResult<List<AmaPQueryStatusVO>> queryStatus() {
        List<AmaPQueryStatusVO> resp = searchAmaPExpireService.getAmaPQueryStatus();
        return CommonResult.success(resp);
    }

    @PostMapping("/amap/query/result")
    @Operation(summary = "amap批量查询结果")
    @PreAuthorize("@ss.hasPermission('query:multiple-amap-enddate-result:forms')")
    public CommonResult<PageResult<QueryAmaPExpireVO>> batchQueryAmaPResult(@RequestBody QueryAmaPExpireDTO queryAmaPExpireDTO) {
        return CommonResult.success(searchAmaPExpireService.amaPBatchQueryResult(queryAmaPExpireDTO));
    }
}
