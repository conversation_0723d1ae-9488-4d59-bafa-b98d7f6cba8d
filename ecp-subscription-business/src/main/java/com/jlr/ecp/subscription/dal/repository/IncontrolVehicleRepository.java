package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 车辆信息Repository接口
 *
 */
public interface IncontrolVehicleRepository extends IService<IncontrolVehicleDO> {

    /**
     * 根据InControl ID删除车辆
     *
     * @param incontrolId InControl ID
     * @return 删除数量
     */
    int deleteVehicleByIncontrol(String incontrolId);

    /**
     * 获取绑定的InControl车辆
     *
     * @param consumerCode 消费者代码
     * @param brandCode 品牌代码
     * @return 车辆列表
     */
    List<IncontrolVehicleDO> getBindIncontrolVehicle(String consumerCode, String brandCode);

    /**
     * 根据车辆VIN列表查询InControl车辆
     *
     * @param carVinList 车辆VIN列表
     * @return 车辆DTO列表
     */
    List<IncontrolVehicleByCarDTO> selectIncontrolVehicleByCarVinList(List<String> carVinList);

    /**
     * 根据车辆VIN列表获取手机号加密信息
     *
     * @param carVinList 车辆VIN列表
     * @return 手机号加密信息映射
     */
    List<Map<String, String>> getPhoneEncryptsByCarVinList(List<String> carVinList);

    /**
     * 根据车辆VIN列表删除车辆
     *
     * @param carVinList 车辆VIN列表
     * @return 删除数量
     */
    int deleteVehicleByCarVinList(List<String> carVinList);

    /**
     * 根据车辆VIN查询单个车辆
     *
     * @param carVin 车辆VIN
     * @return 车辆信息
     */
    IncontrolVehicleDO selectOneByCarVin(String carVin);

    /**
     * 根据ICR查询车辆列表
     *
     * @param icr ICR账号
     * @return 车辆列表
     */
    List<IncontrolVehicleDO> selectListByICR(String icr);

    /**
     * 批量插入车辆
     *
     * @param vehicleList 车辆列表
     * @return 插入数量
     */
    boolean insertBatch(List<IncontrolVehicleDO> vehicleList);

    /**
     * 批量保存或更新车辆
     *
     * @param entities 车辆实体集合
     * @return 是否成功
     */
    boolean saveOrUpdateBatch(Collection<IncontrolVehicleDO> entities);

    /**
     * 插入单个车辆
     *
     * @param vehicleDO 车辆信息
     * @return 插入数量
     */
    int insert(IncontrolVehicleDO vehicleDO);

    /**
     * 根据车辆VIN列表查询车辆信息
     *
     * @param carVinCollection 车辆VIN列表
     * @return 车辆列表
     */
    List<IncontrolVehicleDO> selectByCarVinList(Collection<String> carVinCollection);

    Map<String, IncontrolVehicleDO> queryIncontrolVehicleDOMap(Collection<String> carVinCollection);

    /**
     * 批量更新车辆信息表允许字段为空
     *
     * @param list list
     */
    void updateBatchWithNulls(List<IncontrolVehicleDO> list);

    /**
     * 根据VIN集合查询车辆信息
     *
     * @param vinSet VIN集合
     * @return 车辆列表
     */
    List<IncontrolVehicleDO> findByVinCollection(Collection<String> vinCollection);

    Map<String, IncontrolVehicleDO> getMapByVinCollection(Collection<String> vinCollection);

    /**
     * 根据车辆VIN列表和品牌查询InControl车辆
     *
     * @param carVinList 车辆VIN列表
     * @return 车辆DTO列表
     */
    List<IncontrolVehicleDO> selectByCarVinListAndBrandCode(List<String> carVinList, String brandCode);
}
