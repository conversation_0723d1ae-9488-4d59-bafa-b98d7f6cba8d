package com.jlr.ecp.subscription.dal.mysql.fufilment;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.CarServiceStatusVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_vcs_order_fufilment(t_vcs_order_fufilment)】的数据库操作Mapper
* @createDate 2024-01-19 14:15:09
* @Entity generator.domain.VcsOrderFufilmentDO
*/
@Mapper
public interface VcsOrderFufilmentDOMapper extends BaseMapperX<VcsOrderFufilmentDO> {

    List<VcsOrderFulfilmentRespVO> getHistoryByOrderItemCodeList(String orderItemCodeList);

    List<VcsOrderFufilmentDO> getLatestByOrderItemCodeList(@Param("orderItemCodeList") List<String> orderItemCodeList);

    List<CarServiceStatusVO> checkActiveServiceByCarVinAndServiceType(@Param("carVinAndServiceTypeList") List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    List<CarServiceStatusVO> checkActiveServiceByCarVinList(@Param("carVinList") List<String> carVinList);

    List<FulfilmentServiceStatusVO> findActiveService(@Param("total") Integer total);
    List<FulfilmentServiceStatusVO> findAllServiceStatus(@Param("orderCode") String orderCode);

    default VcsOrderFufilmentDO getOneByFulfilmentId(String fufilmentId){
        return selectOne(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                .eq(VcsOrderFufilmentDO::getFufilmentId,fufilmentId)
                .eq(BaseDO::getIsDeleted,false));
    }

    List<VinsAndServiceDateDTO> findVinAndServiceDate(@Param("carVinList") List<String> carvinList);
}




