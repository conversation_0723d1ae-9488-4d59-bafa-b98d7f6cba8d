package com.jlr.ecp.subscription.enums.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UnicomRnrQueryFailedTypeEnum {

    VIN_ERROR(1, "VIN格式校验错误"),

    ICCID_ERROR(2, "ICCID查询失败"),

    UNICOM_ERROR(3, "联通查询失败");

    /**
     * 类型
     * */
    public final Integer type;

    /**
     * 描述
     * */
    public final String desc;

    public static String getDescriptionByCode(Integer code) {
        // 或者返回一个默认描述，如 "未知"
        if (code == null) {
            return null;
        }

        for (UnicomRnrQueryFailedTypeEnum status : UnicomRnrQueryFailedTypeEnum.values()) {
            if (status.getType().equals(code)) {
                return status.getDesc();
            }
        }
        return code.toString();
    }
}
