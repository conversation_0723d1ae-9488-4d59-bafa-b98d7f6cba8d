package com.jlr.ecp.subscription.dal.mysql.icroder;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.VehicleSubscriptionDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.service.vin.expiry.dto.IncontrolVehicleDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_incontrol_vehicle(t_incontrol_vehicle)】的数据库操作Mapper
* @createDate 2024-01-16 09:42:36
* @Entity com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO
*/
@Mapper
public interface IncontrolVehicleDOMapper extends BaseMapperX<IncontrolVehicleDO> {

    int deleteVehicleByIncontrol(String incontrolId);

    List<IncontrolVehicleDO> getBindIncontrolVehicle(@Param("consumerCode") String consumerCode,@Param("brandCode") String brandCode);

    List<IncontrolVehicleByCarDTO> selectIncontrolVehicleByCarVinList(List<String> carVinList);

    List<Map<String, String>> getPhoneEncryptsByCarVinList(@Param("carVinList") List<String> carVinList);

    int deleteVehicleByCarVinList(@Param("carVinList") List<String> carVinList);

    /**
     * 根据Carvin查询关联信息
     * @param carVin VIN信息
     * @return IncontrolVehicleDO
     */
    default IncontrolVehicleDO selectOneByCarVin(String carVin){
        return selectOne(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getCarVin, carVin)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .orderByDesc(IncontrolVehicleDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    default List<IncontrolVehicleDO> selectListByICR(String icr){

        return selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getIncontrolId, icr)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .orderByDesc(IncontrolVehicleDO::getId));
    }

    List<VehicleSubscriptionDTO> findSubscriptionsByIncontrolIdsAndDays(
            @Param("incontrolIds") List<String> incontrolIds,
            @Param("currentDateTime") LocalDateTime currentDateTime,
            @Param("days") int days,
            @Param("daysPlusOne") int daysPlusOne,
            @Param("serviceType") Integer serviceType);

    List<IncontrolVehicleDTO> getReportOrds(@Param("carVins") List<String> strings);

    void updateBatchWithNulls(@Param("list") List<IncontrolVehicleDO> list);
}




