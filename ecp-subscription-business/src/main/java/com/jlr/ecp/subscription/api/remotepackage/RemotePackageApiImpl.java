package com.jlr.ecp.subscription.api.remotepackage;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageCreateDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackagePageReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.BatchUploadRespVO;
import com.jlr.ecp.subscription.api.remotepackage.vo.RemotePackageListRespVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Locale;

/**
 * 服务包API  提供 RESTful API 接口，给 Feign 调用
 *
 * <AUTHOR>
 */
@RestController
@Validated
@Slf4j
public class RemotePackageApiImpl implements RemotePackageApi {
    @Resource
    private RemotePackageDOService remotePackageDOService;

    @Override
    public CommonResult<String> createRemotePackage(RemotePackageCreateDTO createDTO) {
        Boolean success = remotePackageDOService.createRemotePackage(createDTO);
        if (success) {
            return CommonResult.success(Constants.PACKAGE_CREATE_SUCCESS_MESSAGE);
        }

        return CommonResult.error(ErrorCodeConstants.PACKAGE_CREATE_FAIL);
    }

    @Override
    public CommonResult<PageResult<RemotePackageListRespVO>> page(RemotePackagePageReqDTO dto) {
        // 为了兼容前端传入的大小写，这里统一转换为小写
        if (dto.getCreatedTimeSort() != null) {
            dto.setCreatedTimeSort(dto.getCreatedTimeSort().toLowerCase(Locale.ROOT));
        }

        return CommonResult.success(remotePackageDOService.getPage(dto));
    }

    @Override
    public CommonResult<String> deleteByPackageCode(String packageCode) {
        Boolean success = remotePackageDOService.deleteByPackageCode(packageCode);
        if (success) {
            return CommonResult.success(Constants.PACKAGE_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.PACKAGE_DELETE_FAIL);
    }

    /**
     * 2MB
     */
    private static final long MAX_FILE_SIZE = 2L * 1024 * 1024;

    @Override
    public CommonResult<BatchUploadRespVO> batchUpload(MultipartFile file,String carSystemModel) {
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.FILE_SIZE_EXCEED_LIMIT);
        }
        return remotePackageDOService.processBatchUpload(file,carSystemModel);
    }

    @Override
    public CommonResult<String> downloadTemplateUrl() {
        try {
            // 获取模板文件的URL
            String templateUrl = remotePackageDOService.getTemplateUrl();

            return CommonResult.success(templateUrl);
        } catch (Exception e) {
            log.error("Error retrieving template URL", e);
            return CommonResult.error(ErrorCodeConstants.SERVER_ERROR);
        }
    }




}
