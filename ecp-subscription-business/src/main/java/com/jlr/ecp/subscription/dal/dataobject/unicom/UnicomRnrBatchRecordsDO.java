package com.jlr.ecp.subscription.dal.dataobject.unicom;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_unicom_todo_order
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_unicom_rnr_batch_records")
public class UnicomRnrBatchRecordsDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;


    /**
     *批次号;批量查询批次号
     */
    private Long batchNo;

     /**
     * 上传文件S3地址;上传文件S3地址
     */    
    private String uploadFileS3Url;

     /**
     * 操作人;操作人
     */    
    private String operator;

}

