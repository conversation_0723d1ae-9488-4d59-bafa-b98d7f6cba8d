package com.jlr.ecp.subscription.service.iccid.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.service.iccid.IccidModifyRecordsDOService;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_iccid_modify_records(t_iccid_modify_records)】的数据库操作Service实现
* @createDate 2024-11-19 19:44:40
*/
@Service
public class IccidModifyRecordsDOServiceImpl extends ServiceImpl<IccidModifyRecordsDOMapper, IccidModifyRecordsDO>
    implements IccidModifyRecordsDOService{

}




