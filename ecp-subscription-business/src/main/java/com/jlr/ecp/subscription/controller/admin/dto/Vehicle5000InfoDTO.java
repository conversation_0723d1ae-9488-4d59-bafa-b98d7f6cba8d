package com.jlr.ecp.subscription.controller.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * vin
 * 品牌名称
 * 型号名称
 * 型号年款
 * 配置编码
 * 系列名称
 *
 * 是否PIVI车机
 * 是否具备信息娱乐服务相关配置
 */
@Data
@Schema(description = "5000号信息及品牌对象")
public class Vehicle5000InfoDTO {
    @Schema(description = "vin")
    private String vin;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "型号名称")
    private String configName;

    @Schema(description = "型号年款")
    private String modelYear;

    @Schema(description = "配置编码")
    private String configCode;

    @Schema(description = "系列编码")
    private String seriesCode;

   @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "系列名称")
    private String seriesName;

    @Schema(description = "PIVI车机属性 'PIVI'标识是，否则不是")
    private String carSystemModel;

    @Schema(description = "是否具备信息娱乐服务相关配置 N 否 Y 是")
    private String hasInfoEntertain;

    @Schema(description = "是否实名 N 否 Y 是")
    private String realName;

    @Schema(description = "ICCID")
    private String iccid;

    @Schema(description = "VIN加密展示")
    private String vinMix;
}
