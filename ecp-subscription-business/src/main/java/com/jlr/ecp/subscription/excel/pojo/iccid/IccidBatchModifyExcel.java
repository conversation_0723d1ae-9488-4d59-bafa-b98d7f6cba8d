package com.jlr.ecp.subscription.excel.pojo.iccid;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = false)
public class IccidBatchModifyExcel {
    @ExcelProperty("VIN")
    private String carVin;

    @ExcelProperty("新ICCID")
    private String newIccid;
}