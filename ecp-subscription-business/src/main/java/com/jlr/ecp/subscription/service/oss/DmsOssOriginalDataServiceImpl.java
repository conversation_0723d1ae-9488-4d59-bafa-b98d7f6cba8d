package com.jlr.ecp.subscription.service.oss;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.report.api.ReportApi;
import com.jlr.ecp.report.api.dto.UninitializedReasonDto;
import com.jlr.ecp.report.api.dto.VinInitialQueryDto;
import com.jlr.ecp.subscription.api.bau.dto.PreCheckListRequest;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.VinInitialQueryDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataMapper;
import com.jlr.ecp.subscription.dal.mysql.oss.VinInitialQueryMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.SortTypeEnum;
import com.jlr.ecp.subscription.enums.amap.QueryUploadResultEnum;
import com.jlr.ecp.subscription.enums.oss.*;
import com.jlr.ecp.subscription.excel.listener.initialize.InitializeStatusQueryListener;
import com.jlr.ecp.subscription.excel.pojo.initialize.InitializeStatusQueryExcel;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.model.vo.OnlineServiceBatchQueryRecordVO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeStatusVO;
import com.jlr.ecp.subscription.util.CarVinUtil;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * t_dms_oss_original_data(DmsOssOriginalData)表服务实现类
 *
 * <AUTHOR>
 */

@Service("dmsOssOriginalDataService")
@Validated
@Slf4j
public class DmsOssOriginalDataServiceImpl implements DmsOssOriginalDataService {

    @Resource
    private DmsOssOriginalDataMapper dmsOssOriginalDataMapper;

    @Resource
    private VinInitialQueryMapper vinInitialQueryMapper;

    @Value("${bau.queryExcelUrl}")
    private String queryExcelUrl;

    @Resource
    private Snowflake snowflake;

    @Resource
    private ReportApi reportApi;

    @Resource
    private FileService fileService;

    private static final String FILE_CODE = "AWS_S3_FILE";

    // 报表编号前缀
    private static final String REPORT_PREFIX = "BU";

    /**
     * 3MB
     */
    private static final long MAX_FILE_SIZE = 3L * 1024 * 1024;

    @Override
    public List<Long> getPreCheckIdList(PreCheckListRequest request) {
        LambdaQueryWrapperX<DmsOssOriginalDataDO> queryWrapperX = new LambdaQueryWrapperX<>();
        // 当appdJobFlag为是，不需要查询jobDate, 查询处理状态为待处理和处理失败的数据
        if (AppdJobFlagEnum.YES.getCode().equals(request.getAppdJobFlag())) {
            queryWrapperX.eq(DmsOssOriginalDataDO::getAppdJobFlag, AppdJobFlagEnum.YES.getCode())
                    .in(DmsOssOriginalDataDO::getStatus, HandleStatusEnum.PENDING.getCode(), HandleStatusEnum.FAIL.getCode())
                    .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                    .select(DmsOssOriginalDataDO::getId);
        } else {
            queryWrapperX.betweenIfPresent(DmsOssOriginalDataDO::getJobDate, request.getStartDate(), request.getEndDate())
                    .eq(DmsOssOriginalDataDO::getStatus, request.getStatus())
                    .eq(DmsOssOriginalDataDO::getSourceType, SourceTypeEnum.BAU_JOB.getCode())
                    .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                    .select(DmsOssOriginalDataDO::getId);
        }
        return dmsOssOriginalDataMapper.selectList(queryWrapperX)
                .stream().map(DmsOssOriginalDataDO::getId).collect(Collectors.toList());
    }

    @Override
    public List<DmsOssOriginalDataDO> getListByIdList(List<Long> idList) {
        return dmsOssOriginalDataMapper.selectList(new LambdaQueryWrapperX<DmsOssOriginalDataDO>()
                .in(DmsOssOriginalDataDO::getId, idList)
                .eq(DmsOssOriginalDataDO::getIsDeleted, false));
    }

    @Override
    public void updateBatch(List<DmsOssOriginalDataDO> updateList) {
        dmsOssOriginalDataMapper.updateBatch(updateList);
    }

    @Override
    public Map<Long, List<Long>> getPreSyncIdList(PreCheckListRequest request) {
        LambdaQueryWrapperX<DmsOssOriginalDataDO> queryWrapperX = new LambdaQueryWrapperX<>();
        // 当appdJobFlag为是，不需要查询jobDate
        if (AppdJobFlagEnum.YES.getCode().equals(request.getAppdJobFlag())) {
            queryWrapperX.eq(DmsOssOriginalDataDO::getAppdJobFlag, AppdJobFlagEnum.YES.getCode())
                    .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                    .groupBy(DmsOssOriginalDataDO::getBauJobId)
                    .select(DmsOssOriginalDataDO::getBauJobId);
        } else {
            queryWrapperX.betweenIfPresent(DmsOssOriginalDataDO::getJobDate, request.getStartDate(), request.getEndDate())
                    .eq(DmsOssOriginalDataDO::getSourceType, SourceTypeEnum.BAU_JOB.getCode())
                    .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                    .groupBy(DmsOssOriginalDataDO::getBauJobId)
                    .select(DmsOssOriginalDataDO::getBauJobId);
        }
        // 查询时间段内的全部任务
        List<Long> bauJobIdList = dmsOssOriginalDataMapper.selectList(queryWrapperX)
                .stream().map(DmsOssOriginalDataDO::getBauJobId).collect(Collectors.toList());
        if (CollUtil.isEmpty(bauJobIdList)) {
            return Collections.emptyMap();
        }
        // 查询待入库的bauJobId和对应的idList
        Map<Long, List<Long>> bauJobIdToMap = dmsOssOriginalDataMapper.selectList(new LambdaQueryWrapperX<DmsOssOriginalDataDO>()
                        .in(DmsOssOriginalDataDO::getBauJobId, bauJobIdList)
                        .eq(DmsOssOriginalDataDO::getVinMatchResult, VinMatchResultEnum.PENDING.getCode())
                        .in(DmsOssOriginalDataDO::getSourceType, SourceTypeEnum.BAU_JOB.getCode(), SourceTypeEnum.APPD.getCode())
                        .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                        .select(DmsOssOriginalDataDO::getId, DmsOssOriginalDataDO::getBauJobId))
                .stream().collect(Collectors.groupingBy(DmsOssOriginalDataDO::getBauJobId, Collectors.mapping(DmsOssOriginalDataDO::getId, Collectors.toList())));
        bauJobIdList.forEach(bauJobId -> bauJobIdToMap.putIfAbsent(bauJobId, new ArrayList<>()));
        return bauJobIdToMap;
    }

    @Override
    public OnlineServiceInitializeStatusVO getStatusByVin(String carVin) {
        DmsOssOriginalDataDO originalDataDO = dmsOssOriginalDataMapper.selectOne(new LambdaQueryWrapperX<DmsOssOriginalDataDO>()
                .eq(DmsOssOriginalDataDO::getCarVin, carVin)
                .ne(DmsOssOriginalDataDO::getStatus, HandleStatusEnum.PENDING.getCode())
                .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                .last("limit 1"));
        if (Objects.isNull(originalDataDO)) {
            return null;
        }

        return OnlineServiceInitializeStatusVO.builder()
                .carVin(originalDataDO.getCarVin())
                .initializedResult(VinMatchResultEnum.getDescByCode(originalDataDO.getVinMatchResult()))
                .dmsResult(CharSequenceUtil.isBlank(originalDataDO.getDmsInvoiceDate()) ?
                        YesOrNoStatusEnum.FAIL.getDesc() : YesOrNoStatusEnum.SUCCESS.getDesc())
                .sotaResult(SotaResultEnum.getDescByCode(originalDataDO.getSotaResult()))
                .appdResult(AppDResultEnum.getDescByCode(originalDataDO.getAppdResult()))
                .amapResult(AMapResultEnum.getDescByCode(originalDataDO.getAmapResult()))
                .cuResult(CuResultEnum.getDescByCode(originalDataDO.getCuResult()))
                .dpResult(DpResultEnum.getDescByCode(originalDataDO.getDpResult()))
                .piviResult(PIVIResultEnum.getDescByCode(originalDataDO.getPiviConfigResult()))
                .specialVinResult(CharSequenceUtil.isBlank(originalDataDO.getSpecialVinConfig()) ?
                        YesOrNoStatusEnum.SUCCESS.getDesc() : YesOrNoStatusEnum.FAIL.getDesc())
                .syncResult(InitializeStatusEnum.getDescByCode(originalDataDO.getSyncStatus()))
                .build();
    }

    @Override
    public String queryExcelUrl() {
        return queryExcelUrl;
    }

    @Override
    public CommonResult<Boolean> uploadBatchSearch(MultipartFile multipartFile) {
        if (Objects.isNull(multipartFile)) {
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_IS_EMPTY);
        }
        long startTime = System.currentTimeMillis();
        log.info("批量VIN在线服务初始化状态查询, multipartFile:{}", multipartFile);
        byte[] readBytes;
        try {
            readBytes = IoUtil.readBytes(multipartFile.getInputStream());
        } catch (IOException e) {
            log.info("批量VIN在线服务初始化状态查询读取excel文件异常:{}", e.getMessage());
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_UPLOAD_ERROR);
        }
        // excel校验
        if (!FileCheckUtil.isExcelFile(readBytes)) {
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_FILE_INVALID);
        }
        if (multipartFile.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_SIZE_EXCEED_LIMIT);
        }
        InitializeStatusQueryListener initializeStatusQueryListener = new InitializeStatusQueryListener();
        try {
            EasyExcel.read(multipartFile.getInputStream(), InitializeStatusQueryExcel.class,
                    initializeStatusQueryListener).sheet().doRead();
        } catch (Exception e) {
            log.info("批量VIN在线服务初始化状态查询读取excel文件异常:{}", e.getMessage());
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_UPLOAD_ERROR);
        }
        if (Boolean.TRUE.equals(initializeStatusQueryListener.getIsFormatError())) {
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_FORMAT_ERROR);
        }
        if (CollUtil.isEmpty(initializeStatusQueryListener.getAllDataList())) {
            return CommonResult.error(ErrorCodeConstants.INITIALIZE_STATUS_EXCEL_IS_EMPTY);
        }
        long endTime = System.currentTimeMillis();
        log.info("批量VIN在线服务初始化状态查询解析完成，花费时间:{}毫秒", (endTime-startTime));
        // 上传S3
        String path = uploadInitialQueryToS3File(readBytes);
        String queryNo = REPORT_PREFIX + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        // 新增查询记录
        insertVinInitialQuery(queryNo, path);
        // VIN转成大写
        List<String> vinList = initializeStatusQueryListener.getAllDataList().stream()
                .map(query -> CarVinUtil.carVinToUpperCase(query.getCarVin())).collect(Collectors.toList());
        initializeStatusQueryListener.setAllDataList(null);
        // 查询并生成报表并更新查询状态
        CompletableFuture.runAsync(() -> generateVinInitialQuery(queryNo, vinList, path)).exceptionally(throwable -> {
            log.error("异步调用生成VIN初始化状态报表失败", throwable);
            return null;
        });
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> generateReasonFile(Long bauJobId) {
        CompletableFuture.runAsync(() -> {
            // 调用report服务生成报表
            UninitializedReasonDto dto = new UninitializedReasonDto();
            dto.setBauJobId(bauJobId);
            CommonResult<Boolean> result = null;
            log.info("调用reportApi生成未初始化原因报表开始, bauJobId:{}", bauJobId);
            try {
                result = reportApi.generateUninitializedReason(dto);
            } catch (Exception e) {
                log.error("调用reportApi生成未初始化原因报表异常,{}", e.getMessage());
            }
            if (result != null && result.getData()) {
                log.info("调用reportApi生成未初始化原因报表成功, bauJobId:{}", bauJobId);
            }
        }).exceptionally(throwable -> {
            log.error("异步调用生成未初始化原因报表失败", throwable);
            return null;
        });
        return CommonResult.success(true);
    }

    @Override
    public PageResult<OnlineServiceBatchQueryRecordVO> getBatchQueryRecords(VinInitialLogPageDTO logPageDTO) {
        log.info("批量查询上传记录查询, logPageDTO:{}", logPageDTO);
        if (Objects.isNull(logPageDTO)) {
            return new PageResult<>();
        }
        Page<VinInitialQueryDO> page = new Page<>(logPageDTO.getPageNo(), logPageDTO.getPageSize());
        LambdaQueryWrapper<VinInitialQueryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VinInitialQueryDO::getIsDeleted, false);
        if (SortTypeEnum.ASC.getSortType().equals(logPageDTO.getOperateTimeSort())) {
            queryWrapper.orderByAsc(VinInitialQueryDO::getCreatedTime);
            queryWrapper.orderByAsc(VinInitialQueryDO::getId);
        } else {
            queryWrapper.orderByDesc(VinInitialQueryDO::getCreatedTime);
            queryWrapper.orderByDesc(VinInitialQueryDO::getId);
        }
        Page<VinInitialQueryDO> recordsDOPage = vinInitialQueryMapper.selectPage(page, queryWrapper);
        if (Objects.isNull(recordsDOPage) || CollUtil.isEmpty(recordsDOPage.getRecords())) {
            return new PageResult<>();
        }
        List<VinInitialQueryDO> queryList = recordsDOPage.getRecords();
        List<OnlineServiceBatchQueryRecordVO> vos = queryList.stream().map(query -> OnlineServiceBatchQueryRecordVO.builder()
                .operateTime(TimeFormatUtil.localDateTimeToString(query.getCreatedTime(), TimeFormatUtil.formatter_6))
                .reportNo(query.getQueryNo())
                .status(QueryUploadResultEnum.SUCCESS.getDesc())
                .build()).collect(Collectors.toList());
        return new PageResult<>(vos, recordsDOPage.getTotal());
    }

    @Override
    public void insert(DmsOssOriginalDataDO originalDataDO) {
        dmsOssOriginalDataMapper.insert(originalDataDO);
    }

    @Override
    public void update(DmsOssOriginalDataDO originalDataDO) {
        dmsOssOriginalDataMapper.updateById(originalDataDO);
    }

    @Override
    public DmsOssOriginalDataDO getByVin(String vin) {
        return dmsOssOriginalDataMapper.selectOne(new LambdaQueryWrapperX<DmsOssOriginalDataDO>()
                .eq(DmsOssOriginalDataDO::getCarVin, vin)
                .eq(DmsOssOriginalDataDO::getIsDeleted, false)
                .last("limit 1"));
    }

    /**
     * 初始化插入车辆识别码（VIN）查询请求
     * 该方法用于在数据库中记录最初的VIN查询请求，包括查询编号、操作者和文件URL
     *
     * @param queryNo 查询编号
     * @param path 文件的S3存储路径，记录了需要查询的VIN相关信息的文件位置
     */
    public void insertVinInitialQuery(String queryNo, String path) {
        VinInitialQueryDO vinInitialQueryDO = VinInitialQueryDO.builder()
                .queryNo(queryNo)
                .operator(getOperator())
                .fileS3Url(path)
                .build();
        vinInitialQueryMapper.insert(vinInitialQueryDO);
    }

    /**
     * 调用report服务生成报表
     *
     * @param queryNo 查询编号
     */
    public void generateVinInitialQuery(String queryNo, List<String> vinList, String path) {
        if (StrUtil.isBlank(queryNo) || StrUtil.isBlank(path)) {
            log.warn("批量VIN在线服务初始化状态查询, queryNo或path为空");
            return ;
        }
        // 调用report服务生成报表
        VinInitialQueryDto dto = new VinInitialQueryDto();
        dto.setQueryNo(queryNo);
        dto.setVinList(vinList);
        dto.setFileS3Url(path);
        CommonResult<Boolean> result = null;
        log.info("调用reportApi生成VIN初始化状态报表开始, queryNo:{}, vin数量:{}", queryNo, vinList.size());
        try {
            result = reportApi.generateVinInitialQuery(dto);
        } catch (Exception e) {
            log.error("调用reportApi生成VIN初始化状态报表异常,{}", e.getMessage());
        }
        if (result != null && result.getData()) {
            log.info("调用reportApi生成VIN初始化状态报表成功, queryNo:{}, vin数量:{}", queryNo, vinList.size());
        }
    }

    /**
     * 获取当前操作员的用户名
     *
     * @return 当前操作员的用户名，如果未登录或用户名为空，则返回空字符串
     */
    private String getOperator() {
        if (StringUtils.isBlank(WebFrameworkUtils.getLoginUserName())) {
            return "";
        }
        return WebFrameworkUtils.getLoginUserName();
    }

    /**
     * 将批量VIN初始化状态查询结果上传到S3文件
     *
     * 此方法负责将包含批量VIN初始化状态查询结果的字节数组上传到S3存储系统中它生成一个基于当前时间的文件名，
     * 以确保文件的唯一性，然后尝试创建文件并上传如果上传过程中发生任何异常，将捕获异常并记录错误信息
     *
     * @param fileBytes 批量VIN初始化状态查询结果的字节数组
     * @return 上传文件的路径如果上传成功，返回完整的文件路径；如果上传失败，返回空字符串
     */
    private String uploadInitialQueryToS3File(byte[] fileBytes) {
        String path = "";
        try {
            String fileName = "批量VIN初始化状态查询" +  System.currentTimeMillis();
            path = fileService.createFile(null, "VinInitialQuery" + File.separator +
                    fileName + ".xlsx", fileBytes, FILE_CODE);
            log.info("将批量VIN初始化状态查询文件上传至S3存储, 文件原始文件路径:{}", path);
        } catch (Throwable e) {
            log.info("将批量VIN初始化状态查询文件上传至S3存储异常:{}", e.getMessage());
        }
        return path;
    }
}

