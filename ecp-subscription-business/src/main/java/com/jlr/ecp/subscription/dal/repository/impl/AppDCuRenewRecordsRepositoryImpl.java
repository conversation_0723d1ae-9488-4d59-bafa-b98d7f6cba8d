package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.repository.AppDCuRenewRecordsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 手动续费Repository实现类
 *
 */
@Component
@Slf4j
public class AppDCuRenewRecordsRepositoryImpl extends ServiceImpl<AppDCuRenewRecordsMapper, AppDCuRenewRecords> implements AppDCuRenewRecordsRepository {

    @Override
    public List<AppDCuRenewRecords> getByVinListAndStatus(List<String> vinList, Integer status, Long renewNo, Integer renewServiceType) {
        return baseMapper.getByVinListAndStatus(vinList, status, renewNo, renewServiceType);
    }

    @Override
    public List<AppDCuRenewRecords> getByVinAndStatus(String vin, Integer status) {
        return baseMapper.getByVinAndStatus(vin, status);
    }
}
