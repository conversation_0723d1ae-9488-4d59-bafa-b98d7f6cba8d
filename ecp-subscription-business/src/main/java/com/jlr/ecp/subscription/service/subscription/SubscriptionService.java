package com.jlr.ecp.subscription.service.subscription;

import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchLocalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;

import java.time.LocalDateTime;
import java.util.List;

public interface SubscriptionService {

    /**
     * 根据vin号查询到期服务日期查询
     * @param carVin
     * @return
     */
    SubscriptionSearchResultDTO getExpireDateByVin(String carVin);

    /**
     * 根据vin号查询到期服务日期深度查询
     * @param carVin
     * @return
     */
    SubscriptionSearchResultDTO getExpireDateByVinDeeply(String carVin);

    /**
     * 根据vin号查询PIVI服务
     * @param carVin carVin
     */
    List<SubscriptionServiceDO> getServicesByVin(String carVin);

    /**
     * 根据vin号查询remote到期服务日期
     * @param carVin carVin
     */
    RemoteSearchResultDTO getRemoteExpireDateByVin(String carVin);

    /**
     * 根据vinList查询remote到期服务日期
     * @param vinList vinList
     */
    List<RemoteSearchResultDTO> getRemoteExpireDateByVinList(List<String> vinList);

    /**
     * 更新remote到期服务日期
     * @param renewRecords renewRecords
     */
    void updateRemoteExpireDate(RemoteRenewDetailRecords renewRecords);


    /**
     * 根据vin号查询到期服务日期查询
     * @param incontrolVehicleDO
     * @return SubscriptionSearchResultDTO
     */
    SubscriptionSearchLocalDTO getExpireDateByVinLocal(IncontrolVehicleDO incontrolVehicleDO);

    /**
     * 从手动续费表查询ECP到期日
     */
    LocalDateTime getExpireDateFromAppDCuRenewRecords(PIVIPackageDO piviPackageDO);
}
