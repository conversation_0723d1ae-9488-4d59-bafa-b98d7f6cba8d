package com.jlr.ecp.subscription.dal.mysql.remotepackage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PIVIPackageDOMapper extends BaseMapperX<PIVIPackageDO> {


    default PIVIPackageDO findICCIDByCarVin(String carVin){
        return selectOne(new LambdaQueryWrapperX<PIVIPackageDO>()
                .eq(BaseDO::getIsDeleted,false)
                .eq(PIVIPackageDO::getVin,carVin)
                .isNotNull(PIVIPackageDO::getIccid)
                .orderByDesc(BaseDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));
    }

    default List<PIVIPackageDO> selectPIVIPackageDOByCarVinList(List<String> carVinList) {
        return selectList(new LambdaQueryWrapperX<PIVIPackageDO>()
                .eq(BaseDO::getIsDeleted, false)
                .in(PIVIPackageDO::getVin, carVinList));
    }

    /**
     *  2024年9月4日新增：remote 或 online服务到期的数据量
     *
     *  Merged: 针对Remote和Subscription到期日为同一天的数据进行VIN的去重。仅远程车控到期的VIN标注为"Remote"，远程车控和订阅服务到期的VIN标注为"Both"，订阅服务到期的VIN标注为"Subscription"
     */
    long getMergedVinExpireServiceCountByDataRangeForAll(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     *  2024年9月4日新增：remote 或 online服务到期的数据量
     *
     *  original版本
     */
    long getVinExpireServiceCountByDataRangeForAll(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 2024年9月4日新增：remote 或 online服务到期的数据
     *
     * 分页版本
     */
    List<VinExpireServiceDTO> getVinExpireServiceByDataRangeForAll(Page page, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 2024年9月4日新增：remote 或 online服务到期的数据
     *
     * 不分页版本
     */
    List<VinExpireServiceDTO> getVinExpireServiceByDataRangeForAllNoPage(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 获取指定vin的pivi数据
     */
    default PIVIPackageDO selectOnePiviDataByVin(String vin){
        return selectOne(new LambdaQueryWrapperX<PIVIPackageDO>().eq(PIVIPackageDO::getVin, vin)
                .eq(PIVIPackageDO::getIsDeleted, false)
                .orderByDesc(PIVIPackageDO::getId)
                .last(Constants.LIMIT_ONE));
    }
}
