package com.jlr.ecp.subscription.excel.vo.amap;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "AMAP手动激活导入的VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AmaPManualActivationImportVO implements Serializable {

    @Schema(description = "上传文件名")
    private String uploadFileName;

    // 上传文件地址
    @Schema(description = "上传文件地址")
    private String uploadS3File;

    // 处理结果文件地址
    @Schema(description = "处理结果文件地址")
    private String resultS3File;

    // 上传用户名
    @Schema(description = "上传用户名")
    private String uploadUserName;

    // 上传时间
    @Schema(description = "上传时间")
    private String uploadTime;

}
