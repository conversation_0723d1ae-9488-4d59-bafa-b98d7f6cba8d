package com.jlr.ecp.subscription.controller.admin.dto.vehicle;

import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchLocalDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ConsumerInfoVO implements Serializable {

    @Schema(description = "icr")
    private String icr;

    @Schema(description = "icrMix")
    private String icrMix;

    @Schema(description = "consumeCode|jlrId")
    private String consumeCode;

    @Schema(description = "icr手机号")
    private String icrMobile;

    @Schema(description = "icr手机号加密的展示文本")
    private String icrMobileMix;

    @Schema(description = "微信手机号")
    private String wxMobile;

    @Schema(description = "微信手机号展示文本")
    private String wxMobileMix;

    @Schema(description = "车辆以及服务信息")
    private List<SubscriptionSearchLocalDTO> subscriptionSearchResultDTOList;

    @Schema(description = "在TSDP已解绑")
    private Boolean unbind;
}
