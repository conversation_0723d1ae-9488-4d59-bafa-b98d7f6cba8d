package com.jlr.ecp.subscription.dal.dataobject.oss;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_vin_initial_query")
public class VinInitialQueryDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 查询编号;BU+时间戳
     */
    @TableField("query_no")
    private String queryNo;

    /**
     * 操作人;操作人账号
     */
    @TableField("operator")
    private String operator;

    /**
     * S3文件地址;S3文件地址
     */
    @TableField("file_s3_url")
    private String fileS3Url;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
