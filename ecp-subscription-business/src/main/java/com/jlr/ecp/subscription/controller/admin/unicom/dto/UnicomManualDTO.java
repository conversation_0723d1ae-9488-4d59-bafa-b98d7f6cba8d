package com.jlr.ecp.subscription.controller.admin.unicom.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UnicomManualDTO {
    /**
     *  车辆VIN
     * */
    @Schema(description = "车辆VIN")
    private String vin;

    /**
     *   VCS订单编码
     * */
    @Schema(description = "VCS订单编码")
    private String vcsOrderCode;


    /**
     * 服务起始时间;服务起始时间
     */
    @Schema(description = "服务起始时间")
    private LocalDateTime serviceBeginDate;


    /**
     * 服务结束时间;服务结束时间
     */
    @Schema(description = "服务结束时间")
    private LocalDateTime serviceEndDate;

    /**
     * 订阅id
     */
    @Schema(description = "订阅id")
    private String fulfilmentId;
}
