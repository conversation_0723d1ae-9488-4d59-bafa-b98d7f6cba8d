package com.jlr.ecp.subscription.controller.admin.dto.remote;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "Remote批量续费分页查询DTO")
public class RemoteBatchRenewalPageDTO extends PageParam {
    @Schema(description = "操作时间排序, 正序:asc, 倒叙:desc")
    @NotEmpty
    private String operateTimeSort;
}
