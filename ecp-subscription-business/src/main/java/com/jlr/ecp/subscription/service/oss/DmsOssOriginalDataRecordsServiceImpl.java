package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataRecordsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * t_dms_oss_original_data_records(DmsOssOriginalDataRecords)表服务实现类
 * <AUTHOR>
 */

@Service("dmsOssOriginalDataRecordsService")
@Validated
@Slf4j
public class DmsOssOriginalDataRecordsServiceImpl implements DmsOssOriginalDataRecordsService {

    @Resource
    private DmsOssOriginalDataRecordsMapper dmsOssOriginalDataRecordsMapper;

    @Override
    public void insertBatch(List<DmsOssOriginalDataRecordsDO> recordsDOList) {
        dmsOssOriginalDataRecordsMapper.insertBatch(recordsDOList);
    }

    @Override
    public List<DmsOssOriginalDataRecordsDO> queryByVinSet(Set<String> vinSet) {
        return dmsOssOriginalDataRecordsMapper.queryByVinSet(vinSet);
    }

    @Override
    public void updateBatch(List<DmsOssOriginalDataRecordsDO> recordsDOList) {
        dmsOssOriginalDataRecordsMapper.updateBatch(recordsDOList);
    }

    @Override
    public void update(DmsOssOriginalDataRecordsDO dataRecordsDO) {
        dmsOssOriginalDataRecordsMapper.updateById(dataRecordsDO);
    }

    @Override
    public void insert(DmsOssOriginalDataRecordsDO dataRecordsDO) {
        dmsOssOriginalDataRecordsMapper.insert(dataRecordsDO);
    }
}

