package com.jlr.ecp.subscription.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderChargeResponseData {
    /**
     *  客户ID
     * */
    @JsonProperty("cid")
    private String cid;

    /**
     * 订单编号
     * */
    @JsonProperty("order_no")
    private Long orderNo;

    /**
     * 客户订单ID
     * */
    @JsonProperty("cus_order_id")
    private String cusOrderId;

    /**
     *  数量
     * */
    @JsonProperty("quantity")
    private Integer quantity;

    /**
     *  单价
     * */
    @JsonProperty("unit_price")
    private Double unitPrice;

    /**
     *  策略编号
     * */
    @JsonProperty("hit_strategy_no")
    private String hitStrategyNo;

    /**
     *  vin码
     * */
    private String vid;

    /**
     *  SID 列表
     * */
    @JsonProperty("sid_list")
    private List<String> sidList;
}
