package com.jlr.ecp.subscription.api.search;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.pivi.search.ServiceExpireBatchQueryApi;
import com.jlr.ecp.subscription.dal.dataobject.search.ExpireSearchBatchRecordDO;
import com.jlr.ecp.subscription.dal.mysql.search.ExpireSearchBatchRecordDOMapper;
import com.jlr.ecp.subscription.enums.amap.DealStatusEnum;
import com.jlr.ecp.subscription.service.search.VehicleExpireBatchSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class ServiceExpireBatchQueryApiImpl implements ServiceExpireBatchQueryApi {

    @Resource
    private VehicleExpireBatchSearchService vehicleExpireBatchSearch;

    @Resource
    private ExpireSearchBatchRecordDOMapper serviceBatchRecordDOMapper;

    /**
     * 服务到期批量查询补偿任务
     *
     * @param intervalTimeMinutes 查询的时间间隔（分钟），如果为null，则默认使用60分钟
     * @return CommonResult<String> 返回补偿任务执行结果，包含执行数量或没有记录需要补偿的信息
     */
    @Override
    public CommonResult<String> batchQuery(Integer intervalTimeMinutes) {
        log.info("服务到期批量查询补偿任务，intervalTimeMinutes：{}", intervalTimeMinutes);
        if (Objects.isNull(intervalTimeMinutes)) {
            intervalTimeMinutes = 60;
        }
        List<ExpireSearchBatchRecordDO> expireBatchRecordDOList = getExpireBatchRecordDOByTime(intervalTimeMinutes);
        if (CollUtil.isEmpty(expireBatchRecordDOList)) {
            return CommonResult.success("服务到期批量查询补偿任务, 没有记录需要补偿任务执行");
        }
        CompletableFuture.runAsync(() -> queryServiceRecordAndUpdate(expireBatchRecordDOList));
        String batchNos = expireBatchRecordDOList.stream()
                .map(ExpireSearchBatchRecordDO::getBatchNo)
                .collect(Collectors.joining(","));
        String resp = String.format("任务已触发，批次数量：%d，批次号：%s", expireBatchRecordDOList.size(), batchNos);
        return CommonResult.success(resp);
    }

    /**
     * 查询服务记录并更新过期结果。
     *
     * @param expireBatchRecordDOList 过期批量记录列表，包含需要查询和更新的批量记录。
     *                               如果列表为空，则函数直接返回，不执行任何操作。
     */
    private void queryServiceRecordAndUpdate(List<ExpireSearchBatchRecordDO> expireBatchRecordDOList) {
        if (CollUtil.isEmpty(expireBatchRecordDOList)) {
            return ;
        }
        int count = 0;
        for (ExpireSearchBatchRecordDO batchRecordDO : expireBatchRecordDOList) {
            Integer result = vehicleExpireBatchSearch.queryServiceAndUpdateExpireResult(batchRecordDO.getBatchNo());
            if (Objects.nonNull(result)) {
                count += result;
            }
        }
        log.info("服务到期批量查询补偿任务, 执行批次号数量：{}，更新数量：{}", expireBatchRecordDOList.size(), count);
    }

    /**
     * 根据指定的时间间隔获取未删除的过期批次记录。
     *
     * @param intervalTimeMinutes 时间间隔（分钟），用于计算过期时间。如果为null，则返回空列表。
     * @return 返回在指定时间间隔内创建的未删除的过期批次记录列表。如果intervalTimeMinutes为null，则返回空列表。
     */
    private List<ExpireSearchBatchRecordDO> getExpireBatchRecordDOByTime(Integer intervalTimeMinutes) {
        if (Objects.isNull(intervalTimeMinutes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ExpireSearchBatchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpireSearchBatchRecordDO::getIsDeleted, false)
                .eq(ExpireSearchBatchRecordDO::getDealStatus, DealStatusEnum.PROGRESS.getStatus())
                .le(ExpireSearchBatchRecordDO::getCreatedTime, LocalDateTime.now().minusMinutes(intervalTimeMinutes));
        return serviceBatchRecordDOMapper.selectList(queryWrapper);
    }
}
