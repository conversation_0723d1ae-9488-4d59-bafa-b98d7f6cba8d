package com.jlr.ecp.subscription.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClients;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.Security;

@Configuration
@Slf4j
public class MTlsConfig {
    @Value("${tls.tsdp-key-store.path}")
    private String keyStorePathTSDP;

    @Value("${tls.tsdp-key-store.password}")
    private String keyStorePasswordTSDP;

    @Value("${tls.appd-key-store.path}")
    private String keyStorePathAPPD;

    @Value("${tls.appd-key-store.password}")
    private String keyStorePasswordAPPD;

    @Value("${tls.key-password}")
    private String keyPassword;

    @Bean
    public RestTemplate restTemplate() throws Exception {
        keyStorePasswordTSDP = keyStorePasswordTSDP == null?"":keyStorePasswordTSDP;
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        Resource resource = new DefaultResourceLoader().getResource(keyStorePathTSDP);

        try (InputStream keyStoreStream = resource.getInputStream()) {
            keyStore.load(keyStoreStream, keyStorePasswordTSDP.toCharArray());
        }

        log.info("mTLS->加载证书类型：{}",keyStore.getType());
        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyManagerFactory.init(keyStore, keyStorePasswordTSDP.toCharArray());
        log.info("mTLS->初始化KeyManagerFactory完成");
        SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
        sslContext.init(keyManagerFactory.getKeyManagers(), new TrustManager[]{new NoOpTrustManager()}, null);
        log.info("mTLS->初始化SSL CONTEXT完成");
        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

        HttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslSocketFactory).build();

        log.info("mTLS-> use mtls configed resttemplate **********");
        return new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
    }

    @Bean("restTemplateAPPD")
    public RestTemplate restTemplateAPPD() throws Exception {
        // 添加 Bouncy Castle 提供者
        Security.addProvider(new BouncyCastleProvider());
        KeyStore keyStoreAppD = KeyStore.getInstance("PKCS12","BC");
        //加载APPD证书及密码
        keyStorePasswordAPPD = keyStorePasswordAPPD == null?"":keyStorePasswordAPPD;
        Resource resource = new DefaultResourceLoader().getResource(keyStorePathAPPD);
        char[] password = keyStorePasswordAPPD.toCharArray();
        try (InputStream keyStoreStream = resource.getInputStream()) {
            keyStoreAppD.load(keyStoreStream, password);
        }

        log.info("restTemplateAPPD mTLS->加载证书类型：{}",keyStoreAppD.getType());
        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyManagerFactory.init(keyStoreAppD, password);
        log.info(" restTemplateAPPD mTLS->初始化KeyManagerFactory完成");
        SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
        sslContext.init(keyManagerFactory.getKeyManagers(), new TrustManager[]{new NoOpTrustManager()}, null);
        log.info("restTemplateAPPD mTLS->初始化SSL CONTEXT完成");
        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

        HttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslSocketFactory).build();

        log.info("restTemplateAPPD mTLS-> use mtls configed resttemplate **********");
        return new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
    }
}
