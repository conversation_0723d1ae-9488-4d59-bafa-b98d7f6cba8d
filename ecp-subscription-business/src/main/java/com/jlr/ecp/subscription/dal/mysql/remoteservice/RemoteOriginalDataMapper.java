package com.jlr.ecp.subscription.dal.mysql.remoteservice;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

@Mapper
public interface RemoteOriginalDataMapper extends BaseMapperX<RemoteOriginalDataDO> {

    // 在RemoteOriginalDataMapper接口中添加方法
    int updateBatchToSuccess(@Param("ids") Collection<Long> ids);
}
