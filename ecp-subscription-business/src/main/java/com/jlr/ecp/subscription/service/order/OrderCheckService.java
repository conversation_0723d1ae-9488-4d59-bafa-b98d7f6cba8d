package com.jlr.ecp.subscription.service.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;

import java.util.List;

public interface OrderCheckService {

    /**
     * 校验是否存在在途订单
     *
     */
    CommonResult<String> checkOrderInTransit(String vin, ServiceTypeEnum serviceTypeEnum);

    /**
     * 校验是否存在在途订单
     *
     */
    CommonResult<List<String>> checkOrderInTransitByVinList(List<String> vinList, ServiceTypeEnum serviceTypeEnum);
}
