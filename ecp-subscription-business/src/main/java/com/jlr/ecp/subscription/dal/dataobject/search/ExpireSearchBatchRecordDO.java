package com.jlr.ecp.subscription.dal.dataobject.search;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_expire_search_batch_record")
public class ExpireSearchBatchRecordDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 批次处理号；批次处理号，雪花算法ID
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 校验结果；校验结果 0：不通过 1：通过
     */
    @TableField("verify_result")
    private Integer verifyResult;

    /**
     * 校验结果；校验不通过的原因
     */
    @TableField("verify_reason")
    private String verifyReason;

    /**
     *  处理状态 0：待处理 1：处理中 2：已处理
     * */
    @TableField("deal_status")
    private Integer dealStatus;

    /**
     * 操作人账号；操作人账号
     */
    @TableField("operator")
    private String operator;

    @TableField("tenant_id")
    private Integer tenantId;
}
