package com.jlr.ecp.subscription.service.iccid.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO;
import com.jlr.ecp.subscription.service.iccid.IccidModifyBatchRecordsDOService;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_iccid_modify_batch_records(t_iccid_modify_batch_records)】的数据库操作Service实现
* @createDate 2024-11-19 19:44:40
*/
@Service
public class IccidModifyBatchRecordsDOServiceImpl extends ServiceImpl<IccidModifyBatchRecordsDOMapper, IccidModifyBatchRecordsDO>
    implements IccidModifyBatchRecordsDOService{

}




