package com.jlr.ecp.subscription.api.amap;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.pivi.amap.AmaPServiceApi;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPRenewalDTO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPQueryRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPQueryRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.amap.*;
import com.jlr.ecp.subscription.enums.fufil.ServiceModifyTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchResponseDTO;
import com.jlr.ecp.subscription.model.dto.AmaPOrderChargeResponseDTO;
import com.jlr.ecp.subscription.model.dto.AmaPOrderChargerRequestDTO;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.amap.SearchAmaPExpireService;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.util.LoginUtil;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class AmaPServiceApiImpl implements AmaPServiceApi {
    @Resource
    private AmaPRenewRecordsDOMapper amaPRenewRecordsDOMapper;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private AmaPProperties amapProperties;

    @Resource
    private AmaPRenewBatchRecordsDOMapper amaPRenewBatchRecordsDOMapper;

    @Resource
    private AmaPQueryRecordsDOMapper amaPQueryRecordsDOMapper;

    @Resource
    private SearchAmaPExpireService searchAmaPExpireService;

    @Resource
    private ManualModifyLogDOService manualModifyLogDOService;


    @Resource
    private SubscriptionServiceMapper serviceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper serviceLogMapper;

    @Resource
    private PIVIPackageDOMapper pivIPackageMapper;

    @Resource
    private OrderCheckService orderCheckService;

    private static final Integer GROUP_SIZE = 5;

    /**
     * 获取待批量续签的ID列表
     *
     * @return 包含待续签协议ID列表的CommonResult对象
     */
    @Override
    public CommonResult<List<List<Long>>> getAmaPBatchRenewalIdList() {
        log.info("获取待批量续签的ID列表");
        List<List<Long>> resp = queryAmaPBatchRenewalIdGroupList(AmaPRenewStatusEnum.WAIT_RENEW.getStatus());
        return CommonResult.success(resp);
    }

    /**
     * 批量发送AMAP续费通知
     *
     * @param amaPRenewalDTO 续费信息传输对象，包含待续费的AMAP记录ID列表
     * @return 返回续费操作的结果数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Integer> batchSendAmaPRenewal(AmaPRenewalDTO amaPRenewalDTO) {
        log.info("批量发送AMAP续费, amaPRenewalDTO:{}", amaPRenewalDTO);
        List<Long> amaPRenewalIdList = amaPRenewalDTO.getAmaPRenewalIdList();
        List<AmaPRenewRecordsDO> amaPRenewRecordsDOList = queryAmaPRenewRecordsByStatus(amaPRenewalIdList,
                AmaPRenewStatusEnum.WAIT_RENEW.getStatus());
        if (CollUtil.isEmpty(amaPRenewRecordsDOList)) {
            return CommonResult.success(0);
        }
        log.info("批量发送AMAP续费, 查询amaPRenewRecordsDOList的数量:{}", amaPRenewRecordsDOList.size());
        List<List<AmaPRenewRecordsDO>> amaPRenewGroupList = amaPRenewRecordsDOListByGroup(amaPRenewRecordsDOList);
        int count = 0;
        for (List<AmaPRenewRecordsDO> amaPRenewList : amaPRenewGroupList) {
            if(CollUtil.isNotEmpty(amaPRenewList)){
                int batchNum = batchChargeAmaPRenewalAndUpdate(amaPRenewList);
                count += batchNum;
            }
        }
        for (List<AmaPRenewRecordsDO> amaPRenewDOList : amaPRenewGroupList) {
            for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewDOList) {
                if (AmaPRenewStatusEnum.RENEW_FAIL.getStatus().equals(amaPRenewRecordsDO.getRenewStatus())) {
                    continue;
                }
                amaPRenewRecordsDO.setRenewStatus(AmaPRenewStatusEnum.RENEW_PROCESS.getStatus());
            }
        }
        updateAmaPRenewRecordsGroup(amaPRenewGroupList);
        if (Boolean.TRUE.equals(amaPRenewalDTO.getEndFlag())) {
            updateAmaPRenewBatchRecords(amaPRenewalIdList);
        }
        return CommonResult.success(count);
    }

    /**
     * 获取AMAP批量续订处理中的任务ID列表
     *
     * @return 包含处理中的任务ID列表的CommonResult对象
     */
    @Override
    public CommonResult<List<Long>> getAmaPChargeProcessIdList() {
        log.info("获取AMAP批量续订处理中的任务ID列表");
        List<Long> resp = queryAmaPBatchRenewalIdListByStatus(AmaPRenewStatusEnum.RENEW_PROCESS.getStatus());
        return CommonResult.success(resp);
    }

    /**
     * 批量查询AMAP的续费结果
     *
     * @param amaPRenewalIdList 亚马逊产品续费ID列表
     * @return 返回处理结果，包含成功续费的数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Integer> batchQueryAmaPChargeResult(List<Long> amaPRenewalIdList) {
        log.info("批量查询AMAP的续费结果, amaPRenewalIdList:{}", amaPRenewalIdList);
        List<AmaPRenewRecordsDO> amaPRenewRecordsDOList = queryAmaPRenewRecordsByChargeResult(amaPRenewalIdList,
                AmaPRenewStatusEnum.RENEW_PROCESS.getStatus(), AmaPErrorCode.SUCCESSFUL.getCode());
        if (CollUtil.isEmpty(amaPRenewRecordsDOList)) {
            updateAmaPRenewBatchRecords(amaPRenewalIdList);
            return CommonResult.success(0);
        }
        List<List<AmaPRenewRecordsDO>> amaPRenewGroupList = amaPRenewRecordsDOListByGroup(amaPRenewRecordsDOList);
        log.info("批量查询AMAP的续费结果,查询之前,amaPRenewGroupList:{}", amaPRenewGroupList);
        int resp = 0;
        for (List<AmaPRenewRecordsDO> amaPRenewList : amaPRenewGroupList) {
            resp += batchResearchChargeResult(amaPRenewList);
        }
        updateAmaPRenewRecordsGroup(amaPRenewGroupList);
        updateAmaPRenewBatchRecords(amaPRenewalIdList);
        return CommonResult.success(resp);
    }

    /**
     * 查询进行中的地图查询任务ID列表
     *
     * @return 返回一个包含进行中的地图查询任务ID列表的CommonResult对象
     */
    @Override
    public CommonResult<List<Long>> getAmaPQueryProgressIdList() {
        List<Long> resp = queryAmaPQueryListByStatus(AmaPQueryStatusEnum.PROGRESS.getQueryStatus());
        return CommonResult.success(resp);
    }

    /**
     * 批量查询AMAP的到期日
     *
     * @param amaPQueryIdList 查询的AMAP任务ID列表
     * @return 包含查询结果的CommonResult对象，返回成功且结果为查询的ID列表大小
     */
    @Override
    public CommonResult<Integer> batchQueryAmaPProgress(List<Long> amaPQueryIdList) {
        if(CollUtil.isEmpty(amaPQueryIdList)) {
            return CommonResult.success(0);
        }
        List<AmaPQueryRecordsDO> amaPQueryRecordsDOList = getAmaPQueryByIdList(amaPQueryIdList);
        Map<Long, List<AmaPQueryRecordsDO>> map = getAmaPQueryMap(amaPQueryRecordsDOList);
        for (Map.Entry<Long, List<AmaPQueryRecordsDO>> entry : map.entrySet()) {
            searchAmaPExpireService.batchQueryAmaPExpireDate(entry.getValue(), entry.getKey());
        }
        return CommonResult.success(amaPQueryIdList.size());
    }

    /**
     * 将查询记录列表转换为映射表
     *
     * @param amaPQueryList AmaPQueryRecordsDO对象列表，代表查询记录列表
     * @return 返回一个Map，其中键为renewNo，值为具有相同renewNo的记录列表
     */
    private Map<Long, List<AmaPQueryRecordsDO>> getAmaPQueryMap(List<AmaPQueryRecordsDO> amaPQueryList) {
        Map<Long, List<AmaPQueryRecordsDO>> map = new HashMap<>();
        if (CollUtil.isEmpty(amaPQueryList)) {
            return map;
        }
        for (AmaPQueryRecordsDO amaPQueryRecordsDO : amaPQueryList) {
            List<AmaPQueryRecordsDO> list = map.getOrDefault(amaPQueryRecordsDO.getRenewNo(), new ArrayList<>());
            list.add(amaPQueryRecordsDO);
            map.put(amaPQueryRecordsDO.getRenewNo(), list);
        }
        return map;
    }

    /**
     * 根据ID列表查询高德查询记录
     *
     * @param amaPQueryIdList 高德查询记录ID列表，用于指定要查询的记录
     * @return 返回查询到的高德查询记录列表，若无匹配记录则可能返回空列表
     */
    private List<AmaPQueryRecordsDO> getAmaPQueryByIdList(List<Long> amaPQueryIdList) {
        LambdaQueryWrapper<AmaPQueryRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AmaPQueryRecordsDO::getId, amaPQueryIdList)
                .eq(AmaPQueryRecordsDO::getIsDeleted, false);
        return amaPQueryRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据查询状态获取查询结果列表
     *
     * @param queryStatus 查询状态，用于过滤查询记录
     * @return 包含指定查询状态的查询记录的ID列表如果查询状态为null或没有匹配的记录，则返回空列表
     */
    private List<Long> queryAmaPQueryListByStatus(Integer queryStatus) {
        if (Objects.isNull(queryStatus)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AmaPQueryRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AmaPQueryRecordsDO::getId)
                .eq(AmaPQueryRecordsDO::getQueryStatus, queryStatus)
                .eq(AmaPQueryRecordsDO::getIsDeleted, false);
        List<AmaPQueryRecordsDO> amaPQueryRecordsDOList = amaPQueryRecordsDOMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(amaPQueryRecordsDOList)) {
            return new ArrayList<>();
        }
        return amaPQueryRecordsDOList.stream()
                .map(AmaPQueryRecordsDO::getId)
                .collect(Collectors.toList());
    }


    /**
     * 更新指定批次号的续订批次记录状态
     *
     * @param amaPRenewalIdList 亚马逊产品续费ID列表
     */
    private void updateAmaPRenewBatchRecords(List<Long> amaPRenewalIdList) {
        log.info("更新指定批次号的续订批次记录状态, amaPRenewalIdList:{}", amaPRenewalIdList);
        List<AmaPRenewRecordsDO> amaPRenewRecordList = queryAmaPRenewListByIdList(amaPRenewalIdList);
        if (CollUtil.isEmpty(amaPRenewRecordList)) {
            log.info("更新指定批次号的续订批次记录状态, 查询amaPRenewRecordList为空, amaPRenewalIdList:{}", amaPRenewalIdList);
            return ;
        }
        Set<Long> batchNoSet = amaPRenewRecordList.stream()
                .map(AmaPRenewRecordsDO::getRenewNo)
                .collect(Collectors.toSet());
        log.info("更新指定批次号的续订批次记录状态, batchNoSet:{}", batchNoSet);
        List<AmaPRenewBatchRecordsDO> amaPBatchRecordList = queryAmaPBatchByBatchNoList(new ArrayList<>(batchNoSet));
        if (CollUtil.isEmpty(amaPBatchRecordList)) {
            log.info("更新指定批次号的续订批次记录状态, 查询amaPBatchRecordList为空, batchNoSet:{}", batchNoSet);
            return ;
        }
        log.info("更新指定批次号的续订批次记录状态，查询AmaPRenewBatchRecordsDO的数量为:{}", amaPBatchRecordList.size());
        for (AmaPRenewBatchRecordsDO amaPRenewBatchRecordsDO : amaPBatchRecordList) {
            amaPRenewBatchRecordsDO.setDealStatus(DealStatusEnum.COMPLETED.getStatus());
            amaPRenewBatchRecordsDO.setUpdatedTime(LocalDateTime.now());
        }
        amaPRenewBatchRecordsDOMapper.updateBatch(amaPBatchRecordList);
    }

    /**
     * 批量更新AMAP续费记录列表

     * @param amaPRenewGroupList 续费记录列表
     */
    private void updateAmaPRenewRecordsGroup(List<List<AmaPRenewRecordsDO>> amaPRenewGroupList) {
        if (CollUtil.isEmpty(amaPRenewGroupList)) {
            return ;
        }
        List<AmaPRenewRecordsDO> resp = new ArrayList<>();
        for (List<AmaPRenewRecordsDO> amaPRenewList : amaPRenewGroupList) {
            for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
                amaPRenewRecordsDO.setUpdatedTime(LocalDateTime.now());
            }
            resp.addAll(amaPRenewList);
        }
        amaPRenewRecordsDOMapper.updateBatch(resp);

        //操作日志记录 -- 过滤出AMap续费成功的记录进行日志保存
        List<AmaPRenewRecordsDO> list = resp.stream().filter(amaPRenewRecordsDO ->
                        AmaPRenewStatusEnum.RENEW_SUCCESS.getStatus().equals(amaPRenewRecordsDO.getRenewStatus()))
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(list)){
            manualModifyLogDOService.recordLogAmapList(resp);
        }
    }

    /**
     * 批量更新服务或PIVI包的到期时间
     *
     * @param amaPRenewList AmaPRenewRecordsDO对象列表，包含需要更新的记录信息
     */
    public void batchUpdateServiceOrPIVIPackage(List<AmaPRenewRecordsDO> amaPRenewList) {
        if (CollUtil.isEmpty(amaPRenewList)) {
            log.info("批量更新服务或PIVI包的到期时间, amaPRenewList为空");
            return ;
        }
        log.info("批量更新服务或PIVI包的到期时间, amaPRenewList的数量:{}", amaPRenewList.size());
        List<String> allCarVinList = amaPRenewList.stream().map(AmaPRenewRecordsDO::getCarVin).collect(Collectors.toList());
        //1.AmaP查询ServiceDO
        Map<String, SubscriptionServiceDO> serviceDOMap = getSubServiceDOMap(allCarVinList,
                ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName());
        //2.ServiceDO不为空，表示用户已经登录了，直接修改ServiceDO和添加日志
        if (CollUtil.isNotEmpty(serviceDOMap)) {
            batchInsertAmaPServiceDOLog(amaPRenewList, serviceDOMap);
            batchUpdateAmaPServiceDO(amaPRenewList, serviceDOMap);
        }
        //3.ServiceDO为空，表示用户未登录了，修改PIVIPack中的Amap的到期时间
        List<String> pivIVinList = filterServiceDOCarVinList(serviceDOMap, allCarVinList);
        Map<String, PIVIPackageDO> piviPackageDOMap = getPIVIPackageDOMap(pivIVinList);
        if (CollUtil.isEmpty(piviPackageDOMap)) {
            log.info("批量更新服务或PIVI包的到期时间, SubscriptionServiceDO和PIVIPackageDO都为空, pivIVinList:{}", pivIVinList);
            return ;
        }
        batchUpdatePIVIPackageDO(amaPRenewList, piviPackageDOMap);
    }

    /**
     * 过滤出未订阅服务的车辆VIN列表
     *
     * @param serviceDOMap 服务DO的映射，键为车辆VIN，值为订阅服务的详细信息
     * @param allCarVinList 所有车辆的VIN列表
     * @return 未订阅服务的车辆VIN列表如果服务DO映射为空，则返回所有车辆VIN列表
     */
    public List<String> filterServiceDOCarVinList(Map<String, SubscriptionServiceDO> serviceDOMap,
                                                  List<String> allCarVinList) {
        if (CollUtil.isEmpty(serviceDOMap)) {
            return allCarVinList;
        }
        List<String> piviVinList = new ArrayList<>();
        for (String carVin : allCarVinList) {
            if (serviceDOMap.containsKey(carVin)) {
                continue;
            }
            piviVinList.add(carVin);
        }
        return piviVinList;
    }

    /**
     * 批量插入AmaP服务日志
     *
     * @param amaPRenewList AmaP续费记录列表，包含待处理的续费信息
     * @param serviceDOMap 服务信息映射，通过车架号(VIN)索引服务信息
     */
    public void batchInsertAmaPServiceDOLog(List<AmaPRenewRecordsDO> amaPRenewList,
                                            Map<String, SubscriptionServiceDO> serviceDOMap) {
        if (CollUtil.isEmpty(amaPRenewList) || CollUtil.isEmpty(serviceDOMap)) {
            return ;
        }
        List<SubscriptionServiceLogDO> subServiceLogDOList = new ArrayList<>();
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            SubscriptionServiceDO subServiceDO = serviceDOMap.get(amaPRenewRecordsDO.getCarVin());
            if (Objects.isNull(subServiceDO)) {
                log.info("批量插入AmaP服务日志, amaPRenewRecordsDO的vin在服务中不存在, amaPRenewRecordsDO:{}", amaPRenewRecordsDO);
                continue;
            }
            subServiceLogDOList.add(buildSubscriptionServiceLogDO(amaPRenewRecordsDO, subServiceDO));
        }
        serviceLogMapper.insertBatch(subServiceLogDOList);
    }

    /**
     * 批量更新订阅服务信息
     *
     * @param amaPRenewList 续订记录列表，包含需要更新的车辆信息
     * @param serviceDOMap 订阅服务映射，通过车架号（Vin）索引订阅服务信息
     */
    public void batchUpdateAmaPServiceDO(List<AmaPRenewRecordsDO> amaPRenewList,
                                         Map<String, SubscriptionServiceDO> serviceDOMap) {
        if (CollUtil.isEmpty(amaPRenewList) || CollUtil.isEmpty(serviceDOMap)) {
            return ;
        }
        List<SubscriptionServiceDO> subServiceDOList = new ArrayList<>();
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            SubscriptionServiceDO subServiceDO = serviceDOMap.get(amaPRenewRecordsDO.getCarVin());
            if (Objects.isNull(subServiceDO)) {
                continue;
            }
            buildSubscriptionServiceDO(amaPRenewRecordsDO, subServiceDO);
            subServiceDOList.add(subServiceDO);
        }
        serviceDOMapper.updateBatch(subServiceDOList);
    }

    /**
     * 批量更新PIVIPackageDO信息
     *
     * @param amaPRenewList 续费记录列表，包含需要更新的PIV相关PackageDO的信息
     * @param piviPackageDOMap PIVPackageDO的映射，通过车架号（CarVin）关联PIVIPackageDO对象
     */
    public void  batchUpdatePIVIPackageDO(List<AmaPRenewRecordsDO> amaPRenewList,
                                          Map<String, PIVIPackageDO> piviPackageDOMap) {
        if (CollUtil.isEmpty(amaPRenewList) || CollUtil.isEmpty(piviPackageDOMap)) {
            return ;
        }
        List<PIVIPackageDO> piviPackageDOList = new ArrayList<>();
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            PIVIPackageDO piviPackageDO = piviPackageDOMap.get(amaPRenewRecordsDO.getCarVin());
            if (Objects.isNull(piviPackageDO)) {
                continue;
            }
            piviPackageDO.setUpdatedBy(LoginUtil.getLoginUserName());
            piviPackageDO.setUpdatedTime(LocalDateTime.now());
            piviPackageDO.setAmaPExpireDate(amaPRenewRecordsDO.getRenewAfterExpiryDate());
            piviPackageDOList.add(piviPackageDO);
        }
        pivIPackageMapper.updateBatch(piviPackageDOList);
    }

    /**
     * 根据续订记录构建订阅服务信息
     *
     * @param amaPRenewRecordsDO 续订记录对象，包含续订相关信息
     * @param amaPServiceDO 订阅服务对象，将被更新以反映续订变更
     */
    public void buildSubscriptionServiceDO(AmaPRenewRecordsDO amaPRenewRecordsDO,
                                           SubscriptionServiceDO amaPServiceDO) {
        if(Objects.isNull(amaPRenewRecordsDO) || Objects.isNull(amaPServiceDO)) {
            return ;
        }
        amaPServiceDO.setExpiryDate(amaPRenewRecordsDO.getRenewAfterExpiryDate());
        if (Objects.nonNull(amaPRenewRecordsDO.getRenewAfterExpiryDate())) {
            amaPServiceDO.setExpireDateUtc0(amaPRenewRecordsDO.getRenewAfterExpiryDate().minusHours(8));
        }
        amaPServiceDO.setUpdatedBy(LoginUtil.getLoginUserName());
        amaPServiceDO.setUpdatedTime(LocalDateTime.now());
    }

    /**
     * 构建订阅服务日志对象
     *
     * @param amaPRenewRecordsDO 续订记录对象，包含续订相关信息
     * @param amaPServiceDO 订阅服务对象，包含服务详情
     * @return 如果输入参数为空，则返回null；否则返回构建的订阅服务日志对象
     */
    public SubscriptionServiceLogDO buildSubscriptionServiceLogDO(AmaPRenewRecordsDO amaPRenewRecordsDO,
                                                                  SubscriptionServiceDO amaPServiceDO) {
        if (Objects.isNull(amaPRenewRecordsDO) || Objects.isNull(amaPServiceDO)) {
            return null;
        }
        return SubscriptionServiceLogDO.builder()
                .subscriptionId(amaPServiceDO.getSubscriptionId())
                .fufilmentId(String.valueOf(amaPRenewRecordsDO.getRenewNo()))
                .refreshBeforeDate(amaPServiceDO.getExpiryDate())
                .refreshAfterDate(amaPRenewRecordsDO.getRenewAfterExpiryDate())
                .serviceName(ServicePackageEnum.CONNECTED_NAVIGATION.getDescCN())
                .modifyType(ServiceModifyTypeEnum.MANUAL_RENEWAL.getType())
                .build();
    }

    /**
     * 根据车辆VIN列表和包名获取订阅服务的映射
     *
     * @param carVinList 车辆VIN列表，用于查询订阅服务信息
     * @param packageName 包名，用于过滤查询结果
     * @return 返回一个映射，键为车辆VIN，值为对应的订阅服务对象
     */
    public Map<String, SubscriptionServiceDO> getSubServiceDOMap(List<String> carVinList, String packageName) {
        Map<String, SubscriptionServiceDO> map = new HashMap<>();
        List<SubscriptionServiceDO> serviceDOList = batchFindServiceDOByVinAndName(carVinList, packageName);
        if (CollUtil.isEmpty(serviceDOList)) {
            return map;
        }
        for (SubscriptionServiceDO subServiceDO : serviceDOList) {
            map.put(subServiceDO.getCarVin(), subServiceDO);
        }
        return map;
    }

    /**
     * 根据VIN列表获取对应的PIVIPackage信息映射
     *
     * @param piviVinList PIVI VIN列表，用于查询PIVI套餐信息
     * @return 返回一个映射，键为VIN，值为对应的PIVI套餐信息对象PIVIPackageDO
     */
    public Map<String, PIVIPackageDO> getPIVIPackageDOMap(List<String> piviVinList) {
        Map<String, PIVIPackageDO> piviPackageDOMap = new HashMap<>();
        List<PIVIPackageDO> piviPackageDOList = batchQueryPIVIPackageByCarVin(piviVinList);
        if (CollUtil.isEmpty(piviPackageDOList)) {
            return piviPackageDOMap;
        }
        for (PIVIPackageDO piviPackageDO : piviPackageDOList) {
            piviPackageDOMap.put(piviPackageDO.getVin(), piviPackageDO);
        }
        return piviPackageDOMap;
    }

    /**
     * 根据车辆VIN码批量查询PIVI包装信息
     *
     * @param piviVinList 车辆VIN码列表，用于查询PIVI包装信息
     * @return 包含PIVI包装信息的列表如果查询不到任何记录，返回空列表
     */
    public List<PIVIPackageDO> batchQueryPIVIPackageByCarVin(List<String> piviVinList) {
        if (CollUtil.isEmpty(piviVinList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PIVIPackageDO::getVin, piviVinList)
                .eq(PIVIPackageDO::getIsDeleted, false);
        return pivIPackageMapper.selectList(queryWrapper);
    }


    /**
     * 根据车辆VIN列表和服务名称批量查询服务数据
     *
     * @param carVinList 车辆VIN列表，用于查询服务数据对象
     * @param packageName 套餐名称，用于限定查询范围
     * @return 匹配的服务数据对象列表如果找不到匹配项，则返回一个空列表
     */
    public List<SubscriptionServiceDO> batchFindServiceDOByVinAndName(List<String> carVinList, String packageName) {
        if (CollUtil.isEmpty(carVinList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SubscriptionServiceDO::getCarVin, carVinList)
                .eq(SubscriptionServiceDO::getServicePackage, packageName)
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        return serviceDOMapper.selectList(queryWrapper);
    }


    /**
     * 批量查询手动续费结果
     *
     * @param amaPRenewList 订阅续费记录列表
     * @return 成功续费的数量
     */
    private int batchResearchChargeResult(List<AmaPRenewRecordsDO> amaPRenewList) {
        if (CollUtil.isEmpty(amaPRenewList)) {
            return 0;
        }
        Map<String, AmaPChargeSearchResponseDTO> chargeSearchMap = getAmaPChargeMap(amaPRenewList);
        int cnt = 0;
        List<AmaPRenewRecordsDO> successList = new ArrayList<>();
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            String key = getAmaPBatchRenewalKey(amaPRenewRecordsDO);
            if (!chargeSearchMap.containsKey(key)) {
                log.info("批量查询手动续费结果未查询到，amaPRenewRecordsDO:{}", amaPRenewRecordsDO);
            } else {
                AmaPChargeSearchResponseDTO response = chargeSearchMap.get(key);
                log.info("批量查询手动续费结果, response:{}", response);
                amaPRenewRecordsDO.setQueryResultCode(response.getCode());
                if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(response.getCode())
                        || Objects.isNull(response.getData()) || CollUtil.isEmpty(response.getData().getChargeRecords())) {
                    log.info("批量查询手动续费结果失败，amaPRenewRecordsDO:{}, response:{}", amaPRenewRecordsDO, response);
                    amaPRenewRecordsDO.setErrorDesc(response.getMessage());
                } else {
                    amaPRenewRecordsDO.setRenewStatus(AmaPRenewStatusEnum.RENEW_SUCCESS.getStatus());
                    String expireTime = response.getData().getChargeRecords().get(0).getEndTime();
                    amaPRenewRecordsDO.setRenewAfterExpiryDate(SubscribeTimeFormatUtil.stringToTimeByISOZonedDateTimeFormat(expireTime));
                    cnt++;
                    successList.add(amaPRenewRecordsDO);
                }
            }
        }
        //更新subService的AMAP到期时间
        batchUpdateServiceOrPIVIPackage(successList);
        return cnt;
    }



    /**
     * 批量续费高德地图服务，并更新续费记录
     *
     * @param amaPRenewList 待续费的高德地图服务记录列表
     * @return 更新的续费记录数量
     */
    private int batchChargeAmaPRenewalAndUpdate(List<AmaPRenewRecordsDO> amaPRenewList) {
        int cnt = 0;
        // sprint47:校验是否存在续费中的记录
        List<AmaPRenewRecordsDO> filteredList = checkProcessRecordsByVinList(amaPRenewList);
        if (CollUtil.isEmpty(filteredList)) {
            log.info("批量续费高德地图服务，全部是正在在续费中的记录，amaPRenewList:{}", amaPRenewList);
            return cnt;
        }
        // sprint47:校验是否存在在途订单
        CommonResult<List<AmaPRenewRecordsDO>> checkResult = checkOrderInTransitByVinList(filteredList);
        if (!checkResult.isSuccess()) {
            return cnt;
        }
        List<AmaPRenewRecordsDO> notInTransitVinList = checkResult.getData();
        if (CollUtil.isEmpty(notInTransitVinList)) {
            log.info("批量续费高德地图服务，全部是在途订单，filteredList:{}", filteredList);
            return cnt;
        }

        Map<String, AmaPCarInfoResponse> beforeMap = getAmaPExpireDateAsync(notInTransitVinList);
        Map<String, AmaPOrderChargeResponseDTO> chargeMap = getBatchSendAmaPRenewalMap(notInTransitVinList);

        for (AmaPRenewRecordsDO amaPRenewRecordsDO : notInTransitVinList) {
            String key = getAmaPBatchRenewalKey(amaPRenewRecordsDO);
            setRenewBeforeExpiryDate(amaPRenewRecordsDO, beforeMap, key);
            if (!chargeMap.containsKey(key)) {
                log.info("高德地图续费订单创建没有创建，amaPRenewRecordsDO:{}", amaPRenewRecordsDO);
            } else {
                AmaPOrderChargeResponseDTO chargeResponse = chargeMap.get(key);
                amaPRenewRecordsDO.setOrderResultCode(chargeResponse.getCode());
                if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(chargeResponse.getCode())
                        || CollUtil.isEmpty(chargeResponse.getData())) {
                    log.info("高德地图续费订单创建异常，amaPRenewRecordsDO:{}, chargeResponse:{}", amaPRenewRecordsDO, chargeResponse);
                    amaPRenewRecordsDO.setRenewStatus(AmaPRenewStatusEnum.RENEW_FAIL.getStatus());
                    amaPRenewRecordsDO.setErrorDesc(chargeResponse.getMessage());
                } else {
                    cnt++;
                }
            }
        }
        return cnt;
    }

    /**
     * 设置续费前的到期日期
     *
     * 此方法旨在从给定的映射中获取车辆的到期信息，并将其设置到续费记录对象中
     * 如果给定的key在映射中不存在，或者映射中的信息不完整，则会记录相应的日志信息
     *
     * @param amaPRenewRecordsDO 续费记录对象，用于记录续费相关信息
     * @param beforeMap 包含车辆到期信息的映射，键为车辆标识，值为车辆的到期响应信息
     * @param key 车辆标识，用于在映射中查找对应的车辆到期信息
     */
    private static void setRenewBeforeExpiryDate(AmaPRenewRecordsDO amaPRenewRecordsDO, Map<String, AmaPCarInfoResponse> beforeMap, String key) {
        if (!beforeMap.containsKey(key)) {
            log.info("高德地图未查询到车辆到期信息，amaPRenewRecordsDO:{}", amaPRenewRecordsDO);
        } else {
            AmaPCarInfoResponse beforeResponse = beforeMap.get(key);
            if (Objects.isNull(beforeResponse) || Objects.isNull(beforeResponse.getData()) ||
                    CollUtil.isEmpty(beforeResponse.getData().getPermissions())) {
                log.info("高德地图查询到车辆到期信息异常，amaPRenewRecordsDO:{}, beforeResponse:{}", amaPRenewRecordsDO, beforeResponse);
            } else {
                amaPRenewRecordsDO.setRenewBeforeExpiryDate(beforeResponse.getData().getPermissions().get(0).getEndTime());
            }
        }
    }

    /**
     * 根据续费记录列表获取高德充电站搜索响应的映射
     *
     * @param amaPRenewList 高德的续费记录列表，包含多个续费记录
     * @return 高德充电站搜索响应的映射，以指定的键值对形式存储每个充电站的信息
     */
    private Map<String, AmaPChargeSearchResponseDTO> getAmaPChargeMap(List<AmaPRenewRecordsDO> amaPRenewList) {
        Map<String, AmaPChargeSearchResponseDTO> map = new HashMap<>();
        Map<AmaPRenewRecordsDO, CompletableFuture<AmaPChargeSearchResponseDTO>> futureMap = new HashMap<>();
        if (CollUtil.isEmpty(amaPRenewList)) {
            return map;
        }
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            CompletableFuture<AmaPChargeSearchResponseDTO> future = CompletableFuture.supplyAsync(
                    () -> piviAmaPService.queryAmaPChargeInfo(amaPRenewRecordsDO.getCusOrderId())
            );
            futureMap.put(amaPRenewRecordsDO, future);
        }
        CompletableFuture.allOf(futureMap.values().toArray(new CompletableFuture[0]));
        for (Map.Entry<AmaPRenewRecordsDO, CompletableFuture<AmaPChargeSearchResponseDTO>> entry : futureMap.entrySet()) {
            AmaPRenewRecordsDO amaPRenewRecordsDO = entry.getKey();
            String key = getAmaPBatchRenewalKey(amaPRenewRecordsDO);
            map.put(key, entry.getValue().join());
        }
        return map;
    }


    /**
     * 批量发送AmaP订单续费请求
     *
     * @param amaPRenewList AmaP续费记录列表
     * @return 包含每个续费请求响应的Map，键为cusOrderId，值为对应的AmaPOrderChargeResponseDTO对象
     */
    private Map<String, AmaPOrderChargeResponseDTO> getBatchSendAmaPRenewalMap(List<AmaPRenewRecordsDO> amaPRenewList) {
        Map<String, AmaPOrderChargeResponseDTO> map = new HashMap<>();
        if (CollUtil.isEmpty(amaPRenewList)) {
            return map;
        }
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            AmaPOrderChargerRequestDTO requestDTO = AmaPOrderChargerRequestDTO.builder()
                    .vid(amaPRenewRecordsDO.getCarVin())
                    .cid(getAmaPBatchRenewalCid(amaPRenewRecordsDO.getRenewYear()))
                    .cusOrderId(amaPRenewRecordsDO.getCusOrderId())
                    .amount(1)
                    .build();
            CompletableFuture<AmaPOrderChargeResponseDTO> future = CompletableFuture.supplyAsync(() -> {
                CommonResult<AmaPOrderChargeResponseDTO> result = piviAmaPService.concurrentCallAMapRenew(requestDTO);
                if (!result.isSuccess()) {
                    AmaPOrderChargeResponseDTO responseDTO = new AmaPOrderChargeResponseDTO();
                    responseDTO.setCode(String.valueOf(result.getCode()));
                    responseDTO.setMessage(result.getMsg());
                    return responseDTO;
                } else {
                    return result.getData();
                }
            });
            String key = getAmaPBatchRenewalKey(amaPRenewRecordsDO);
            map.put(key, future.join());
        }
        return map;
    }

    /**
     * 根据续保记录生成批量续保的key
     *
     * @param amaPRenewRecordsDO 续保记录对象，包含续保相关的必要信息
     * @return 如果参数对象为空，则返回空字符串；否则，返回根据续保记录信息生成的标识符字符串
     */
    private String getAmaPBatchRenewalKey(AmaPRenewRecordsDO amaPRenewRecordsDO) {
        if (Objects.isNull(amaPRenewRecordsDO)) {
            return "";
        }
        return amaPRenewRecordsDO.getRenewNo() + "," + amaPRenewRecordsDO.getCarVin() + ","
                + amaPRenewRecordsDO.getRenewYear();
    }

    /**
     * 根据续签年份获取高德地图批处理续签的cid
     *
     * @param renewYear 续签年份，支持的年份包括一年和三年
     * @return 如果续签年份为一年，则返回一年期的cid；如果续签年份为三年，则返回三年期的cid；如果续签年份不受支持，则返回空字符串
     */
    private String getAmaPBatchRenewalCid(Integer renewYear) {
        if (!AmaPRenewalYearEnum.containsRenewalYearCode(renewYear)) {
            return "";
        }
        if (AmaPRenewalYearEnum.ONE.getCode().equals(renewYear)) {
            return amapProperties.getOneYearCid();
        }
        return amapProperties.getThreeYearCid();
    }


    /**
     * 异步获取高德车辆到期信息
     *
     * @param amaPRenewList 续费记录列表，包含多个车辆的续费信息
     * @return 返回一个Map，其中键为车辆VIN号，值为从高德API获取的车辆信息
     */
    private Map<String, AmaPCarInfoResponse> getAmaPExpireDateAsync(List<AmaPRenewRecordsDO> amaPRenewList) {
        Map<String, AmaPCarInfoResponse> map = new HashMap<>();
        if (CollUtil.isEmpty(amaPRenewList)) {
            return map;
        }
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewList) {
            CompletableFuture<AmaPCarInfoResponse> future = CompletableFuture.supplyAsync(() ->
                            piviAmaPService.getCarAmaPInfo(amapProperties.getPid(), amaPRenewRecordsDO.getCarVin()),
                    subscribeAsyncThreadPool);
            String key = getAmaPBatchRenewalKey(amaPRenewRecordsDO);
            map.put(key, future.join());
        }
        return map;
    }

    /**
     * 根据分组大小将续订记录列表切分为多个子列表
     *
     * @param amaPRenewList 续订记录列表，包含所有需要分组的数据
     * @return 返回一个列表，其中包含根据分组大小切分的多个子列表
     */
    private List<List<AmaPRenewRecordsDO>> amaPRenewRecordsDOListByGroup(List<AmaPRenewRecordsDO> amaPRenewList) {
        List<List<AmaPRenewRecordsDO>> resp = new ArrayList<>();
        if (CollUtil.isEmpty(amaPRenewList)) {
            return resp;
        }
        for (int i = 0; i < amaPRenewList.size(); i += GROUP_SIZE) {
            int endIdx = Math.min(amaPRenewList.size(), i+GROUP_SIZE);
            List<AmaPRenewRecordsDO> group = amaPRenewList.subList(i, endIdx);
            resp.add(group);
        }
        return resp;
    }

    /**
     * 根据续签ID列表查询续签记录列表
     *
     * @param amaPRenewalIdList 续签ID列表，用于标识需要查询的续签记录
     * @return 返回一个包含所有查询到的续签记录的列表
     */
    private List<AmaPRenewRecordsDO> queryAmaPRenewListByIdList(List<Long> amaPRenewalIdList) {
        log.info("根据续签ID列表查询续签记录列表, amaPRenewalIdList:{}", amaPRenewalIdList);
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AmaPRenewRecordsDO::getId, amaPRenewalIdList)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        return amaPRenewRecordsDOMapper.selectList(queryWrapper);
    }


    /**
     * 根据收费结果查询续费记录
     *
     * @param amaPRenewalIdList 续费ID列表
     * @param renewalStatus 续费状态
     * @param orderResultCode 订单续费结果代码
     * @return 符合条件的续费记录列表
     */
    private List<AmaPRenewRecordsDO> queryAmaPRenewRecordsByChargeResult(List<Long> amaPRenewalIdList,
                                                                         Integer renewalStatus,
                                                                         String orderResultCode) {
        log.info("根据收费结果查询续费记录, amaPRenewalIdList:{}, renewalStatus:{}", amaPRenewalIdList, renewalStatus);
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AmaPRenewRecordsDO::getId, amaPRenewalIdList)
                .eq(AmaPRenewRecordsDO::getRenewStatus, renewalStatus)
                .eq(AmaPRenewRecordsDO::getOrderResultCode, orderResultCode)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        return amaPRenewRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据状态查询续费记录
     *
     * @param amaPRenewalIdList 续费ID列表，用于指定查询范围
     * @return 返回符合条件的续费记录列表
     */
    private List<AmaPRenewRecordsDO> queryAmaPRenewRecordsByStatus(List<Long> amaPRenewalIdList, Integer renewalStatus) {
        log.info("根据状态查询续费记录, amaPRenewalIdList:{}, renewalStatus:{}", amaPRenewalIdList, renewalStatus);
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AmaPRenewRecordsDO::getId, amaPRenewalIdList)
                .eq(AmaPRenewRecordsDO::getRenewStatus, renewalStatus)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        return amaPRenewRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据续费状态查询AmaP批量续费任务ID列表
     *
     * @param renewalStatus 续费状态，用于过滤需要的续费记录
     * @return 返回符合条件的续费任务ID列表，如果没有找到则返回空列表
     */
    private List<Long> queryAmaPBatchRenewalIdListByStatus(Integer renewalStatus) {
        log.info("根据续费状态查询AmaP批量续费任务ID列表, renewalStatus:{}", renewalStatus);
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AmaPRenewRecordsDO::getId);
        queryWrapper.eq(AmaPRenewRecordsDO::getRenewStatus, renewalStatus)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        List<AmaPRenewRecordsDO> amaPRenewRecordsDOList = amaPRenewRecordsDOMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(amaPRenewRecordsDOList)) {
            log.info("未查询到待续费的记录, renewalStatus:{}", renewalStatus);
            return new ArrayList<>();
        }
        return amaPRenewRecordsDOList.stream().map(AmaPRenewRecordsDO::getId).collect(Collectors.toList());
    }

    /**
     * 根据续签状态分批查询续签ID分组列表
     *
     * @param renewalStatus 续签状态
     * @return 续签ID分组列表
     */
    private List<List<Long>> queryAmaPBatchRenewalIdGroupList(Integer renewalStatus) {
        log.info("根据续签状态分批查询续签ID分组列表, renewalStatus:{}", renewalStatus);
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AmaPRenewRecordsDO::getId);
        queryWrapper.eq(AmaPRenewRecordsDO::getRenewStatus, renewalStatus)
                .eq(AmaPRenewRecordsDO::getIsDeleted, false);
        List<AmaPRenewRecordsDO> amaPRenewRecordsDOList = amaPRenewRecordsDOMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(amaPRenewRecordsDOList)) {
            log.info("根据续签状态分批查询续签ID分组列表为空, renewalStatus:{}", renewalStatus);
            return new ArrayList<>();
        }
        List<List<Long>> resp = new ArrayList<>();
        Map<Long, List<Long>> map = new HashMap<>();
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : amaPRenewRecordsDOList) {
            List<Long> list = map.getOrDefault(amaPRenewRecordsDO.getRenewNo(), new ArrayList<>());
            list.add(amaPRenewRecordsDO.getId());
            map.put(amaPRenewRecordsDO.getRenewNo(), list);
        }
        for (Map.Entry<Long, List<Long>> entry : map.entrySet()) {
            resp.add(entry.getValue());
        }
        return resp;
    }

    /**
     * 根据续费状态查询AMA P Renew的续费记录数量
     *
     * @param batchNo 批次编号
     * @param renewalStatus 续费状态，用于筛选符合条件的续费记录
     * @return 符合条件的续费记录数量如果查询结果为null，则返回0
     */
    private int queryAmaPRenewalCountByStatus(Long batchNo, Integer renewalStatus) {
        LambdaQueryWrapper<AmaPRenewRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmaPRenewRecordsDO::getRenewNo, batchNo)
                .eq(AmaPRenewRecordsDO::getRenewStatus, renewalStatus)
                .eq(AmaPRenewRecordsDO::getIsDeleted,false);
        Long count = amaPRenewRecordsDOMapper.selectCount(queryWrapper);
        if (Objects.isNull(count)) {
            return 0;
        }
        return count.intValue();
    }

    /**
     * 根据批次编号列表查询批量续签记录
     *
     * @param batchNoList 批次编号列表，用于查询特定的续签记录
     * @return 返回符合批次编号且未被标记为已删除的续签记录列表
     */
    private List<AmaPRenewBatchRecordsDO> queryAmaPBatchByBatchNoList(List<Long> batchNoList) {
        log.info("根据批次编号列表查询批量续签记录, batchNoList:{}", batchNoList);
        LambdaQueryWrapper<AmaPRenewBatchRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AmaPRenewBatchRecordsDO::getBatchNo, batchNoList)
                .eq(AmaPRenewBatchRecordsDO::getIsDeleted, false);
        return amaPRenewBatchRecordsDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据车架号列表检查并更新续费记录
     * 该方法主要用于识别并处理那些车架号已经存在于续费过程中的续费记录
     * 对于存在于续费过程中的车架号，将更新相关记录的状态和错误描述
     *
     * @param amaPRenewList 一组待检查的续费记录对象
     * @return 返回更新后的续费记录列表，不包含那些标识为续费中的记录
     */
    public List<AmaPRenewRecordsDO> checkProcessRecordsByVinList(List<AmaPRenewRecordsDO> amaPRenewList) {
        Long renewNo = amaPRenewList.get(0).getRenewNo();
        // renewList转为map，key为vin，value为主键id
        Map<String, Long> vinMap = amaPRenewList.stream()
                .collect(Collectors.toMap(AmaPRenewRecordsDO::getCarVin,
                        AmaPRenewRecordsDO::getId, (v1, v2) -> v1));

        // 查询是否存在非当前批次号的续费中的记录
        List<String> processVinList = amaPRenewRecordsDOMapper.getProcessRecordsByVinSetAndRenewNo(vinMap.keySet(), renewNo)
                .stream()
                // 当前记录的id在vinMap.get(records.getCarVin())之前，才认为是存在续费中的记录
                .filter(records -> records.getId() < vinMap.get(records.getCarVin()))
                .map(AmaPRenewRecordsDO::getCarVin)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(processVinList)) {
            log.info("不存在续费中的高德手动续费记录, vinSet:{}", vinMap.keySet());
            return amaPRenewList;
        }
        log.info("存在续费中的高德手动续费记录, processVinList:{}", processVinList);
        // 将存在续费中的vin更新到续费记录中
        return updateRenewRecords(amaPRenewList, processVinList);
    }

    /**
     * 根据VIN列表检查订单是否在途中
     * 此方法过滤出需要续费的车辆记录，检查这些车辆是否已有在途订单
     * 如果存在在途订单，则更新续费记录的状态，否则返回过滤后的续费记录列表
     *
     * @param filteredList 过滤后的续费记录列表，包含车辆的VIN信息
     * @return 返回一个CommonResult对象，包含续费记录列表
     */
    public CommonResult<List<AmaPRenewRecordsDO>> checkOrderInTransitByVinList(List<AmaPRenewRecordsDO> filteredList) {
        List<String> filteredVinList = filteredList.stream().map(AmaPRenewRecordsDO::getCarVin).collect(Collectors.toList());
        CommonResult<List<String>> checkOrderInTransit = orderCheckService.checkOrderInTransitByVinList(filteredVinList, ServiceTypeEnum.PIVI);
        if (!checkOrderInTransit.isSuccess()) {
            return CommonResult.error(checkOrderInTransit.getCode(), checkOrderInTransit.getMsg());
        }
        List<String> inTransitVinList = checkOrderInTransit.getData();
        if (CollUtil.isEmpty(inTransitVinList)) {
            log.info("不存在高德的在途订单, filteredVinList:{}", filteredVinList);
            return CommonResult.success(filteredList);
        }
        log.info("存在高德的在途订单, inTransitVinList:{}", inTransitVinList);
        // 将存在在途订单的vin更新到续费记录中
        return CommonResult.success(updateRenewRecords(filteredList, inTransitVinList));
    }

    /**
     * 更新续费记录
     * 根据给定的车辆VIN列表，更新续费记录列表中对应车辆的续费状态
     * 如果车辆VIN在指定列表中，则标记续费失败，否则保持原有状态
     *
     * @param renewList 续费记录列表
     * @param processVinList 正在处理的车辆VIN列表
     * @return 返回更新后的续费记录列表
     */
    private List<AmaPRenewRecordsDO> updateRenewRecords(List<AmaPRenewRecordsDO> renewList, List<String> processVinList) {
        // 需要继续处理的记录
        List<AmaPRenewRecordsDO> newRenewList = new ArrayList<>();
        for (AmaPRenewRecordsDO amaPRenewRecordsDO : renewList) {
            if (processVinList.contains(amaPRenewRecordsDO.getCarVin())) {
                amaPRenewRecordsDO.setOrderResultCode(String.valueOf(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR.getCode()));
                amaPRenewRecordsDO.setRenewStatus(AmaPRenewStatusEnum.RENEW_FAIL.getStatus());
                amaPRenewRecordsDO.setErrorDesc(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR.getMsg());
            } else {
                newRenewList.add(amaPRenewRecordsDO);
            }
        }
        return newRenewList;
    }
}
