package com.jlr.ecp.subscription.enums.appd;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RenewServiceTypeEnum {

    APPD(1, "信息娱乐服务"),
    UNICOM(2, "网络流量");

    private final Integer serviceType;

    private final String serviceName;

    /**
     * 根据服务类型获取服务名称
     *
     * @param serviceType 服务类型，用于匹配 RenewServiceTypeEnum 中定义的服务类型
     * @return 匹配的服务名称，如果未找到匹配项，则返回空字符串
     */
    public static String getServiceName(Integer serviceType) {
        for (RenewServiceTypeEnum typeEnum : values()) {
            if (typeEnum.getServiceType().equals(serviceType)) {
                return typeEnum.getServiceName();
            }
        }
        return "";
    }
}
