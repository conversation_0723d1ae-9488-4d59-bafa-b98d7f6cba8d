--- #################### 数据库相关配置 ####################
spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        ecp_system:
          name: ecp_system
          url: jdbc:mysql://${AURORA_ADDR_WRITE}/${spring.datasource.dynamic.datasource.ecp_system.name}?allowMultiQueries=true&useUnicode=true&useSSL=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          driver-class-name: com.mysql.jdbc.Driver
          username: ${DB_USER}
          password: ${DB_PASSWD}
          ssl:
            trust-store: file:/home/<USER>/ecp/rds.truststore.jks
            trust-store-password: ${RDS_TRUST_STORE_PASSWORD}
        master:
          name: ecp_subscription
          url: jdbc:mysql://${AURORA_ADDR_WRITE}/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          driver-class-name: com.mysql.jdbc.Driver
          username: ${DB_USER}
          password: ${DB_PASSWD}
          ssl:
            trust-store: file:/home/<USER>/ecp/rds.truststore.jks
            trust-store-password: ${RDS_TRUST_STORE_PASSWORD}
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          name: ecp_subscription
          url: jdbc:mysql://${AURORA_ADDR_READER}/${spring.datasource.dynamic.datasource.slave.name}?allowMultiQueries=true&useUnicode=true&useSSL=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          driver-class-name: com.mysql.jdbc.Driver
          username: ${DB_USER}
          password: ${DB_PASSWD}
          ssl:
            trust-store: file:/home/<USER>/ecp/rds.truststore.jks
            trust-store-password: ${RDS_TRUST_STORE_PASSWORD}

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    redisson:
      config: |
        clusterServersConfig:
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 3000
          retryAttempts: 3
          retryInterval: 1500
          failedSlaveReconnectionInterval: 3000
          failedSlaveCheckInterval: 60000
          password: ${REDIS_PASSWD:uiMYufcA8urorYSK}
          subscriptionsPerConnection: 5
          clientName: null
          loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 50
          slaveConnectionMinimumIdleSize: 6
          slaveConnectionPoolSize: 24
          masterConnectionMinimumIdleSize: 6
          masterConnectionPoolSize: 12
          readMode: "SLAVE"
          subscriptionMode: "SLAVE"
          nodeAddresses:
          - "rediss://${REDIS_ADDR_MASTER}"
          scanInterval: 1000
        threads: 16
        nettyThreads: 32
        codec: !<org.redisson.codec.Kryo5Codec> {}
        transportMode: "NIO"

#      file: classpath:redisson.yaml
#    host: ************* # 地址
#    port: 6379 # 端口
#    database: 1 # 数据库索引
#    password: 123456 # 密码，建议生产环境开启
  kafka:
#    bootstrap-servers: ${KAFKA_ADDR}
    security:
      protocol: "SSL"
    ssl:
    #客户端的信任证书库，单向TLS情况配置,表示客户端信任哪些服务端的证书
      trust-store-location: file:/home/<USER>/ecp/client.truststore.jks
      trust-store-password: ${TRUST_STORE_PASSWORD}
      trust-store-type: JKS
    bootstrap-servers: ${KAFKA_V1_ADDR}
    producer:
      # 发生错误后，消息重发的次数。
      retries: 3
      #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
#      batch-size: 16384
      # 设置生产者内存缓冲区的大小。
#      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: 1
    consumer:
      group-id: ${spring.application.name}
      auto-commit-interval: 100
      enable-auto-commit: true
#      auto-offset-reset: earliest
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      # 在侦听器容器中运行的线程数。
#      concurrency: 5
      # listner负责ack，每调用一次，就立即commit
#      ack-mode: manual_immediate
      missing-topics-fatal: false





--- #################### MQ 消息队列相关配置 ####################
spring:
  cloud:
    stream:
      rocketmq:
        # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
        binder:
          name-server: **************:9876 # RocketMQ Namesrv 地址

--- #################### 定时任务相关配置 ####################
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin # 调度中心部署跟地址
    enabled: false

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring


--- #################### RR低代码相关配置 ####################

# RR低代码配置项，设置当前项目所有自定义的配置
lc:
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  pay:
    pay-notify-url: http://niubi.natapp1.cc/api/pay/order/notify
    pay-return-url: http://niubi.natapp1.cc/api/pay/order/return
    refund-notify-url: http://niubi.natapp1.cc/api/pay/refund/notify
  demo: false # 开启演示模式

dataCenterFlag: product-data-center
workFlag: product-work

dp:
  appKey: ${DP_APPID}
  appSecret: ${DP_APPSECRET}
  appId: 10144
  host: dp-service-prod.jlrinfo.cn

tsdp:
  ifAsToken: ${TSDP_IFASTOKEN}
  ifEcomToken: ${TSDP_IFECOMTOKEN}
  icrLoginUrl: ${TSDP_ICRLOGINURL}
  icrSubscriptionUrl: ${TSDP_ICRSUBSCRIPTIONURL}
  subscriptionExpireUrl: ${TSDP_SUBSCRIPTIONEXPIREURL}
  subscriptionRenewUrl: ${TSDP_SUBSCRIPTIONRENEWURL}
  getByVinUrl: https://ifecom.prodfeu-chn.jlrmotor.com/subscriptions/vehicles/

tls:
  tsdp-key-store:
    path: classpath:/prod/client.p12
    password:
  appd-key-store:
    path: classpath:/prod/appd_prod.p12
    password: ecp
  key-password:

appd:
  tokenUrl: https://pgws.prod-chn-ci.jlrmotor.com/oauth2/b2b/token
  subUrl: https://pgws.prod-chn-ci.jlrmotor.com/b2b/SCP/IF_ServiceProvider/1.0.0/serviceProvider/subscriptionRequests
  getUrl: https://pgws.prod-chn-ci.jlrmotor.com/b2b/SCP/IF_ServiceProvider/1.0.0/serviceProvider/vehicles/{vin}/subscriptions
  grantType: client_credentials
  clientId: JLR.ECP
  clientSecret: ${APPD_CLIENT_SECRET}

unicom:
  productId: 1054102
  url: http://vpce-0e8961d1dc169e0fb-bgk6iato.vpce-svc-041703cda97105d3e.cn-northwest-1.vpce.amazonaws.com.cn:17022/jlrwebservice/api/service
  appkey: ${UNICOM_APPKEY}
  version: 1.0
  industryCode: jlrecp
  rnrTemplateUrl: https://ecp-static.jaguarlandrover.cn/file/ECP_实名制批量查询上传模板.xlsx
  rnrJobName: rnrBatchQueryParseJob

access:
  token:
    url: ${FORGEROCK_TOKEN_URL}
    grantType: client_credentials
    scope: ${FORGEROCK_TOKEN_SCOPE}
    clientId: ${APIM_CLIENTID}
    clientAssertionType: urn:ietf:params:oauth:client-assertion-type:jwt-bearer

apimService:
  prefixUrl: https://apimnw.awscn.jlrint.com
  sotaPrefixUrl: /sota/v1/vehicles/
  sotaSuffixUrl: /serialised-components
  apiKey: ${APIM_APIKEY}
  apiSecret:

forgeRock:
  requestUrl: ${FORGEROCK_REQUESTURL}
  clientId: ${APIM_CLIENTID}
  jti: 1jBxCpWVeIal0EFmeJfUU
  privateKey: ${FORGEROCK_PKCS8}
  publicKey: ${FORGEROCK_PUBLICKEY}

amap:
  url: ${AMAP_URL}
  accessKey: ${AMAP_ACCESS_KEY}
  secretKey: ${AMAP_SECRET_KEY}
  oneYearCid: ${AMAP_ONE_YEAR_CID}
  threeYearCID: ${AMAP_THREE_YEAR_CID}
  pid: ${AMAP_PID}
  renewalExcelUrl: ${AMAP_RENEWAL_EXCEL_URL}
  queryExcelUrl: ${AMAP_QUERY_EXCEL_URL}

bau:
  oss:
    ak: ${ALIYUN_OSS_AK}
    sk: ${ALIYUN_OSS_SK}
    bucket: oss-dp-prd-dms
    endpoint: oss-cn-beijing.aliyuncs.com
  queryExcelUrl: https://ecp-static.jaguarlandrover.cn/file/bau/车辆在线服务入库批量查询模板.xlsx

appduc:
  renewalExcelUrl: https://ecp-static.jaguarlandrover.cn/file/appduc/APPD&CU批量续费上传模板.xlsx

iccid:
  modifyExcelUrl: https://ecp-static.jaguarlandrover.cn/file/iccid/ICCID修改上传模版.xlsx