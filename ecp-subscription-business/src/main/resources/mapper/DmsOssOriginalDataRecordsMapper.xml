<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataRecordsMapper">
    <sql id="tableName">
        t_dms_oss_original_data_records
    </sql>

    <sql id="baseColumn">
        id,bau_job_id,data_id,car_vin,sota_result,appd_result,cu_result,amap_result,dp_result,appd_sync_result,cu_sync_result,tenant_id,created_by,created_time,updated_by,updated_time,is_deleted,revision
    </sql>
    <select id="queryByVinSet"
            resultType="com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO">
        SELECT * FROM
        t_dms_oss_original_data_records
        where id in
        (
            SELECT MAX(id) FROM
            t_dms_oss_original_data_records
            WHERE car_vin IN
            <foreach item="item" index="index" collection="vinSet" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY car_vin
        )
        AND is_deleted = 0
    </select>

</mapper>