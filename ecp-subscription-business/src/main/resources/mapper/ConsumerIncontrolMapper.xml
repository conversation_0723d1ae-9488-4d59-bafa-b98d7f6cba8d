<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerIncontrolMapper">
    <sql id="tableName">
        t_consumer_incontrol
    </sql>

    <sql id="baseColumn">
        id,consumer_code,incontrol_id,bind_time,unbind_time,bind_status,tenant_id,created_by,created_time,updated_by,updated_time,is_deleted,revision
    </sql>
    <update id="unBindICR">
        update t_consumer_incontrol
        set
        bind_status = 0,is_deleted = 1
        where
        incontrol_id = #{inControlID}
        AND bind_channel = #{clientId}
    </update>

</mapper>