<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fufilmentId" column="fufilment_id" jdbcType="VARCHAR"/>
            <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
            <result property="orderItemCode" column="order_item_code" jdbcType="VARCHAR"/>
            <result property="carVin" column="car_vin" jdbcType="VARCHAR"/>
            <result property="serviceBeginDate" column="service_begin_date" jdbcType="TIMESTAMP"/>
            <result property="serviceEndDate" column="service_end_date" jdbcType="TIMESTAMP"/>
            <result property="serviceStatus" column="service_status" jdbcType="INTEGER"/>
            <result property="orderItemRemark" column="order_item_remark" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fufilment_id,order_code,
        order_item_code,car_vin,service_begin_date,
        service_end_date,service_status,order_item_remark,
        tenant_id,created_by,created_time,
        updated_by,updated_time,is_deleted,
        revision
    </sql>

    <select id="getHistoryByOrderItemCodeList" resultType="com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO">
        SELECT * FROM (
        SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY order_item_code ORDER BY id DESC) as row_num
        FROM
        t_vcs_order_fufilment
        WHERE
        order_item_code IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0
        ) AS sub_query
        WHERE sub_query.row_num <![CDATA[
           <=
        ]]> 4
    </select>

    <select id="getLatestByOrderItemCodeList"
            resultType="com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO">
        SELECT * FROM t_vcs_order_fufilment
        WHERE id IN (
        SELECT MAX(id) FROM t_vcs_order_fufilment
        WHERE order_item_code IN
        <foreach item="item" collection="orderItemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0
        GROUP BY order_item_code
        )
    </select>

    <select id="checkActiveServiceByCarVinList"
            resultType="com.jlr.ecp.subscription.api.vcsorderfufilment.vo.CarServiceStatusVO">
        SELECT
        vof.car_vin,
        SUM(CASE WHEN vof.service_status = 1 THEN 1 ELSE 0 END) as active_count,
        SUM(CASE WHEN vor.service_status = 1 THEN 1 ELSE 0 END) as rollback_active_count
        FROM
        t_vcs_order_fufilment vof
        LEFT JOIN
        t_vcs_order_rollback vor ON vof.fufilment_id = vor.fufilment_id
        WHERE
        vof.car_vin IN
        <foreach item="carVin" collection="carVinList" open="(" separator="," close=")">
            #{carVin}
        </foreach>
        GROUP BY
        vof.car_vin
    </select>

    <select id="checkActiveServiceByCarVinAndServiceType"
            resultType="com.jlr.ecp.subscription.api.vcsorderfufilment.vo.CarServiceStatusVO">
        SELECT
        vof.car_vin,
        vof.service_type,
        SUM(CASE WHEN vof.service_status = 1 THEN 1 ELSE 0 END) as active_count,
        SUM(CASE WHEN vor.service_status = 1 THEN 1 ELSE 0 END) as rollback_active_count
        FROM
        t_vcs_order_fufilment vof
        LEFT JOIN
        t_vcs_order_rollback vor ON vof.fufilment_id = vor.fufilment_id
        WHERE
        <foreach item="item" index="index" collection="carVinAndServiceTypeList" open="(" separator="or" close=")">
            vof.car_vin = #{item.carVin} and vof.service_type = #{item.serviceType}
        </foreach>
        GROUP BY
        vof.car_vin, vof.service_type
    </select>

    <select id="findActiveService"
            resultType="com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO">
        select vof.fufilment_id as fufilmentId,vof.order_code as orderCode,
        vof.order_item_code as orderItemCode, vof.vcs_order_code as vcsOrderCode,
        vof.car_vin as carVin, vof.service_end_date vofServiceEndDate,
        vor.rollback_fufilment_id as rollbackFufilmentId,vor.refund_order_code as refundOrderCode,
        vor.service_status vorServiceStatus,vor.service_end_date vorServiceEndDate, vof.service_status vofServiceStatus
        from
        t_vcs_order_fufilment vof
        left join t_vcs_order_rollback vor
        on vor.fufilment_id = vof.fufilment_id
        where vof.is_deleted = false
        and vof.service_type = 1
        and (vof.service_status = 1
        or vor.service_status = 1)
        <if test="total!=null and total!=''">
            limit  #{total}
        </if>
    </select>

    <select id="findVinAndServiceDate"
            resultType="com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO">
        select  s.car_vin as vin ,max(s.expiry_date) as serviceDate from t_subscription_service s
        left join t_remote_package rp on rp.package_code = s.service_package
        where s.service_type = 1
        and s.is_deleted = 0
        and rp.is_deleted = 0
        and rp.car_system_model = 'PIVI'
        and s.car_vin in
        <foreach item="carVin" collection="carVinList" open="(" separator="," close=")">
            #{carVin}
        </foreach>
        GROUP BY s.car_vin
    </select>

    <select id="findAllServiceStatus"
            resultType="com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO">
        select vof.fufilment_id as fufilmentId,vof.order_code as orderCode,
        vof.order_item_code as orderItemCode, vof.vcs_order_code as vcsOrderCode,
        vof.car_vin as carVin, vof.service_end_date vofServiceEndDate,
        vor.rollback_fufilment_id as rollbackFufilmentId,vor.refund_order_code as refundOrderCode,
        vor.service_status vorServiceStatus,vor.service_end_date vorServiceEndDate, vof.service_status vofServiceStatus
        from
        t_vcs_order_fufilment vof
        left join t_vcs_order_rollback vor
        on vor.fufilment_id = vof.fufilment_id
        where vof.is_deleted = false
        and vof.service_type IN (1,2)
        and vof.order_code = #{orderCode}
    </select>

</mapper>
