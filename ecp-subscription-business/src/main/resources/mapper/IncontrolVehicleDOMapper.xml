<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper">
    <sql id="tableName">
        t_incontrol_vehicle
    </sql>

    <sql id="baseColumn">
        id,car_no,consumer_code,incontrol_id,series_code,series_name,car_vin,model_year,car_system_model,bind_time,tenant_id,created_by,created_time,updated_by,updated_time,is_deleted,revision
    </sql>
    <delete id="deleteVehicleByIncontrol">
        delete from t_incontrol_vehicle
        where incontrol_id = #{incontrolId}
    </delete>
    <delete id="deleteVehicleByCarVinList">
        delete from t_incontrol_vehicle
        WHERE
        car_vin IN
        <foreach item="item" index="index" collection="carVinList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getBindIncontrolVehicle"
            resultType="com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO">
        select iv.*
        from t_incontrol_vehicle iv
        join t_consumer_incontrol ci on iv.incontrol_id = ci.incontrol_id
        where
        ci.bind_status = 1
        and ci.is_deleted = 0  and iv.is_deleted = 0
        and ci.consumer_code = #{consumerCode}
        and ci.bind_channel = #{brandCode}
        and iv.brand_code = #{brandCode}
    </select>

    <select id="selectIncontrolVehicleByCarVinList"
            resultType="com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO">
        SELECT
        id,
        car_vin,
        incontrol_id,

        series_code,
        series_name,
        brand_code,
        brand_name,
        config_code,
        config_name,
        model_year,
        car_system_model,
        hob_en,
        production_en,
        bind_time
        FROM
        t_incontrol_vehicle
        WHERE
        id IN (
        SELECT MAX(id)
        FROM t_incontrol_vehicle
        WHERE car_vin IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY car_vin
        )
    </select>


    <!-- 定义查询方法 -->
    <select id="getPhoneEncryptsByCarVinList" resultType="map">
        SELECT
        icc.phone_encrypt AS phone_encrypt,
        icv.car_vin AS car_vin
        FROM
        t_incontrol_customer icc
        INNER JOIN
        t_incontrol_vehicle icv
        ON
        icc.incontrol_id = icv.incontrol_id
        WHERE
        icv.car_vin IN
        <foreach item="item" index="index" collection="carVinList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND icc.is_deleted = 0
        AND icv.is_deleted = 0
    </select>

    <!-- 订阅服务结果映射 -->
    <resultMap id="VehicleSubscriptionResult" type="com.jlr.ecp.subscription.api.incontrol.dto.VehicleSubscriptionDTO">
        <id property="incontrolId" column="incontrol_id"/>
        <result property="carVin" column="car_vin"/>
        <result property="serviceType" column="service_type"/>
        <result property="expiryDate" column="expiry_date"/>
    </resultMap>

    <!-- 查询即将过期的订阅服务（关键修复） -->
    <select id="findSubscriptionsByIncontrolIdsAndDays" resultMap="VehicleSubscriptionResult">
        SELECT
        iv.incontrol_id,
        iv.car_vin,
        iv.brand_code,
        sv.service_type,
        sv.expiry_date
        FROM t_incontrol_vehicle iv
        JOIN t_subscription_service sv
        ON iv.car_vin = sv.car_vin
        AND iv.incontrol_id = sv.incontrol_id
        WHERE
        iv.car_system_model = 'PIVI'
        AND iv.is_deleted = 0
        AND sv.is_deleted = 0
        AND sv.service_type = #{serviceType}
        AND sv.expiry_date >= DATE_ADD(DATE(#{currentDateTime}), INTERVAL #{days} DAY)
        AND sv.expiry_date &lt; DATE_ADD(DATE(#{currentDateTime}), INTERVAL #{daysPlusOne} DAY)
        AND iv.incontrol_id IN
        <foreach item="id" collection="incontrolIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY iv.car_vin, sv.service_type
    </select>
    <select id="getReportOrds" resultType="com.jlr.ecp.subscription.service.vin.expiry.dto.IncontrolVehicleDTO">

        select
        inv.car_vin,
        inv.brand_name,
        inv.config_name,

        inv.model_year,
        inv.car_system_model,
        inv.config_code,
        inv.series_code,
        inv.series_name,

        inv.production_en
        from t_incontrol_vehicle inv
        where 1 = 0
        <if test="carVins!=null and carVins.size >0">
            or (is_deleted = 0 and car_vin in
            <foreach collection="carVins" item="item" open='(' close=')' separator=','>
                #{item}
            </foreach>)
        </if>

    </select>

    <update id="updateBatchWithNulls" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE t_incontrol_vehicle
            <set>
                <if test="item.incontrolId != null">incontrol_id = #{item.incontrolId},</if>
                <if test="item.incontrolId == null">incontrol_id = NULL,</if>

                <if test="item.incontrolPhone != null">incontrol_phone = #{item.incontrolPhone},</if>
                <if test="item.incontrolPhone == null">incontrol_phone = NULL,</if>

                <if test="item.bindTime != null">bind_time = #{item.bindTime},</if>
                <if test="item.bindTime == null">bind_time = NULL,</if>

                <if test="item.dmsInvoiceDate != null">dms_invoice_date = #{item.dmsInvoiceDate},</if>
                <if test="item.dmsInvoiceDate == null">dms_invoice_date = NULL,</if>

                <if test="item.updatedTime != null">updated_time = #{item.updatedTime},</if>
                <if test="item.updatedTime == null">updated_time = NULL,</if>

                <if test="item.revision != null">revision = #{item.revision},</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>
