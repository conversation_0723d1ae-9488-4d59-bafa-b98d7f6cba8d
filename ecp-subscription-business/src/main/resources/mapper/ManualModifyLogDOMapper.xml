<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.manuallog.ManualModifyLogDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.subscription.dal.dataobject.manuallog.ManualModifyLogDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="carVin" column="car_vin" jdbcType="VARCHAR"/>
            <result property="modifyType" column="modify_type" jdbcType="VARCHAR"/>
            <result property="modifyBeforeValue" column="modify_before_value" jdbcType="VARCHAR"/>
            <result property="modifyAfterValue" column="modify_after_value" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,car_vin,modify_type,
        modify_before_value,modify_after_value,operator,
        operate_time,tenant_id,created_by,
        created_time,updated_by,updated_time,
        is_deleted,revision
    </sql>
</mapper>
