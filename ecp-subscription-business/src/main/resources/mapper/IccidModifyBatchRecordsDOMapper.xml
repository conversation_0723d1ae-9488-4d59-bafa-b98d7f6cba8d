<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
            <result property="batchNo" column="batch_no" jdbcType="BIGINT"/>
            <result property="uploadFile" column="upload_file" jdbcType="VARCHAR"/>
            <result property="verifyResult" column="verify_result" jdbcType="INTEGER"/>
            <result property="dealStatus" column="deal_status" jdbcType="INTEGER"/>
            <result property="verifyResultFile" column="verify_result_file" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,revision,batch_no,
        upload_file,verify_result,deal_status,
        verify_result_file,operator,tenant_id,
        created_by,created_time,updated_by,
        updated_time,is_deleted
    </sql>
</mapper>
