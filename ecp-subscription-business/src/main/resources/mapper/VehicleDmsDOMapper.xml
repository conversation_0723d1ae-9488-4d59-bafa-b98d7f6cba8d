<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper">
    <select id="selectVehicleDmsDOByCarVinList" resultType="com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO">
        SELECT * FROM t_vehicle_dms
        WHERE car_vin IN
        <foreach item="vin" index="index" collection="carVinList" open="(" separator="," close=")">
            #{vin}
        </foreach>
    </select>
</mapper>