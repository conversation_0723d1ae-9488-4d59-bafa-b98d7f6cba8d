<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.remoteservice.RemoteOriginalDataMapper">


    <update id="updateBatchToSuccess">
        UPDATE t_remote_original_data
        SET status = 2,
        fail_type = NULL,
        fail_desc = NULL,
        miss_count = 0,
        updated_time = DATE_ADD(NOW(), INTERVAL 8 HOUR),  <!-- 增加8小时 -->
        revision = revision + 1
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>