<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.vehicle.VinExpiryReportDataDOMapper">


    <insert id="insertRemoteDataFromDetail">
        INSERT INTO t_vin_expiry_report_data (
        car_vin, job_id, job_date, expiry_date, service_type,
        imported_en, brand, series_name, tenant_id,
        created_time, updated_time, created_by, updated_by, is_deleted, revision
        )
        SELECT
        d.car_vin,
        d.job_id,
        d.job_date,
        d.expiry_date,
        d.service_type,
        d.imported_en,
        d.brand,
        d.series_name,
        d.tenant_id,
        DATE_ADD(NOW(), INTERVAL 8 HOUR),
        DATE_ADD(NOW(), INTERVAL 8 HOUR),
        COALESCE(d.created_by, 'system'),  <!-- 处理可能的null值 -->
        COALESCE(d.updated_by, 'system'),
        0,
        1
        FROM t_vin_expiry_monthly_detail d
        WHERE d.job_id = #{jobId}
        AND d.brand IS NOT NULL
        AND d.brand != ''
        AND d.car_system_model = 'PIVI'
        AND d.service_type = 'Remote'
    </insert>

    <insert id="insertNonRemoteDataFromDetail">
        INSERT INTO t_vin_expiry_report_data (
        car_vin, job_id, job_date, expiry_date, service_type,
        imported_en, brand, series_name, tenant_id,
        created_time, updated_time, created_by, updated_by, is_deleted, revision
        )
        SELECT
        d.car_vin,
        d.job_id,
        d.job_date,
        d.expiry_date,
        d.service_type,
        d.imported_en,
        d.brand,
        d.series_name,
        d.tenant_id,
        DATE_ADD(NOW(), INTERVAL 8 HOUR),
        DATE_ADD(NOW(), INTERVAL 8 HOUR),
        COALESCE(d.created_by, 'system'),  <!-- 处理可能的null值 -->
        COALESCE(d.updated_by, 'system'),
        0,
        1
        FROM t_vin_expiry_monthly_detail d
        WHERE d.job_id = #{jobId}
        AND d.brand IS NOT NULL
        AND d.brand != ''
        AND d.car_system_model = 'PIVI'
        AND d.service_type != 'Remote'
        AND d.real_name_flag = 1
    </insert>

</mapper>