<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="modifyNo" column="modify_no" jdbcType="BIGINT"/>
            <result property="carVin" column="car_vin" jdbcType="VARCHAR"/>
            <result property="modifyBeforeIccid" column="modify_before_iccid" jdbcType="VARCHAR"/>
            <result property="modifyAfterIccid" column="modify_after_iccid" jdbcType="VARCHAR"/>
            <result property="modifyStatus" column="modify_status" jdbcType="INTEGER"/>
            <result property="dataSource" column="data_source" jdbcType="TINYINT"/>
            <result property="errorDesc" column="error_desc" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,modify_no,car_vin,
        modify_before_iccid,modify_after_iccid,modify_status,
        data_source,error_desc,tenant_id,
        created_by,created_time,updated_by,
        updated_time,is_deleted,revision
    </sql>
</mapper>
