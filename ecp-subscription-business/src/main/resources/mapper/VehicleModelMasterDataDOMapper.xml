<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.model.VehicleModelMasterDataMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.subscription.dal.dataobject.model.VehicleModelMasterDataDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="TENANT_ID" jdbcType="INTEGER"/>
            <result property="revision" column="REVISION" jdbcType="INTEGER"/>
            <result property="createdBy" column="CREATED_BY" jdbcType="INTEGER"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="UPDATED_BY" jdbcType="INTEGER"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="INTEGER"/>
            <result property="brandCode" column="brand_code" jdbcType="VARCHAR"/>
            <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
            <result property="carSystemModel" column="car_system_model" jdbcType="VARCHAR"/>
            <result property="seriesCode" column="series_code" jdbcType="VARCHAR"/>
            <result property="seriesName" column="series_name" jdbcType="VARCHAR"/>
            <result property="configCode" column="config_code" jdbcType="VARCHAR"/>
            <result property="configName" column="config_name" jdbcType="VARCHAR"/>
            <result property="modelYear" column="model_year" jdbcType="VARCHAR"/>
            <result property="productionEn" column="production_en" jdbcType="VARCHAR"/>
            <result property="hobEn" column="hob_en" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,TENANT_ID,REVISION,
        CREATED_BY,CREATED_TIME,UPDATED_BY,
        UPDATED_TIME,IS_DELETED,brand_code,
        brand_name,
        car_system_model,
        series_code,
        series_name,
        config_code,
        config_name,
        model_year,
        production_en,
        hob_en
    </sql>
    <select id="getPage" resultType="com.jlr.ecp.subscription.api.model.vo.VehicleModelMasterDataPageVO">
        select  car_system_model as carSystemModel,series_code as seriesCode,GROUP_CONCAT(model_year SEPARATOR ',') as modelYears
        from t_vehicle_model_master_data
        where is_deleted = false
        GROUP BY car_system_model,series_code
        order by created_time desc
    </select>
</mapper>
