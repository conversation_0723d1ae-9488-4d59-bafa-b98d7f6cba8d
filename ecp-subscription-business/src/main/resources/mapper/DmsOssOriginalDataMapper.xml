<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataMapper">
    <sql id="tableName">
        t_dms_oss_original_data
    </sql>

    <sql id="baseColumn">
        id,bau_job_id,data_id,job_date,car_vin,dms_invoice_date,status,sync_status,sota_result,appd_result,cu_result,amap_result,dp_result,pivi_config_result,vin_match_result,special_vin_config,iccid,jlr_subscription_id,amap_expire_date,tenant_id,created_by,created_time,updated_by,updated_time,is_deleted,revision
    </sql>
    <select id="selectCountByJobId"
            resultType="com.jlr.ecp.subscription.model.dto.VinMatchCountDto">
        SELECT bau_job_id as bauJobId, vin_match_result as vinMatchResult, count(*) as vinCount
        FROM t_dms_oss_original_data
        WHERE
        bau_job_id IN
        <foreach item="item" index="index" collection="jobIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0
        GROUP BY bau_job_id, vin_match_result
    </select>

</mapper>