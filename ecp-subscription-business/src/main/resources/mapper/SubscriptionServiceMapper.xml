<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper">
    <sql id="tableName">
        t_subscription_service
    </sql>

    <sql id="baseColumn">
        id,car_no,incontrol_id,car_vin,service_name,service_package,expiry_date,service_type,tenant_id,created_by,created_time,updated_by,updated_time,is_deleted,revision
    </sql>


    <resultMap id="ServiceExpireVoMap" type="com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireVo">
        <result column="car_vin" property="carVin"/>
        <result column="expiry_date" property="expiryDate"/>
        <result column="service_name" property="serviceName"/>
        <result column="service_package" property="servicePackage"/>
        <result column="incontrol_id" property="incontrolId"/>
        <result column="iccid" property="iccid"/>
    </resultMap>

    <select id="queryCombinedExpireList" resultMap="ServiceExpireVoMap">
        SELECT *
        FROM (
                 -- 优先级 1: t_subscription_service 的数据
                 SELECT
                     s.car_vin,
                     s.expiry_date,
                     s.service_name,
                     s.service_package,
                     s.jlr_subscription_id,
                     s.iccid,
                     s.subscription_id,
                     1 AS source_priority,  -- 优先级标记
                     s.id AS sort_id        -- 主键用于排序
                 FROM t_subscription_service s
                 WHERE s.is_deleted = 0
                     AND s.service_type = #{serviceType}
                     AND s.expiry_date BETWEEN #{startTime} AND #{endTime}
                     AND s.service_package = #{servicePackage}

                 UNION ALL

                 -- 优先级 2: t_pivi_package 中未关联的数据
                 SELECT
                     p.vin AS car_vin,
                     p.expire_date,
                     p.service_name,
                     NULL AS service_package,  -- 补全字段
                     p.jlr_subscription_id,
                     p.iccid,
                     NULL AS subscription_id, -- 补全字段
                     2 AS source_priority,     -- 优先级标记
                     p.id AS sort_id            -- 主键用于排序
                 FROM t_pivi_package p
                     LEFT JOIN t_subscription_service s
                     ON p.vin = s.car_vin
                     AND s.is_deleted = 0
                     AND s.service_type = #{serviceType}
                 WHERE
                     p.is_deleted = 0
                     AND p.expire_date BETWEEN #{startTime} AND #{endTime}
                     AND s.car_vin IS NULL  -- 排除已存在的 vin
             ) combined
        ORDER BY
            source_priority ASC,  -- 按优先级排序（1在前，2在后）
            sort_id ASC           -- 保底排序字段（按主键保证稳定性）
            LIMIT #{pageSize} OFFSET #{offset}
    </select>


    <select id="queryCombinedExpireCount" resultType="java.lang.Integer">
        SELECT (
                       (SELECT COUNT(DISTINCT s.car_vin)
                        FROM t_subscription_service s
                        WHERE s.is_deleted = 0
                          AND s.service_type = #{serviceType}
                          AND s.expiry_date BETWEEN #{startTime} AND #{endTime}
                          AND s.service_package = #{servicePackage})
                       +
                       (SELECT COUNT(DISTINCT p.vin)
                        FROM t_pivi_package p
                        LEFT JOIN t_subscription_service s
                            ON p.vin = s.car_vin
                            AND s.is_deleted = 0
                            AND s.service_type = #{serviceType}
                        WHERE p.is_deleted = 0
                            AND p.expire_date BETWEEN #{startTime} AND #{endTime}
                            AND s.car_vin IS NULL)
                   ) AS total
    </select>


    <select id="queryByCarVinAndServiceType"
            resultType="com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO">
        SELECT * FROM
        t_subscription_service
        WHERE
        <foreach item="item" index="index" collection="carVinAndServiceTypeList" open="(" separator="or" close=")">
            car_vin = #{item.carVin} and service_type = #{item.serviceType}
        </foreach>
        AND is_deleted = 0
    </select>

    <select id="getLatestExpireDate" resultType="com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO">
        SELECT
        service_type , MIN(expiry_date) AS expiry_date
        FROM
        t_subscription_service
        WHERE
        car_vin IN
        <foreach item="item" index="index" collection="carVinSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0 AND service_type = 1
        GROUP BY service_type

        UNION

        SELECT
        2 AS service_type , MIN(expiry_date) AS expiry_date
        FROM
        t_subscription_service
        WHERE
        car_vin IN
        <foreach item="item" index="index" collection="carVinSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0 AND service_type = 3 AND service_package = 'ONLINE-PACK'
        GROUP BY service_type

    </select>


    <select id="getLatestUnicomExpireDate" resultType="com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO">
        SELECT
        2 AS service_type , MIN(expiry_date) AS expiry_date
        FROM
        t_subscription_service
        WHERE
        car_vin IN
        <foreach item="item" index="index" collection="carVinSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0 AND service_type = 3 AND service_package = 'DATA-PLAN'
        GROUP BY service_type

    </select>
</mapper>