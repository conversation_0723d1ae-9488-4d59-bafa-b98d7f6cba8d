<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper">
    <select id="selectPIVIPackageDOByCarVinList" resultType="com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO">
        SELECT * FROM t_pivi_package
        WHERE vin IN
        <foreach item="vin" index="index" collection="carVinList" open="(" separator="," close=")">
            #{vin}
        </foreach>
    </select>

    <select id="getMergedVinExpireServiceCountByDataRangeForAll" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            (SELECT
                 car_vin,
                 MAX(remoteExpireDate) AS remoteExpireDate,
                 MAX(piviExpireDate) AS piviExpireDate,
                 MAX(iccid) AS iccid,
                 CASE
                     WHEN MAX(remoteExpireDate) IS NOT NULL AND MAX(piviExpireDate) IS NOT NULL
                         AND DATE(MAX(remoteExpireDate)) = DATE(MAX(piviExpireDate)) THEN 'Both'
                     WHEN MAX(remoteExpireDate) IS NOT NULL THEN 'Remote'
                     ELSE 'Subscription'
                     END AS service_type
             FROM (
                 SELECT
                 car_vin,
                 expiry_date AS remoteExpireDate,
                 NULL AS piviExpireDate,
                 NULL AS iccid
                 FROM t_subscription_service s
                 WHERE s.is_deleted = 0
                 AND s.service_type = 1
                 AND s.expiry_date BETWEEN #{startTime} AND #{endTime}

                 UNION ALL

                 SELECT
                 p.vin AS car_vin,
                 NULL AS remoteExpireDate,
                 COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate,
                 p.iccid AS iccid
                 FROM t_pivi_package p
                 LEFT JOIN t_subscription_service s
                 ON p.vin = s.car_vin
                 AND s.is_deleted = 0
                 AND s.service_type = 3
                 AND s.service_package IN ('ONLINE-PACK', 'DATA-PLAN')
                 AND s.service_name IN ('online-media', 'UNICOM')
                 WHERE p.is_deleted = 0
                 AND (
                 (p.jlr_subscription_id IS NOT NULL AND s.service_package = 'ONLINE-PACK')
                 OR
                 (p.jlr_subscription_id IS NULL AND s.service_package = 'DATA-PLAN')
                 )
                 AND COALESCE(s.expiry_date, p.expire_date) BETWEEN #{startTime} AND #{endTime}
                 ) combined
             GROUP BY car_vin) total
    </select>
    <select id="getVinExpireServiceByDataRangeForAll"
            resultType="com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO">
        SELECT
            car_vin,
            expiry_date AS remoteExpireDate,
            NULL AS piviExpireDate,
            NULL AS iccid
        FROM
            t_subscription_service s
        WHERE s.is_deleted = 0
          AND s.service_type = 1
          AND s.expiry_date BETWEEN #{startTime} AND #{endTime}
        GROUP BY car_vin

        UNION ALL

        SELECT
            *
        FROM
            (SELECT
                 p.vin AS car_vin,
                 NULL AS remoteExpireDate,
                 COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate,
                 p.iccid AS iccid
             FROM
                 t_pivi_package p
                     LEFT JOIN t_subscription_service s
                               ON p.vin = s.car_vin
                                   AND s.is_deleted = 0
                                   AND s.service_type = 3
                                   AND s.service_package = 'ONLINE-PACK'
                                   AND s.service_name = 'online-media'
             WHERE p.is_deleted = 0
               AND p.jlr_subscription_id IS NOT NULL

             UNION ALL

             SELECT
                 p.vin AS car_vin,
                 NULL AS remoteExpireDate,
                 COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate,
                 p.iccid AS iccid
             FROM
                 t_pivi_package p
                     LEFT JOIN t_subscription_service s
                               ON p.vin = s.car_vin
                                   AND s.is_deleted = 0
                                   AND s.service_type = 3
                                   AND s.service_package = 'DATA-PLAN'
                                   AND s.service_name = 'UNICOM'
             WHERE p.is_deleted = 0
               AND p.jlr_subscription_id IS NULL) pivi
        WHERE pivi.piviExpireDate BETWEEN #{startTime} AND #{endTime}

    </select>

    <select id="getVinExpireServiceCountByDataRangeForAll" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            (SELECT
                 car_vin,
                 expiry_date AS remoteExpireDate,
                 NULL AS piviExpireDate
             FROM
                 t_subscription_service s
             WHERE s.is_deleted = 0
               AND s.service_type = 1
               AND s.expiry_date BETWEEN #{startTime} AND #{endTime}
             GROUP BY car_vin
             UNION ALL
             SELECT
                 *
             FROM
                 (SELECT
                      p.vin AS car_vin,
                      NULL AS remoteExpireDate,
                      COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate
                  FROM
                      t_pivi_package p
                          LEFT JOIN t_subscription_service s
                                    ON p.vin = s.car_vin
                                        AND s.is_deleted = 0
                                        AND s.service_type = 3
                                        AND s.service_package = 'ONLINE-PACK'
                                        AND s.service_name = 'online-media'
                  WHERE p.is_deleted = 0
                    AND p.jlr_subscription_id IS NOT NULL
                  UNION ALL
                  SELECT
                      p.vin AS car_vin,
                      NULL AS remoteExpireDate,
                      COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate
                  FROM
                      t_pivi_package p
                          LEFT JOIN t_subscription_service s
                                    ON p.vin = s.car_vin
                                        AND s.is_deleted = 0
                                        AND s.service_type = 3
                                        AND s.service_package = 'DATA-PLAN'
                                        AND s.service_name = 'UNICOM'
                  WHERE p.is_deleted = 0
                    AND p.jlr_subscription_id IS NULL) pivi
             WHERE pivi.piviExpireDate BETWEEN #{startTime} AND #{endTime}) total

    </select>
    <select id="getVinExpireServiceByDataRangeForAllNoPage"
            resultType="com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO">
        SELECT
            car_vin,
            expiry_date AS remoteExpireDate,
            NULL AS piviExpireDate,
            NULL AS iccid
        FROM
            t_subscription_service s
        WHERE s.is_deleted = 0
          AND s.service_type = 1
          AND s.expiry_date BETWEEN #{startTime} AND #{endTime}
        GROUP BY car_vin

        UNION ALL

        SELECT
            *
        FROM
            (SELECT
                 p.vin AS car_vin,
                 NULL AS remoteExpireDate,
                 COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate,
                 p.iccid AS iccid
             FROM
                 t_pivi_package p
                     LEFT JOIN t_subscription_service s
                               ON p.vin = s.car_vin
                                   AND s.is_deleted = 0
                                   AND s.service_type = 3
                                   AND s.service_package = 'ONLINE-PACK'
                                   AND s.service_name = 'online-media'
             WHERE p.is_deleted = 0
               AND p.jlr_subscription_id IS NOT NULL

             UNION ALL

             SELECT
                 p.vin AS car_vin,
                 NULL AS remoteExpireDate,
                 COALESCE(s.expiry_date, p.expire_date) AS piviExpireDate,
                 p.iccid AS iccid
             FROM
                 t_pivi_package p
                     LEFT JOIN t_subscription_service s
                               ON p.vin = s.car_vin
                                   AND s.is_deleted = 0
                                   AND s.service_type = 3
                                   AND s.service_package = 'DATA-PLAN'
                                   AND s.service_name = 'UNICOM'
             WHERE p.is_deleted = 0
               AND p.jlr_subscription_id IS NULL) pivi
        WHERE pivi.piviExpireDate BETWEEN #{startTime} AND #{endTime}


    </select>
</mapper>