swagger: '2.0'
#
# Version History
# When         who             Change
info:
  version: '1.0'
  description: This application handles subscription management related interactions with an external eCommerce platform. All Api(s) are protected using 3 security layers,
    <ul>
    <li> IP White-Listing Allows http client requests from legitimate source.</li>
    <li> TLS certificates - MTLS certificates are required to be sent for every API request</li>
    <li> Basic Authorization - API calls are authorised using basic authorization by passing Authorization header for every API request</li>
    </ul>
  title: API Documentation for Subscription Management Flow
host: ifecom.prod-row.jlrmotor.com
basePath: /

# tags are used for organising operations
paths:
  /notification/services:
    post:
      tags:
        - TSDP
      summary: Renew or extends a specific set of services
      operationId: renewSubscribedServicesUsingPOST
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: Authorization
          in: header
          required: true
          type: string
          description: Basic Autotization to access IF-ECOM api.
        - in: body
          name: renewRequest
          description: Omit <b>user</b> and <b>transactionId</b> properties if this endpoint is used for only extension without bound status check.
          required: false
          schema:
            $ref: '#/definitions/RenewalRequest'
      responses:
        '200':
          description: <ul>
            <li>Created, provisioning must run through the user context</li>
            <li>Success, if the endpoint perform extension without user bound</li>
            check.
            </ul>
          schema:
            $ref: '#/definitions/RenewalResponse'
        '201':
          description: Created
          schema:
            $ref: '#/definitions/RenewalResponse'
        '400':
          description: Bad input parameter, e.g. non existing vin, expiryDate in the past or trying to renew a service/package that was not on the car
            Also when this endpoint is used to expire subscriptions without user and transactionId attributes in which case the input end date
            shall not be less than existing end date.
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '409':
          description: The passed transaction id already notified from eCom
  /subscriptions/vehicles/{vin}:
    get:
      tags:
        - TSDP
      summary: Retrieves the vehicle, user and subscription that is stored in TSDP.
      operationId: fetchProducName
      description: |
        Triggered by SCP to fetch vehicle information along with subscriptions
      produces:
        - application/json
      parameters:
        - in: path
          type: string
          description: Vehicle Identification Number
          required: true
          name: vin
      responses:
        200:
          description: Vehicle Information including Subscriptions fetched.
          schema:
            $ref: '#/definitions/ExpiringSubscription'
        400:
          description: Bad request
        429:
          description: Too many requests if API rate limit exceeded. The rate limit is set to 30 requests/min basically one request for 2 seconds.
        404:
          description: Vehicle not found.
definitions:
  ExpiringSubscription:
    type: object
    required:
      - boundToCustomer
      - earliestExpiryDate
      - subscriptions
      - vehicleInformation
      - vin
      - provisionedState
    properties:
      provisionedState:
        type: string
        example: "JLR_PROVISIONED_MODE"
      boundToCustomer:
        $ref: '#/definitions/Customer'
      earliestExpiryDate:
        type: string
        format: date-time
        example: 3N1AB6AP3CL700036
      subscriptions:
        type: array
        items:
          $ref: '#/definitions/Subscription2'
      vehicleInformation:
        $ref: '#/definitions/Vehicle'
      vin:
        type: string
        example: '2016-08-29T09:12:33.001Z'
  Customer:
    type: object
    required:
      - email
    properties:
      address:
        $ref: '#/definitions/CustomerAddress'
      email:
        type: string
      firstName:
        type: string
        example: John
      language:
        type: string
        example: en-GB
      phone:
        type: string
        example: '+47643534539'
      surname:
        type: string
        example: Doe
  CustomerAddress:
    type: object
    properties:
      street1:
        type: string
      street2:
        type: string
      street3:
        type: string
      countyOrState:
        type: string
      city:
        type: string
      zip:
        type: string
      country:
        type: string
  ExtensionRequest:
    type: object
    properties:
      packages:
        type: array
        items:
          $ref: '#/definitions/ProductItem'
  ProductItem:
    properties:
      servicePackage:
        type: string
        example: GBR011AB_E1E2_L
      newExpiryDate:
        type: string
        description: Extension Date in future from existing, formt yyyy-MM-ddTHH:mm:SS.SSSZ.
        example: '2025-10-11T23:59:00.000Z'
  Subscription:
    required:
      - servicePackage
      - newExpiryDate
    properties:
      package:
        type: string
        example: GBR011AB_E1E2_L
      newExpiryDate:
        type: string
        format: date-time
        example: '2016-08-29T09:12:33.001Z'
      services:
        type: array
        items:
          type: string
        example: ['VHS','HBLF','REON']
      serviceSate:
        type: string
        example: ACTIVATED
  vehicleInfoV2:
    type: object
    description: |
      Information on subscriptions packages updated with new end Date
    properties:
      vin:
        type: string
      subscriptions:
        type: array
        items:
          $ref: '#/definitions/Subscription'
  Subscription2:
    type: object
    properties:
      servicePackage:
        type: string
      expiryDate:
        type: string
        format: date-time
      services:
        type: array
        items:
          type: string
      serviceState:
        type: string
  Vehicle:
    type: object
    properties:
      brand:
        type: string
        example: Range Rover
      make:
        type: string
        example: Land Rover
      model:
        type: string
        example: Evoque
      nickName:
        type: string
        example: My dirt car
      registrationNumber:
        type: string
        example: CAR UNO
      vin:
        type: string
        example: 3N1AB6AP3CL700036
  RenewalRequest:
    type: object
    required:
      - newOrModifiedSubscriptions
      - vin
    properties:
      newOrModifiedSubscriptions:
        type: array
        items:
          $ref: '#/definitions/Service'
      transactionId:
        type: string
        description: Optional if just extension required without bound status
      user:
        type: string
        description: Optional if just extension required without bound status
      vin:
        type: string
  RenewalResponse:
    type: object
    properties:
      message:
        type: string
      portalLink:
        type: string
  Service:
    type: object
    properties:
      expiryDate:
        type: string
        format: date-time
      serviceName:
        type: string
      servicePackage:
        type: string
