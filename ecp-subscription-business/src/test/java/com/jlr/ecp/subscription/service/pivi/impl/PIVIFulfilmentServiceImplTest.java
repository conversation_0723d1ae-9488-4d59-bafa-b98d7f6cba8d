package com.jlr.ecp.subscription.service.pivi.impl;

import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchDataDTO;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchRecordDTO;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchResponseDTO;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PIVIFulfilmentServiceImplTest {

    @Mock
    private PIVIAmaPService mockAmaPService;
    @Mock
    private VcsOrderFufilmentRecordsMapper mockVcsOrderFufilmentRecordsMapper;
    @Mock
    private VcsOrderFufilmentDOMapper mockVcsOrderFufilmentDOMapper;
    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceDOMapper;
    @Mock
    private SubscriptionServiceLogMapper mockSubscriptionServiceLogMapper;
    @Mock
    private VcsOrderFufilmentDOService mockVcsOrderFufilmentDOService;

    @InjectMocks
    private PIVIFulfilmentServiceImpl piviFulfilmentServiceImplUnderTest;

    @Test
    public void testUpdatePIVIOrderFulfilment() {
        // Setup
        // Configure PIVIAmaPService.queryAmaPChargeInfo(...).
        final AmaPChargeSearchResponseDTO amaPChargeSearchResponseDTO = new AmaPChargeSearchResponseDTO();
        amaPChargeSearchResponseDTO.setVersion("version");
        amaPChargeSearchResponseDTO.setCode("1");
        final AmaPChargeSearchDataDTO data = new AmaPChargeSearchDataDTO();
        final AmaPChargeSearchRecordDTO amaPChargeSearchRecordDTO = new AmaPChargeSearchRecordDTO();
        amaPChargeSearchRecordDTO.setEndTime("endTime");
        data.setChargeRecords(List.of(amaPChargeSearchRecordDTO));
        amaPChargeSearchResponseDTO.setData(data);
        when(mockAmaPService.queryAmaPChargeInfo("vcsOrderCode")).thenReturn(amaPChargeSearchResponseDTO);

        // Configure VcsOrderFufilmentDOMapper.getOneByFulfilmentId(...).
        final VcsOrderFufilmentDO vcsOrderFufilmentDO = VcsOrderFufilmentDO.builder()
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .vcsOrderCode("vcsOrderCode")
                .carVin("carVin")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build();
        when(mockVcsOrderFufilmentDOMapper.getOneByFulfilmentId("fulfilmentId")).thenReturn(vcsOrderFufilmentDO);

        // Configure VcsOrderFufilmentRecordsMapper.selectList(...).
        final List<VcsOrderFufilmentRecords> vcsOrderFufilmentRecords = List.of(VcsOrderFufilmentRecords.builder()
                .fufilmentId("fufilmentId")
                .servicePackage("CONNECTED-NAVIGATION")
                .activationStatus(0)
                .expireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockVcsOrderFufilmentRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vcsOrderFufilmentRecords);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .subscriptionId("subscriptionId")
                .carVin("carVin")
                .servicePackage("CONNECTED-NAVIGATION")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Run the test
        piviFulfilmentServiceImplUnderTest.updatePIVIOrderFulfilment("vcsOrderCode", "fulfilmentId");

        // Verify the results
        verify(mockVcsOrderFufilmentRecordsMapper).updateById(any(VcsOrderFufilmentRecords.class));
        verify(mockVcsOrderFufilmentDOMapper).updateById(any(VcsOrderFufilmentDO.class));
        verify(mockSubscriptionServiceLogMapper).insertBatch(anyList());
        verify(mockSubscriptionServiceDOMapper).updateBatch(anyList());
        verify(mockVcsOrderFufilmentDOService).executeOrderStatusSync("orderCode", "vcsOrderCode");
    }

    @Test
    public void testUpdatePIVIOrderFulfilment_VcsOrderFufilmentRecordsMapperSelectListReturnsNoItems() {
        // Setup
        // Configure PIVIAmaPService.queryAmaPChargeInfo(...).
        final AmaPChargeSearchResponseDTO amaPChargeSearchResponseDTO = new AmaPChargeSearchResponseDTO();
        amaPChargeSearchResponseDTO.setVersion("version");
        amaPChargeSearchResponseDTO.setCode("code");
        final AmaPChargeSearchDataDTO data = new AmaPChargeSearchDataDTO();
        final AmaPChargeSearchRecordDTO amaPChargeSearchRecordDTO = new AmaPChargeSearchRecordDTO();
        amaPChargeSearchRecordDTO.setEndTime("endTime");
        data.setChargeRecords(List.of(amaPChargeSearchRecordDTO));
        amaPChargeSearchResponseDTO.setData(data);
        when(mockAmaPService.queryAmaPChargeInfo("vcsOrderCode")).thenReturn(amaPChargeSearchResponseDTO);

        // Run the test
        piviFulfilmentServiceImplUnderTest.updatePIVIOrderFulfilment("vcsOrderCode", "fulfilmentId");

        // Verify the results
        verify(mockVcsOrderFufilmentRecordsMapper, times(0)).updateById(any());
    }

    @Test
    public void testUpdatePIVIOrderFulfilment_SubscriptionServiceMapperSelectListReturnsNoItems() {
        // Setup
        // Configure PIVIAmaPService.queryAmaPChargeInfo(...).
        final AmaPChargeSearchResponseDTO amaPChargeSearchResponseDTO = new AmaPChargeSearchResponseDTO();
        amaPChargeSearchResponseDTO.setVersion("version");
        amaPChargeSearchResponseDTO.setCode("code");
        final AmaPChargeSearchDataDTO data = new AmaPChargeSearchDataDTO();
        final AmaPChargeSearchRecordDTO amaPChargeSearchRecordDTO = new AmaPChargeSearchRecordDTO();
        amaPChargeSearchRecordDTO.setEndTime("endTime");
        data.setChargeRecords(List.of(amaPChargeSearchRecordDTO));
        amaPChargeSearchResponseDTO.setData(data);
        when(mockAmaPService.queryAmaPChargeInfo("vcsOrderCode")).thenReturn(amaPChargeSearchResponseDTO);

        // Run the test
        piviFulfilmentServiceImplUnderTest.updatePIVIOrderFulfilment("vcsOrderCode", "fulfilmentId");

        // Verify the results
        verify(mockVcsOrderFufilmentRecordsMapper, times(0)).updateById(any());
    }

    @Test
    public void testUpdatePIVIFulfilmentByVcsOrder() {
        // Setup
        final List<VcsOrderFufilmentDO> vcsOrderFulfilmentDOList = List.of(VcsOrderFufilmentDO.builder()
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .vcsOrderCode("vcsOrderCode")
                .carVin("carVin")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());

        // Configure PIVIAmaPService.queryAmaPChargeInfo(...).
        final AmaPChargeSearchResponseDTO amaPChargeSearchResponseDTO = new AmaPChargeSearchResponseDTO();
        amaPChargeSearchResponseDTO.setVersion("version");
        amaPChargeSearchResponseDTO.setCode("1");
        final AmaPChargeSearchDataDTO data = new AmaPChargeSearchDataDTO();
        final AmaPChargeSearchRecordDTO amaPChargeSearchRecordDTO = new AmaPChargeSearchRecordDTO();
        amaPChargeSearchRecordDTO.setEndTime("endTime");
        data.setChargeRecords(List.of(amaPChargeSearchRecordDTO));
        amaPChargeSearchResponseDTO.setData(data);
        when(mockAmaPService.queryAmaPChargeInfo("vcsOrderCode")).thenReturn(amaPChargeSearchResponseDTO);

        // Configure VcsOrderFufilmentRecordsMapper.selectList(...).
        final List<VcsOrderFufilmentRecords> vcsOrderFufilmentRecords = List.of(VcsOrderFufilmentRecords.builder()
                .fufilmentId("fufilmentId")
                .servicePackage("servicePackage")
                .activationStatus(0)
                .expireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockVcsOrderFufilmentRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vcsOrderFufilmentRecords);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .subscriptionId("subscriptionId")
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Run the test
        piviFulfilmentServiceImplUnderTest.updatePIVIFulfilmentByVcsOrder(vcsOrderFulfilmentDOList);

        verify(mockSubscriptionServiceLogMapper).insertBatch(anyList());
        verify(mockSubscriptionServiceDOMapper).updateBatch(anyList());
        verify(mockVcsOrderFufilmentDOService).executeOrderStatusSync("orderCode", "vcsOrderCode");
    }
}
