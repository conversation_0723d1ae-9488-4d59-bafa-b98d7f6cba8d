package com.jlr.ecp.subscription.service.sota;

import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.util.HttpUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SOTAServiceImplTest {

    @Mock
    private HttpUtils mockHttpUtils;
    @Mock
    private Redisson mockRedisson;

    @InjectMocks
    private SOTAServiceImpl sotaServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(sotaServiceImplUnderTest, "prefixUrl", "prefixUrl");
        ReflectionTestUtils.setField(sotaServiceImplUnderTest, "sotaPrefixUrl", "sotaPrefixUrl");
        ReflectionTestUtils.setField(sotaServiceImplUnderTest, "sotaSuffixUrl", "sotaSuffixUrl");
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
    }

    @Test
    public void testGetSOTAInfoByVin() {
        // Setup
        final SOTAResultVO expectedResult = new SOTAResultVO();
        expectedResult.setVin("vin");
        expectedResult.setPivi("pivi");
        expectedResult.setEsimiccid("esimiccid");
        expectedResult.setQueryResult("queryResult");

        // Configure HttpUtils.callApimServiceForGet(...).
        final SOTAResultVO sotaResultVO = new SOTAResultVO();
        sotaResultVO.setVin("vin");
        sotaResultVO.setPivi("pivi");
        sotaResultVO.setEsimiccid("esimiccid");
        sotaResultVO.setQueryResult("queryResult");
        when(mockHttpUtils.callApimServiceForGet(anyString(), any(), anyString()))
                .thenReturn(sotaResultVO);

        // Run the test
        final SOTAResultVO result = sotaServiceImplUnderTest.getSOTAInfoByVin("vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
