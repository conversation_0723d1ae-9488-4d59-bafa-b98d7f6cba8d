package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomReqDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRnrQueryVO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomTodoOrderDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomTodoOrderMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.SimCardInfoDTO;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.env.MockEnvironment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PIVIUnicomServiceImplTest {

    @Mock
    private RedisService mockRedisService;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private VcsOrderFulfilmentCallMapper mockCallMapper;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;
    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleMapper;
    @Mock
    private UnicomTodoOrderMapper mockUnicomTodoOrderMapper;
    @Mock
    private SubscriptionServiceLogMapper mockSubscriptionServiceLogMapper;
    @Mock
    private ApplicationContext mockApplicationContext;
    @Mock
    private SOTAService mockSotaService;
    @Mock
    private Redisson mockRedisson;

    @InjectMocks
    private PIVIUnicomServiceImpl piviUnicomServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(piviUnicomServiceImplUnderTest, "industryCode", "industryCode");
        ReflectionTestUtils.setField(piviUnicomServiceImplUnderTest, "version", "version");
        ReflectionTestUtils.setField(piviUnicomServiceImplUnderTest, "appKey", "appKey");
        ReflectionTestUtils.setField(piviUnicomServiceImplUnderTest, "productId", "productId");
        ReflectionTestUtils.setField(piviUnicomServiceImplUnderTest, "url", "http://localhost");
        ReflectionTestUtils.setField(piviUnicomServiceImplUnderTest, "environment", new MockEnvironment());
        piviUnicomServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), PIVIPackageDO.class);
    }

    @Test
    public void testCallUnicomService() {
        // Setup
        final FufilmentMessage message = new FufilmentMessage("orderItemCode", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockEcpIdUtil.nextIdStr()).thenReturn("requestId");

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomServiceImplUnderTest);

        when(mockLock.tryLock()).thenReturn(true);

        // Run the test
        // Verify the results
        assertThrows(RestClientException.class,
                () -> piviUnicomServiceImplUnderTest.callUnicomService(message, "fulfilmentId"));
    }

    @Test
    public void testUnicomManualRenewal() {
        // Setup
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        final CommonResult<UnicomRespVO> expectedResult = CommonResult.success(unicomRespVO);
        when(mockEcpIdUtil.nextIdStr()).thenReturn("cusOrderId");

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("carVin")).thenReturn(piviPackageDO);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomServiceImplUnderTest);

        when(mockLock.tryLock()).thenReturn(true);

        // Run the test
        final CommonResult<UnicomRespVO> result = piviUnicomServiceImplUnderTest.unicomManualRenewal("carVin",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUnicomManualRenewalJob() {
        // Setup
        final CommonResult<UnicomRespVO> expectedResult = CommonResult.error(ORDER_IN_TRANSIT_ERROR);

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("carVin")).thenReturn(piviPackageDO);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomServiceImplUnderTest);

        when(mockLock.tryLock()).thenReturn(false);

        // Run the test
        final CommonResult<UnicomRespVO> result = piviUnicomServiceImplUnderTest.unicomManualRenewalJob("carVin",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "cusOrderId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCallUnicomRequest() {
        // Setup
        final FufilmentMessage message = new FufilmentMessage("orderItemCode", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockEcpIdUtil.nextIdStr()).thenReturn("requestId");

        // Run the test
        final String result = piviUnicomServiceImplUnderTest.callUnicomRequest(message, "iccid");

        // Verify the results
        assertEquals("", result);
    }

    @Test
    public void testCancelUnicomService() {
        // Setup
        final CancelMessage message = new CancelMessage("refundOrderCode", "fulfilmentId", null);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("requestId");

        // Run the test
        final Boolean result = piviUnicomServiceImplUnderTest.cancelUnicomService(message, "fulfilmentId");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testNewCancelUnicomService() {
        // Setup
        CancelMessage cancelMessage = new CancelMessage("refundOrderCode", "fulfilmentId", null);
        cancelMessage.setServiceEndDate(LocalDateTime.of(2021, 1, 1, 0, 0, 0));

        // Configure SubscriptionServiceLogMapper.findLastLogByFulfilmentId(...).
        final SubscriptionServiceLogDO subscriptionServiceLogDO = SubscriptionServiceLogDO.builder()
                .refreshBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockSubscriptionServiceLogMapper.findLastLogByFulfilmentId("fulfilmentId"))
                .thenReturn(subscriptionServiceLogDO);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("requestId");

        // Configure ApplicationContext.getBean(...).
        final PIVIUnicomServiceImpl piviUnicomService = new PIVIUnicomServiceImpl();
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomService);

        when(mockLock.tryLock()).thenReturn(true);

        // Verify the results
        assertThrows(RestClientException.class,
                () -> piviUnicomServiceImplUnderTest.newCancelUnicomService(cancelMessage, "fulfilmentId"));
    }

    @Test
    public void testNewCancelUnicomService_SubscriptionServiceLogMapperReturnsNull() {
        // Setup
        final CancelMessage cancelMessage = new CancelMessage("refundOrderCode", "fulfilmentId", null);
        when(mockSubscriptionServiceLogMapper.findLastLogByFulfilmentId("fulfilmentId")).thenReturn(null);

        // Run the test
        final Boolean result = piviUnicomServiceImplUnderTest.newCancelUnicomService(cancelMessage, "fulfilmentId");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testNewCancelUnicomService_PIVIPackageDOMapperReturnsNull() {
        // Setup
        CancelMessage cancelMessage = new CancelMessage("refundOrderCode", "fulfilmentId", null);
        cancelMessage.setServiceEndDate(LocalDateTime.of(2021, 1, 1, 0, 0, 0));

        // Configure SubscriptionServiceLogMapper.findLastLogByFulfilmentId(...).
        final SubscriptionServiceLogDO subscriptionServiceLogDO = SubscriptionServiceLogDO.builder()
                .refreshBeforeDate(LocalDateTime.of(2022, 1, 1, 0, 0, 0))
                .build();
        when(mockSubscriptionServiceLogMapper.findLastLogByFulfilmentId("fulfilmentId"))
                .thenReturn(subscriptionServiceLogDO);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("requestId");

        // Run the test
        final Boolean result = piviUnicomServiceImplUnderTest.newCancelUnicomService(cancelMessage, "fulfilmentId");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSaveCancelTodo() {
        // Setup
        CancelMessage message = new CancelMessage("refundOrderCode", "fulfilmentId", null);
        message.setVin("vin");

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("vin")).thenReturn(piviPackageDO);

        // Run the test
        final Boolean result = piviUnicomServiceImplUnderTest.saveCancelTodo(message);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSaveCancelTodo_PIVIPackageDOMapperReturnsNull() {
        // Setup
        final CancelMessage message = new CancelMessage("refundOrderCode", "fulfilmentId", null);
        // Run the test
        final Boolean result = piviUnicomServiceImplUnderTest.saveCancelTodo(message);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testExecuteTodoOrder() {
        // Setup
        final UnicomTodoOrderVO unicomTodoOrderVO = UnicomTodoOrderVO.builder()
                .id(0L)
                .iccid("iccid")
                .extBookId("cusOrderId")
                .validExtBookId("validExtBookId")
                .build();

        // Configure UnicomTodoOrderMapper.selectOne(...).
        final UnicomTodoOrderDO unicomTodoOrderDO = UnicomTodoOrderDO.builder()
                .id(0L)
                .iccid("iccid")
                .requestType("type")
                .extBookId("cusOrderId")
                .productId("productId")
                .bookStatus("bookStatus")
                .status(0)
                .requestCount(0)
                .validExtBookId("validExtBookId")
                .validBookStatus("type")
                .build();
        when(mockUnicomTodoOrderMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(unicomTodoOrderDO);

        // Run the test
        final Integer result = piviUnicomServiceImplUnderTest.executeTodoOrder(unicomTodoOrderVO);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testGetRnrQuery() {
        // Setup
        final UnicomRnrQueryVO unicomRnrQueryVO = new UnicomRnrQueryVO();
        unicomRnrQueryVO.setCarVin("Q1234567890123456");
        unicomRnrQueryVO.setQueryStatus(3);
        unicomRnrQueryVO.setQueryStatusTxt("查询失败");
        unicomRnrQueryVO.setErrorDesc("未查到ICCID对应的信息");
        final CommonResult<UnicomRnrQueryVO> expectedResult = CommonResult.success(unicomRnrQueryVO);

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("Q1234567890123456")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("Q1234567890123456")).thenReturn(piviPackageDO);

        // Run the test
        final CommonResult<UnicomRnrQueryVO> result = piviUnicomServiceImplUnderTest.getRnrQuery("Q1234567890123456");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetRnrQuery_PIVIPackageDOMapperReturnsNull() {
        // Setup
        final UnicomRnrQueryVO unicomRnrQueryVO = new UnicomRnrQueryVO();
        unicomRnrQueryVO.setCarVin("Q1234567890123456");
        unicomRnrQueryVO.setQueryStatus(3);
        unicomRnrQueryVO.setQueryStatusTxt("查询失败");
        unicomRnrQueryVO.setErrorDesc("未查到ICCID对应的信息");
        final CommonResult<UnicomRnrQueryVO> expectedResult = CommonResult.success(unicomRnrQueryVO);
        when(mockPiviPackageDOMapper.findICCIDByCarVin("Q1234567890123456")).thenReturn(null);

        // Configure SOTAService.getSOTAInfoByVin(...).
        final SOTAResultVO sotaResultVO = new SOTAResultVO();
        sotaResultVO.setVin("Q1234567890123456");
        sotaResultVO.setPivi("pivi");
        sotaResultVO.setEsimiccid("esimiccid");
        sotaResultVO.setQueryResult("queryResult");
        when(mockSotaService.getSOTAInfoByVin("Q1234567890123456")).thenReturn(sotaResultVO);

        // Run the test
        final CommonResult<UnicomRnrQueryVO> result = piviUnicomServiceImplUnderTest.getRnrQuery("Q1234567890123456");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUnicomRenewalByIccid() {
        // Setup
        final FufilmentMessage message = new FufilmentMessage("orderItemCode", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<UnicomRespVO> expectedResult = CommonResult.error(ErrorCodeConstants.SYNC_CU_ERROR);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomServiceImplUnderTest);

        when(mockLock.tryLock()).thenReturn(true);
        when(mockEcpIdUtil.nextIdStr()).thenReturn("requestId");

        // Run the test
        final CommonResult<UnicomRespVO> result = piviUnicomServiceImplUnderTest.unicomRenewalByIccid(message, "iccid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUnicomRenewalByIccid_iccidNull() {
        // Setup
        final FufilmentMessage message = new FufilmentMessage("orderItemCode", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<UnicomRespVO> expectedResult = CommonResult.error(ErrorCodeConstants.APPDUC_ICCID_EMPTY);

        // Run the test
        final CommonResult<UnicomRespVO> result = piviUnicomServiceImplUnderTest.unicomRenewalByIccid(message, "");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSimCarInfoByCarVin() {
        // Setup
        final UnicomRespVO expectedResult = new UnicomRespVO();
        expectedResult.setQueryResult("ConnectException: Connection refused: connect");

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("carVin")).thenReturn(piviPackageDO);

        // Run the test
        final UnicomRespVO result = piviUnicomServiceImplUnderTest.getSimCarInfoByCarVin("carVin");

        // Verify the results
        assertNotNull(result.getQueryResult());
    }

    @Test
    public void testGetSimCardInfoList() {
        // Setup
        final List<SimCardInfoDTO> simCardInfoDTOList = List.of(SimCardInfoDTO.builder()
                .iccid("iccid")
                .vin("vin")
                .build());
        List<UnicomRespVO> expectedResult = new ArrayList<>();
        expectedResult.add(null);
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Run the test
        final List<UnicomRespVO> result = piviUnicomServiceImplUnderTest.getSimCardInfoList(simCardInfoDTOList);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
    }

    @Test
    public void testGetSimCardInfoSync() {
        // Setup
        final List<SimCardInfoDTO> simCardInfoDTOList = List.of(SimCardInfoDTO.builder()
                .iccid("iccid")
                .vin("vin")
                .build());
        final List<UnicomRespVO> expectedResult = Collections.emptyList();

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomServiceImplUnderTest);

        // Run the test
        final List<UnicomRespVO> result = piviUnicomServiceImplUnderTest.getSimCardInfoSync(simCardInfoDTOList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAddRealNameList() {
        // Setup
        final UnicomReqDTO unicomReqDTO = new UnicomReqDTO();
        unicomReqDTO.setCarVinList(List.of("value"));

        when(mockRedisService.setCacheList("unicom:return:test:key", List.of("value"))).thenReturn(0L);

        // Run the test
        final List<String> result = piviUnicomServiceImplUnderTest.addRealNameList(unicomReqDTO);

        // Verify the results
        assertNull(result);
        verify(mockRedisService).deleteObject("unicom:return:test:key");
    }

    @Test
    public void testEsimstate() {
        // Setup
        final UnicomReqDTO unicomReqDTO = new UnicomReqDTO();
        unicomReqDTO.setCarVinList(List.of("vin"));

        final CommonResult<List<EsimInfoVO>> expectedResult = CommonResult.success(Collections.emptyList());

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("vin")
                .carSystemModel("carSystemModel")
                .build());
        when(mockIncontrolVehicleMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Run the test
        final CommonResult<List<EsimInfoVO>> result = piviUnicomServiceImplUnderTest.esimstate(unicomReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testEsimstate_IncontrolVehicleDOMapperReturnsNoItems() {
        // Setup
        final UnicomReqDTO unicomReqDTO = new UnicomReqDTO();
        unicomReqDTO.setCarVinList(List.of("value"));

        when(mockIncontrolVehicleMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<List<EsimInfoVO>> result = piviUnicomServiceImplUnderTest.esimstate(unicomReqDTO);

        // Verify the results
        assertEquals(CommonResult.success(Collections.emptyList()), result);
    }

    @Test
    public void testEsimstate_PIVIPackageDOMapperReturnsNoItems() {
        // Setup
        final UnicomReqDTO unicomReqDTO = new UnicomReqDTO();
        unicomReqDTO.setCarVinList(List.of("vin"));

        final CommonResult<List<EsimInfoVO>> expectedResult = CommonResult.success(Collections.emptyList());

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("vin")
                .carSystemModel("carSystemModel")
                .build());
        when(mockIncontrolVehicleMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("carVin")
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Run the test
        final CommonResult<List<EsimInfoVO>> result = piviUnicomServiceImplUnderTest.esimstate(unicomReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckVinRNRInfo() {
        // Setup
        final EsimInfoVO expectedResult = new EsimInfoVO();
        expectedResult.setVin("carVin");
        expectedResult.setIccid("iccid");
        expectedResult.setRealnameFlag("realnameFlag");
        expectedResult.setCardState("cardState");
        expectedResult.setCardStateTxt("cardStateTxt");

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("carVin")).thenReturn(piviPackageDO);

        assertThrows(NullPointerException.class,
                () -> piviUnicomServiceImplUnderTest.checkVinRNRInfo("carVin"));
    }

    @Test
    public void testConcurrentCallUnicomRenew() {
        // Setup
        final FufilmentMessage message = new FufilmentMessage("orderItemCode", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<String> expectedResult = CommonResult.error(ORDER_IN_TRANSIT_ERROR);

        // Configure ApplicationContext.getBean(...).
        final PIVIUnicomServiceImpl piviUnicomService = new PIVIUnicomServiceImpl();
        when(mockApplicationContext.getBean(PIVIUnicomServiceImpl.class)).thenReturn(piviUnicomService);

        when(mockLock.tryLock()).thenReturn(false);
        // Run the test
        final CommonResult<String> result = piviUnicomServiceImplUnderTest.concurrentCallUnicomRenew(message, "iccid");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
