package com.jlr.ecp.subscription.service.appd.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuSingleRenewalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleExpireVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSinglePageListV0;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuSingleRenewalV0;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionData;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AppDCuSingleRenewalServiceImplTest {

    @Mock
    private PIVIAppDService mockPiviAppDService;
    @Mock
    private PIVIUnicomService mockPiviUnicomService;
    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private ManualModifyLogDOService mockManualModifyLogDOService;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private SubscriptionServiceMapper mockServiceDOMapper;
    @Mock
    private SubscriptionServiceLogMapper mockServiceLogMapper;
    @Mock
    private OrderCheckService mockOrderCheckService;

    @InjectMocks
    private AppDCuSingleRenewalServiceImpl appDCuSingleRenewalServiceImplUnderTest;

    @Test
    public void testAppDCuSingleOperateRenewal() {
        // Setup
        final AppDCuSingleRenewalDTO appDCuSingleRenewalDTO = new AppDCuSingleRenewalDTO();
        appDCuSingleRenewalDTO.setCarVin("Q1234567890123456");
        appDCuSingleRenewalDTO.setAppDExpireDate("2020/01/01");
        appDCuSingleRenewalDTO.setCuExpireDate("2020/01/01");

        final CommonResult<AppDCuSingleRenewalV0> expectedResult = CommonResult.success(AppDCuSingleRenewalV0.builder()
                .renewalResult("请求已经发送，请前往续费记录查看结果")
                .appDResultCode(0)
                .unicomResultCode(0)
                .build());

        // Configure AppDCuRenewRecordsMapper.getByVinAndStatus(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords = List.of(AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("activationFailedMsg")
                .build());
        when(mockAppDCuRenewRecordsMapper.getByVinAndStatus("Q1234567890123456", 4)).thenReturn(appDCuRenewRecords);

        when(mockOrderCheckService.checkOrderInTransit("Q1234567890123456", ServiceTypeEnum.PIVI))
                .thenReturn(CommonResult.success("value"));

        // Configure PIVIAppDService.getVinSubscriptions(...).
        final AppDSubscriptionResp appDSubscriptionResp = new AppDSubscriptionResp();
        final AppDSubscriptionData appDSubscriptionData = new AppDSubscriptionData();
        appDSubscriptionData.setJlrSubscriptionId(0L);
        appDSubscriptionData.setSubscriptionId("subscriptionId");
        appDSubscriptionData.setSubscriberUniqueId("subscriberUniqueId");
        appDSubscriptionData.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionResp.setResult(List.of(appDSubscriptionData));
        when(mockPiviAppDService.getVinSubscriptions("Q1234567890123456")).thenReturn(appDSubscriptionResp);

        when(mockSnowflake.nextId()).thenReturn(0L);
        when(mockSnowflake.nextIdStr()).thenReturn("cusOrderId");

        // Configure PIVIAppDService.appDManualRenewal(...).
        final CommonResult<VcsOrderFufilmentCall> result1 = CommonResult.success(VcsOrderFufilmentCall.builder()
                .activationStatus(1)
                .activationFailedMsg("activationFailedMsg")
                .build());
        when(mockPiviAppDService.appDManualRenewal("Q1234567890123456", LocalDateTime.of(2020, 1, 1, 23, 59, 59)))
                .thenReturn(result1);

        // Configure SubscriptionServiceMapper.queryServiceDOByCarVinAndPackage(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .subscriptionId("subscriptionId")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockServiceDOMapper.queryServiceDOByCarVinAndPackage("Q1234567890123456", "ONLINE-PACK"))
                .thenReturn(subscriptionServiceDOS);

        // Configure PIVIUnicomService.getSimCarInfoByCarVin(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("orderResultCode");
        unicomRespVO.setResponseDesc("activationFailedMsg");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setBookStatus("bookStatus");
        unicomRespData.setProductBookInfoList(List.of(productBookInfo));
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUnicomService.getSimCarInfoByCarVin("Q1234567890123456")).thenReturn(unicomRespVO);

        // Configure PIVIUnicomService.unicomManualRenewal(...).
        final UnicomRespVO unicomRespVO1 = new UnicomRespVO();
        unicomRespVO1.setResponseCode("orderResultCode");
        unicomRespVO1.setResponseDesc("activationFailedMsg");
        final UnicomRespData unicomRespData1 = new UnicomRespData();
        final ProductBookInfo productBookInfo1 = new ProductBookInfo();
        productBookInfo1.setBookStatus("bookStatus");
        unicomRespData1.setProductBookInfoList(List.of(productBookInfo1));
        unicomRespVO1.setUnicomRespData(unicomRespData1);
        final CommonResult<UnicomRespVO> unicomRespVOCommonResult = CommonResult.success(unicomRespVO1);
        when(mockPiviUnicomService.unicomManualRenewal("Q1234567890123456", LocalDateTime.of(2020, 1, 1, 23, 59, 59)))
                .thenReturn(unicomRespVOCommonResult);

        // Run the test
        final CommonResult<AppDCuSingleRenewalV0> result = appDCuSingleRenewalServiceImplUnderTest.appDCuSingleOperateRenewal(
                appDCuSingleRenewalDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAppDCuRenewRecordsMapper, times(2)).insert(any(AppDCuRenewRecords.class));
        verify(mockAppDCuRenewRecordsMapper, times(2)).updateById(any(AppDCuRenewRecords.class));
        verify(mockServiceLogMapper).insertBatch(anyList());
        verify(mockServiceDOMapper).updateBatch(anyList());
        verify(mockManualModifyLogDOService).recordLog(any(AppDCuRenewRecords.class));
    }

    @Test
    public void testAppDCuSingleOperateRenewal_OrderCheckServiceReturnsError() {
        // Setup
        final AppDCuSingleRenewalDTO appDCuSingleRenewalDTO = new AppDCuSingleRenewalDTO();
        appDCuSingleRenewalDTO.setCarVin("Q1234567890123456");
        appDCuSingleRenewalDTO.setAppDExpireDate("2020/01/01");
        appDCuSingleRenewalDTO.setCuExpireDate("2020/01/01");

        final CommonResult<AppDCuSingleRenewalV0> expectedResult = CommonResult.error(500, "message");

        when(mockOrderCheckService.checkOrderInTransit("Q1234567890123456", ServiceTypeEnum.PIVI))
                .thenReturn(CommonResult.error(new ServiceException(500, "message")));

        // Run the test
        final CommonResult<AppDCuSingleRenewalV0> result = appDCuSingleRenewalServiceImplUnderTest.appDCuSingleOperateRenewal(
                appDCuSingleRenewalDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateUnicomRenewRecords() {
        // Setup
        final AppDCuRenewRecords appDCuRenewRecords = AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("activationFailedMsg")
                .build();
        final CommonResult<UnicomRespVO> unicomRespVOResult = CommonResult.success(null);

        // Run the test
        appDCuSingleRenewalServiceImplUnderTest.updateUnicomRenewRecords(appDCuRenewRecords, unicomRespVOResult);

        // Verify the results
        verify(mockAppDCuRenewRecordsMapper).updateById(any(AppDCuRenewRecords.class));
    }

    @Test
    public void testGetAppDCuSinglePageList() {
        // Setup
        final AppDUcSingleRenewalPageDTO pageDTO = new AppDUcSingleRenewalPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<AppDCuSinglePageListV0> expectedResult = new PageResult<>(
                List.of(AppDCuSinglePageListV0.builder()
                        .operateTime("")
                        .carVin("carVin")
                        .operator("operator")
                        .renewalServiceName("信息娱乐服务")
                        .batchNo("1")
                        .build()), 0L);
        Page<AppDCuRenewRecords> page = new Page<>();
        page.setRecords(List.of(AppDCuRenewRecords.builder()
                .carVin("carVin")
                .operator("operator")
                .renewServiceType(1)
                .renewNo(1L)
                .build()));
        when(mockAppDCuRenewRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<AppDCuSinglePageListV0> result = appDCuSingleRenewalServiceImplUnderTest.getAppDCuSinglePageList(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateAppDRenewRecords() {
        // Setup
        final AppDCuRenewRecords appDCuRenewRecords = AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("activationFailedMsg")
                .build();
        final CommonResult<VcsOrderFufilmentCall> vcsOrderFufilmentCallResp = CommonResult.success(
                VcsOrderFufilmentCall.builder()
                        .activationStatus(1)
                        .activationFailedMsg("activationFailedMsg")
                        .build());

        // Run the test
        appDCuSingleRenewalServiceImplUnderTest.updateAppDRenewRecords(appDCuRenewRecords, vcsOrderFufilmentCallResp);

        // Verify the results
        verify(mockAppDCuRenewRecordsMapper).updateById(any(AppDCuRenewRecords.class));
    }

    @Test
    public void testGetAppDCuSingleExpireList() {
        // Setup
        final List<AppDCuSingleExpireVO> expectedResult = List.of(AppDCuSingleExpireVO.builder()
                .vin("carVin")
                .service("信息娱乐服务")
                .oldDate("2020/01/01")
                .build(),
                AppDCuSingleExpireVO.builder()
                        .vin("carVin")
                        .service("网络流量")
                        .oldDate("")
                        .build());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Configure PIVIAppDService.getVinSubscriptions(...).
        final AppDSubscriptionResp appDSubscriptionResp = new AppDSubscriptionResp();
        final AppDSubscriptionData appDSubscriptionData = new AppDSubscriptionData();
        appDSubscriptionData.setJlrSubscriptionId(0L);
        appDSubscriptionData.setSubscriptionId("subscriptionId");
        appDSubscriptionData.setSubscriberUniqueId("subscriberUniqueId");
        appDSubscriptionData.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionResp.setResult(List.of(appDSubscriptionData));
        when(mockPiviAppDService.getVinSubscriptions("carVin")).thenReturn(appDSubscriptionResp);

        // Configure PIVIUnicomService.getSimCarInfoByCarVin(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("orderResultCode");
        unicomRespVO.setResponseDesc("activationFailedMsg");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setBookStatus("bookStatus");
        unicomRespData.setProductBookInfoList(List.of(productBookInfo));
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUnicomService.getSimCarInfoByCarVin("carVin")).thenReturn(unicomRespVO);

        // Run the test
        final List<AppDCuSingleExpireVO> result = appDCuSingleRenewalServiceImplUnderTest.getAppDCuSingleExpireList(
                "carVin");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockSubscribeAsyncThreadPool, times(2)).execute(any(Runnable.class));
    }

    @Test
    public void testInsertAppDCuServiceDOLog() {
        // Setup
        final AppDCuRenewRecords appDCuRenewRecords = AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("activationFailedMsg")
                .build();
        final List<SubscriptionServiceDO> appDCuServiceDOs = List.of(SubscriptionServiceDO.builder()
                .subscriptionId("subscriptionId")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());

        // Run the test
        appDCuSingleRenewalServiceImplUnderTest.insertAppDCuServiceDOLog(appDCuRenewRecords, appDCuServiceDOs,
                ServicePackageEnum.ONLINE_PACK);

        // Verify the results
        verify(mockServiceLogMapper).insertBatch(anyList());
    }

    @Test
    public void testUpdateAppDCuServiceDO() {
        // Setup
        final AppDCuRenewRecords appDCuRenewRecords = AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("activationFailedMsg")
                .build();
        final List<SubscriptionServiceDO> appDCuServiceDOs = List.of(SubscriptionServiceDO.builder()
                .subscriptionId("subscriptionId")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());

        // Run the test
        appDCuSingleRenewalServiceImplUnderTest.updateAppDCuServiceDO(appDCuRenewRecords, appDCuServiceDOs);

        // Verify the results
        verify(mockServiceDOMapper).updateBatch(anyList());
    }

    @Test
    public void testCheckProcessRecord() {
        // Setup
        final AppDCuSingleRenewalDTO appDCuSingleRenewalDTO = new AppDCuSingleRenewalDTO();
        appDCuSingleRenewalDTO.setCarVin("carVin");
        appDCuSingleRenewalDTO.setAppDExpireDate("appDExpireDate");
        appDCuSingleRenewalDTO.setCuExpireDate("cuExpireDate");

        // Run the test
        final boolean result = appDCuSingleRenewalServiceImplUnderTest.checkProcessRecord(appDCuSingleRenewalDTO);

        // Verify the results
        assertFalse(result);
    }
}
