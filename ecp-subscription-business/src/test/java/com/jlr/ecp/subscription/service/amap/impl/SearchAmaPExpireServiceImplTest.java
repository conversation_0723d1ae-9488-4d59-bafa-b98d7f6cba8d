package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.QueryAmaPExpireDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.QueryAmaPExpireVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPQueryBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPQueryRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPQueryBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPQueryRecordsDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.amap.AmaPQueryStatusEnum;
import com.jlr.ecp.subscription.excel.listener.amap.SearchAmaPExpireCheckListener;
import com.jlr.ecp.subscription.excel.pojo.amap.SearchAmaPExpireBatchExcel;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SearchAmaPExpireServiceImplTest {

    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private AmaPQueryBatchRecordsDOMapper mockAmaPQueryBatchRecordsDOMapper;
    @Mock
    private AmaPQueryRecordsDOMapper mockAmaPQueryRecordsDOMapper;
    @Mock
    private AmaPProperties mockAmaPProperties;
    @Mock
    private PIVIAmaPService mockPiviAmaPService;
    @Mock
    private ThreadPoolTaskScheduler mockSubscribeScheduledThreadPool;

    @InjectMocks
    private SearchAmaPExpireServiceImpl searchAmaPExpireServiceImplUnderTest;

    @Test
    public void testGetQueryTemplateUrl() {
        // Setup
        when(mockAmaPProperties.getQueryExcelUrl()).thenReturn("queryExcelUrl");

        // Run the test
        final String result = searchAmaPExpireServiceImplUnderTest.getQueryTemplateUrl();

        // Verify the results
        assertEquals("queryExcelUrl", result);
    }

    @Test
    public void testQueryBatchPageList() {
        // Setup
        final AmaPBatchQueryPageDTO queryPageDTO = new AmaPBatchQueryPageDTO();
        queryPageDTO.setPageNo(1);
        queryPageDTO.setPageSize(10);
        queryPageDTO.setOperateTimeSort("DESC");

        final PageResult<AmaPBatchQueryPageVO> expectedResult = new PageResult<>(List.of(AmaPBatchQueryPageVO.builder()
                .operateTime("2020/01/01 00:00:00")
                .checkResultStatus(1)
                .checkResultDesc("上传成功")
                .batchNo("0")
                .errorDetail("errorDetail")
                .build()), 1L);
        Page<AmaPQueryBatchRecordsDO> page = new Page<>();
        AmaPQueryBatchRecordsDO build = AmaPQueryBatchRecordsDO.builder()
                .batchNo(0L)
                .verifyResult(1)
                .verifyReason("errorDetail")
                .build();
        build.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        page.setRecords(List.of(build));
        page.setTotal(1);
        when(mockAmaPQueryBatchRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<AmaPBatchQueryPageVO> result = searchAmaPExpireServiceImplUnderTest.queryBatchPageList(
                queryPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchSearchAmaPExpire() {
        // Setup
        // 创建一个模拟的 Excel 文件内容
        byte[] excelContent = createExcelContent();

        // 创建 MockMultipartFile
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file", // 文件字段名
                "test.xlsx", // 文件名
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // 文件类型
                excelContent // 文件内容
        );
        final CommonResult<AmaPBatchRenewalUploadVO> expectedResult = CommonResult.success(
                AmaPBatchRenewalUploadVO.builder()
                        .type("success")
                        .msg("上传成功，请关注批量查询结果")
                        .build());

        when(mockAmaPQueryBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(mockSnowflake.nextId()).thenReturn(0L);

        // Configure AmaPQueryRecordsDOMapper.selectList(...).
        final List<AmaPQueryRecordsDO> amaPQueryRecordsDOS = List.of(AmaPQueryRecordsDO.builder()
                .id(1L)
                .carVin("HGCM82633A12")
                .renewNo(0L)
                .queryStatus(0)
                .operator("system")
                .serviceStatus(0)
                .amaPExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .ecpExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockAmaPQueryRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(amaPQueryRecordsDOS);

        // Configure PIVIAmaPService.queryAmaPInfo(...).

        // Run the test
        final CommonResult<AmaPBatchRenewalUploadVO> result = searchAmaPExpireServiceImplUnderTest.batchSearchAmaPExpire(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAmaPQueryBatchRecordsDOMapper).insert(AmaPQueryBatchRecordsDO.builder()
                .batchNo(0L)
                .verifyResult(1)
                .dealStatus(1)
                .operator("")
                .build());
        verify(mockAmaPQueryRecordsDOMapper).insertBatch(List.of(AmaPQueryRecordsDO.builder()
                .carVin("HGCM82633A12")
                .renewNo(0L)
                .queryStatus(0)
                .operator("")
                .build()));
    }

    @Test
    public void testBatchSearchAmaPExpire_AmaPQueryBatchRecordsDOMapperSelectListReturnsNotNull() {
        // Setup
        // 创建一个模拟的 Excel 文件内容
        byte[] excelContent = createExcelContent();

        // 创建 MockMultipartFile
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file", // 文件字段名
                "test.xlsx", // 文件名
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // 文件类型
                excelContent // 文件内容
        );
        final CommonResult<AmaPBatchRenewalUploadVO> expectedResult = CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);

        final List<AmaPQueryBatchRecordsDO> amaPQueryBatchRecordsDOS = List.of(AmaPQueryBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .verifyResult(0)
                .verifyReason("errorDetail")
                .dealStatus(0)
                .operator("operator")
                .build());

        when(mockAmaPQueryBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(amaPQueryBatchRecordsDOS));

        // Configure AmaPQueryRecordsDOMapper.selectList(...).
        // Run the test
        final CommonResult<AmaPBatchRenewalUploadVO> result = searchAmaPExpireServiceImplUnderTest.batchSearchAmaPExpire(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchSearchAmaPExpire_AmaPQueryRecordsDOMapperSelectListReturnsNoItems() {
        // Setup
        // 创建一个模拟的 Excel 文件内容
        byte[] excelContent = createExcelContent();

        // 创建 MockMultipartFile
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file", // 文件字段名
                "test.xlsx", // 文件名
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // 文件类型
                excelContent // 文件内容
        );

        final CommonResult<AmaPBatchRenewalUploadVO> expectedResult = CommonResult.success(
                AmaPBatchRenewalUploadVO.builder()
                        .type("success")
                        .msg("上传成功，请关注批量查询结果")
                        .build());

        // Configure AmaPQueryBatchRecordsDOMapper.selectList(...).
        when(mockAmaPQueryBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(mockSnowflake.nextId()).thenReturn(0L);
        when(mockAmaPQueryRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure PIVIAmaPService.queryAmaPInfo(...).
        final AmaPSearchCenterVO amaPSearchCenterVO = AmaPSearchCenterVO.builder()
                .amaPExpireDate("amaPExpireDate")
                .ecpExpireDate("ecpExpireDate")
                .amaPServiceStatus(0)
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build();

        // Run the test
        final CommonResult<AmaPBatchRenewalUploadVO> result = searchAmaPExpireServiceImplUnderTest.batchSearchAmaPExpire(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAmaPQueryBatchRecordsDOMapper).insert(AmaPQueryBatchRecordsDO.builder()
                .batchNo(0L)
                .verifyResult(1)
                .dealStatus(1)
                .operator("")
                .build());
        verify(mockAmaPQueryRecordsDOMapper).insertBatch(List.of(AmaPQueryRecordsDO.builder()
                .carVin("HGCM82633A12")
                .renewNo(0L)
                .queryStatus(0)
                .operator("")
                .build()));
    }

    @Test
    public void testGetAmaPQueryBatchNo() {
        // Setup
        // Configure AmaPQueryBatchRecordsDOMapper.selectOne(...).
        final AmaPQueryBatchRecordsDO amaPQueryBatchRecordsDO = AmaPQueryBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .verifyResult(0)
                .verifyReason("errorDetail")
                .dealStatus(0)
                .operator("operator")
                .build();

        // Run the test
        final String result = searchAmaPExpireServiceImplUnderTest.getAmaPQueryBatchNo("batchNo");

        // Verify the results
        assertEquals("", result);
    }

    @Test
    public void testGetAmaPQueryStatus() {
        // Setup
        final List<AmaPQueryStatusVO> expectedResult = List.of(AmaPQueryStatusVO.builder()
                .queryStatusCode(0)
                .queryStatusDesc("查询中")
                .build(),AmaPQueryStatusVO.builder()
                .queryStatusCode(1)
                .queryStatusDesc("查询成功")
                .build(),AmaPQueryStatusVO.builder()
                .queryStatusCode(2)
                .queryStatusDesc("查询失败")
                .build());

        // Run the test
        final List<AmaPQueryStatusVO> result = searchAmaPExpireServiceImplUnderTest.getAmaPQueryStatus();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAmaPBatchQueryResult() {
        // Setup
        final QueryAmaPExpireDTO queryAmaPExpireDTO = new QueryAmaPExpireDTO();
        queryAmaPExpireDTO.setPageNo(0);
        queryAmaPExpireDTO.setPageSize(0);
        queryAmaPExpireDTO.setCarVin("carVin");
        queryAmaPExpireDTO.setBatchNoList(List.of("value"));
        queryAmaPExpireDTO.setStartTime("startTime");
        queryAmaPExpireDTO.setEndTime("endTime");
        queryAmaPExpireDTO.setQueryStatus(0);
        queryAmaPExpireDTO.setOperator("operator");
        queryAmaPExpireDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<QueryAmaPExpireVO> expectedResult = new PageResult<>(List.of(QueryAmaPExpireVO.builder()
                .batchNo("0")
                .carVin("carVin")
                .queryStatus(0)
                .queryStatusDesc("查询中")
                .amaPExpireDate("2020/01/01")
                .ecpExpireDate("2020/01/01")
                .serviceStatus(0)
                .serviceStatusDesc("未激活")
                .operator("operator")
                .operateTime("")
                .errorInfo("")
                .build()), 0L);
        AmaPQueryRecordsDO build = AmaPQueryRecordsDO.builder()
                .id(0L)
                .carVin("carVin")
                .renewNo(0L)
                .queryStatus(0)
                .operator("operator")
                .serviceStatus(0)
                .amaPExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .ecpExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .resultCode("1").build();
        Page<AmaPQueryRecordsDO> page = new Page<>();
        page.setRecords(List.of(build));
        when(mockAmaPQueryRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<QueryAmaPExpireVO> result = searchAmaPExpireServiceImplUnderTest.amaPBatchQueryResult(
                queryAmaPExpireDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchQueryAmaPExpireDate() {
        // Setup
        final List<AmaPQueryRecordsDO> amaPQueryRecordsDOList = List.of(AmaPQueryRecordsDO.builder()
                .id(0L)
                .carVin("carVin")
                .renewNo(0L)
                .queryStatus(0)
                .operator("operator")
                .serviceStatus(0)
                .amaPExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .ecpExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeScheduledThreadPool).execute(any(Runnable.class));

        // Configure PIVIAmaPService.queryAmaPInfo(...).
        final AmaPSearchCenterVO amaPSearchCenterVO = AmaPSearchCenterVO.builder()
                .amaPExpireDate("amaPExpireDate")
                .ecpExpireDate("ecpExpireDate")
                .amaPServiceStatus(0)
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build();
        when(mockPiviAmaPService.queryAmaPInfo("carVin")).thenReturn(amaPSearchCenterVO);

        // Configure AmaPQueryBatchRecordsDOMapper.selectOne(...).
        final AmaPQueryBatchRecordsDO amaPQueryBatchRecordsDO = AmaPQueryBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .verifyResult(0)
                .verifyReason("errorDetail")
                .dealStatus(0)
                .operator("operator")
                .build();
        when(mockAmaPQueryBatchRecordsDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(amaPQueryBatchRecordsDO);

        // Run the test
        searchAmaPExpireServiceImplUnderTest.batchQueryAmaPExpireDate(amaPQueryRecordsDOList, 0L);

        // Verify the results
        verify(mockSubscribeScheduledThreadPool).execute(any(Runnable.class));
        verify(mockAmaPQueryRecordsDOMapper).updateBatch(anyList());
        verify(mockAmaPQueryBatchRecordsDOMapper).updateById(any(AmaPQueryBatchRecordsDO.class));
    }

    @Test
    public void testQueryAmaPAndUpdateExpireResult() {
        // Setup
        // Configure AmaPQueryRecordsDOMapper.selectList(...).
        final List<AmaPQueryRecordsDO> amaPQueryRecordsDOS = List.of(AmaPQueryRecordsDO.builder()
                .id(0L)
                .carVin("carVin")
                .renewNo(0L)
                .queryStatus(0)
                .operator("operator")
                .serviceStatus(0)
                .amaPExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .ecpExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build());
        when(mockAmaPQueryRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(amaPQueryRecordsDOS);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeScheduledThreadPool).execute(any(Runnable.class));

        // Configure PIVIAmaPService.queryAmaPInfo(...).
        final AmaPSearchCenterVO amaPSearchCenterVO = AmaPSearchCenterVO.builder()
                .amaPExpireDate("amaPExpireDate")
                .ecpExpireDate("ecpExpireDate")
                .amaPServiceStatus(0)
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build();
        when(mockPiviAmaPService.queryAmaPInfo("carVin")).thenReturn(amaPSearchCenterVO);

        // Run the test
        searchAmaPExpireServiceImplUnderTest.queryAmaPAndUpdateExpireResult(0L);

        // Verify the results
        verify(mockAmaPQueryBatchRecordsDOMapper).update(any(AmaPQueryBatchRecordsDO.class), any(LambdaUpdateWrapper.class));
        verify(mockSubscribeScheduledThreadPool).execute(any(Runnable.class));
        verify(mockAmaPQueryRecordsDOMapper).updateBatch(anyList());
    }

    @Test
    public void testQueryAmaPAndUpdateExpireResult_AmaPQueryRecordsDOMapperSelectListReturnsNoItems() {
        // Setup
        when(mockAmaPQueryRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure PIVIAmaPService.queryAmaPInfo(...).

        // Run the test
        searchAmaPExpireServiceImplUnderTest.queryAmaPAndUpdateExpireResult(0L);

        // Verify the results
        verify(mockAmaPQueryBatchRecordsDOMapper).update(any(AmaPQueryBatchRecordsDO.class), any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testQueryAmaPExpireDate() {
        // Setup
        final List<AmaPQueryRecordsDO> batchAmaPList = List.of(AmaPQueryRecordsDO.builder()
                .id(0L)
                .carVin("carVin")
                .renewNo(0L)
                .queryStatus(0)
                .operator("operator")
                .serviceStatus(0)
                .amaPExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .ecpExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeScheduledThreadPool).execute(any(Runnable.class));

        // Configure PIVIAmaPService.queryAmaPInfo(...).
        final AmaPSearchCenterVO amaPSearchCenterVO = AmaPSearchCenterVO.builder()
                .amaPExpireDate("amaPExpireDate")
                .ecpExpireDate("ecpExpireDate")
                .amaPServiceStatus(0)
                .resultCode("resultCode")
                .errorDesc("errorDesc")
                .build();
        when(mockPiviAmaPService.queryAmaPInfo("carVin")).thenReturn(amaPSearchCenterVO);

        // Run the test
        searchAmaPExpireServiceImplUnderTest.queryAmaPExpireDate(batchAmaPList, 0L);

        // Verify the results
        verify(mockSubscribeScheduledThreadPool).execute(any(Runnable.class));
        verify(mockAmaPQueryRecordsDOMapper).updateBatch(anyList());
    }

    @Test
    public void testUpdateBatchQueryStatus() {
        // Setup
        // Run the test
        searchAmaPExpireServiceImplUnderTest.updateBatchQueryStatus(0L);

        // Verify the results
        verify(mockAmaPQueryBatchRecordsDOMapper).update(any(AmaPQueryBatchRecordsDO.class), any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testQueryBatchRecordsDOPage() {
        // Setup
        final AmaPBatchQueryPageDTO queryPageDTO = new AmaPBatchQueryPageDTO();
        queryPageDTO.setPageNo(0);
        queryPageDTO.setPageSize(0);
        queryPageDTO.setOperateTimeSort("operateTimeSort");

        when(mockAmaPQueryBatchRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(new Page<>(0L, 0L, 0L, false));

        final Page<AmaPQueryBatchRecordsDO> expectedResult = new Page<>(0L, 0L, 0L, false);

        // Run the test
        final Page<AmaPQueryBatchRecordsDO> result = searchAmaPExpireServiceImplUnderTest.queryBatchRecordsDOPage(
                queryPageDTO);

        // Verify the results
        assertEquals(expectedResult.getRecords(), result.getRecords());
    }

    @Test
    public void testInsertAmaPQueryRecords() {
        // Setup
        SearchAmaPExpireCheckListener mockSearchExpireListener = mock(SearchAmaPExpireCheckListener.class);
        final SearchAmaPExpireBatchExcel batchExcel = SearchAmaPExpireBatchExcel.builder()
                .carVin("carVin")
                .build();
        List<List<SearchAmaPExpireBatchExcel>> list = new ArrayList<>();
        list.add(List.of(batchExcel));
        mockSearchExpireListener.setAllDataList(list);

        // Run the test
        searchAmaPExpireServiceImplUnderTest.insertAmaPQueryRecords(mockSearchExpireListener, 0L);

        // Verify the results
/*        verify(mockAmaPQueryRecordsDOMapper).insertBatch(List.of(AmaPQueryRecordsDO.builder()
                .carVin("carVin")
                .renewNo(0L)
                .queryStatus(AmaPQueryStatusEnum.PROGRESS.getQueryStatus())
                .operator("")
                .build()));*/
    }

    @Test
    public void testBuildAmaPQueryRecordsDO() {
        // Setup
        final SearchAmaPExpireBatchExcel searchExpireExcel = SearchAmaPExpireBatchExcel.builder()
                .carVin("carVin")
                .build();
        final AmaPQueryRecordsDO expectedResult = AmaPQueryRecordsDO.builder()
                .carVin("CARVIN")
                .renewNo(0L)
                .queryStatus(0)
                .operator("")
                .build();

        // Run the test
        final AmaPQueryRecordsDO result = searchAmaPExpireServiceImplUnderTest.buildAmaPQueryRecordsDO(
                searchExpireExcel, 0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testInsertBatchAmaPQueryRecords() {
        // Setup
        // Run the test
        searchAmaPExpireServiceImplUnderTest.insertBatchAmaPQueryRecords(0L);

        // Verify the results
        verify(mockAmaPQueryBatchRecordsDOMapper).insert(AmaPQueryBatchRecordsDO.builder()
                .batchNo(0L)
                .verifyResult(1)
                .dealStatus(1)
                .operator("")
                .build());
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM82633A12"); // 示例 VIN

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }
}
