package com.jlr.ecp.subscription.service.unicom;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomTodoOrderDO;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomTodoOrderMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnicomTodoOrderServiceImplTest {

    @Mock
    private UnicomTodoOrderMapper mockUnicomTodoOrderMapper;

    @InjectMocks
    private UnicomTodoOrderServiceImpl unicomTodoOrderServiceImplUnderTest;

    @Test
    public void testFindUnicomTodoOrder() {
        // Setup
        final CommonResult<List<UnicomTodoOrderVO>> expectedResult = CommonResult.success(
                List.of(UnicomTodoOrderVO.builder()
                        .status(0)
                        .requestCount(0)
                        .build()));

        // Configure UnicomTodoOrderMapper.selectList(...).
        final List<UnicomTodoOrderDO> unicomTodoOrderDOS = List.of(UnicomTodoOrderDO.builder()
                .status(0)
                .requestCount(0)
                .build());
        when(mockUnicomTodoOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(unicomTodoOrderDOS);

        // Run the test
        final CommonResult<List<UnicomTodoOrderVO>> result = unicomTodoOrderServiceImplUnderTest.findUnicomTodoOrder(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFindUnicomTodoOrder_UnicomTodoOrderMapperReturnsNoItems() {
        // Setup
        when(mockUnicomTodoOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<List<UnicomTodoOrderVO>> result = unicomTodoOrderServiceImplUnderTest.findUnicomTodoOrder(0);

        // Verify the results
        assertEquals(CommonResult.success(Collections.emptyList()), result);
    }
}
