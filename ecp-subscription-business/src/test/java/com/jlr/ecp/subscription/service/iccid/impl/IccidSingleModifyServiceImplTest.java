package com.jlr.ecp.subscription.service.iccid.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidSingleModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.SimCardInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyPageV0;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidSingleModifyV0;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomResultEnum;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.env.MockEnvironment;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.VIN_NOT_FOUND;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IccidSingleModifyServiceImplTest {

    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private PIVIUnicomService mockUnicomService;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private IccidModifyRecordsDOMapper mockIccidModifyRecordsDOMapper;
    @Mock
    private ManualModifyLogDOService mockManualModifyLogDOService;

    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;

    @Mock
    private AppDCuSingleRenewalService mockAppDCuSingleRenewalService;

    @Mock
    private OrderCheckService mockOrderCheckService;

    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;

    @Mock
    private PIVIUnicomService piviUnicomService;


    @InjectMocks
    private IccidSingleModifyServiceImpl iccidSingleModifyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(iccidSingleModifyServiceImplUnderTest, "environment", new MockEnvironment());
        ReflectionTestUtils.setField(iccidSingleModifyServiceImplUnderTest, "snowflake", mockSnowflake);
        ReflectionTestUtils.setField(iccidSingleModifyServiceImplUnderTest, "unicomService",
                mockUnicomService);
    }

    @Test
    public void testIccidSingleModifyPreCheck() {
        // Setup
        final IccidSingleModifyDTO iccidSingleModifyDTO = new IccidSingleModifyDTO();
        iccidSingleModifyDTO.setCarVin("QWEQWE12345678901");
        iccidSingleModifyDTO.setNewIccid("12345678901234567890");

        final CommonResult<IccidSingleModifyV0> expectedResult = CommonResult.success(IccidSingleModifyV0.builder()
                .renewalResult("前置校验通过")
                .carVin("QWEQWE12345678901")
                .oldIccid("12345678901234567890")
                .newIccid("12345678901234567890")
                .build());

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("12345678901234567890")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("QWEQWE12345678901")).thenReturn(piviPackageDO);

        // Run the test
        final CommonResult<IccidSingleModifyV0> result = iccidSingleModifyServiceImplUnderTest.iccidSingleModifyPreCheck(
                iccidSingleModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testIccidSingleModifyPreCheck_PIVIPackageDOMapperReturnsNull() {
        // Setup
        final IccidSingleModifyDTO iccidSingleModifyDTO = new IccidSingleModifyDTO();
        iccidSingleModifyDTO.setCarVin("QWEQWE12345678901");
        iccidSingleModifyDTO.setNewIccid("12345678901234567890");

        final CommonResult<IccidSingleModifyV0> expectedResult = CommonResult.error(VIN_NOT_FOUND);
        when(mockPiviPackageDOMapper.findICCIDByCarVin("QWEQWE12345678901")).thenReturn(null);

        // Run the test
        final CommonResult<IccidSingleModifyV0> result = iccidSingleModifyServiceImplUnderTest.iccidSingleModifyPreCheck(
                iccidSingleModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testIccidSingleOperateModify() {
        // Setup
        final IccidSingleModifyDTO iccidSingleModifyDTO = new IccidSingleModifyDTO();
        iccidSingleModifyDTO.setCarVin("QWEQWE12345678901");
        iccidSingleModifyDTO.setNewIccid("12345678901234567891");

        final CommonResult<IccidSingleModifyV0> expectedResult = CommonResult.success(IccidSingleModifyV0.builder()
                .renewalResult("请求已发送，请前往修改记录查看结果")
                .build());

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("12345678901234567890")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("QWEQWE12345678901")).thenReturn(piviPackageDO);

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        simCardInfo.setIccid("12345678901234567891");
        unicomRespData.setSimCardInfo(simCardInfo);
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockUnicomService.getSimCardInfo("12345678901234567891")).thenReturn(unicomRespVO);

        when(mockSnowflake.nextId()).thenReturn(0L);

        List<SubscriptionServiceDO> subscriptionList = Arrays.asList(
                SubscriptionServiceDO.builder()
                        .servicePackage(ServicePackageEnum.ONLINE_PACK.getPackageName())
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .build(),
                SubscriptionServiceDO.builder()
                        .servicePackage(ServicePackageEnum.DATA_PLAN.getPackageName())
                        .expiryDate(LocalDateTime.now().plusYears(2))
                        .build()
        );
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(subscriptionList);

        UnicomRespVO mockResponse = new UnicomRespVO();
        mockResponse.setResponseDesc(UnicomResultEnum.SUCCESS.getDesc());

        when(piviUnicomService.unicomRenewalByIccid(any(FufilmentMessage.class), anyString())).thenReturn(CommonResult.success(mockResponse));

        when(mockAppDCuSingleRenewalService.checkProcessRecord(any())).thenReturn(false);

        when(mockOrderCheckService.checkOrderInTransit(any(), any(ServiceTypeEnum.class))).thenReturn(CommonResult.success(null));

        when(mockSnowflake.nextId()).thenReturn(1L);

        // Run the test
        final CommonResult<IccidSingleModifyV0> result = iccidSingleModifyServiceImplUnderTest.iccidSingleOperateModify(
                iccidSingleModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockIccidModifyRecordsDOMapper).insert(any(IccidModifyRecordsDO.class));
        verify(mockPiviPackageDOMapper).updateById(any(PIVIPackageDO.class));
        verify(mockManualModifyLogDOService).recordLog(any(IccidModifyRecordsDO.class));
    }

    @Test
    public void testIccidSingleOperateModify_updateCuExpireDate_error() {
        // Setup
        final IccidSingleModifyDTO iccidSingleModifyDTO = new IccidSingleModifyDTO();
        iccidSingleModifyDTO.setCarVin("QWEQWE12345678901");
        iccidSingleModifyDTO.setNewIccid("12345678901234567891");

        final CommonResult<IccidSingleModifyV0> expectedResult = CommonResult.success(IccidSingleModifyV0.builder()
                .renewalResult("已有在途订单，请稍后再试")
                .build());

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("12345678901234567890")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("QWEQWE12345678901")).thenReturn(piviPackageDO);

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        simCardInfo.setIccid("12345678901234567891");
        unicomRespData.setSimCardInfo(simCardInfo);
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockUnicomService.getSimCardInfo("12345678901234567891")).thenReturn(unicomRespVO);

        when(mockSnowflake.nextId()).thenReturn(0L);

        List<SubscriptionServiceDO> subscriptionList = Arrays.asList(
                SubscriptionServiceDO.builder()
                        .servicePackage(ServicePackageEnum.ONLINE_PACK.getPackageName())
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .build(),
                SubscriptionServiceDO.builder()
                        .servicePackage(ServicePackageEnum.DATA_PLAN.getPackageName())
                        .expiryDate(LocalDateTime.now().plusYears(2))
                        .build()
        );
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(subscriptionList);

        when(mockAppDCuSingleRenewalService.checkProcessRecord(any())).thenReturn(true);

        when(mockSnowflake.nextId()).thenReturn(1L);

        // Run the test
        final CommonResult<IccidSingleModifyV0> result = iccidSingleModifyServiceImplUnderTest.iccidSingleOperateModify(
                iccidSingleModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockIccidModifyRecordsDOMapper).insert(any(IccidModifyRecordsDO.class));
    }

    @Test
    public void testIccidSingleOperateModify_error() {
        // Setup
        final IccidSingleModifyDTO iccidSingleModifyDTO = new IccidSingleModifyDTO();
        iccidSingleModifyDTO.setCarVin("QWEQWE12345678901");
        iccidSingleModifyDTO.setNewIccid("12345678901234567891");

        final CommonResult<IccidSingleModifyV0> expectedResult = CommonResult.success(IccidSingleModifyV0.builder()
                .renewalResult("error")
                .build());

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("12345678901234567890")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("QWEQWE12345678901")).thenReturn(piviPackageDO);

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseDesc("error");
        unicomRespVO.setQueryResult("error");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        simCardInfo.setIccid("12345678901234567891");
        unicomRespData.setSimCardInfo(simCardInfo);
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockUnicomService.getSimCardInfo("12345678901234567891")).thenReturn(unicomRespVO);

        // Run the test
        final CommonResult<IccidSingleModifyV0> result = iccidSingleModifyServiceImplUnderTest.iccidSingleOperateModify(
                iccidSingleModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetIccidSinglePageList() {
        // Setup
        final IccidSingleModifyPageDTO pageDTO = new IccidSingleModifyPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(10); // 设置一个合理的分页大小
        pageDTO.setOperateTimeSort("operateTimeSort");

        // 模拟 IccidModifyRecordsDO 数据
        final List<IccidModifyRecordsDO> iccidModifyRecordsDOS = List.of(
                IccidModifyRecordsDO.builder()
                        .id(1L)
                        .modifyNo(1L)
                        .carVin("carVin")
                        .modifyBeforeIccid("oldIccid")
                        .modifyAfterIccid("newIccid")
                        .modifyStatus(0)
                        .errorDesc("ecpDisplay")
                        .build()
        );

        // 模拟分页结果
        final Page<IccidModifyRecordsDO> page = new Page<>(0, 10, 1);
        page.setRecords(iccidModifyRecordsDOS);

        // 模拟 mockIccidModifyRecordsDOMapper.selectPage 返回非空的 Page 对象
        when(mockIccidModifyRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // 预期结果
        final PageResult<IccidSingleModifyPageV0> expectedResult = new PageResult<>(
                List.of(
                        IccidSingleModifyPageV0.builder()
                                .operateTime(String.valueOf(System.currentTimeMillis())) // 这里需要根据实际的日期格式化进行调整
                                .operator("operator")
                                .carVin("carVin")
                                .modifyAfterIccid("newIccid")
                                .modifyNo("1")
                                .build()
                ), 1L);

        // Run the test
        final PageResult<IccidSingleModifyPageV0> result = iccidSingleModifyServiceImplUnderTest.getIccidSinglePageList(pageDTO);

        // Verify the results
        assertEquals(expectedResult.getTotal(), result.getTotal());
        assertEquals(expectedResult.getList().size(), result.getList().size());
        assertEquals(expectedResult.getList().get(0).getCarVin(), result.getList().get(0).getCarVin());
        assertEquals(expectedResult.getList().get(0).getModifyAfterIccid(), result.getList().get(0).getModifyAfterIccid());
        assertEquals(expectedResult.getList().get(0).getModifyNo(), result.getList().get(0).getModifyNo());
    }

    @Test
    public void testUpdateCuExpireDate_WhenSubscriptionListIsEmpty() {
        // Setup
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .vin("VIN1234567890")
                .iccid("ICCID1234567890")
                .expiryDate(LocalDateTime.now().plusYears(1))
                .build();

        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        LocalDateTime manualExpireDate = LocalDateTime.now().plusYears(2);
        when(mockAppDCuRenewRecordsMapper.getByVinAndStatus(anyString(), anyInt()))
                .thenReturn(Collections.singletonList(AppDCuRenewRecords.builder()
                        .renewAfterExpiryDate(manualExpireDate)
                        .build()));

        FufilmentMessage message = new FufilmentMessage();
        message.setVin(piviPackageDO.getVin());
        message.setServiceBeginDate(manualExpireDate.minusDays(1));
        message.setServiceEndDate(manualExpireDate);
        message.setVcsOrderCode(mockSnowflake.nextIdStr());
        message.setTenantId(TenantContextHolder.getTenantId());

        UnicomRespVO mockResponse = new UnicomRespVO();
        mockResponse.setResponseDesc(UnicomResultEnum.SUCCESS.getDesc());
        CommonResult<UnicomRespVO> expectedResult = CommonResult.success(mockResponse);

        when(piviUnicomService.unicomRenewalByIccid(any(FufilmentMessage.class), anyString())).thenReturn(expectedResult);

        when(mockAppDCuSingleRenewalService.checkProcessRecord(any())).thenReturn(false);

        when(mockOrderCheckService.checkOrderInTransit(anyString(), any(ServiceTypeEnum.class))).thenReturn(CommonResult.success(null));

        // Run the test
        CommonResult<UnicomRespVO> result = iccidSingleModifyServiceImplUnderTest.updateCuExpireDate(piviPackageDO);

        // Verify
        assertEquals(expectedResult, result);
        verify(piviUnicomService).unicomRenewalByIccid(any(FufilmentMessage.class), eq(piviPackageDO.getIccid()));
    }

    @Test
    public void testUpdateCuExpireDate_WhenSubscriptionListIsNotEmpty() {
        // Setup
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .vin("VIN1234567890")
                .iccid("ICCID1234567890")
                .build();

        List<SubscriptionServiceDO> subscriptionList = Arrays.asList(
                SubscriptionServiceDO.builder()
                        .servicePackage(ServicePackageEnum.ONLINE_PACK.getPackageName())
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .build(),
                SubscriptionServiceDO.builder()
                        .servicePackage(ServicePackageEnum.DATA_PLAN.getPackageName())
                        .expiryDate(LocalDateTime.now().plusYears(2))
                        .build()
        );

        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(subscriptionList);

        CommonResult<String> orderCheckResult = CommonResult.success("No in-transit orders");
        when(mockOrderCheckService.checkOrderInTransit(anyString(), any(ServiceTypeEnum.class))).thenReturn(orderCheckResult);

        FufilmentMessage message = new FufilmentMessage();
        message.setVin(piviPackageDO.getVin());
        message.setServiceBeginDate(subscriptionList.get(0).getExpiryDate().minusDays(1));
        message.setServiceEndDate(subscriptionList.get(0).getExpiryDate());
        message.setVcsOrderCode(mockSnowflake.nextIdStr());
        message.setTenantId(TenantContextHolder.getTenantId());

        UnicomRespVO mockResponse = new UnicomRespVO();
        mockResponse.setResponseDesc(UnicomResultEnum.SUCCESS.getDesc());
        CommonResult<UnicomRespVO> expectedResult = CommonResult.success(mockResponse);

        when(piviUnicomService.unicomRenewalByIccid(any(FufilmentMessage.class), anyString())).thenReturn(expectedResult);

        // Run the test
        CommonResult<UnicomRespVO> result = iccidSingleModifyServiceImplUnderTest.updateCuExpireDate(piviPackageDO);

        // Verify
        assertEquals(expectedResult, result);
        verify(piviUnicomService).unicomRenewalByIccid(any(FufilmentMessage.class), eq(piviPackageDO.getIccid()));
    }
}
