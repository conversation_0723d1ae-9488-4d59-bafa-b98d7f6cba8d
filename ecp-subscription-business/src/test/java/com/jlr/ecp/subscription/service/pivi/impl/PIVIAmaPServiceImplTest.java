package com.jlr.ecp.subscription.service.pivi.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.*;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PIVIAmaPServiceImplTest {

    @Mock
    private AmaPProperties mockAmapProperties;
    @Mock
    private VcsOrderFulfilmentCallMapper mockRecordsMapper;
    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleDOMapper;
    @Mock
    private Redisson mockRedisson;
    @Mock
    private ApplicationContext mockApplicationContext;

    @Mock
    private SubscriptionService mockSubscriptionService;

    @InjectMocks
    private PIVIAmaPServiceImpl piviAmaPServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Before
    public void setUp() throws InterruptedException {
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
    }

    @Test
    public void testCallAmaPService() {
        // Setup
        final FufilmentMessage fufilmentMessage = new FufilmentMessage("orderItemCode", 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final boolean result = piviAmaPServiceImplUnderTest.callAmaPService(fufilmentMessage, "fulfilmentId");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testQueryAmaPInfo() {
        // Setup
        final AmaPSearchCenterVO expectedResult = AmaPSearchCenterVO.builder()
                .queryStatus(2)
                .queryStatusDesc("查询失败")
                .ecpExpireDate("2020/01/01")
                .errorDesc("")
                .carSystemModel("非PIVI车机")
                .build();
        when(mockAmapProperties.getPid()).thenReturn("result");
        when(mockAmapProperties.getUrl()).thenReturn("result");
        when(mockAmapProperties.getAccessKey()).thenReturn("result");
        when(mockAmapProperties.getSecretKey()).thenReturn("result");

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(subscriptionServiceDOS);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure IncontrolVehicleDOMapper.selectOne(...).
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .carVin("carVin")
                .carSystemModel("carSystemModel")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDO);
        when(mockSubscriptionService.getExpireDateFromAppDCuRenewRecords(any())).thenReturn(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final AmaPSearchCenterVO result = piviAmaPServiceImplUnderTest.queryAmaPInfo("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryAmaPInfo_SubscriptionServiceMapperReturnsNoItems() {
        // Setup
        final AmaPSearchCenterVO expectedResult = AmaPSearchCenterVO.builder()
                .queryStatus(2)
                .queryStatusDesc("查询失败")
                .ecpExpireDate("2020/01/01")
                .errorDesc("")
                .carSystemModel("非PIVI车机")
                .build();
        when(mockAmapProperties.getPid()).thenReturn("result");
        when(mockAmapProperties.getUrl()).thenReturn("result");
        when(mockAmapProperties.getAccessKey()).thenReturn("result");
        when(mockAmapProperties.getSecretKey()).thenReturn("result");
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure IncontrolVehicleDOMapper.selectOne(...).
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .carVin("carVin")
                .carSystemModel("carSystemModel")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDO);
        when(mockSubscriptionService.getExpireDateFromAppDCuRenewRecords(any())).thenReturn(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final AmaPSearchCenterVO result = piviAmaPServiceImplUnderTest.queryAmaPInfo("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCarAmaPInfo() {
        // Setup
        final AmaPCarInfoResponse expectedResult = new AmaPCarInfoResponse();
        expectedResult.setQueryResult("UnknownHostException: result");

        when(mockAmapProperties.getUrl()).thenReturn("result");
        when(mockAmapProperties.getAccessKey()).thenReturn("result");
        when(mockAmapProperties.getSecretKey()).thenReturn("result");

        // Run the test
        final AmaPCarInfoResponse result = piviAmaPServiceImplUnderTest.getCarAmaPInfo("pid", "vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCarAmaPInfoBatch() {
        // Setup
        final AmaPCarInfoResponse expectedResult = new AmaPCarInfoResponse();

        when(mockAmapProperties.getUrl()).thenReturn("result");
        when(mockAmapProperties.getAccessKey()).thenReturn("result");
        when(mockAmapProperties.getSecretKey()).thenReturn("result");

        // Run the test
        final AmaPCarInfoResponse result = piviAmaPServiceImplUnderTest.getCarAmaPInfoBatch("pid", "vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryAmaPChargeInfo() {
        // Setup
        final AmaPChargeSearchResponseDTO expectedResult = null;

        when(mockAmapProperties.getUrl()).thenReturn("result");
        when(mockAmapProperties.getAccessKey()).thenReturn("result");
        when(mockAmapProperties.getSecretKey()).thenReturn("result");

        // Run the test
        final AmaPChargeSearchResponseDTO result = piviAmaPServiceImplUnderTest.queryAmaPChargeInfo("cusOrderId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryAmaPExpireDate() {
        // Setup
        final AmaPCarInfoResponse expectedResult = new AmaPCarInfoResponse();
        expectedResult.setQueryResult("UnknownHostException: result");

        when(mockAmapProperties.getPid()).thenReturn("result");
        when(mockAmapProperties.getUrl()).thenReturn("result");
        when(mockAmapProperties.getAccessKey()).thenReturn("result");
        when(mockAmapProperties.getSecretKey()).thenReturn("result");

        // Run the test
        final AmaPCarInfoResponse result = piviAmaPServiceImplUnderTest.queryAmaPExpireDate("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryPIVIPackageDOByCarVin() {
        // Setup
        final PIVIPackageDO expectedResult = PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Run the test
        final PIVIPackageDO result = piviAmaPServiceImplUnderTest.queryPIVIPackageDOByCarVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testConcurrentCallAMapRenew() {
        // Setup
        final AmaPOrderChargerRequestDTO requestDTO = AmaPOrderChargerRequestDTO.builder()
                .vid("vid")
                .cid("cid")
                .cusOrderId("cusOrderId")
                .amount(0)
                .build();
        final AmaPOrderChargeResponseDTO responseDTO = new AmaPOrderChargeResponseDTO();
        responseDTO.setVersion("version");
        responseDTO.setCode("code");
        responseDTO.setErrDetail("activationFailedMsg");
        responseDTO.setResult(false);
        final OrderChargeResponseData orderChargeResponseData = new OrderChargeResponseData();
        responseDTO.setData(List.of(orderChargeResponseData));
        final CommonResult<AmaPOrderChargeResponseDTO> expectedResult = CommonResult.error(ORDER_IN_TRANSIT_ERROR);

        // Configure ApplicationContext.getBean(...).
        final PIVIAmaPServiceImpl piviAmaPService = new PIVIAmaPServiceImpl();
        when(mockApplicationContext.getBean(PIVIAmaPServiceImpl.class)).thenReturn(piviAmaPService);

        when(mockLock.tryLock()).thenReturn(false);

        // Run the test
        final CommonResult<AmaPOrderChargeResponseDTO> result = piviAmaPServiceImplUnderTest.concurrentCallAMapRenew(
                requestDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
