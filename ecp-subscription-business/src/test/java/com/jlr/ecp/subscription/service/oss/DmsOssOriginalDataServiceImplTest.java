package com.jlr.ecp.subscription.service.oss;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.report.api.ReportApi;
import com.jlr.ecp.report.api.dto.UninitializedReasonDto;
import com.jlr.ecp.subscription.api.bau.dto.PreCheckListRequest;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.VinInitialQueryDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataMapper;
import com.jlr.ecp.subscription.dal.mysql.oss.VinInitialQueryMapper;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.model.vo.OnlineServiceBatchQueryRecordVO;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeStatusVO;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DmsOssOriginalDataServiceImplTest {

    @Mock
    private DmsOssOriginalDataMapper mockDmsOssOriginalDataMapper;
    @Mock
    private VinInitialQueryMapper mockVinInitialQueryMapper;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private ReportApi mockReportApi;
    @Mock
    private FileService mockFileService;

    @InjectMocks
    private DmsOssOriginalDataServiceImpl dmsOssOriginalDataServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(dmsOssOriginalDataServiceImplUnderTest, "queryExcelUrl", "queryExcelUrl");
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), DmsOssOriginalDataDO.class);

    }

    @Test
    public void testGetPreCheckIdList() {
        // Setup
        final PreCheckListRequest request = new PreCheckListRequest();
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setStatus(0);

        // Configure DmsOssOriginalDataMapper.selectList(...).
        final List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS = List.of(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());
        when(mockDmsOssOriginalDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(dmsOssOriginalDataDOS);

        // Run the test
        final List<Long> result = dmsOssOriginalDataServiceImplUnderTest.getPreCheckIdList(request);

        // Verify the results
        assertEquals(List.of(0L), result);
    }

    @Test
    public void testGetListByIdList() {
        // Setup
        final List<DmsOssOriginalDataDO> expectedResult = List.of(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());

        // Configure DmsOssOriginalDataMapper.selectList(...).
        final List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS = List.of(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());
        when(mockDmsOssOriginalDataMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(dmsOssOriginalDataDOS);

        // Run the test
        final List<DmsOssOriginalDataDO> result = dmsOssOriginalDataServiceImplUnderTest.getListByIdList(List.of(0L));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateBatch() {
        // Setup
        final List<DmsOssOriginalDataDO> updateList = List.of(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());

        // Run the test
        dmsOssOriginalDataServiceImplUnderTest.updateBatch(updateList);

        // Verify the results
        verify(mockDmsOssOriginalDataMapper).updateBatch(List.of(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build()));
    }

    @Test
    public void testGetPreSyncIdList() {
        final PreCheckListRequest request = new PreCheckListRequest();
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setStatus(0);
        final Map<Long, List<Long>> expectedResult = Map.ofEntries(Map.entry(0L, List.of(0L)));

        // Configure DmsOssOriginalDataMapper.selectList(...).
        final List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS = List.of(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());
        when(mockDmsOssOriginalDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(dmsOssOriginalDataDOS);

        // Run the test
        final Map<Long, List<Long>> result = dmsOssOriginalDataServiceImplUnderTest.getPreSyncIdList(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPreSyncIdList_DmsOssOriginalDataMapperReturnsNoItems() {
        // Setup
        final PreCheckListRequest request = new PreCheckListRequest();
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setStatus(0);
        final Map<Long, List<Long>> expectedResult = Maps.newHashMap();
        when(mockDmsOssOriginalDataMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<Long>> result = dmsOssOriginalDataServiceImplUnderTest.getPreSyncIdList(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetStatusByVin() {
        // Setup
        final OnlineServiceInitializeStatusVO expectedResult = OnlineServiceInitializeStatusVO.builder()
                .carVin("carVin")
                .initializedResult("是")
                .dmsResult("是")
                .sotaResult("是")
                .appdResult("是")
                .amapResult("是")
                .cuResult("是")
                .dpResult("是")
                .piviResult("是")
                .specialVinResult("否")
                .syncResult("是")
                .build();

        // Configure DmsOssOriginalDataMapper.selectOne(...).
        final DmsOssOriginalDataDO dmsOssOriginalDataDO = DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(1)
                .syncStatus(1)
                .sotaResult(1)
                .appdResult(1)
                .cuResult(1)
                .amapResult(1)
                .dpResult(1)
                .piviConfigResult(1)
                .vinMatchResult(2)
                .specialVinConfig("specialVinConfig")
                .sourceType(1)
                .build();
        when(mockDmsOssOriginalDataMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(dmsOssOriginalDataDO);

        // Run the test
        final OnlineServiceInitializeStatusVO result = dmsOssOriginalDataServiceImplUnderTest.getStatusByVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryExcelUrl() {
        assertEquals("queryExcelUrl", dmsOssOriginalDataServiceImplUnderTest.queryExcelUrl());
    }

    @Test
    public void testUploadBatchSearch() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent());
        final CommonResult<Boolean> expectedResult = CommonResult.success(true);
        when(mockFileService.createFile(eq(null), anyString(), any(byte[].class), eq("AWS_S3_FILE")))
                .thenReturn("path");
        when(mockReportApi.generateVinInitialQuery(any()))
                .thenReturn(CommonResult.success(true));

        // Run the test
        final CommonResult<Boolean> result = dmsOssOriginalDataServiceImplUnderTest.uploadBatchSearch(multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockVinInitialQueryMapper).insert(any(VinInitialQueryDO.class));
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM82633A12"); // 示例 VIN

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }

    @Test
    public void testGenerateReasonFile() {
        // Setup
        final CommonResult<Boolean> expectedResult = CommonResult.success(true);
        when(mockReportApi.generateUninitializedReason(new UninitializedReasonDto(0L)))
                .thenReturn(CommonResult.success(false));

        // Run the test
        final CommonResult<Boolean> result = dmsOssOriginalDataServiceImplUnderTest.generateReasonFile(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBatchQueryRecords() {
        // Setup
        final VinInitialLogPageDTO logPageDTO = new VinInitialLogPageDTO();
        logPageDTO.setPageNo(0);
        logPageDTO.setPageSize(0);
        logPageDTO.setOperateTimeSort("operateTimeSort");
        logPageDTO.setCreatedTimeSort("createdTimeSort");
        logPageDTO.setBauJobId(0L);

        final PageResult<OnlineServiceBatchQueryRecordVO> expectedResult = new PageResult<>(
                List.of(OnlineServiceBatchQueryRecordVO.builder()
                        .operateTime("")
                        .status("上传成功")
                        .reportNo("queryNo")
                        .build()), 0L);
        Page<VinInitialQueryDO> page = new Page<>();
        page.setRecords(List.of(VinInitialQueryDO.builder()
                .queryNo("queryNo")
                .operator("operator")
                .fileS3Url("fileS3Url")
                .build()));
        when(mockVinInitialQueryMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<OnlineServiceBatchQueryRecordVO> result = dmsOssOriginalDataServiceImplUnderTest.getBatchQueryRecords(
                logPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testInsert() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build();

        // Run the test
        dmsOssOriginalDataServiceImplUnderTest.insert(originalDataDO);

        // Verify the results
        verify(mockDmsOssOriginalDataMapper).insert(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());
    }

    @Test
    public void testUpdate() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build();

        // Run the test
        dmsOssOriginalDataServiceImplUnderTest.update(originalDataDO);

        // Verify the results
        verify(mockDmsOssOriginalDataMapper).updateById(DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build());
    }

    @Test
    public void testGetByVin() {
        // Setup
        final DmsOssOriginalDataDO expectedResult = DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build();

        // Configure DmsOssOriginalDataMapper.selectOne(...).
        final DmsOssOriginalDataDO dmsOssOriginalDataDO = DmsOssOriginalDataDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("specialVinConfig")
                .sourceType(0)
                .build();
        when(mockDmsOssOriginalDataMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(dmsOssOriginalDataDO);

        // Run the test
        final DmsOssOriginalDataDO result = dmsOssOriginalDataServiceImplUnderTest.getByVin("vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
