package com.jlr.ecp.subscription.service.remote.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.excel.pojo.remote.RemoteBatchRenewalExcel;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.listener.dto.TSDPSubscriptionDTO;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remote.RemoteSingleRenewalService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.util.RLockUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.AMAP_TASK_LINE_UP;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RemoteBatchRenewalServiceImplTest {

    @Mock
    private RemoteRenewBatchRecordsMapper mockRemoteRenewBatchRecordsMapper;
    @Mock
    private RemoteRenewDetailRecordsMapper mockRemoteRenewDetailRecordsMapper;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private FileService mockFileService;
    @Mock
    private Redisson mockRedisson;
    @Mock
    private ApplicationContext mockApplicationContext;
    @Mock
    private SubscriptionService mockSubscriptionService;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private RemoteCallService mockRemoteCallService;
    @Mock
    private RemoteSingleRenewalService mockRemoteSingleRenewalService;
    @Mock
    private OrderCheckService mockOrderCheckService;

    @Mock
    private RLock mockLock;

    @InjectMocks
    private RemoteBatchRenewalServiceImpl remoteBatchRenewalServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(remoteBatchRenewalServiceImplUnderTest, "renewalExcelUrl", "renewalExcelUrl");
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
    }

    @Test
    public void testGetRemoteTemplateUrl() {
        assertEquals("renewalExcelUrl", remoteBatchRenewalServiceImplUnderTest.getRemoteTemplateUrl());
    }

    @Test
    public void testQueryRemoteBatchRenewalPageList() {
        // Setup
        final RemoteBatchRenewalPageDTO pageDTO = new RemoteBatchRenewalPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<RemoteBatchRenewalPageVO> expectedResult = new PageResult<>(
                List.of(RemoteBatchRenewalPageVO.builder()
                        .operateTime("")
                        .operator("operator")
                        .checkResultDesc("校验通过")
                        .checkResultStatus(1)
                        .operateStatus(2)
                        .batchNo(0L)
                        .build()), 0L);
        Page<RemoteRenewBatchRecords> page = new Page<>();
        page.setRecords(List.of(RemoteRenewBatchRecords.builder()
                .batchNo(0L)
                .operator("operator")
                .dealStatus(2)
                .verifyResult(1)
                .build()));
        when(mockRemoteRenewBatchRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<RemoteBatchRenewalPageVO> result = remoteBatchRenewalServiceImplUnderTest.queryRemoteBatchRenewalPageList(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUploadRemoteExcelRenewal() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent());
        final CommonResult<RemoteBatchRenewalUploadVO> expectedResult = CommonResult.success(
                RemoteBatchRenewalUploadVO.builder()
                        .type("success")
                        .msg("请关注续费校验结果")
                        .build());
        when(mockFileService.createFile(any(), any(), any(byte[].class), eq("AWS_S3_FILE")))
                .thenReturn("uploadFile");
        when(mockSnowflake.nextId()).thenReturn(0L);

        // Run the test
        final CommonResult<RemoteBatchRenewalUploadVO> result = remoteBatchRenewalServiceImplUnderTest.uploadRemoteExcelRenewal(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockRemoteRenewBatchRecordsMapper).insert(any(RemoteRenewBatchRecords.class));
    }

    @Test
    public void testBatchSendRemoteRenewal() throws Exception {
        // Setup
        final RemoteBatchSendDTO remoteBatchSendDTO = new RemoteBatchSendDTO();
        remoteBatchSendDTO.setBatchNo("1");

        final CommonResult<String> expectedResult = CommonResult.success("请求已经发送，请前往续费记录查看结果");

        // 捕获传入 selectList 的参数
        ArgumentCaptor<LambdaQueryWrapper<RemoteRenewBatchRecords>> queryWrapperCaptor = ArgumentCaptor.forClass(LambdaQueryWrapper.class);

        AtomicInteger count = new AtomicInteger(0);
        // Configure AppDCuRenewBatchRecordsMapper.selectList(...).
        // 模拟 queryAppDUcBatchByDealStatus 返回空列表
        when(mockRemoteRenewBatchRecordsMapper.selectList(queryWrapperCaptor.capture())).thenAnswer(invocation -> {
            int increment = count.get();
            if (increment == 0) {
                count.getAndIncrement();
                return Collections.emptyList();
            } else  {
                return List.of(RemoteRenewBatchRecords.builder()
                        .id(0L)
                        .batchNo(0L)
                        .uploadFile("uploadFile")
                        .verifyResult(0)
                        .dealStatus(0)
                        .verifyResultFile("errorDetailPath")
                        .operator("operator")
                        .build());
            }
        });

        try (MockedStatic<RLockUtil> mockedStatic = mockStatic(RLockUtil.class)) {
            mockedStatic.when(() -> RLockUtil.tryLock(mockLock, 30, 60, TimeUnit.SECONDS)).thenReturn(true);

            // Configure ApplicationContext.getBean(...).
            final RemoteBatchRenewalServiceImpl remoteBatchRenewalService = new RemoteBatchRenewalServiceImpl();
            when(mockApplicationContext.getBean(RemoteBatchRenewalServiceImpl.class)).thenReturn(remoteBatchRenewalService);

            when(mockFileService.getFileContentByAllPath("AWS_S3_FILE", "uploadFile")).thenReturn(createExcelContent());

            // Run the test
            final CommonResult<String> result = remoteBatchRenewalServiceImplUnderTest.batchSendRemoteRenewal(
                    remoteBatchSendDTO);

            // Verify the results
            assertEquals(expectedResult, result);
            verify(mockRemoteRenewBatchRecordsMapper).updateById(any(RemoteRenewBatchRecords.class));
        }
    }

    @Test
    public void testDoReadRenewalExcel() {
        // Setup
        final List<List<RemoteBatchRenewalExcel>> expectedResult = List.of(List.of(RemoteBatchRenewalExcel.builder()
                .carVin("HGCM1234567890000")
                .renewalDate("2020/01/01")
                .build()));

        // Run the test
        final List<List<RemoteBatchRenewalExcel>> result = remoteBatchRenewalServiceImplUnderTest.doReadRenewalExcel(
                createExcelContent());

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchInsertRenewalRecord() {
        // Setup
        final List<RemoteBatchRenewalExcel> batchRenewalExcelList = List.of(RemoteBatchRenewalExcel.builder()
                .carVin("CARVIN")
                .renewalDate("renewalDate")
                .build());
        final RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
        resultDTO.setCarVin("CARVIN");
        resultDTO.setExistInEcp(true);
        resultDTO.setPiviModel(true);
        resultDTO.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setServiceDOList(List.of(SubscriptionServiceDO.builder().build()));
        final Map<String, RemoteSearchResultDTO> expectedResult = Map.ofEntries(Map.entry("CARVIN", resultDTO));

        // Configure SubscriptionService.getRemoteExpireDateByVinList(...).
        final RemoteSearchResultDTO resultDTO1 = new RemoteSearchResultDTO();
        resultDTO1.setCarVin("CARVIN");
        resultDTO1.setExistInEcp(true);
        resultDTO1.setPiviModel(true);
        resultDTO1.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO1.setServiceDOList(List.of(SubscriptionServiceDO.builder().build()));
        final List<RemoteSearchResultDTO> remoteSearchResultDTOS = List.of(resultDTO1);
        when(mockSubscriptionService.getRemoteExpireDateByVinList(List.of("CARVIN"))).thenReturn(remoteSearchResultDTOS);

        // Run the test
        final Map<String, RemoteSearchResultDTO> result = remoteBatchRenewalServiceImplUnderTest.batchInsertRenewalRecord(
                batchRenewalExcelList, 0L, "operator");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockRemoteRenewDetailRecordsMapper).insertBatch(anyList());
    }

    @Test
    public void testBatchSendRenewalAndUpdate() {
        // Setup
        final List<RemoteRenewDetailRecords> list = List.of(RemoteRenewDetailRecords.builder()
                .id(3L)
                .batchNo(0L)
                .carVin("carVin")
                .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyStatus(0)
                .errorDesc("desc")
                .dataSource(0)
                .operator("operator")
                .build(),
                RemoteRenewDetailRecords.builder()
                        .id(4L)
                        .batchNo(0L)
                        .carVin("carVin2")
                        .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .modifyStatus(0)
                        .errorDesc("desc")
                        .dataSource(0)
                        .operator("operator")
                        .build());
        final RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
        resultDTO.setCarVin("carVin");
        resultDTO.setExistInEcp(false);
        resultDTO.setPiviModel(false);
        resultDTO.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setServiceDOList(List.of(SubscriptionServiceDO.builder().build()));
        final RemoteSearchResultDTO resultDTO2 = new RemoteSearchResultDTO();
        resultDTO2.setCarVin("carVin2");
        resultDTO2.setExistInEcp(false);
        resultDTO2.setPiviModel(false);
        resultDTO2.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO2.setServiceDOList(List.of(SubscriptionServiceDO.builder().build()));
        final Map<String, RemoteSearchResultDTO> allMap = Map.ofEntries(Map.entry("carVin", resultDTO),Map.entry("carVin2", resultDTO2));

        try (MockedStatic<RLockUtil> mockedStatic = mockStatic(RLockUtil.class)) {
            mockedStatic.when(() -> RLockUtil.tryLock(mockLock, 30, 60, TimeUnit.SECONDS)).thenReturn(true);

            // Configure RemoteRenewDetailRecordsMapper.getProcessRecordsByVinSetAndBatchNo(...).
            final List<RemoteRenewDetailRecords> records = List.of(RemoteRenewDetailRecords.builder()
                    .id(1L)
                    .batchNo(0L)
                    .carVin("carVin")
                    .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                    .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                    .modifyStatus(0)
                    .errorDesc("desc")
                    .dataSource(0)
                    .operator("operator")
                    .build());
            when(mockRemoteRenewDetailRecordsMapper.getProcessRecordsByVinSetAndBatchNo(anySet(), eq(0L)))
                    .thenReturn(records);

            when(mockOrderCheckService.checkOrderInTransitByVinList(anyList(), eq(ServiceTypeEnum.REMOTE)))
                    .thenReturn(CommonResult.success(List.of("carVin")));

            // Configure RemoteSingleRenewalService.buildCallApiParam(...).
            final OrdFufilmentBusiDTO ordFufilmentBusiDTO = new OrdFufilmentBusiDTO();
            ordFufilmentBusiDTO.setVin("carVin");
            ordFufilmentBusiDTO.setUser("user");
            ordFufilmentBusiDTO.setTransactionId("transactionId");
            final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
            tsdpSubscriptionDTO.setExpiryDate("expiryDate");
            ordFufilmentBusiDTO.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));
            when(mockRemoteSingleRenewalService.buildCallApiParam(any(), any(RemoteRenewDetailRecords.class)
            )).thenReturn(ordFufilmentBusiDTO);

            // Configure RemoteCallService.concurrentCallTSDPRenew(...).
            final RemoteModifyRespDTO remoteModifyRespDTO = new RemoteModifyRespDTO();
            remoteModifyRespDTO.setSuccess(false);
            remoteModifyRespDTO.setErrorMsg("errorMsg");
            final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
            param.setVin("carVin");
            param.setUser("user");
            param.setTransactionId("transactionId");
            final TSDPSubscriptionDTO tsdpSubscriptionDTO1 = new TSDPSubscriptionDTO();
            tsdpSubscriptionDTO1.setExpiryDate("expiryDate");
            param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO1));
            when(mockRemoteCallService.concurrentCallTSDPRenew(param)).thenReturn(remoteModifyRespDTO);

            // Run the test
            remoteBatchRenewalServiceImplUnderTest.batchSendRenewalAndUpdate(list, allMap);

            // Verify the results
            verify(mockRemoteRenewDetailRecordsMapper, times(2)).updateBatch(anyList());

            // Confirm RemoteSingleRenewalService.updateRemoteRenewRecords(...).
            verify(mockRemoteSingleRenewalService).updateRemoteRenewRecords(any(RemoteRenewDetailRecords.class), any(RemoteModifyRespDTO.class));
        }
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");
        Cell durationHeader = headerRow.createCell(1);
        durationHeader.setCellValue("远程车控服务续期日期(yyyy/mm/dd)");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM1234567890000"); // 示例 VIN
        dataRow.createCell(1).setCellValue("2020/01/01"); // 示例续费时长

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }
}
