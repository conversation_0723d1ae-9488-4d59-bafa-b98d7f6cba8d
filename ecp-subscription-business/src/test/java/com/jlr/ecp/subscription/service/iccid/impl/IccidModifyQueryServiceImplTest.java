package com.jlr.ecp.subscription.service.iccid.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidModifyQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidModifyQueryPageVO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IccidModifyQueryServiceImplTest {

    @Mock
    private IccidModifyRecordsDOMapper mockIccidModifyRecordsDOMapper;
    @Mock
    private IccidModifyBatchRecordsDOMapper mockIccidModifyBatchRecordsDOMapper;

    @InjectMocks
    private IccidModifyQueryServiceImpl iccidModifyQueryServiceImplUnderTest;

    @Test
    public void testQueryModifyResultPageList() {
        // Setup
        final IccidModifyQueryPageDTO pageDTO = new IccidModifyQueryPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(10); // 设置一个合理的分页大小
        pageDTO.setCarVin("carVin");
        pageDTO.setBatchNoList(List.of("1"));
        pageDTO.setModifyStatus(0);
        pageDTO.setOperateStartTime("operateStartTime");
        pageDTO.setOperateEndTime("operateEndTime");
        pageDTO.setOperator("operator");
        pageDTO.setOperateTimeSort("operateTimeSort");

        // 模拟 IccidModifyRecordsDO 数据
        final List<IccidModifyRecordsDO> iccidModifyRecordsDOS = List.of(
                IccidModifyRecordsDO.builder()
                        .id(1L)
                        .modifyNo(1L)
                        .carVin("carVin")
                        .modifyBeforeIccid("modifyBeforeIccid")
                        .modifyAfterIccid("modifyAfterIccid")
                        .modifyStatus(0)
                        .errorDesc("ecpDisplay")
                        .build()
        );

        // 模拟分页结果
        final Page<IccidModifyRecordsDO> page = new Page<>(0, 10, 1);
        page.setRecords(iccidModifyRecordsDOS);

        // 模拟 mockIccidModifyRecordsDOMapper.selectPage 返回非空的 Page 对象
        when(mockIccidModifyRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // 预期结果
        final PageResult<IccidModifyQueryPageVO> expectedResult = new PageResult<>(
                List.of(
                        IccidModifyQueryPageVO.builder()
                                .operateTime("operateTime") // 这里需要根据实际的日期格式化进行调整
                                .operator("operator")
                                .batchNo("1")
                                .carVin("carVin")
                                .modifyBeforeIccid("modifyBeforeIccid")
                                .modifyAfterIccid("modifyAfterIccid")
                                .modifyStatus(0)
                                .modifyStatusDesc("modifyStatusDesc") // 这里需要根据实际的枚举值进行调整
                                .errorDesc("ecpDisplay")
                                .build()
                ), 1L);

        // Run the test
        final PageResult<IccidModifyQueryPageVO> result = iccidModifyQueryServiceImplUnderTest.queryModifyResultPageList(pageDTO);

        // Verify the results
        assertEquals(expectedResult.getTotal(), result.getTotal());
        assertEquals(expectedResult.getList().size(), result.getList().size());
        assertEquals(expectedResult.getList().get(0).getCarVin(), result.getList().get(0).getCarVin());
        assertEquals(expectedResult.getList().get(0).getModifyBeforeIccid(), result.getList().get(0).getModifyBeforeIccid());
        assertEquals(expectedResult.getList().get(0).getModifyAfterIccid(), result.getList().get(0).getModifyAfterIccid());
        assertEquals(expectedResult.getList().get(0).getModifyStatus(), result.getList().get(0).getModifyStatus());
    }


    @Test
    public void testQueryModifyBatchNo() {
        // Setup
        // Configure IccidModifyBatchRecordsDOMapper.selectList(...).
        final List<IccidModifyBatchRecordsDO> iccidModifyBatchRecordsDOS = List.of(IccidModifyBatchRecordsDO.builder()
                .batchNo(1L)
                .build());
        when(mockIccidModifyBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(iccidModifyBatchRecordsDOS);

        // Run the test
        final String result = iccidModifyQueryServiceImplUnderTest.queryModifyBatchNo("1");

        // Verify the results
        assertEquals("1", result);
    }

    @Test
    public void testQueryModifyBatchNo_IccidModifyBatchRecordsDOMapperReturnsNoItems() {
        // Setup
        when(mockIccidModifyBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure IccidModifyRecordsDOMapper.selectList(...).
        final List<IccidModifyRecordsDO> iccidModifyRecordsDOS = List.of(IccidModifyRecordsDO.builder()
                .id(0L)
                .modifyNo(0L)
                .carVin("carVin")
                .modifyBeforeIccid("modifyBeforeIccid")
                .modifyAfterIccid("modifyAfterIccid")
                .modifyStatus(0)
                .errorDesc("ecpDisplay")
                .build());
        when(mockIccidModifyRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(iccidModifyRecordsDOS);

        // Run the test
        final String result = iccidModifyQueryServiceImplUnderTest.queryModifyBatchNo("1");

        // Verify the results
        assertEquals("0", result);
    }
}
