package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalOperateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPSingleRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalYearVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPSingleRenewalPageVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.model.dto.*;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AmaPSingleManualRenewalServiceImplTest {

    @Mock
    private AmaPRenewRecordsDOMapper mockAmapRenewRecordsDOMapper;
    @Mock
    private AmaPProperties mockAmaPProperties;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private PIVIAmaPService mockPiviAmaPService;
    @Mock
    private ThreadPoolTaskScheduler mockSubscribeScheduledThreadPool;
    @Mock
    private ManualModifyLogDOService mockManualModifyLogDOService;

    @InjectMocks
    private AmaPSingleManualRenewalServiceImpl amaPSingleManualRenewalServiceImplUnderTest;

    @Mock
    private OrderCheckService mockOrderCheckService;

    @Test
    public void testQueryRenewalPageList() {
        // Setup
        final AmaPSingleRenewalPageDTO amaPPageDto = new AmaPSingleRenewalPageDTO();
        amaPPageDto.setPageNo(1);
        amaPPageDto.setPageSize(10);
        amaPPageDto.setOperateTimeSort("DESC");

        final PageResult<AmaPSingleRenewalPageVO> expectedResult = new PageResult<>(
                List.of(AmaPSingleRenewalPageVO.builder()
                        .operateTime("2020/01/01 00:00:00")
                        .vin("carVin")
                        .operateUser("operateUser")
                        .renewNo(0L)
                        .build()), 1L);
        Page<AmaPRenewRecordsDO> page = new Page<>();
        AmaPRenewRecordsDO build = AmaPRenewRecordsDO.builder()
                .carVin("carVin")
                .operator("operateUser")
                .renewNo(0L).build();
        build.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        page.setRecords(List.of(build));
        page.setTotal(1L);
        when(mockAmapRenewRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<AmaPSingleRenewalPageVO> result = amaPSingleManualRenewalServiceImplUnderTest.queryRenewalPageList(
                amaPPageDto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetAmaPRenewalYear() {
        // Setup
        final List<AmaPRenewalYearVO> expectedResult = List.of(AmaPRenewalYearVO.builder()
                        .code(1)
                        .desc("一年")
                        .build(),
                AmaPRenewalYearVO.builder()
                        .code(3)
                        .desc("三年")
                        .build());

        // Run the test
        final List<AmaPRenewalYearVO> result = amaPSingleManualRenewalServiceImplUnderTest.getAmaPRenewalYear();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAmaPSingleManualRenewal() {
        // Setup
        final AmaPSingleRenewalOperateDTO operateDTO = new AmaPSingleRenewalOperateDTO();
        operateDTO.setCarVin("Q1234567890123456");
        operateDTO.setRenewYearCode(1);

        final CommonResult<String> expectedResult = CommonResult.success("请求已发送，请前往续费记录查看结果");
        when(mockSnowflake.nextIdStr()).thenReturn("cusOrderId");
        when(mockAmaPProperties.getOneYearCid()).thenReturn("cid");
        when(mockAmaPProperties.getPid()).thenReturn("pid");

        // Configure PIVIAmaPService.getCarAmaPInfo(...).
        final AmaPCarInfoResponse amaPCarInfoResponse = new AmaPCarInfoResponse();
        amaPCarInfoResponse.setCode("code");
        amaPCarInfoResponse.setErrDetail("errDetail");
        amaPCarInfoResponse.setMessage("message");
        amaPCarInfoResponse.setResult(false);
        amaPCarInfoResponse.setData(AmaPCarData.builder()
                .permissions(List.of(AmaPPermissionInfo.builder()
                        .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .build()))
                .build());
        when(mockPiviAmaPService.getCarAmaPInfo("pid", "Q1234567890123456")).thenReturn(amaPCarInfoResponse);

        when(mockSnowflake.nextId()).thenReturn(0L);

        // Configure PIVIAmaPService.chargeAmaPOrder(...).
        final AmaPOrderChargeResponseDTO amaPOrderChargeResponseDTO = new AmaPOrderChargeResponseDTO();
        amaPOrderChargeResponseDTO.setVersion("version");
        amaPOrderChargeResponseDTO.setCode("1");
        amaPOrderChargeResponseDTO.setMessage("errorDesc");
        amaPOrderChargeResponseDTO.setErrDetail("errDetail");
        final OrderChargeResponseData orderChargeResponseData = new OrderChargeResponseData();
        amaPOrderChargeResponseDTO.setData(List.of(orderChargeResponseData));

        // Configure PIVIAmaPService.queryAmaPChargeInfo(...).
        final AmaPChargeSearchResponseDTO amaPChargeSearchResponseDTO = new AmaPChargeSearchResponseDTO();
        amaPChargeSearchResponseDTO.setCode("1");
        amaPChargeSearchResponseDTO.setMessage("errorDesc");
        final AmaPChargeSearchDataDTO data = new AmaPChargeSearchDataDTO();
        final AmaPChargeSearchRecordDTO amaPChargeSearchRecordDTO = new AmaPChargeSearchRecordDTO();
        amaPChargeSearchRecordDTO.setEndTime("endTime");
        data.setChargeRecords(List.of(amaPChargeSearchRecordDTO));
        amaPChargeSearchResponseDTO.setData(data);

        when(mockAmapRenewRecordsDOMapper.getProcessRecordByVin(operateDTO.getCarVin())).thenReturn(Collections.emptyList());
        when(mockOrderCheckService.checkOrderInTransit(operateDTO.getCarVin(), ServiceTypeEnum.PIVI)).thenReturn(CommonResult.success(""));
        when(mockPiviAmaPService.concurrentCallAMapRenew(any())).thenReturn(CommonResult.success(new AmaPOrderChargeResponseDTO()));

        // Run the test
        final CommonResult<String> result = amaPSingleManualRenewalServiceImplUnderTest.amaPSingleManualRenewal(
                operateDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAmapRenewRecordsDOMapper).insert(any());
        verify(mockAmapRenewRecordsDOMapper).updateById(any());

        verify(mockAmapRenewRecordsDOMapper).updateById((any()));
    }

    @Test
    public void testAddSingleAmaPRenewRecordsDO() {
        // Setup
        final AmaPSingleRenewalOperateDTO operateDTO = new AmaPSingleRenewalOperateDTO();
        operateDTO.setCarVin("CARVIN");
        operateDTO.setRenewYearCode(1);

        final AmaPRenewRecordsDO expectedResult = AmaPRenewRecordsDO.builder()
                .carVin("CARVIN")
                .cusOrderId("cusOrderId")
                .renewYear(1)
                .renewNo(0L)
                .operator("system")
                .dataSource(1)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockAmaPProperties.getPid()).thenReturn("pid");

        // Configure PIVIAmaPService.getCarAmaPInfo(...).
        final AmaPCarInfoResponse amaPCarInfoResponse = new AmaPCarInfoResponse();
        amaPCarInfoResponse.setCode("code");
        amaPCarInfoResponse.setErrDetail("errDetail");
        amaPCarInfoResponse.setMessage("message");
        amaPCarInfoResponse.setResult(false);
        amaPCarInfoResponse.setData(AmaPCarData.builder()
                .permissions(List.of(AmaPPermissionInfo.builder()
                        .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .build()))
                .build());
        when(mockPiviAmaPService.getCarAmaPInfo("pid", "CARVIN")).thenReturn(amaPCarInfoResponse);

        when(mockSnowflake.nextId()).thenReturn(0L);

        // Run the test
        final AmaPRenewRecordsDO result = amaPSingleManualRenewalServiceImplUnderTest.addSingleAmaPRenewRecordsDO(
                operateDTO, "cusOrderId");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAmapRenewRecordsDOMapper).insert(AmaPRenewRecordsDO.builder()
                .carVin("CARVIN")
                .cusOrderId("cusOrderId")
                .renewYear(1)
                .renewNo(0L)
                .operator("system")
                .dataSource(1)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
    }
}
