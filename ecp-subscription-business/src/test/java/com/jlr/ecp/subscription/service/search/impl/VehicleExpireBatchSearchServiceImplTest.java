package com.jlr.ecp.subscription.service.search.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.dto.Vehicle5000InfoDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ExpireResultQueryDTO;
import com.jlr.ecp.subscription.controller.admin.dto.search.ServiceBatchQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPQueryStatusVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.SearchExpireUploadVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceBatchQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.search.ServiceExpireQueryResultVO;
import com.jlr.ecp.subscription.dal.dataobject.search.ExpireSearchBatchRecordDO;
import com.jlr.ecp.subscription.dal.dataobject.search.ExpireSearchDetailRecordDO;
import com.jlr.ecp.subscription.dal.mysql.search.ExpireSearchBatchRecordDOMapper;
import com.jlr.ecp.subscription.dal.mysql.search.ExpireSearchDetailRecordDOMapper;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.system.api.permission.PermissionApi;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class VehicleExpireBatchSearchServiceImplTest {

    @Mock
    private ExpireSearchBatchRecordDOMapper mockServiceBatchRecordDOMapper;
    @Mock
    private ExpireSearchDetailRecordDOMapper mockServiceDetailRecordDOMapper;
    @Mock
    private SubscriptionService mockSubscriptionService;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private PermissionApi mockPermissionApi;

    @InjectMocks
    private VehicleExpireBatchSearchServiceImpl vehicleExpireBatchSearchServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(vehicleExpireBatchSearchServiceImplUnderTest, "templateUrl", "templateUrl");
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), ExpireSearchDetailRecordDO.class);
    }

    @Test
    public void testGetVehicleExpireTemplateUrl() {
        assertEquals("templateUrl", vehicleExpireBatchSearchServiceImplUnderTest.getVehicleExpireTemplateUrl());
    }

    @Test
    public void testQueryBatchServicePageList() {
        // Setup
        final ServiceBatchQueryPageDTO queryPageDTO = new ServiceBatchQueryPageDTO();
        queryPageDTO.setPageNo(0);
        queryPageDTO.setPageSize(0);
        queryPageDTO.setOperateTimeSort("asc");

        final PageResult<ServiceBatchQueryPageVO> expectedResult = new PageResult<>(
                List.of(ServiceBatchQueryPageVO.builder()
                        .operateTime("")
                        .batchNo("1")
                        .build()), 0L);
        Page<ExpireSearchBatchRecordDO> page = new Page<>();
        page.setRecords(List.of(ExpireSearchBatchRecordDO.builder()
                        .batchNo("1")
                        .dealStatus(1)
                .build()));
        when(mockServiceBatchRecordDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<ServiceBatchQueryPageVO> result = vehicleExpireBatchSearchServiceImplUnderTest.queryBatchServicePageList(
                queryPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchSearchExpire() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent());
        final CommonResult<SearchExpireUploadVO> expectedResult = CommonResult.success(SearchExpireUploadVO.builder()
                .type("success")
                .msg("上传成功，请关注批量查询结果")
                .build());

        // Configure ExpireSearchBatchRecordDOMapper.selectList(...).
        when(mockServiceBatchRecordDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(mockSnowflake.nextIdStr()).thenReturn("batchNo");

        // Configure ExpireSearchDetailRecordDOMapper.selectList(...).
        final List<ExpireSearchDetailRecordDO> expireSearchDetailRecordDOS = List.of(
                ExpireSearchDetailRecordDO.builder()
                        .id(0L)
                        .carVin("HGCM1234567890000")
                        .batchNo("batchNo")
                        .queryStatus(0)
                        .brandName("brandName")
                        .configName("configName")
                        .modelYear("modelYear")
                        .configCode("configCode")
                        .seriesCode("seriesCode")
                        .brandCode("brandCode")
                        .seriesName("seriesName")
                        .carSystemModel("carSystemModel")
                        .hasInfoEntertain("hasInfoEntertain")
                        .remoteServiceDate("remoteServiceDate")
                        .piviServiceDate("piviServiceDate")
                        .appDServiceDate("appDServiceDate")
                        .amaPServiceDate("amaPServiceDate")
                        .unicomServiceDate("unicomServiceDate")
                        .invoiceServiceDate("invoiceServiceDate")
                        .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .operator("operator")
                        .errorReason("errorReason")
                        .build());
        when(mockServiceDetailRecordDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(expireSearchDetailRecordDOS);

        // Configure SubscriptionService.getExpireDateByVin(...).
        final SubscriptionSearchResultDTO subscriptionSearchResultDTO = new SubscriptionSearchResultDTO();
        subscriptionSearchResultDTO.setServiceList(List.of(ServiceExpireInfoVO.builder()
                .serviceName("serviceName")
                .expireDate("expireDate")
                .build()));
        final Vehicle5000InfoDTO vehicle5000Info = new Vehicle5000InfoDTO();
        vehicle5000Info.setBrandName("brandName");
        vehicle5000Info.setConfigName("configName");
        vehicle5000Info.setModelYear("modelYear");
        vehicle5000Info.setConfigCode("configCode");
        vehicle5000Info.setSeriesCode("seriesCode");
        vehicle5000Info.setBrandCode("brandCode");
        vehicle5000Info.setSeriesName("seriesName");
        vehicle5000Info.setCarSystemModel("carSystemModel");
        vehicle5000Info.setHasInfoEntertain("hasInfoEntertain");
        subscriptionSearchResultDTO.setVehicle5000Info(vehicle5000Info);
        when(mockSubscriptionService.getExpireDateByVin("HGCM1234567890000")).thenReturn(subscriptionSearchResultDTO);

        // Run the test
        final CommonResult<SearchExpireUploadVO> result = vehicleExpireBatchSearchServiceImplUnderTest.batchSearchExpire(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockServiceBatchRecordDOMapper).insert(any(ExpireSearchBatchRecordDO.class));
        verify(mockServiceDetailRecordDOMapper).insertBatch(anyList());
    }

    @Test
    public void testBatchSearchExpire_ExpireSearchDetailRecordDOMapperSelectListReturnsNoItems() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent());
        final CommonResult<SearchExpireUploadVO> expectedResult = CommonResult.success(SearchExpireUploadVO.builder()
                .type("success")
                .msg("上传成功，请关注批量查询结果")
                .build());

        // Configure ExpireSearchBatchRecordDOMapper.selectList(...).
        when(mockServiceBatchRecordDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(mockSnowflake.nextIdStr()).thenReturn("batchNo");
        when(mockServiceDetailRecordDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<SearchExpireUploadVO> result = vehicleExpireBatchSearchServiceImplUnderTest.batchSearchExpire(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockServiceBatchRecordDOMapper).insert(any(ExpireSearchBatchRecordDO.class));
        verify(mockServiceDetailRecordDOMapper).insertBatch(anyList());
    }

    @Test
    public void testGetExpireQueryBatchNo() {
        // Setup
        // Configure ExpireSearchBatchRecordDOMapper.selectOne(...).
        final ExpireSearchBatchRecordDO expireSearchBatchRecordDO = ExpireSearchBatchRecordDO.builder()
                .id(0L)
                .batchNo("batchNo")
                .verifyResult(0)
                .verifyReason("errorDetail")
                .dealStatus(0)
                .operator("operator")
                .build();
        when(mockServiceBatchRecordDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(expireSearchBatchRecordDO);

        // Run the test
        final String result = vehicleExpireBatchSearchServiceImplUnderTest.getExpireQueryBatchNo("batchNo");

        // Verify the results
        assertEquals("batchNo", result);
    }

    @Test
    public void testGetExpireQueryStatus() {
        // Setup
        final List<AmaPQueryStatusVO> expectedResult = List.of(AmaPQueryStatusVO.builder()
                .queryStatusCode(0)
                .queryStatusDesc("查询中")
                .build(),
                AmaPQueryStatusVO.builder()
                        .queryStatusCode(1)
                        .queryStatusDesc("查询完成")
                        .build(),
                AmaPQueryStatusVO.builder()
                        .queryStatusCode(2)
                        .queryStatusDesc("查询失败")
                        .build());

        // Run the test
        final List<AmaPQueryStatusVO> result = vehicleExpireBatchSearchServiceImplUnderTest.getExpireQueryStatus();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testExpireBatchQueryResult() {
        // Setup
        final ExpireResultQueryDTO expireResultQueryDTO = new ExpireResultQueryDTO();
        expireResultQueryDTO.setPageNo(0);
        expireResultQueryDTO.setPageSize(0);
        expireResultQueryDTO.setCarVin("carVin");
        expireResultQueryDTO.setBatchNoList(List.of("value"));
        expireResultQueryDTO.setStartTime("startTime");
        expireResultQueryDTO.setEndTime("endTime");
        expireResultQueryDTO.setQueryStatus(0);
        expireResultQueryDTO.setOperator("operator");
        expireResultQueryDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<ServiceExpireQueryResultVO> expectedResult = new PageResult<>(
                List.of(ServiceExpireQueryResultVO.builder()
                        .batchNo("batchNo")
                        .queryStatus("")
                        .carVin("HGCM1234567890000")
                        .brandName("brandName")
                        .seriesName("seriesName")
                        .seriesCode("seriesCode")
                        .carSystemModel("否")
                        .hasInfoEntertain("-")
                        .operateTime("")
                        .build()), 0L);
        Page<ExpireSearchDetailRecordDO> page = new Page<>();
        page.setRecords(List.of(ExpireSearchDetailRecordDO.builder()
                .batchNo("batchNo")
                .brandName("brandName")
                .seriesName("seriesName")
                .seriesCode("seriesCode")
                .carVin("HGCM1234567890000")
                .build()));
        when(mockServiceDetailRecordDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<ServiceExpireQueryResultVO> result = vehicleExpireBatchSearchServiceImplUnderTest.expireBatchQueryResult(
                expireResultQueryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBuildExpireDetailRecordsDO() {
        // Setup
        final ExpireSearchDetailRecordDO expectedResult = ExpireSearchDetailRecordDO.builder()
                .batchNo("batchNo")
                .build();

        // Run the test
        final ExpireSearchDetailRecordDO result = vehicleExpireBatchSearchServiceImplUnderTest.buildExpireDetailRecordsDO(
                null, "batchNo");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM1234567890000"); // 示例 VIN

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }
}
