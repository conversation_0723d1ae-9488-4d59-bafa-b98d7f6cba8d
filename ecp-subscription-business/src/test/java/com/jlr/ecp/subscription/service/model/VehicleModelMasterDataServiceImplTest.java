package com.jlr.ecp.subscription.service.model;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.model.dto.ModelYearDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataCreateDTO;
import com.jlr.ecp.subscription.api.model.dto.VehicleModelMasterDataUpdateDTO;
import com.jlr.ecp.subscription.api.model.vo.*;
import com.jlr.ecp.subscription.config.DPService;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.dal.dataobject.model.VehicleModelMasterDataDO;
import com.jlr.ecp.subscription.dal.mysql.model.VehicleModelMasterDataMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.RemotePackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.env.MockEnvironment;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VehicleModelMasterDataServiceImplTest {

    @Mock
    private VehicleModelMasterDataMapper mockVehicleModelMasterDataMapper;
    @Mock
    private DPService mockDpService;
    @Mock
    private RedisService mockRedisService;

    @InjectMocks
    private VehicleModelMasterDataServiceImpl vehicleModelMasterDataServiceImplUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(vehicleModelMasterDataServiceImplUnderTest, "environment", new MockEnvironment());
        vehicleModelMasterDataServiceImplUnderTest.redisService = mockRedisService;
    }

    @Test
    public void testCreateVehicleModelMasterData_ThrowsServiceException() {
        // Setup
        final VehicleModelMasterDataCreateDTO createDTO = new VehicleModelMasterDataCreateDTO();
        createDTO.setCarSystemModel("carSystemModel");
        createDTO.setSeriesCode("seriesCode");
        final ModelYearDTO modelYearDTO = new ModelYearDTO();
        modelYearDTO.setModelYear("modelYear");
        modelYearDTO.setId(0L);
        modelYearDTO.setRevision(0);
        createDTO.setModelYearList(List.of(modelYearDTO));

        // Configure VehicleModelMasterDataMapper.selectOne(...).
        final VehicleModelMasterDataDO vehicleModelMasterDataDO = VehicleModelMasterDataDO.builder()
                .tenantId(0)
                .id(0L)
                .carSystemModel("carSystemModel")
                .seriesCode("seriesCode")
                .modelYear("modelYear")
                .build();
        when(mockVehicleModelMasterDataMapper.selectOne(any(LambdaQueryWrapperX.class)))
                .thenReturn(vehicleModelMasterDataDO);

        // Run the test
        assertThatThrownBy(
                () -> vehicleModelMasterDataServiceImplUnderTest.createVehicleModelMasterData(createDTO))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    public void testCreateVehicleModelMasterData_VehicleModelMasterDataMapperSelectOneReturnsNull() {
        // Setup
        final VehicleModelMasterDataCreateDTO createDTO = new VehicleModelMasterDataCreateDTO();
        createDTO.setCarSystemModel("carSystemModel");
        createDTO.setSeriesCode("seriesCode");
        final ModelYearDTO modelYearDTO = new ModelYearDTO();
        modelYearDTO.setModelYear("modelYear");
        modelYearDTO.setId(0L);
        modelYearDTO.setRevision(0);
        createDTO.setModelYearList(List.of(modelYearDTO));

        when(mockVehicleModelMasterDataMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final Boolean result = vehicleModelMasterDataServiceImplUnderTest.createVehicleModelMasterData(createDTO);

        // Verify the results
        assertThat(result).isTrue();

    }

    @Test
    public void testEditVehicleModelMasterData_VehicleModelMasterDataMapperSelectOneReturnsNull() {
        // Setup
        final VehicleModelMasterDataUpdateDTO updateDTO = new VehicleModelMasterDataUpdateDTO();
        updateDTO.setCarSystemModel("carSystemModel");
        updateDTO.setSeriesCode("seriesCode");
        final ModelYearDTO modelYearDTO = new ModelYearDTO();
        modelYearDTO.setModelYear("modelYear");
        modelYearDTO.setId(0L);
        modelYearDTO.setRevision(0);
        updateDTO.setModelYearList(List.of(modelYearDTO));
        updateDTO.setNewSeriesCode("seriesCode");


        // Configure VehicleModelMasterDataMapper.selectList(...).
        VehicleModelMasterDataDO build = VehicleModelMasterDataDO.builder()
                .tenantId(0)
                .id(0L)
                .carSystemModel("carSystemModel")
                .seriesCode("seriesCode")
                .modelYear("modelYear")
                .build();
        build.setRevision(0);
        final List<VehicleModelMasterDataDO> vehicleModelMasterDataDOS = List.of(build);
        when(mockVehicleModelMasterDataMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vehicleModelMasterDataDOS);

        // Run the test
        final Boolean result = vehicleModelMasterDataServiceImplUnderTest.editVehicleModelMasterData(updateDTO);

        // Verify the results
        assertThat(result).isTrue();

    }

    @Test
    public void testGetOneBySeriesCode() {
        // Setup
        final VehicleModelMasterDataVO expectedResult = new VehicleModelMasterDataVO();
        expectedResult.setCarSystemModel("carSystemModel");
        expectedResult.setSeriesCode("seriesCode");
        final ModelYearDTO modelYearDTO = new ModelYearDTO();
        modelYearDTO.setModelYear("modelYear");
        modelYearDTO.setId(0L);
        modelYearDTO.setRevision(0);
        expectedResult.setModelYearList(List.of(modelYearDTO));

        // Configure VehicleModelMasterDataMapper.selectList(...).
        final List<VehicleModelMasterDataDO> vehicleModelMasterDataDOS = List.of(VehicleModelMasterDataDO.builder()
                .tenantId(0)
                .id(0L)
                .carSystemModel("carSystemModel")
                .seriesCode("seriesCode")
                .modelYear("modelYear")
                .build());
        when(mockVehicleModelMasterDataMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vehicleModelMasterDataDOS);

        // Run the test
        final VehicleModelMasterDataVO result = vehicleModelMasterDataServiceImplUnderTest.getOneBySeriesCode(
                "seriesCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOneBySeriesCode_VehicleModelMasterDataMapperReturnsNoItems() {
        // Setup
        final VehicleModelMasterDataVO expectedResult = new VehicleModelMasterDataVO();
        expectedResult.setCarSystemModel("carSystemModel");
        expectedResult.setSeriesCode("seriesCode");
        final ModelYearDTO modelYearDTO = new ModelYearDTO();
        modelYearDTO.setModelYear("modelYear");
        modelYearDTO.setId(0L);
        modelYearDTO.setRevision(0);
        expectedResult.setModelYearList(List.of(modelYearDTO));

        when(mockVehicleModelMasterDataMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final VehicleModelMasterDataVO result = vehicleModelMasterDataServiceImplUnderTest.getOneBySeriesCode(
                "seriesCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetPage() {
        // Setup
        final PageParam param = new PageParam();
        param.setPageNo(0);
        param.setPageSize(0);

        final VehicleModelMasterDataPageVO vehicleModelMasterDataPageVO = new VehicleModelMasterDataPageVO();
        vehicleModelMasterDataPageVO.setCarSystemModel("carSystemModel");
        vehicleModelMasterDataPageVO.setSeriesCode("seriesCode");
        vehicleModelMasterDataPageVO.setModelYearList(List.of("value"));
        vehicleModelMasterDataPageVO.setModelYears("modelYears");
        final PageResult<VehicleModelMasterDataPageVO> expectedResult = new PageResult<>(
                List.of(vehicleModelMasterDataPageVO), 0L);
        when(mockVehicleModelMasterDataMapper.getPage(any(Page.class))).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Run the test
        final PageResult<VehicleModelMasterDataPageVO> result = vehicleModelMasterDataServiceImplUnderTest.getPage(
                param);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testDeleteBySeriesCode() {
        // Setup
        when(mockVehicleModelMasterDataMapper.delete(any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = vehicleModelMasterDataServiceImplUnderTest.deleteBySeriesCode("seriesCode");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testFindDpList() {
        // Setup
        final UserDPResultVO userDPResultVO = new UserDPResultVO();
        userDPResultVO.setVin("vin");
        userDPResultVO.setBrandCode("brandCode");
        userDPResultVO.setModelYear("modelYear");
        userDPResultVO.setSeriesCode("seriesCode");
        userDPResultVO.setCarSystemModel("carSystemModel");
        final List<UserDPResultVO> expectedResult = List.of(userDPResultVO);

        // Configure DPService.callApi(...).
        final DPResultVO dpResultVO = new DPResultVO();
        dpResultVO.setVin("vin");
        dpResultVO.setBrandCode("brandCode");
        dpResultVO.setBrandName("brandName");
        dpResultVO.setModelYear("modelYear");
        dpResultVO.setSeriesCode("seriesCode");
        final List<DPResultVO> dpResultVOS = List.of(dpResultVO);
        DPResponseVO dpResponseVO = new DPResponseVO();
        dpResponseVO.setResult(dpResultVOS);
        when(mockDpService.callApi(List.of("value"))).thenReturn(dpResponseVO);

        // Configure RedisService.getCacheMap(...).
        final DPResultVO dpResultVO1 = new DPResultVO();
        dpResultVO1.setVin("vin");
        dpResultVO1.setBrandCode("brandCode");
        dpResultVO1.setBrandName("brandName");
        dpResultVO1.setModelYear("modelYear");
        dpResultVO1.setSeriesCode("seriesCode");
        final Map<String, Object> stringDPResultVOMap = Map.ofEntries(Map.entry("value", dpResultVO1));

        // Configure VehicleModelMasterDataMapper.selectList(...).
        final List<VehicleModelMasterDataDO> vehicleModelMasterDataDOS = List.of(VehicleModelMasterDataDO.builder()
                .tenantId(0)
                .id(0L)
                .carSystemModel("carSystemModel")
                .seriesCode("seriesCode")
                .modelYear("modelYear")
                .build());
        when(mockVehicleModelMasterDataMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vehicleModelMasterDataDOS);

        // Run the test
        final List<UserDPResultVO> result = vehicleModelMasterDataServiceImplUnderTest.findDpList(List.of("value"));

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testFindDpList_VehicleModelMasterDataMapperReturnsNoItems() {
        // Setup
        final UserDPResultVO userDPResultVO = new UserDPResultVO();
        userDPResultVO.setVin("vin");
        userDPResultVO.setBrandCode("brandCode");
        userDPResultVO.setModelYear("modelYear");
        userDPResultVO.setSeriesCode("seriesCode");
        userDPResultVO.setCarSystemModel("carSystemModel");
        final List<UserDPResultVO> expectedResult = List.of(userDPResultVO);

        // Configure DPService.callApi(...).
        final DPResultVO dpResultVO = new DPResultVO();
        dpResultVO.setVin("vin");
        dpResultVO.setBrandCode("brandCode");
        dpResultVO.setBrandName("brandName");
        dpResultVO.setModelYear("modelYear");
        dpResultVO.setSeriesCode("seriesCode");
        final List<DPResultVO> dpResultVOS = List.of(dpResultVO);
        DPResponseVO dpResponseVO = new DPResponseVO();
        dpResponseVO.setResult(dpResultVOS);
        when(mockDpService.callApi(List.of("value"))).thenReturn(dpResponseVO);

        // Configure RedisService.getCacheMap(...).
        final DPResultVO dpResultVO1 = new DPResultVO();
        dpResultVO1.setVin("vin");
        dpResultVO1.setBrandCode("brandCode");
        dpResultVO1.setBrandName("brandName");
        dpResultVO1.setModelYear("modelYear");
        dpResultVO1.setSeriesCode("seriesCode");
        final Map<String, Object> stringDPResultVOMap = Map.ofEntries(Map.entry("value", dpResultVO1));

        when(mockVehicleModelMasterDataMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<UserDPResultVO> result = vehicleModelMasterDataServiceImplUnderTest.findDpList(List.of("value"));

        // Verify the results
        assertThat(result).isNotNull();
    }
}
