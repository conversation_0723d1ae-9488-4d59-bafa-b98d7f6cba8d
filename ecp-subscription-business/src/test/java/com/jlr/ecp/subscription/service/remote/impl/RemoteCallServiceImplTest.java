package com.jlr.ecp.subscription.service.remote.impl;

import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.listener.dto.TSDPSubscriptionDTO;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.util.RLockUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RemoteCallServiceImplTest {

    @Mock
    private RestTemplate mockRestTemplate;
    @Mock
    private Redisson mockRedisson;

    @InjectMocks
    private RemoteCallServiceImpl remoteCallServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Mock
    private VcsOrderFulfilmentCallMapper mockCallMapper;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(remoteCallServiceImplUnderTest, "subscriptionRenewUrl", "subscriptionRenewUrl");
        ReflectionTestUtils.setField(remoteCallServiceImplUnderTest, "ifEcomToken", "ifEcomToken");
        remoteCallServiceImplUnderTest.restTemplate = mockRestTemplate;
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
    }

    @Test
    public void testCallTSDPRenewService() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(true);
        expectedResult.setResponse("{}");

        try (MockedStatic<RLockUtil> mockedStatic = mockStatic(RLockUtil.class)) {
            mockedStatic.when(() -> RLockUtil.tryLock(mockLock, 2500, 5000, TimeUnit.MILLISECONDS)).thenReturn(true);
            // Configure RestTemplate.postForEntity(...).
            final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                    HttpStatus.OK);
            when(mockRestTemplate.postForEntity(anyString(), any(),
                    eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

            // Run the test
            final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.callTSDPRenewService(param);

            // Verify the results
            assertEquals(expectedResult, result);
        }
    }

    @Test
    public void testCallTSDPRenewService_RestTemplateThrowsRestClientException() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setTooManyRequests(false);
        expectedResult.setErrorMsg("请求异常, 调用TSDP续费获取锁失败");

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.callTSDPRenewService(param);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testConcurrentCallTSDPRenew() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setTooManyRequests(false);
        expectedResult.setErrorMsg("请求异常, 调用TSDP续费获取锁失败");

        when(mockLock.tryLock()).thenReturn(true);

        // Configure RestTemplate.postForEntity(...).
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.OK);

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.concurrentCallTSDPRenew(param);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testConcurrentCallTSDPRenew_RestTemplateThrowsRestClientException() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setErrorMsg("已有在途订单，请稍后再试");

        when(mockLock.tryLock()).thenReturn(false);

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.concurrentCallTSDPRenew(param);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListenerCallTSDPRenew() {
        // Setup
        final List<SubscriptionServiceDO> services = List.of(SubscriptionServiceDO.builder()
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .build());
        final BaseMessage baseMessage = new BaseMessage();
        baseMessage.setMessageId("messageId");
        baseMessage.setTenantId(0L);
        baseMessage.setOrderCode("orderCode");
        baseMessage.setVin("vin");
        baseMessage.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setErrorMsg("已有在途订单，请稍后再试");

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.listenerCallTSDPRenew(services,
                "fulfilmentId", baseMessage, "inControlId");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockCallMapper).insert(any(VcsOrderFufilmentCall.class));
    }
}
