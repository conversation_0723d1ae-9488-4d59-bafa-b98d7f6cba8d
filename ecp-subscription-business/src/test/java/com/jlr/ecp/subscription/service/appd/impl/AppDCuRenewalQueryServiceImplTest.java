package com.jlr.ecp.subscription.service.appd.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDCuRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDCuRenewalQueryPageVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AppDCuRenewalQueryServiceImplTest {

    @Mock
    private AppDCuRenewBatchRecordsMapper mockAppDCuRenewBatchRecordsMapper;
    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;

    @InjectMocks
    private AppDCuRenewalQueryServiceImpl appDCuRenewalQueryServiceImplUnderTest;

    @Test
    public void testQueryAppDCuRenewalBatchNo() {
        // Setup
        // Configure AppDCuRenewBatchRecordsMapper.selectList(...).
        final List<AppDCuRenewBatchRecords> appDCuRenewBatchRecords = List.of(AppDCuRenewBatchRecords.builder()
                .batchNo(0L)
                .build());
        when(mockAppDCuRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(appDCuRenewBatchRecords);

        // Run the test
        final String result = appDCuRenewalQueryServiceImplUnderTest.queryAppDCuRenewalBatchNo("1");

        // Verify the results
        assertEquals("0", result);
    }

    @Test
    public void testQueryAppDCuRenewalBatchNo_AppDCuRenewBatchRecordsMapperReturnsNoItems() {
        // Setup
        when(mockAppDCuRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure AppDCuRenewRecordsMapper.selectList(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords = List.of(AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .operator("operator")
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("ecpDisplay")
                .build());
        when(mockAppDCuRenewRecordsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(appDCuRenewRecords);

        // Run the test
        final String result = appDCuRenewalQueryServiceImplUnderTest.queryAppDCuRenewalBatchNo("1");

        // Verify the results
        assertEquals("0", result);
    }

    @Test
    public void testQueryAppDCuRenewalBatchNo_AppDCuRenewRecordsMapperReturnsNoItems() {
        // Setup
        // Configure AppDCuRenewBatchRecordsMapper.selectList(...).
        final List<AppDCuRenewBatchRecords> appDCuRenewBatchRecords = List.of(AppDCuRenewBatchRecords.builder()
                .batchNo(0L)
                .build());
        when(mockAppDCuRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(appDCuRenewBatchRecords);

        // Run the test
        final String result = appDCuRenewalQueryServiceImplUnderTest.queryAppDCuRenewalBatchNo("0");

        // Verify the results
        assertEquals("0", result);
    }

    @Test
    public void testQueryAppDCuRenewalService() {
        // Setup
        final List<AppDCuEnumQueryVO> expectedResult = List.of(AppDCuEnumQueryVO.builder()
                .code(1)
                .desc("信息娱乐服务")
                .build(),
                AppDCuEnumQueryVO.builder()
                        .code(2)
                        .desc("网络流量")
                        .build());

        // Run the test
        final List<AppDCuEnumQueryVO> result = appDCuRenewalQueryServiceImplUnderTest.queryAppDCuRenewalService();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryAppDCuRenewalStatus() {
        // Setup
        final List<AppDCuEnumQueryVO> expectedResult = List.of(
                AppDCuEnumQueryVO.builder().code(0).desc("进行中").build(),
                AppDCuEnumQueryVO.builder().code(2).desc("成功").build(),
                AppDCuEnumQueryVO.builder().code(3).desc("失败").build()
        );

        // Run the test
        final List<AppDCuEnumQueryVO> result = appDCuRenewalQueryServiceImplUnderTest.queryAppDCuRenewalStatus();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryAppDCuRenewalPageList() {
        // Setup
        final AppDCuRenewalQueryPageDTO queryPageDTO = new AppDCuRenewalQueryPageDTO();
        queryPageDTO.setPageNo(0);
        queryPageDTO.setPageSize(0);
        queryPageDTO.setCarVin("carVin");
        queryPageDTO.setBatchNoList(List.of("1"));
        queryPageDTO.setRenewalServiceType(0);
        queryPageDTO.setOperateStartTime("operateStartTime");
        queryPageDTO.setOperateEndTime("operateEndTime");
        queryPageDTO.setRenewalStatus(0);
        queryPageDTO.setOperator("operator");
        queryPageDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<AppDCuRenewalQueryPageVO> expectedResult = new PageResult<>(
                List.of(AppDCuRenewalQueryPageVO.builder()
                        .batchNo("1")
                        .carVin("carVin")
                        .renewalServiceType(0)
                        .renewalServiceDesc("")
                        .renewalStatus(0)
                        .renewalStatusDesc("进行中")
                        .renewalBeforeExpiryDate("2020/01/01")
                        .renewalAfterExpiryDate("")
                        .operateTime("2020/01/01 00:00:00")
                        .operator("operator")
                        .errorDesc("")
                        .build()), 0L);
        AppDCuRenewRecords appDCuRenewRecords = AppDCuRenewRecords.builder()
                .renewNo(1L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .operator("operator")
                .orderResultCode("1013")
                .build();
        appDCuRenewRecords.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        Page<AppDCuRenewRecords> page = new Page<>();
        page.setRecords(List.of(appDCuRenewRecords));
        when(mockAppDCuRenewRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<AppDCuRenewalQueryPageVO> result = appDCuRenewalQueryServiceImplUnderTest.queryAppDCuRenewalPageList(
                queryPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
