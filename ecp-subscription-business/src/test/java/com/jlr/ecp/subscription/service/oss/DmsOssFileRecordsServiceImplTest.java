package com.jlr.ecp.subscription.service.oss;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.bau.dto.HandleFileResultVO;
import com.jlr.ecp.subscription.api.bau.dto.TransferFileVO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinInitialLogPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssFileRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssFileRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataMapper;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.model.dto.VinMatchCountDto;
import com.jlr.ecp.subscription.model.vo.OnlineServiceInitializeLogVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DmsOssFileRecordsServiceImplTest {

    @Mock
    private DmsOssFileRecordsMapper mockDmsOssFileRecordsMapper;
    @Mock
    private FileService mockFileService;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private DmsOssOriginalDataMapper mockDmsOssOriginalDataMapper;

    @InjectMocks
    private DmsOssFileRecordsServiceImpl dmsOssFileRecordsServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        dmsOssFileRecordsServiceImplUnderTest.OSS_ACCESS_KEY_ID = "LTAI5tDJCVdLARtfvSa8xpy6";
        dmsOssFileRecordsServiceImplUnderTest.OSS_ACCESS_KEY_SECRET = "******************************";
        dmsOssFileRecordsServiceImplUnderTest.OSS_BUCKET_NAME = "jlr-dev-dms";
        dmsOssFileRecordsServiceImplUnderTest.OSS_ENDPOINT = "oss-cn-beijing.aliyuncs.com";
        dmsOssFileRecordsServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
    }

    @Test
    public void testTransferFile() throws Exception {
        // Setup
        final TransferFileVO expectedResult = TransferFileVO.builder()
                .errorDesc("OSS上没有flag文件")
                .build();

        // Run the test
        final TransferFileVO result = dmsOssFileRecordsServiceImplUnderTest.transferFile("dateStr");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testTransferFile_FileServiceGetFileContentThrowsException() throws Exception {
        // Setup
        final TransferFileVO expectedResult = TransferFileVO.builder()
                .errorDesc("S3已经存在flag文件无需上传文件")
                .build();
        when(mockFileService.getFileContent("AWS_S3_FILE", "Inc/dateStr/flag/transfer_success.txt")).thenReturn("content".getBytes());

        // Run the test
        final TransferFileVO result = dmsOssFileRecordsServiceImplUnderTest.transferFile("dateStr");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testHandleFile() throws Exception {
        // Setup
        final HandleFileResultVO expectedResult = HandleFileResultVO.builder()
                .total(1)
                .canInsert(1)
                .insert(1)
                .insertFail(0)
                .build();

        // Configure DmsOssFileRecordsMapper.selectOne(...).
        final DmsOssFileRecordsDO recordsDO = DmsOssFileRecordsDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("dateStr")
                .dmsOssFile("dmsOssFile")
                .ecpS3File("[\"s3/file\"]")
                .totalVinNum(0)
                .successVinNum(0)
                .failedVinNum(0)
                .build();
        when(mockDmsOssFileRecordsMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(recordsDO);

        // Configure DmsOssOriginalDataMapper.selectList(...).
        final List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS = List.of(DmsOssOriginalDataDO.builder()
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .build());
        when(mockDmsOssOriginalDataMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(dmsOssOriginalDataDOS);

        when(mockFileService.getFileContent("AWS_S3_FILE", "Inc/dateStr/flag/transfer_success.txt")).thenReturn("content".getBytes());
        when(mockFileService.getFileContent("AWS_S3_FILE", "s3/file")).thenReturn(createCsvContent(null));

        // Run the test
        final HandleFileResultVO result = dmsOssFileRecordsServiceImplUnderTest.handleFile("dateStr");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testHandleFile_Error() throws Exception {
        // Setup
        final HandleFileResultVO expectedResult = HandleFileResultVO.builder()
                .total(0)
                .canInsert(0)
                .insert(0)
                .insertFail(0)
                .build();

        // Configure DmsOssFileRecordsMapper.selectOne(...).
        final DmsOssFileRecordsDO recordsDO = DmsOssFileRecordsDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("dateStr")
                .dmsOssFile("dmsOssFile")
                .ecpS3File("[\"s3/file\"]")
                .totalVinNum(0)
                .successVinNum(0)
                .failedVinNum(0)
                .build();
        when(mockDmsOssFileRecordsMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(recordsDO);

        // Configure DmsOssOriginalDataMapper.selectList(...).
        final List<DmsOssOriginalDataDO> dmsOssOriginalDataDOS = List.of(DmsOssOriginalDataDO.builder()
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .build());
        when(mockDmsOssOriginalDataMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(dmsOssOriginalDataDOS);

        when(mockFileService.getFileContent("AWS_S3_FILE", "Inc/dateStr/flag/transfer_success.txt")).thenReturn("content".getBytes());
        when(mockFileService.getFileContent("AWS_S3_FILE", "s3/file")).thenReturn(createCsvContent("123,"));

        // Run the test
        final HandleFileResultVO result = dmsOssFileRecordsServiceImplUnderTest.handleFile("dateStr");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    private static byte[] createCsvContent(String status) {
        StringBuilder csvContent = new StringBuilder();

        // 创建表头
        csvContent.append("VIN,")
                .append("INVOICEDATE,")
                .append("STATUS,")
                .append("INVOICE_TYPE,")
                .append("\n");

        // 填充示例数据
        csvContent.append("HGCM1234567890000,")
                .append("20200101,")
                .append(Objects.isNull(status)? "60721001," : status)
                .append("60711001,")
                .append("\n"); // 示例 VIN

        // 将内容转换为字节数组
        return csvContent.toString().getBytes();
    }

    @Test
    public void testHandleFile_DmsOssFileRecordsMapperSelectOneReturnsNull() {
        // Setup
        final HandleFileResultVO expectedResult = HandleFileResultVO.builder()
                .total(0)
                .canInsert(0)
                .insert(0)
                .insertFail(0)
                .errorDesc("需要处理的ossFileRecords为空")
                .build();
        when(mockDmsOssFileRecordsMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final HandleFileResultVO result = dmsOssFileRecordsServiceImplUnderTest.handleFile("dateStr");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testInsertOriginalData() {
        // Setup
        final List<DmsOssOriginalDataDO> collect = List.of(DmsOssOriginalDataDO.builder()
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .build());
        final DmsOssFileRecordsDO ossFileRecordsDO = DmsOssFileRecordsDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("dateStr")
                .dmsOssFile("dmsOssFile")
                .ecpS3File("ecpS3File")
                .totalVinNum(0)
                .successVinNum(0)
                .failedVinNum(0)
                .build();

        // Run the test
        final int result = dmsOssFileRecordsServiceImplUnderTest.insertOriginalData(1, collect, 0, ossFileRecordsDO);

        // Verify the results
        assertEquals(0, result);
        verify(mockDmsOssOriginalDataMapper).insert(any(DmsOssOriginalDataDO.class));
        verify(mockDmsOssFileRecordsMapper).updateById(any(DmsOssFileRecordsDO.class));
    }

    @Test
    public void testCleanString() {
        assertEquals("input", DmsOssFileRecordsServiceImpl.cleanString("input"));
    }

    @Test
    public void testGetLogPageList() {
        // Setup
        final VinInitialLogPageDTO logPageDTO = new VinInitialLogPageDTO();
        logPageDTO.setPageNo(0);
        logPageDTO.setPageSize(0);
        logPageDTO.setOperateTimeSort("operateTimeSort");
        logPageDTO.setCreatedTimeSort("createdTimeSort");
        logPageDTO.setBauJobId(0L);
        logPageDTO.setStartTime("startTime");
        logPageDTO.setEndTime("endTime");

        final PageResult<OnlineServiceInitializeLogVO> expectedResult = new PageResult<>(
                List.of(OnlineServiceInitializeLogVO.builder()
                        .processDate("")
                        .createdTime("")
                        .successVinNum(1)
                        .failedVinNum(0)
                        .totalVinNum(1)
                        .showButton(false)
                        .build()), 0L);
        Page<DmsOssFileRecordsDO> page = new Page<>();
        page.setRecords(List.of(DmsOssFileRecordsDO.builder()
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .successVinNum(1)
                .failedVinNum(0)
                .totalVinNum(1)
                .build()));
        when(mockDmsOssFileRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapperX.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<OnlineServiceInitializeLogVO> result = dmsOssFileRecordsServiceImplUnderTest.getLogPageList(
                logPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateSyncResult() {
        // Setup
        final CommonResult<Boolean> expectedResult = CommonResult.success(true);

        // Configure DmsOssOriginalDataMapper.selectCountByJobId(...).
        final List<VinMatchCountDto> vinMatchCountDtos = List.of(VinMatchCountDto.builder()
                .bauJobId(0L)
                .vinMatchResult(0)
                .vinCount(0)
                .build());
        when(mockDmsOssOriginalDataMapper.selectCountByJobId(Set.of(0L))).thenReturn(vinMatchCountDtos);

        // Configure DmsOssFileRecordsMapper.selectList(...).
        final List<DmsOssFileRecordsDO> dmsOssFileRecordsDOS = List.of(DmsOssFileRecordsDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("dateStr")
                .dmsOssFile("dmsOssFile")
                .ecpS3File("ecpS3File")
                .totalVinNum(0)
                .successVinNum(0)
                .failedVinNum(0)
                .build());
        when(mockDmsOssFileRecordsMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(dmsOssFileRecordsDOS);

        // Run the test
        final CommonResult<Boolean> result = dmsOssFileRecordsServiceImplUnderTest.updateSyncResult(Set.of(0L));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateManualSyncResult() {
        // Setup
        // Configure DmsOssFileRecordsMapper.selectOne(...).
        final DmsOssFileRecordsDO recordsDO = DmsOssFileRecordsDO.builder()
                .id(0L)
                .bauJobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("dateStr")
                .dmsOssFile("dmsOssFile")
                .ecpS3File("ecpS3File")
                .totalVinNum(0)
                .successVinNum(0)
                .failedVinNum(0)
                .build();
        when(mockDmsOssFileRecordsMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(recordsDO);

        // Run the test
        dmsOssFileRecordsServiceImplUnderTest.updateManualSyncResult(0L);

        // Verify the results
        verify(mockDmsOssFileRecordsMapper).updateById(any(DmsOssFileRecordsDO.class));
    }
}
