package com.jlr.ecp.subscription.service.vin.expiry;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.report.api.ReportApi;
import com.jlr.ecp.subscription.api.unicom.dto.GenerateReportRequestV2;
import com.jlr.ecp.subscription.api.unicom.dto.MonthlyRnrBatchQueryDTO;
import com.jlr.ecp.subscription.api.unicom.vo.InitResponse;
import com.jlr.ecp.subscription.dal.dataobject.expiryvin.VinExpiryMonthlyRecordDO;
import com.jlr.ecp.subscription.dal.dataobject.vin.expiry.VinExpiryMonthlyDetailDO;
import com.jlr.ecp.subscription.dal.mysql.expiryvin.VinExpiryMonthlyRecordDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.vin.expiry.VinExpiryMonthlyDetailMapper;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.vin.expiry.dto.VinExpireServiceDTO;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class VinExpiryMonthlyDetailServiceImplTest {

    @Mock
    private PIVIUnicomService mockPiviUnicomService;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private VinExpiryMonthlyRecordDOMapper mockVinExpiryMonthlyRecordDOMapper;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleMapper;
    @Mock
    private ReportApi mockReportApi;

    @InjectMocks
    private VinExpiryMonthlyDetailServiceImpl vinExpiryMonthlyDetailServiceImplUnderTest;

    @Mock
    private VinExpiryMonthlyDetailMapper mockVinExpiryMonthlyDetailMapper;

    @Before
    public void setUp() throws Exception {
        vinExpiryMonthlyDetailServiceImplUnderTest.piviPackageDOMapper = mockPiviPackageDOMapper;
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), VinExpiryMonthlyDetailDO.class);
    }

    @Test
    public void testGetInitializedCount() {
        // Setup
        // Run the test
        assertThrows(NullPointerException.class, () -> vinExpiryMonthlyDetailServiceImplUnderTest.getInitializedCount(0L));
    }

    @Test
    public void testInitializeRecords() {
        // Setup
        final GenerateReportRequestV2 request = GenerateReportRequestV2.builder()
                .jobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("jobParam")
                .beginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobMonth(1)
                .jobYear(2020)
                .build();
        final InitResponse expectedResult = new InitResponse();
        expectedResult.setJobId(0L);
        expectedResult.setTotalCount(1);

        when(mockEcpIdUtil.nextId()).thenReturn(0L);

        // Configure VinExpiryMonthlyRecordDOMapper.selectByYearMonth(...).
        final VinExpiryMonthlyRecordDO vinExpiryMonthlyRecordDO = VinExpiryMonthlyRecordDO.builder()
                .jobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("jobParam")
                .beginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobMonth("jobMonth")
                .jobYear("jobYear")
                .reportS3File("reportS3File")
                .build();
        when(mockVinExpiryMonthlyRecordDOMapper.selectByYearMonth(2020, 1)).thenReturn(vinExpiryMonthlyRecordDO);

        // Configure PIVIPackageDOMapper.getVinExpireServiceByDataRangeForAllNoPage(...).
        final VinExpireServiceDTO vinExpireServiceDTO = new VinExpireServiceDTO();
        vinExpireServiceDTO.setCarVin("carVin");
        vinExpireServiceDTO.setRemoteExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        vinExpireServiceDTO.setPiviExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        vinExpireServiceDTO.setIccid("iccid");
        vinExpireServiceDTO.setServiceType("serviceType");
        vinExpireServiceDTO.setExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinExpireServiceDTO> vinExpireServiceDTOS = List.of(vinExpireServiceDTO);
        when(mockPiviPackageDOMapper.getVinExpireServiceByDataRangeForAllNoPage(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 2, 0, 0, 0))).thenReturn(vinExpireServiceDTOS);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Run the test
        final InitResponse result = vinExpiryMonthlyDetailServiceImplUnderTest.initializeRecords(request);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockVinExpiryMonthlyRecordDOMapper).updateById(any(VinExpiryMonthlyRecordDO.class));
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
    }

    @Test
    public void testInitializeRecords_VinExpiryMonthlyRecordDOMapperSelectByYearMonthReturnsNull() {
        // Setup
        final GenerateReportRequestV2 request = GenerateReportRequestV2.builder()
                .jobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("jobParam")
                .beginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobMonth(1)
                .jobYear(2020)
                .build();
        final InitResponse expectedResult = new InitResponse();
        expectedResult.setJobId(0L);
        expectedResult.setTotalCount(0);

        when(mockEcpIdUtil.nextId()).thenReturn(0L);
        when(mockVinExpiryMonthlyRecordDOMapper.selectByYearMonth(2020, 1)).thenReturn(null);

        // Run the test
        final InitResponse result = vinExpiryMonthlyDetailServiceImplUnderTest.initializeRecords(request);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockVinExpiryMonthlyRecordDOMapper).insert(any(VinExpiryMonthlyRecordDO.class));
    }

    @Test
    public void testInitializeRecords_PIVIPackageDOMapperReturnsNoItems() {
        // Setup
        final GenerateReportRequestV2 request = GenerateReportRequestV2.builder()
                .jobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("jobParam")
                .beginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobMonth(1)
                .jobYear(2020)
                .build();
        final InitResponse expectedResult = new InitResponse();
        expectedResult.setJobId(0L);
        expectedResult.setTotalCount(0);

        when(mockEcpIdUtil.nextId()).thenReturn(0L);

        // Configure VinExpiryMonthlyRecordDOMapper.selectByYearMonth(...).
        final VinExpiryMonthlyRecordDO vinExpiryMonthlyRecordDO = VinExpiryMonthlyRecordDO.builder()
                .jobId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobParam("jobParam")
                .beginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .jobMonth("jobMonth")
                .jobYear("jobYear")
                .reportS3File("reportS3File")
                .build();
        when(mockVinExpiryMonthlyRecordDOMapper.selectByYearMonth(2020, 1)).thenReturn(vinExpiryMonthlyRecordDO);

        when(mockPiviPackageDOMapper.getVinExpireServiceByDataRangeForAllNoPage(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 2, 0, 0, 0))).thenReturn(Collections.emptyList());

        // Run the test
        final InitResponse result = vinExpiryMonthlyDetailServiceImplUnderTest.initializeRecords(request);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockVinExpiryMonthlyRecordDOMapper).updateById(any(VinExpiryMonthlyRecordDO.class));
    }

    @Test
    public void testSelectQueryVinRecordsByJobId() {
        // Setup
        // Run the test
        assertThrows(NullPointerException.class, () -> vinExpiryMonthlyDetailServiceImplUnderTest.selectQueryVinRecordsByJobId(0L));
    }

    @Test
    public void testRnrBatchQueryExecutor() {
        // Setup
        final MonthlyRnrBatchQueryDTO monthlyRnrBatchQueryDTO = MonthlyRnrBatchQueryDTO.builder()
                .idList(List.of(0L))
                .build();

        assertThrows(NullPointerException.class, () -> vinExpiryMonthlyDetailServiceImplUnderTest.rnrBatchQueryExecutor(monthlyRnrBatchQueryDTO));
    }
}
