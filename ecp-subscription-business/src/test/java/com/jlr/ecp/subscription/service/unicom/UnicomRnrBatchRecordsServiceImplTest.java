package com.jlr.ecp.subscription.service.unicom;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.*;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomRnrBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomRnrQueryRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomRnrBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomRnrQueryRecordsMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnicomRnrBatchRecordsServiceImplTest {

    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private FileService mockFileService;
    @Mock
    private UnicomRnrBatchRecordsMapper mockUnicomRnrBatchRecordsMapper;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private UnicomRnrQueryRecordsMapper mockUnicomRnrQueryRecordsMapper;
    @Mock
    private SOTAService mockSotaService;
    @Mock
    private PIVIUnicomService mockPiviUnicomService;
    @Mock
    private Redisson mockRedisson;
    @Mock
    private ThreadPoolTaskExecutor mockRnrBatchQueryThreadPool;

    @InjectMocks
    private UnicomRnrBatchRecordsServiceImpl unicomRnrBatchRecordsServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(unicomRnrBatchRecordsServiceImplUnderTest, "unicomRnrTemplateExcelUrl",
                "unicomRnrTemplateExcelUrl");
        ReflectionTestUtils.setField(unicomRnrBatchRecordsServiceImplUnderTest, "rnrJobName", "beanName");
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), UnicomRnrQueryRecordsDO.class);
    }

    @Test
    public void testDownloadUnicomRnrTemplate() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success("unicomRnrTemplateExcelUrl");

        // Run the test
        final CommonResult<String> result = unicomRnrBatchRecordsServiceImplUnderTest.downloadUnicomRnrTemplate();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    private static byte[] createExcelContent(String vin) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue(Objects.isNull(vin) ? "HGCM1234567890000" : vin); // 示例 VIN

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }

    @Test
    public void testUploadRnrQueryExcel() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent(null));
        final UnicomRnrBatchRecordsVO unicomRnrBatchRecordsVO = new UnicomRnrBatchRecordsVO();
        unicomRnrBatchRecordsVO.setBatchNo(0L);
        unicomRnrBatchRecordsVO.setBeanName("beanName");
        final CommonResult<UnicomRnrBatchRecordsVO> expectedResult = CommonResult.success(unicomRnrBatchRecordsVO);

        // Configure UnicomRnrBatchRecordsMapper.selectOne(...).
        final UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = UnicomRnrBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFileS3Url("uploadFileS3Url")
                .operator("operator")
                .build();
        when(mockUnicomRnrBatchRecordsMapper.selectOne(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrBatchRecordsDO);

        // Configure UnicomRnrQueryRecordsMapper.selectOne(...).
        when(mockUnicomRnrQueryRecordsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);

        when(mockFileService.createFile(any(), any(), any(byte[].class), eq(Constants.FILE_CODE)))
                .thenReturn("uploadFileS3Url");
        when(mockSnowflake.nextId()).thenReturn(0L);
        when(mockUnicomRnrBatchRecordsMapper.insert(any(UnicomRnrBatchRecordsDO.class))).thenReturn(1);

        // Run the test
        final CommonResult<UnicomRnrBatchRecordsVO> result = unicomRnrBatchRecordsServiceImplUnderTest.uploadRnrQueryExcel(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUploadRnrQueryExcel_UnicomRnrBatchRecordsMapperSelectOneReturnsNotNull() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent(null));
        final UnicomRnrBatchRecordsVO unicomRnrBatchRecordsVO = new UnicomRnrBatchRecordsVO();
        unicomRnrBatchRecordsVO.setBatchNo(0L);
        unicomRnrBatchRecordsVO.setBeanName("beanName");
        final CommonResult<UnicomRnrBatchRecordsVO> expectedResult = CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);

        // Configure UnicomRnrBatchRecordsMapper.selectOne(...).
        final UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = UnicomRnrBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFileS3Url("uploadFileS3Url")
                .operator("operator")
                .build();
        when(mockUnicomRnrBatchRecordsMapper.selectOne(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrBatchRecordsDO);

        // Configure UnicomRnrQueryRecordsMapper.selectOne(...).
        final UnicomRnrQueryRecordsDO unicomRnrQueryRecordsDO = UnicomRnrQueryRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .carVin("carVin")
                .queryStatus(0)
                .iccid("iccid")
                .realNameFlag(0)
                .cardState("cardState")
                .errorDesc("errorDesc")
                .build();
        when(mockUnicomRnrQueryRecordsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(unicomRnrQueryRecordsDO);

        // Run the test
        final CommonResult<UnicomRnrBatchRecordsVO> result = unicomRnrBatchRecordsServiceImplUnderTest.uploadRnrQueryExcel(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testRnrBatchQueryParseJob() throws Exception {
        // Setup
        // Configure UnicomRnrBatchRecordsMapper.selectOne(...).
        final UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = UnicomRnrBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFileS3Url("uploadFileS3Url")
                .operator("operator")
                .build();
        when(mockUnicomRnrBatchRecordsMapper.selectOne(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrBatchRecordsDO);

        when(mockFileService.getFileContent(Constants.FILE_CODE, "adFileS3Url")).thenReturn(createExcelContent(null));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockRnrBatchQueryThreadPool).execute(any(Runnable.class));

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .iccid("iccid")
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("HGCM1234567890000")).thenReturn(piviPackageDO);

        // Run the test
        final Integer result = unicomRnrBatchRecordsServiceImplUnderTest.rnrBatchQueryParseJob(0L);

        // Verify the results
        assertEquals(Integer.valueOf(1), result);
        verify(mockRnrBatchQueryThreadPool).execute(any(Runnable.class));
        verify(mockUnicomRnrQueryRecordsMapper).insertBatch(anyList());
    }

    @Test
    public void testRnrBatchQueryParseJob_FileServiceThrowsException() throws Exception {
        // Setup
        // Configure UnicomRnrBatchRecordsMapper.selectOne(...).
        final UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = UnicomRnrBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFileS3Url("uploadFileS3Url")
                .operator("operator")
                .build();
        when(mockUnicomRnrBatchRecordsMapper.selectOne(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrBatchRecordsDO);

        when(mockFileService.getFileContent(Constants.FILE_CODE, "adFileS3Url")).thenThrow(Exception.class);

        // Run the test
        final Integer result = unicomRnrBatchRecordsServiceImplUnderTest.rnrBatchQueryParseJob(0L);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testRnrBatchQueryParseJob_vin_error() throws Exception {
        // Setup
        // Configure UnicomRnrBatchRecordsMapper.selectOne(...).
        final UnicomRnrBatchRecordsDO unicomRnrBatchRecordsDO = UnicomRnrBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFileS3Url("uploadFileS3Url")
                .operator("operator")
                .build();
        when(mockUnicomRnrBatchRecordsMapper.selectOne(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrBatchRecordsDO);

        when(mockFileService.getFileContent(Constants.FILE_CODE, "adFileS3Url")).thenReturn(createExcelContent("error"));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockRnrBatchQueryThreadPool).execute(any(Runnable.class));

        // Run the test
        final Integer result = unicomRnrBatchRecordsServiceImplUnderTest.rnrBatchQueryParseJob(0L);

        // Verify the results
        assertEquals(Integer.valueOf(1), result);
        verify(mockRnrBatchQueryThreadPool).execute(any(Runnable.class));
        verify(mockUnicomRnrQueryRecordsMapper).insertBatch(anyList());
    }

    @Test
    public void testGetBatchFilePage() {
        // Setup
        final UnicomBatchFilePageDTO pageDTO = new UnicomBatchFilePageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("operateTimeSort");

        final UnicomRnrBatchRecordListVO unicomRnrBatchRecordListVO = new UnicomRnrBatchRecordListVO();
        unicomRnrBatchRecordListVO.setBatchNo(0L);
        unicomRnrBatchRecordListVO.setState("文件上传成功");
        unicomRnrBatchRecordListVO.setOperateTime("");
        unicomRnrBatchRecordListVO.setOperator("operator");
        final PageResult<UnicomRnrBatchRecordListVO> expectedResult = new PageResult<>(
                List.of(unicomRnrBatchRecordListVO), 0L);
        Page<UnicomRnrBatchRecordsDO> pageResult = new Page<>();
        pageResult.setRecords(List.of(UnicomRnrBatchRecordsDO.builder()
                .batchNo(0L)
                .operator("operator")
                .build()));
        when(mockUnicomRnrBatchRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(pageResult);

        // Run the test
        final PageResult<UnicomRnrBatchRecordListVO> result = unicomRnrBatchRecordsServiceImplUnderTest.getBatchFilePage(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetUnicomQueryBatchNo() {
        // Setup
        // Configure UnicomRnrBatchRecordsMapper.selectList(...).
        final List<UnicomRnrBatchRecordsDO> unicomRnrBatchRecordsDOS = List.of(UnicomRnrBatchRecordsDO.builder()
                .id(0L)
                .batchNo(1L)
                .uploadFileS3Url("uploadFileS3Url")
                .operator("operator")
                .build());
        when(mockUnicomRnrBatchRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrBatchRecordsDOS);

        // Run the test
        final String result = unicomRnrBatchRecordsServiceImplUnderTest.getUnicomQueryBatchNo("1");

        // Verify the results
        assertEquals("1", result);
    }

    @Test
    public void testGetUnicomQueryBatchNo_UnicomRnrBatchRecordsMapperReturnsNoItems() {
        // Setup
        when(mockUnicomRnrBatchRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Configure UnicomRnrQueryRecordsMapper.selectList(...).
        final List<UnicomRnrQueryRecordsDO> unicomRnrQueryRecordsDOS = List.of(UnicomRnrQueryRecordsDO.builder()
                .id(0L)
                .batchNo(1L)
                .carVin("carVin")
                .queryStatus(0)
                .iccid("iccid")
                .realNameFlag(0)
                .cardState("cardState")
                .errorDesc("errorDesc")
                .build());
        when(mockUnicomRnrQueryRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrQueryRecordsDOS);

        // Run the test
        final String result = unicomRnrBatchRecordsServiceImplUnderTest.getUnicomQueryBatchNo("1");

        // Verify the results
        assertEquals("1", result);
    }

    @Test
    public void testRnrBatchQueryExecutor() {
        // Setup
        // Configure UnicomRnrQueryRecordsMapper.selectList(...).
        final List<UnicomRnrQueryRecordsDO> unicomRnrQueryRecordsDOS = List.of(UnicomRnrQueryRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .carVin("carVin")
                .queryStatus(2)
                .realNameFlag(0)
                .cardState("cardState")
                .errorDesc("errorDesc")
                .build());
        when(mockUnicomRnrQueryRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrQueryRecordsDOS);

        // Configure SOTAService.getSOTAInfoByVin(...).
        final SOTAResultVO sotaResultVO = new SOTAResultVO();
        sotaResultVO.setVin("vin");
        sotaResultVO.setPivi("pivi");
        sotaResultVO.setEsimiccid("iccid");
        sotaResultVO.setQueryResult("queryResult");
        when(mockSotaService.getSOTAInfoByVin("carVin")).thenReturn(sotaResultVO);

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        simCardInfo.setIccid("iccid");
        simCardInfo.setCardState("cardState");
        simCardInfo.setRealnameFlag("1");
        unicomRespData.setSimCardInfo(simCardInfo);
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUnicomService.getSimCardInfo("iccid")).thenReturn(unicomRespVO);

        // Run the test
        final Integer result = unicomRnrBatchRecordsServiceImplUnderTest.rnrBatchQueryExecutor(List.of(0L));

        // Verify the results
        assertEquals(Integer.valueOf(1), result);
        verify(mockUnicomRnrQueryRecordsMapper).updateBatch(anyList());
    }

    @Test
    public void testRnrBatchQueryExecutor_iccid_notNull() {
        // Setup
        // Configure UnicomRnrQueryRecordsMapper.selectList(...).
        final List<UnicomRnrQueryRecordsDO> unicomRnrQueryRecordsDOS = List.of(UnicomRnrQueryRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .carVin("carVin")
                .iccid("iccid")
                .queryStatus(2)
                .realNameFlag(0)
                .cardState("cardState")
                .errorDesc("errorDesc")
                .build());
        when(mockUnicomRnrQueryRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrQueryRecordsDOS);

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData = new UnicomRespData();
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUnicomService.getSimCardInfo("iccid")).thenReturn(unicomRespVO);

        // Run the test
        final Integer result = unicomRnrBatchRecordsServiceImplUnderTest.rnrBatchQueryExecutor(List.of(0L));

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockUnicomRnrQueryRecordsMapper).updateBatch(anyList());
    }

    @Test
    public void testRnrBatchQueryExecutor_SOTAServiceReturnsNull() {
        // Setup
        // Configure UnicomRnrQueryRecordsMapper.selectList(...).
        final List<UnicomRnrQueryRecordsDO> unicomRnrQueryRecordsDOS = List.of(UnicomRnrQueryRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .carVin("carVin")
                .queryStatus(2)
                .realNameFlag(0)
                .cardState("cardState")
                .errorDesc("errorDesc")
                .build());
        when(mockUnicomRnrQueryRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrQueryRecordsDOS);

        when(mockSotaService.getSOTAInfoByVin("carVin")).thenReturn(null);

        // Run the test
        final Integer result = unicomRnrBatchRecordsServiceImplUnderTest.rnrBatchQueryExecutor(List.of(0L));

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockUnicomRnrQueryRecordsMapper).updateBatch(anyList());
    }
}
