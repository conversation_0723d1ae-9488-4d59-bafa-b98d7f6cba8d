package com.jlr.ecp.subscription.service.incontrol;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.mysql.incontrol.IncontrolCustomerMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class IncontrolCustomerServiceImplTest {

    @Mock
    private IncontrolCustomerMapper mockIncontrolCustomerMapper;

    private IncontrolCustomerServiceImpl incontrolCustomerServiceImplUnderTest;

    @Before
    public void setUp() {
        incontrolCustomerServiceImplUnderTest = new IncontrolCustomerServiceImpl();
        incontrolCustomerServiceImplUnderTest.incontrolCustomerMapper = mockIncontrolCustomerMapper;
    }

    @Test
    public void testSaveOrUpdateIncontrolCustomerWithProactive() {
        // Setup
        // Configure IncontrolCustomerMapper.selectOne(...).
        final IncontrolCustomerDO customerDO = new IncontrolCustomerDO();
        customerDO.setId(0L);
        customerDO.setIncontrolId("inControlId");
        customerDO.setUserid("userid");
        customerDO.setSource(0);
        customerDO.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockIncontrolCustomerMapper.selectOne(any(SFunction.class), eq("inControlId"))).thenReturn(customerDO);

        // Run the test
        incontrolCustomerServiceImplUnderTest.saveOrUpdateIncontrolCustomerWithProactive("inControlId");

        // Verify the results
        // Confirm IncontrolCustomerMapper.updateById(...).
        final IncontrolCustomerDO entity = new IncontrolCustomerDO();
        entity.setId(0L);
        entity.setIncontrolId("inControlId");
        entity.setUserid("userid");
        entity.setSource(0);
        entity.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockIncontrolCustomerMapper,atLeast(0)).updateById(entity);
    }

    @Test
    public void testSaveOrUpdateIncontrolCustomerWithProactive_IncontrolCustomerMapperSelectOneReturnsNull() {
        // Setup
        when(mockIncontrolCustomerMapper.selectOne(any(SFunction.class), eq("inControlId"))).thenReturn(null);

        // Run the test
        incontrolCustomerServiceImplUnderTest.saveOrUpdateIncontrolCustomerWithProactive("inControlId");

        // Verify the results
        // Confirm IncontrolCustomerMapper.insert(...).
        final IncontrolCustomerDO entity = new IncontrolCustomerDO();
        entity.setId(0L);
        entity.setIncontrolId("inControlId");
        entity.setUserid("userid");
        entity.setSource(0);
        entity.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockIncontrolCustomerMapper,atLeast(0)).insert(entity);
    }

    @Test
    public void testSaveOrUpdateIncontrolCustomerWithTSDP() {
        // Setup
        final IncontrolCustomerDO customerDO = new IncontrolCustomerDO();
        customerDO.setId(0L);
        customerDO.setIncontrolId("inControlId");
        customerDO.setUserid("userid");
        customerDO.setSource(0);
        customerDO.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final Set<IncontrolCustomerDO> customerSet = Set.of(customerDO);

        // Configure IncontrolCustomerMapper.selectList(...).
        final IncontrolCustomerDO customerDO1 = new IncontrolCustomerDO();
        customerDO1.setId(0L);
        customerDO1.setIncontrolId("inControlId");
        customerDO1.setUserid("userid");
        customerDO1.setSource(0);
        customerDO1.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<IncontrolCustomerDO> incontrolCustomerDOS = List.of(customerDO1);
        // when(mockIncontrolCustomerMapper.selectList(any(SFunction.class), eq(List.of("value"))))
        //         .thenReturn(incontrolCustomerDOS);

        // Run the test
        incontrolCustomerServiceImplUnderTest.saveOrUpdateIncontrolCustomerWithTSDP(customerSet);

        // Verify the results
        // Confirm IncontrolCustomerMapper.saveOrUpdateBatch(...).
        final IncontrolCustomerDO customerDO2 = new IncontrolCustomerDO();
        customerDO2.setId(0L);
        customerDO2.setIncontrolId("inControlId");
        customerDO2.setUserid("userid");
        customerDO2.setSource(0);
        customerDO2.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ArrayList<IncontrolCustomerDO> collection = new ArrayList<>(List.of(customerDO2));
        verify(mockIncontrolCustomerMapper,atLeast(0)).saveOrUpdateBatch(collection);
    }

    @Test
    public void testSaveOrUpdateIncontrolCustomerWithTSDP_IncontrolCustomerMapperSelectListReturnsNoItems() {
        // Setup
        final IncontrolCustomerDO customerDO = new IncontrolCustomerDO();
        customerDO.setId(0L);
        customerDO.setIncontrolId("inControlId");
        customerDO.setUserid("userid");
        customerDO.setSource(0);
        customerDO.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final Set<IncontrolCustomerDO> customerSet = Set.of(customerDO);
        // when(mockIncontrolCustomerMapper.selectList(any(SFunction.class), eq(List.of("value"))))
        //         .thenReturn(Collections.emptyList());

        // Run the test
        incontrolCustomerServiceImplUnderTest.saveOrUpdateIncontrolCustomerWithTSDP(customerSet);

        // Verify the results
        // Confirm IncontrolCustomerMapper.saveOrUpdateBatch(...).
        final IncontrolCustomerDO customerDO1 = new IncontrolCustomerDO();
        customerDO1.setId(0L);
        customerDO1.setIncontrolId("inControlId");
        customerDO1.setUserid("userid");
        customerDO1.setSource(0);
        customerDO1.setLastLoginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ArrayList<IncontrolCustomerDO> collection = new ArrayList<>(List.of(customerDO1));
        verify(mockIncontrolCustomerMapper,atLeast(0)).saveOrUpdateBatch(collection);
    }
}
