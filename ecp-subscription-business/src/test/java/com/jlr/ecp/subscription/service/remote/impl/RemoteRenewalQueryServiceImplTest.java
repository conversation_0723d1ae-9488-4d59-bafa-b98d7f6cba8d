package com.jlr.ecp.subscription.service.remote.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteEnumQueryVO;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteRenewalQueryPageVO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RemoteRenewalQueryServiceImplTest {

    @Mock
    private RemoteRenewBatchRecordsMapper mockRemoteRenewBatchRecordsMapper;
    @Mock
    private RemoteRenewDetailRecordsMapper mockRemoteRenewDetailRecordsMapper;

    @InjectMocks
    private RemoteRenewalQueryServiceImpl remoteRenewalQueryServiceImplUnderTest;

    @Test
    public void testQueryRemoteRenewalBatchNo() {
        // Setup
        // Configure RemoteRenewBatchRecordsMapper.selectList(...).
        final List<RemoteRenewBatchRecords> remoteRenewBatchRecords = List.of(RemoteRenewBatchRecords.builder()
                .batchNo(1L)
                .build());
        when(mockRemoteRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(remoteRenewBatchRecords);

        // Run the test
        final String result = remoteRenewalQueryServiceImplUnderTest.queryRemoteRenewalBatchNo("1");

        // Verify the results
        assertEquals("1", result);
    }

    @Test
    public void testQueryRemoteRenewalBatchNo_RemoteRenewBatchRecordsMapperReturnsNoItems() {
        // Setup
        when(mockRemoteRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RemoteRenewDetailRecordsMapper.selectList(...).
        final List<RemoteRenewDetailRecords> records = List.of(RemoteRenewDetailRecords.builder()
                .id(0L)
                .batchNo(1L)
                .carVin("carVin")
                .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyStatus(0)
                .errorDesc("errorDesc")
                .operator("operator")
                .build());
        when(mockRemoteRenewDetailRecordsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(records);

        // Run the test
        final String result = remoteRenewalQueryServiceImplUnderTest.queryRemoteRenewalBatchNo("1");

        // Verify the results
        assertEquals("1", result);
    }

    @Test
    public void testQueryRemoteRenewalBatchNo_RemoteRenewDetailRecordsMapperReturnsNoItems() {
        // Setup
        // Configure RemoteRenewBatchRecordsMapper.selectList(...).
        final List<RemoteRenewBatchRecords> remoteRenewBatchRecords = List.of(RemoteRenewBatchRecords.builder()
                .batchNo(1L)
                .build());
        when(mockRemoteRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(remoteRenewBatchRecords);

        // Run the test
        final String result = remoteRenewalQueryServiceImplUnderTest.queryRemoteRenewalBatchNo("1");

        // Verify the results
        assertEquals("1", result);
    }

    @Test
    public void testQueryRemoteRenewalStatus() {
        // Setup
        final List<RemoteEnumQueryVO> expectedResult = List.of(RemoteEnumQueryVO.builder()
                        .code(1)
                        .desc("进行中")
                        .build(),
                RemoteEnumQueryVO.builder()
                        .code(2)
                        .desc("成功")
                        .build(),
                RemoteEnumQueryVO.builder()
                        .code(3)
                        .desc("失败")
                        .build());

        // Run the test
        final List<RemoteEnumQueryVO> result = remoteRenewalQueryServiceImplUnderTest.queryRemoteRenewalStatus();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryRemoteRenewalService() {
        // Setup
        final List<RemoteEnumQueryVO> expectedResult = List.of(RemoteEnumQueryVO.builder()
                .code(1)
                .desc("远程车控")
                .build());

        // Run the test
        final List<RemoteEnumQueryVO> result = remoteRenewalQueryServiceImplUnderTest.queryRemoteRenewalService();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryRemoteRenewalPageList() {
        // Setup
        final RemoteRenewalQueryPageDTO queryPageDTO = new RemoteRenewalQueryPageDTO();
        queryPageDTO.setPageNo(0);
        queryPageDTO.setPageSize(0);
        queryPageDTO.setCarVin("carVin");
        queryPageDTO.setBatchNoList(List.of("1"));
        queryPageDTO.setOperateStartTime("operateStartTime");
        queryPageDTO.setOperateEndTime("operateEndTime");
        queryPageDTO.setRenewalStatus(0);
        queryPageDTO.setOperator("operator");
        queryPageDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<RemoteRenewalQueryPageVO> expectedResult = new PageResult<>(
                List.of(RemoteRenewalQueryPageVO.builder()
                        .batchNo("1")
                        .carVin("carVin")
                        .renewalServiceDesc("远程车控")
                        .renewalStatus(1)
                        .renewalStatusDesc("进行中")
                        .renewalBeforeExpiryDate("2020/01/01")
                        .renewalAfterExpiryDate("2020/01/01")
                        .operateTime("")
                        .operator("operator")
                        .build()), 0L);
        Page<RemoteRenewDetailRecords> page = new Page<>();
        page.setRecords(List.of(RemoteRenewDetailRecords.builder()
                .batchNo(1L)
                .carVin("carVin")
                .modifyStatus(1)
                .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .operator("operator")
                .build()));
        when(mockRemoteRenewDetailRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<RemoteRenewalQueryPageVO> result = remoteRenewalQueryServiceImplUnderTest.queryRemoteRenewalPageList(
                queryPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
