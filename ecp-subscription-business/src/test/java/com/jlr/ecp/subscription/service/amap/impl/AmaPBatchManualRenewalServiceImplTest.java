package com.jlr.ecp.subscription.service.amap.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPBatchRenewalExcel;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AmaPBatchManualRenewalServiceImplTest {

    @Mock
    private AmaPRenewBatchRecordsDOMapper mockAmaPRenewBatchRecordsDOMapper;
    @Mock
    private AmaPRenewRecordsDOMapper mockAmaPRenewRecordsDOMapper;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private FileService mockFileService;
    @Mock
    private AmaPProperties mockAmaPProperties;

    @InjectMocks
    private AmaPBatchManualRenewalServiceImpl amaPBatchManualRenewalServiceImplUnderTest;

    @Test
    public void testQueryBatchRenewalPageList() {
        // Setup
        final AmaPBatchRenewalPageDTO amaPPageDto = new AmaPBatchRenewalPageDTO();
        amaPPageDto.setPageNo(1);
        amaPPageDto.setPageSize(10);
        amaPPageDto.setOperateTimeSort("DESC");

        List<AmaPBatchRenewalPageVO> build = List.of(AmaPBatchRenewalPageVO.builder()
                .operateTime("2020/01/01 00:00:00")
                .operator("operator")
                .checkResultDesc("校验通过")
                .checkResultStatus(1)
                .operateStatus(0)
                .batchNo(0L)
                .errorDetailPath("errorDetailPath")
                .build());
        final PageResult<AmaPBatchRenewalPageVO> expectedResult = new PageResult<>(build, 1L);

        List<AmaPRenewBatchRecordsDO> amaPRenewBatchRecordsDOLisyl;
        Page<AmaPRenewBatchRecordsDO> page = new Page<>();
        AmaPRenewBatchRecordsDO recordsDO = AmaPRenewBatchRecordsDO.builder()
                .operator("operator")
                .verifyResult(1)
                .batchNo(0L)
                .dealStatus(0)
                .batchNo(0L)
                .verifyResultFile("errorDetailPath")
                .build();
        recordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        List<AmaPRenewBatchRecordsDO> records = List.of(recordsDO);
        page.setRecords(records);
        page.setTotal(1);
        when(mockAmaPRenewBatchRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<AmaPBatchRenewalPageVO> result = amaPBatchManualRenewalServiceImplUnderTest.queryBatchRenewalPageList(
                amaPPageDto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUploadAmaPExcelRenewal() {
        // Setup
        // 创建一个模拟的 Excel 文件内容
        byte[] excelContent = createExcelContent();

        // 创建 MockMultipartFile
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", // 文件字段名
                "test.xlsx", // 文件名
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // 文件类型
                excelContent // 文件内容
        );
        final CommonResult<AmaPBatchRenewalUploadVO> expectedResult = CommonResult.success(
                AmaPBatchRenewalUploadVO.builder()
                        .type("success")
                        .msg("请关注续费文件校验结果")
                        .build());
        when(mockSnowflake.nextId()).thenReturn(0L);

        // Run the test
        final CommonResult<AmaPBatchRenewalUploadVO> result = amaPBatchManualRenewalServiceImplUnderTest.uploadAmaPExcelRenewal(
                mockFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAmaPRenewBatchRecordsDOMapper).insert(AmaPRenewBatchRecordsDO.builder()
                .batchNo(0L)
                .verifyResult(0)
                .dealStatus(0)
                .operator("System")
                .build());
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");
        Cell durationHeader = headerRow.createCell(1);
        durationHeader.setCellValue("续费时长（仅支持填写一年或三年）");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM82633A12"); // 示例 VIN
        dataRow.createCell(1).setCellValue("一年"); // 示例续费时长

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }

    @Test
    public void testBatchSendAmaPRenewal() throws Exception {
        // Setup
        final AmaPBatchSendDTO amaPBatchSendDTO = new AmaPBatchSendDTO();
        amaPBatchSendDTO.setBatchNo("0");

        final CommonResult<String> expectedResult = CommonResult.success("请求已经发送，请前往续费记录查看结果");

        // Configure AmaPRenewBatchRecordsDOMapper.selectList(...).
        when(mockAmaPRenewBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure AmaPRenewBatchRecordsDOMapper.selectOne(...).
        final AmaPRenewBatchRecordsDO amaPRenewBatchRecordsDO = AmaPRenewBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFile("file/path")
                .verifyResult(0)
                .dealStatus(0)
                .verifyResultFile("errorDetailPath")
                .operator("operator")
                .build();
        when(mockAmaPRenewBatchRecordsDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(amaPRenewBatchRecordsDO);

        when(mockFileService.getFileContent("AWS_S3_FILE", "path")).thenReturn(createExcelContent());
        when(mockSnowflake.nextIdStr()).thenReturn("0");

        // Run the test
        final CommonResult<String> result = amaPBatchManualRenewalServiceImplUnderTest.batchSendAmaPRenewal(
                amaPBatchSendDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAmaPRenewRecordsDOMapper).insertBatch(List.of(AmaPRenewRecordsDO.builder()
                .carVin("HGCM82633A12")
                .cusOrderId("0")
                .renewYear(1)
                .renewNo(0L)
                .operator("System")
                .dataSource(2)
                .renewStatus(0)
                .build()));
        verify(mockAmaPRenewBatchRecordsDOMapper).updateById(AmaPRenewBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFile("file/path")
                .verifyResult(0)
                .dealStatus(1)
                .verifyResultFile("errorDetailPath")
                .operator("operator")
                .build());
    }

    @Test
    public void testBatchSendAmaPRenewal_AmaPRenewBatchRecordsDOMapperSelectListReturnsNotNull() throws Exception {
        // Setup
        final AmaPBatchSendDTO amaPBatchSendDTO = new AmaPBatchSendDTO();
        amaPBatchSendDTO.setBatchNo("0");

        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);
        final List<AmaPRenewBatchRecordsDO> amaPRenewBatchRecordsDOS = List.of(AmaPRenewBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFile("uploadFile")
                .verifyResult(0)
                .dealStatus(0)
                .verifyResultFile("errorDetailPath")
                .operator("operator")
                .build());
        when(mockAmaPRenewBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(amaPRenewBatchRecordsDOS);

        // Run the test
        final CommonResult<String> result = amaPBatchManualRenewalServiceImplUnderTest.batchSendAmaPRenewal(
                amaPBatchSendDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchSendAmaPRenewal_FileServiceThrowsException() throws Exception {
        // Setup
        final AmaPBatchSendDTO amaPBatchSendDTO = new AmaPBatchSendDTO();
        amaPBatchSendDTO.setBatchNo("0");

        final CommonResult<String> expectedResult = CommonResult.error(1000001,null);

        // Configure AmaPRenewBatchRecordsDOMapper.selectList(...).
        when(mockAmaPRenewBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure AmaPRenewBatchRecordsDOMapper.selectOne(...).
        final AmaPRenewBatchRecordsDO amaPRenewBatchRecordsDO = AmaPRenewBatchRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .uploadFile("file/path")
                .verifyResult(0)
                .dealStatus(0)
                .verifyResultFile("errorDetailPath")
                .operator("operator")
                .build();
        when(mockAmaPRenewBatchRecordsDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(amaPRenewBatchRecordsDO);

        when(mockFileService.getFileContent("AWS_S3_FILE", "path")).thenThrow(Exception.class);

        // Run the test
        final CommonResult<String> result = amaPBatchManualRenewalServiceImplUnderTest.batchSendAmaPRenewal(
                amaPBatchSendDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTemplateUrl() {
        // Setup
        when(mockAmaPProperties.getRenewalExcelUrl()).thenReturn("renewalExcelUrl");

        // Run the test
        final String result = amaPBatchManualRenewalServiceImplUnderTest.getTemplateUrl();

        // Verify the results
        assertEquals("renewalExcelUrl", result);
    }

    @Test
    public void testBatchInsertAmaPBatchRenewalRecord() {
        // Setup
        final List<AmaPBatchRenewalExcel> batchRenewalExcelList = List.of(AmaPBatchRenewalExcel.builder()
                .carVin("carVin")
                .renewalYear("renewalYear")
                .build());
        when(mockSnowflake.nextIdStr()).thenReturn("cusOrderId");

        // Run the test
        amaPBatchManualRenewalServiceImplUnderTest.batchInsertAmaPBatchRenewalRecord(batchRenewalExcelList, 0L);

        // Verify the results
        verify(mockAmaPRenewRecordsDOMapper).insertBatch(List.of(AmaPRenewRecordsDO.builder()
                .carVin("CARVIN")
                .cusOrderId("cusOrderId")
                .renewNo(0L)
                .operator("System")
                .dataSource(2)
                .renewStatus(0)
                .build()));
    }

    @Test
    public void testDoReadAmaPRenewalExcel() {
        // Setup
        final List<List<AmaPBatchRenewalExcel>> expectedResult = List.of(List.of(AmaPBatchRenewalExcel.builder()
                .carVin("HGCM82633A12")
                .renewalYear("一年")
                .build()));

        // Run the test
        final List<List<AmaPBatchRenewalExcel>> result = amaPBatchManualRenewalServiceImplUnderTest.doReadAmaPRenewalExcel(
                createExcelContent());

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
