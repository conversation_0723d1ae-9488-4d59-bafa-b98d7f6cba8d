package com.jlr.ecp.subscription.service.fufilment;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.OrderAppApi;
import com.jlr.ecp.order.api.order.dto.OrderItemCodeListDTO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.SubBrandListDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.*;
import com.jlr.ecp.subscription.constant.KafkaConstants;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.dal.dataobject.businesserror.InterBusiErrorLogDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.businesserror.InterBusiErrorLogMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderRollbackDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.FufilmentServiceStatusEnum;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VcsOrderFufilmentDOServiceImplTest {

    @Mock
    private VcsOrderFufilmentDOMapper mockVcsOrderFufilmentDOMapper;
    @Mock
    private VcsOrderRollbackDOMapper mockVcsOrderRollbackDOMapper;
    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;
    @Mock
    private OrderAppApi mockOrderAppApi;

    @InjectMocks
    private VcsOrderFufilmentDOServiceImpl vcsOrderFufilmentDOServiceImplUnderTest;

    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleDOMapper;

    @Mock
    private PIVIUnicomService mockPiviUnicomService;

    @Mock
    private ProducerTool mockProducerTool;

    @Mock
    private InterBusiErrorLogMapper mockInterBusiErrorLogMapper;

    @Mock
    private RestTemplate mockRestTemplate;
    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), VcsOrderFufilmentDO.class);
        ReflectionTestUtils.setField(vcsOrderFufilmentDOServiceImplUnderTest, "subscriptionRenewUrl",
                "subscriptionRenewUrl");
        ReflectionTestUtils.setField(vcsOrderFufilmentDOServiceImplUnderTest, "ifEcomToken", "ifEcomToken");
        vcsOrderFufilmentDOServiceImplUnderTest.restTemplate = mockRestTemplate;
    }
    @Test
    public void testGetOneByOrderItemCode() {
        // Setup
        final VcsOrderFulfilmentRespVO expectedResult = new VcsOrderFulfilmentRespVO();
        expectedResult.setFulfilmentId("fulfilmentId");
        expectedResult.setOrderCode("orderCode");
        expectedResult.setOrderItemCode("orderItemCode");
        expectedResult.setCarVin("carVin");
        expectedResult.setServiceStatusDesc("serviceStatusDesc");

        // Configure VcsOrderFufilmentDOMapper.selectOne(...).
        final VcsOrderFufilmentDO vcsOrderFufilmentDO = VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build();
        when(mockVcsOrderFufilmentDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDO);

        // Configure VcsOrderRollbackDOMapper.selectOne(...).
        final VcsOrderRollbackDO vcsOrderRollbackDO = VcsOrderRollbackDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .serviceStatus(2)
                .build();
        when(mockVcsOrderRollbackDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderRollbackDO);

        // Run the test
        final VcsOrderFulfilmentRespVO result = vcsOrderFufilmentDOServiceImplUnderTest.getOneByOrderItemCode(
                "orderItemCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOneByOrderItemCode_VcsOrderFufilmentDOMapperReturnsNull() {
        // Setup
        final VcsOrderFulfilmentRespVO expectedResult = new VcsOrderFulfilmentRespVO();
        expectedResult.setFulfilmentId("fulfilmentId");
        expectedResult.setOrderCode("orderCode");
        expectedResult.setOrderItemCode("orderItemCode");
        expectedResult.setCarVin("carVin");
        expectedResult.setServiceStatusDesc("serviceStatusDesc");

        when(mockVcsOrderFufilmentDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final VcsOrderFulfilmentRespVO result = vcsOrderFufilmentDOServiceImplUnderTest.getOneByOrderItemCode(
                "orderItemCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetHistoryByOrderItemCodeList() {
        // Setup
        // Configure VcsOrderFufilmentDOMapper.getHistoryByOrderItemCodeList(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId("fulfilmentId");
        vcsOrderFulfilmentRespVO.setOrderCode("orderCode");
        vcsOrderFulfilmentRespVO.setOrderItemCode("orderItemCode");
        vcsOrderFulfilmentRespVO.setCarVin("carVin");
        vcsOrderFulfilmentRespVO.setServiceStatusDesc("serviceStatusDesc");
        final List<VcsOrderFulfilmentRespVO> list = List.of(vcsOrderFulfilmentRespVO);
        when(mockVcsOrderFufilmentDOMapper.getHistoryByOrderItemCodeList("orderItemCodeList")).thenReturn(list);

        // Run the test
        final List<VcsOrderFulfilmentRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getHistoryByOrderItemCodeList(
                "orderItemCodeList");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetHistoryByOrderItemCodeList_VcsOrderFufilmentDOMapperReturnsNoItems() {
        // Setup
        when(mockVcsOrderFufilmentDOMapper.getHistoryByOrderItemCodeList("orderItemCodeList"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VcsOrderFulfilmentRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getHistoryByOrderItemCodeList(
                "orderItemCodeList");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetSubscriptionList() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("carVin", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("value");
        vehicleInfo.setSeriesName("");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productInfo.setOrderItemCode("orderItemCode");
        productInfo.setProductVersionCode("productVersionCode");
        productInfo.setProductCode("productCode");
        productInfo.setProductSkuCode("productSkuCode");
        productInfo.setProductName("productName");
        productInfo.setServiceStatusDesc("订阅过期");
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .vcsOrderCode("vcsOrderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .serviceType(2)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderRollbackDOMapper.selectList(...).
        final List<VcsOrderRollbackDO> vcsOrderRollbackDOS = List.of(VcsOrderRollbackDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .serviceStatus(2)
                .build());
        when(mockVcsOrderRollbackDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderRollbackDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("carVin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("carVin"))).thenReturn(vinsAndServiceDateDTOS);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("ONLINE-PACK")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(3)
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(List.of(orderItemBaseVO));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("orderItemCode"));
        when(mockOrderAppApi.getOrderItemInfo(orderItemCodeListDTO)).thenReturn(listCommonResult);

        when(mockOrderAppApi.getAfterSalesOrderCode(List.of("orderCode")))
                .thenReturn(CommonResult.success(Set.of("orderCode")));

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .carSystemModel("PIVI")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSubscriptionList_orderItemNull() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_VcsOrderFufilmentDOMapperSelectListReturnsNoItems() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(List.of(orderItemBaseVO));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_VcsOrderFufilmentDOMapperFindVinAndServiceDateReturnsNoItems() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(Collections.emptyList());

        // Configure OrderAppApi.getOrderItemInfo(...).
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(List.of(orderItemBaseVO));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_OrderAppApiGetOrderItemInfoReturnsNoItems() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(Collections.emptyList());
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_OrderAppApiGetOrderItemInfoReturnsError() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.error(
                new ServiceException(12312, "message"));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_OrderAppApiQueryOrderCodeSetReturnsNoItems() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(List.of(orderItemBaseVO));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_OrderAppApiQueryOrderCodeSetReturnsError() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(List.of(orderItemBaseVO));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSubscriptionList_VcsOrderFufilmentDOMapperCheckActiveServiceByCarVinListReturnsNoItems() {
        // Setup
        final SubBrandListDTO subBrandListDTO = new SubBrandListDTO();
        subBrandListDTO.setBrandVehicles(Map.ofEntries(Map.entry("value", Map.ofEntries(Map.entry("value", "value")))));
        subBrandListDTO.setBrandCode("brandCode");

        final OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();
        final SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode("seriesCode");
        vehicleInfo.setSeriesName("seriesName");
        vehicleInfo.setCarVin("carVin");
        vehicleInfo.setReNew(false);
        orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);
        final SubProductItemInfoVO productInfo = new SubProductItemInfoVO();
        productInfo.setServiceStatusDesc("serviceStatusDesc");
        productInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setProductInfo(productInfo);
        final SubDurationHistoryVO subDurationHistoryVO = new SubDurationHistoryVO();
        subDurationHistoryVO.setHistoryStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subDurationHistoryVO.setHistoryEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderSubscriptionRespVO.setDurationHistoryList(List.of(subDurationHistoryVO));
        final List<OrderSubscriptionRespVO> expectedResult = List.of(orderSubscriptionRespVO);

        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Configure OrderAppApi.getOrderItemInfo(...).
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final CommonResult<List<OrderItemBaseVO>> listCommonResult = CommonResult.success(List.of(orderItemBaseVO));
        final OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(List.of("value"));

        // Run the test
        final List<OrderSubscriptionRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getSubscriptionList(
                subBrandListDTO);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetLatestByOrderItemCodeList() {
        // Setup
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId("fulfilmentId");
        vcsOrderFulfilmentRespVO.setOrderCode("orderCode");
        vcsOrderFulfilmentRespVO.setOrderItemCode("orderItemCode");
        vcsOrderFulfilmentRespVO.setCarVin("carVin");
        vcsOrderFulfilmentRespVO.setServiceStatusDesc("serviceStatusDesc");
        final List<VcsOrderFulfilmentRespVO> expectedResult = List.of(vcsOrderFulfilmentRespVO);

        // Configure VcsOrderFufilmentDOMapper.getLatestByOrderItemCodeList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(2)
                .build());
        when(mockVcsOrderFufilmentDOMapper.getLatestByOrderItemCodeList(List.of("value")))
                .thenReturn(vcsOrderFufilmentDOS);

        // Run the test
        final List<VcsOrderFulfilmentRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getLatestByOrderItemCodeList(
                List.of("value"));

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetLatestByOrderItemCodeList_VcsOrderFufilmentDOMapperReturnsNoItems() {
        // Setup
        when(mockVcsOrderFufilmentDOMapper.getLatestByOrderItemCodeList(List.of("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VcsOrderFulfilmentRespVO> result = vcsOrderFufilmentDOServiceImplUnderTest.getLatestByOrderItemCodeList(
                List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCheckCanBuyService() {
        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinList(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);

        // Run the test
        final Boolean result = vcsOrderFufilmentDOServiceImplUnderTest.checkCanBuyService(any());

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testGetFulfilmentListByCarVinList() {
        // Setup
        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderFufilmentDOS);

        // Configure VcsOrderRollbackDOMapper.selectList(...).
        final List<VcsOrderRollbackDO> vcsOrderRollbackDOS = List.of(VcsOrderRollbackDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .serviceStatus(0)
                .build());
        when(mockVcsOrderRollbackDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderRollbackDOS);

        // Run the test
        final List<String> result = vcsOrderFufilmentDOServiceImplUnderTest.getFulfilmentListByCarVinList(
                List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(List.of("fufilmentId"));
    }

    @Test
    public void testGetFulfilmentListByCarVinList_VcsOrderFufilmentDOMapperReturnsNoItems() {
        // Setup
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure VcsOrderRollbackDOMapper.selectList(...).
        final List<VcsOrderRollbackDO> vcsOrderRollbackDOS = List.of(VcsOrderRollbackDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .serviceStatus(0)
                .build());

        // Run the test
        final List<String> result = vcsOrderFufilmentDOServiceImplUnderTest.getFulfilmentListByCarVinList(
                List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetFulfilmentListByCarVinList_VcsOrderRollbackDOMapperReturnsNoItems() {
        // Setup
        // Configure VcsOrderFufilmentDOMapper.selectList(...).
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .build());
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderFufilmentDOS);

        when(mockVcsOrderRollbackDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = vcsOrderFufilmentDOServiceImplUnderTest.getFulfilmentListByCarVinList(
                List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindVinAndServiceDate() {
        // Setup
        final VinsAndServiceDateDTO vinsAndServiceDateDTO = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO.setVin("vin");
        vinsAndServiceDateDTO.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> expectedResult = List.of(vinsAndServiceDateDTO);

        // Configure VcsOrderFufilmentDOMapper.findVinAndServiceDate(...).
        final VinsAndServiceDateDTO vinsAndServiceDateDTO1 = new VinsAndServiceDateDTO();
        vinsAndServiceDateDTO1.setVin("vin");
        vinsAndServiceDateDTO1.setServiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<VinsAndServiceDateDTO> vinsAndServiceDateDTOS = List.of(vinsAndServiceDateDTO1);
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(vinsAndServiceDateDTOS);

        // Run the test
        final List<VinsAndServiceDateDTO> result = vcsOrderFufilmentDOServiceImplUnderTest.findVinAndServiceDate(
                List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindVinAndServiceDate_VcsOrderFufilmentDOMapperReturnsNoItems() {
        // Setup
        when(mockVcsOrderFufilmentDOMapper.findVinAndServiceDate(List.of("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<VinsAndServiceDateDTO> result = vcsOrderFufilmentDOServiceImplUnderTest.findVinAndServiceDate(
                List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testExtractSubDurationHistory() {
        // Setup
         Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin = Map.ofEntries(
                Map.entry("value", List.of(VcsOrderFufilmentDO.builder()
                        .id(0L)
                        .fufilmentId("fufilmentId")
                        .orderCode("orderCode")
                        .orderItemCode("orderItemCode")
                        .brandCode("brandCode")
                        .carVin("carVin")
                        .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceStatus(0)
                        .build())));
         Map<String, List<SubDurationHistoryVO>> expectedResult = Map.ofEntries(Map.entry("value",
                List.of(new SubDurationHistoryVO(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)))));

        // Run the test
         Map<String, List<SubDurationHistoryVO>> result = vcsOrderFufilmentDOServiceImplUnderTest.extractSubDurationHistory(
                vcsOrderGroupedByCarVin);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testMapCarVinToLatestProductInfo() {
        // Setup
        final Map<String, String> carVinToOrderItemCodeMap = Map.ofEntries(Map.entry("value", "value"));
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode("orderItemCode");
        orderItemBaseVO.setProductVersionCode("productVersionCode");
        orderItemBaseVO.setProductCode("productCode");
        orderItemBaseVO.setProductSkuCode("productSkuCode");
        orderItemBaseVO.setProductName("productName");
        final List<OrderItemBaseVO> latestProductList = List.of(orderItemBaseVO);
        final OrderItemBaseVO orderItemBaseVO1 = new OrderItemBaseVO();
        orderItemBaseVO1.setOrderItemCode("orderItemCode");
        orderItemBaseVO1.setProductVersionCode("productVersionCode");
        orderItemBaseVO1.setProductCode("productCode");
        orderItemBaseVO1.setProductSkuCode("productSkuCode");
        orderItemBaseVO1.setProductName("productName");
        final Map<String, OrderItemBaseVO> expectedResult = Map.ofEntries(Map.entry("value", orderItemBaseVO1));

        // Run the test
        final Map<String, OrderItemBaseVO> result = vcsOrderFufilmentDOServiceImplUnderTest.mapCarVinToLatestProductInfo(
                carVinToOrderItemCodeMap, latestProductList);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testMapCarVinToLatestOrderItemCode() {
        // Setup
         Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin = Map.ofEntries(
                Map.entry("value", List.of(VcsOrderFufilmentDO.builder()
                        .id(0L)
                        .fufilmentId("fufilmentId")
                        .orderCode("orderCode")
                        .orderItemCode("orderItemCode")
                        .brandCode("brandCode")
                        .carVin("carVin")
                        .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceStatus(0)
                        .build())));
         Map<String, String> expectedResult = Map.ofEntries(Map.entry("value", "orderItemCode"));

        // Run the test
         Map<String, String> result = vcsOrderFufilmentDOServiceImplUnderTest.mapCarVinToLatestOrderItemCode(
                vcsOrderGroupedByCarVin);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }


    @Test
    public void testCheckCanBuyServiceForPC() {
        // Setup
        final List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList = List.of(
                new CarVinAndServiceTypeDTO("carVin", 2));
        final CommonResult<Boolean> expectedResult = CommonResult.success(true);

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinAndServiceType(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setCarVin("carVin");
        carServiceStatusVO.setServiceType(1);
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);
        when(mockVcsOrderFufilmentDOMapper.checkActiveServiceByCarVinAndServiceType(
                List.of(new CarVinAndServiceTypeDTO("carVin", 2)))).thenReturn(carServiceStatusVOS);

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .carSystemModel("PIVI")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Configure PIVIUnicomService.checkVinRNRInfo(...).
        final EsimInfoVO esimInfoVO = new EsimInfoVO();
        esimInfoVO.setVin("vin");
        esimInfoVO.setIccid("iccid");
        esimInfoVO.setRealnameFlag("realnameFlag");
        esimInfoVO.setCardState("cardState");
        esimInfoVO.setCardStateTxt("cardStateTxt");
        when(mockPiviUnicomService.checkVinRNRInfo("carVin")).thenReturn(esimInfoVO);

        // Configure SubscriptionServiceMapper.queryByCarVinAndServiceType(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                        .carVin("carVin")
                        .servicePackage("ONLINE-PACK")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .build(),
                SubscriptionServiceDO.builder()
                        .carVin("carVin")
                        .servicePackage("CONNECTED-NAVIGATION")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .build());
        when(mockSubscriptionServiceMapper.queryByCarVinAndServiceType(
                List.of(new CarVinAndServiceTypeDTO("carVin", 3)))).thenReturn(subscriptionServiceDOS);

        // Run the test
        final CommonResult<Boolean> result = vcsOrderFufilmentDOServiceImplUnderTest.checkCanBuyServiceForPC(
                carVinAndServiceTypeList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testExecuteRefundOrderStatusSync() {
        // Setup
        // Configure VcsOrderFufilmentDOMapper.findAllServiceStatus(...).
        final FulfilmentServiceStatusVO fulfilmentServiceStatusVO = new FulfilmentServiceStatusVO();
        fulfilmentServiceStatusVO.setFufilmentId("fufilmentId");
        fulfilmentServiceStatusVO.setOrderCode("orderCode");
        fulfilmentServiceStatusVO.setVcsOrderCode("vcsOrderCode");
        fulfilmentServiceStatusVO.setVofServiceStatus(0);
        fulfilmentServiceStatusVO.setVorServiceStatus(0);
        final List<FulfilmentServiceStatusVO> fulfilmentServiceStatusVOS = List.of(fulfilmentServiceStatusVO);
        when(mockVcsOrderFufilmentDOMapper.findAllServiceStatus("orderCode")).thenReturn(fulfilmentServiceStatusVOS);

        // Run the test
        vcsOrderFufilmentDOServiceImplUnderTest.executeRefundOrderStatusSync("orderCode", "refundOrderCode",
                "fufilmentId");

        // Verify the results
        verify(mockProducerTool).sendMsg(eq(KafkaConstants.ORDER_ROLLBACK_STATUS_UPDATES_TOPIC), eq(""), anyString());
    }

    @Test
    public void testProcessTsdpFailedTasks() {
        // Setup
        // Configure InterBusiErrorLogMapper.selectList(...).
        final InterBusiErrorLogDO interBusiErrorLogDO = new InterBusiErrorLogDO();
        interBusiErrorLogDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        interBusiErrorLogDO.setIsDeleted(false);
        interBusiErrorLogDO.setId(0);
        interBusiErrorLogDO.setBusinessId("businessId");
        interBusiErrorLogDO.setBusinessType("businessType");
        interBusiErrorLogDO.setBusinesParams("{}");
        final List<InterBusiErrorLogDO> interBusiErrorLogDOS = List.of(interBusiErrorLogDO);
        when(mockInterBusiErrorLogMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(interBusiErrorLogDOS);

        // Configure RestTemplate.postForEntity(...).
        final ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(new JSONObject(0, false), HttpStatus.OK);
        when(mockRestTemplate.postForEntity(anyString(), any(HttpEntity.class),
                eq(JSONObject.class))).thenReturn(responseEntity);

        // Run the test
        final Integer result = vcsOrderFufilmentDOServiceImplUnderTest.processTsdpFailedTasks(0);

        // Verify the results
        assertEquals(Integer.valueOf(1), result);
        verify(mockInterBusiErrorLogMapper).deleteBatchIds(anyList());
    }

    @Test
    public void testExecuteOrderStatusSync2() {
        // Setup
        final List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList = List.of(VcsOrderFufilmentDO.builder()
                .id(0L)
                .fufilmentId("fufilmentId")
                .orderCode("orderCode")
                .vcsOrderCode("vcsOrderCode")
                .orderItemCode("orderItemCode")
                .brandCode("brandCode")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceStatus(0)
                .serviceType(0)
                .build());

        // Configure VcsOrderFufilmentDOMapper.findAllServiceStatus(...).
        final FulfilmentServiceStatusVO fulfilmentServiceStatusVO = new FulfilmentServiceStatusVO();
        fulfilmentServiceStatusVO.setFufilmentId("fufilmentId");
        fulfilmentServiceStatusVO.setOrderCode("orderCode");
        fulfilmentServiceStatusVO.setVcsOrderCode("vcsOrderCode");
        fulfilmentServiceStatusVO.setVofServiceStatus(0);
        fulfilmentServiceStatusVO.setVorServiceStatus(0);
        final List<FulfilmentServiceStatusVO> fulfilmentServiceStatusVOS = List.of(fulfilmentServiceStatusVO);
        when(mockVcsOrderFufilmentDOMapper.findAllServiceStatus("orderCode")).thenReturn(fulfilmentServiceStatusVOS);

        // Run the test
        vcsOrderFufilmentDOServiceImplUnderTest.executeOrderStatusSync(vcsOrderFufilmentDOList);

        // Verify the results
        verify(mockProducerTool).sendMsg(eq(KafkaConstants.ORDER_COMPLETE_STATUS_UPDATES_TOPIC), eq(""), anyString());
    }

    @Test
    public void testCheckCanBuyServiceV2() {
        // Setup
        final List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList = List.of(
                new CarVinAndServiceTypeDTO("carVin", 2));

        // Configure VcsOrderFufilmentDOMapper.checkActiveServiceByCarVinAndServiceType(...).
        final CarServiceStatusVO carServiceStatusVO = new CarServiceStatusVO();
        carServiceStatusVO.setCarVin("carVin");
        carServiceStatusVO.setServiceType(0);
        carServiceStatusVO.setActiveCount(0);
        carServiceStatusVO.setRollbackActiveCount(0);
        final List<CarServiceStatusVO> carServiceStatusVOS = List.of(carServiceStatusVO);
        when(mockVcsOrderFufilmentDOMapper.checkActiveServiceByCarVinAndServiceType(
                List.of(new CarVinAndServiceTypeDTO("carVin", 2)))).thenReturn(carServiceStatusVOS);

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .carSystemModel("PIVI")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Configure SubscriptionServiceMapper.queryByCarVinAndServiceType(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                        .carVin("carVin")
                        .servicePackage("ONLINE-PACK")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .build(),
                SubscriptionServiceDO.builder()
                        .carVin("carVin")
                        .servicePackage("CONNECTED-NAVIGATION")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .build());
        when(mockSubscriptionServiceMapper.queryByCarVinAndServiceType(
                List.of(new CarVinAndServiceTypeDTO("carVin", 3)))).thenReturn(subscriptionServiceDOS);

        // Run the test
        final Boolean result = vcsOrderFufilmentDOServiceImplUnderTest.checkCanBuyServiceV2(carVinAndServiceTypeList);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testCheckStatus_WithActivateFailure() {
        // Arrange
        String orderCode = "ORDER123";
        List<VcsOrderFufilmentDO> mockList = Arrays.asList(
                new VcsOrderFufilmentDO().setServiceStatus(FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus())
        );

        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(mockList);

        // Act
        boolean result = vcsOrderFufilmentDOServiceImplUnderTest.checkStatus(orderCode);

        // Assert
        assertTrue(result);
        verify(mockVcsOrderFufilmentDOMapper).selectList(any(LambdaQueryWrapperX.class));
    }

    @Test
    public void testCheckStatus_NoActivateFailure() {
        // Arrange
        String orderCode = "ORDER123";
        List<VcsOrderFufilmentDO> mockList = Arrays.asList(
                new VcsOrderFufilmentDO().setServiceStatus(FufilmentServiceStatusEnum.ACTIVATED.getStatus())
        );

        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(mockList);

        // Act
        boolean result = vcsOrderFufilmentDOServiceImplUnderTest.checkStatus(orderCode);

        // Assert
        assertFalse(result);
        verify(mockVcsOrderFufilmentDOMapper).selectList(any(LambdaQueryWrapperX.class));
    }

    @Test
    public void testCheckStatus_EmptyList() {
        // Arrange
        String orderCode = "ORDER123";

        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Act
        boolean result = vcsOrderFufilmentDOServiceImplUnderTest.checkStatus(orderCode);

        // Assert
        assertFalse(result);
        verify(mockVcsOrderFufilmentDOMapper).selectList(any(LambdaQueryWrapperX.class));
    }

    @Test
    public void testGetFailOrders_WithValidData() {
        // Arrange
        List<VcsOrderFufilmentDO> fufilmentList = new ArrayList<>(Arrays.asList(
                new VcsOrderFufilmentDO().setFufilmentId("FUL1").setOrderCode("ORDER1"),
                new VcsOrderFufilmentDO().setFufilmentId("FUL2").setOrderCode("ORDER2")
        ));

        List<String> rollbackIds = Arrays.asList("FUL2");

        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(fufilmentList);
        when(mockVcsOrderRollbackDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(rollbackIds.stream()
                        .map(id -> new VcsOrderRollbackDO().setFufilmentId(id))
                        .collect(Collectors.toList()));

        // Act
        Set<String> result = vcsOrderFufilmentDOServiceImplUnderTest.getFailOrders();

        // Assert
        assertEquals(1, result.size());
        assertTrue(result.contains("ORDER1"));
        verify(mockVcsOrderFufilmentDOMapper).selectList(any(LambdaQueryWrapperX.class));
        verify(mockVcsOrderRollbackDOMapper).selectList(any(LambdaQueryWrapperX.class));
    }

    @Test
    public void testGetFailOrders_EmptyList() {
        // Arrange
        when(mockVcsOrderFufilmentDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Act
        Set<String> result = vcsOrderFufilmentDOServiceImplUnderTest.getFailOrders();

        // Assert
        assertTrue(result.isEmpty());
        verify(mockVcsOrderFufilmentDOMapper).selectList(any(LambdaQueryWrapperX.class));
    }


}
