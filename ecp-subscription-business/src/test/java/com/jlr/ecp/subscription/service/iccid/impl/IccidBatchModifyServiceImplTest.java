package com.jlr.ecp.subscription.service.iccid.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.IccidBatchModifyPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.iccid.SendICCIDBatchModifyDTO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.iccid.IccidBatchModifyUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.iccid.IccidModifyRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.excel.pojo.iccid.IccidBatchModifyExcel;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.properties.IccidProperties;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IccidBatchModifyServiceImplTest {

    @Mock
    private IccidProperties mockIccidProperties;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private IccidModifyBatchRecordsDOMapper mockIccidBatchModifyRecordsMapper;
    @Mock
    private FileService mockFileService;
    @Mock
    private Redisson mockRedisson;
    @Mock
    private RLock mockLock;
    @Mock
    private ApplicationContext mockApplicationContext;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private IccidModifyRecordsDOMapper mockIccidModifyRecordsMapper;

    @InjectMocks
    private IccidBatchModifyServiceImpl iccidBatchModifyServiceImplUnderTest;

    @Before
    public void setUp() throws InterruptedException {
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
    }

    @Test
    public void testBatchModifyIccid() throws Exception {
        // Setup
        final SendICCIDBatchModifyDTO sendICCIDBatchModifyDTO = new SendICCIDBatchModifyDTO();
        sendICCIDBatchModifyDTO.setBatchNo("1");

        final CommonResult<String> expectedResult = CommonResult.success("1:iccidBatchModifyJob");

        // Configure IccidModifyBatchRecordsDOMapper.selectList(...).
        final List<IccidModifyBatchRecordsDO> iccidModifyBatchRecordsDOS = List.of(IccidModifyBatchRecordsDO.builder()
                .id(0L)
                .batchNo(1L)
                .uploadFile("uploadFile")
                .verifyResult(0)
                .dealStatus(0)
                .verifyResultFile("errorDetailPath")
                .operator("operator")
                .build());

        // 捕获传入 selectList 的参数
        ArgumentCaptor<LambdaQueryWrapper<IccidModifyBatchRecordsDO>> queryWrapperCaptor = ArgumentCaptor.forClass(LambdaQueryWrapper.class);

        AtomicInteger count = new AtomicInteger(0);
        // Configure AppDCuRenewBatchRecordsMapper.selectList(...).
        // 模拟 queryAppDUcBatchByDealStatus 返回空列表
        when(mockIccidBatchModifyRecordsMapper.selectList(queryWrapperCaptor.capture())).thenAnswer(invocation -> {
            int increment = count.get();
            if (increment == 0) {
                count.getAndIncrement();
                return Collections.emptyList();
            } else  {
                return iccidModifyBatchRecordsDOS;
            }
        });


        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(IccidBatchModifyServiceImpl.class)).thenReturn(iccidBatchModifyServiceImplUnderTest);

        when(mockFileService.getFileContentByAllPath("AWS_S3_FILE", "uploadFile")).thenReturn(createExcelContent());

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("vin")
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(piviPackageDOS);

        // Configure IccidModifyRecordsDOMapper.selectOne(...).
        final IccidModifyRecordsDO iccidModifyRecordsDO = IccidModifyRecordsDO.builder()
                .modifyNo(0L)
                .carVin("carVin")
                .modifyBeforeIccid("modifyBeforeIccid")
                .modifyAfterIccid("modifyAfterIccid")
                .modifyStatus(0)
                .dataSource(0)
                .errorDesc("errorDesc")
                .build();
        when(mockIccidModifyRecordsMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(iccidModifyRecordsDO);

        // Run the test
        final CommonResult<String> result = iccidBatchModifyServiceImplUnderTest.batchModifyIccid(
                sendICCIDBatchModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockIccidBatchModifyRecordsMapper).updateById(any(IccidModifyBatchRecordsDO.class));
        verify(mockIccidModifyRecordsMapper).insertBatch(anyList());
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");
        Cell appDHeader = headerRow.createCell(1);
        appDHeader.setCellValue("新ICCID");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM82633A12"); // 示例 VIN
        dataRow.createCell(1).setCellValue("1234567890");

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }

    @Test
    public void testBatchModifyIccid_AMAP_TASK_LINE_UP() {
        // Setup
        final SendICCIDBatchModifyDTO sendICCIDBatchModifyDTO = new SendICCIDBatchModifyDTO();
        sendICCIDBatchModifyDTO.setBatchNo("1");

        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.AMAP_TASK_LINE_UP);

        // Configure IccidModifyBatchRecordsDOMapper.selectList(...).
        final List<IccidModifyBatchRecordsDO> iccidModifyBatchRecordsDOS = List.of(IccidModifyBatchRecordsDO.builder()
                .id(0L)
                .batchNo(1L)
                .uploadFile("uploadFile")
                .verifyResult(0)
                .dealStatus(0)
                .verifyResultFile("errorDetailPath")
                .operator("operator")
                .build());

        when(mockIccidBatchModifyRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(iccidModifyBatchRecordsDOS);

        // Run the test
        final CommonResult<String> result = iccidBatchModifyServiceImplUnderTest.batchModifyIccid(
                sendICCIDBatchModifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchInsertIccidModifyRecords() {
        // Setup
        final List<IccidBatchModifyExcel> batchModifyExcels = List.of(IccidBatchModifyExcel.builder()
                .carVin("VIN")
                .newIccid("modifyAfterIccid")
                .build());

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .iccid("iccid")
                .vin("VIN")
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(piviPackageDOS);

        // Run the test
        iccidBatchModifyServiceImplUnderTest.batchInsertIccidModifyRecords(batchModifyExcels, 0L, "operator");

        // Verify the results
        verify(mockIccidModifyRecordsMapper).insertBatch(anyList());
    }

    @Test
    public void testQueryBatchModifyPageList() {
        // Setup
        final IccidBatchModifyPageDTO pageDto = new IccidBatchModifyPageDTO();
        pageDto.setPageNo(0);
        pageDto.setPageSize(0);
        pageDto.setOperateTimeSort("operateTimeSort");

        final PageResult<IccidBatchModifyPageVO> expectedResult = new PageResult<>(
                List.of(IccidBatchModifyPageVO.builder()
                        .operateTime("")
                        .operator("operator")
                        .checkResultDesc("")
                        .dealStatus(0)
                        .batchNo(0L)
                        .build()), 0L);
        Page<IccidModifyBatchRecordsDO> page = new Page<>();
        page.setRecords(List.of(IccidModifyBatchRecordsDO.builder()
                .operator("operator")
                .dealStatus(0)
                .batchNo(0L)
                .build()
        ));
        when(mockIccidBatchModifyRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<IccidBatchModifyPageVO> result = iccidBatchModifyServiceImplUnderTest.queryBatchModifyPageList(
                pageDto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetIccidTemplateUrl() {
        // Setup
        when(mockIccidProperties.getModifyExcelUrl()).thenReturn("modifyExcelUrl");

        // Run the test
        final String result = iccidBatchModifyServiceImplUnderTest.getIccidTemplateUrl();

        // Verify the results
        assertEquals("modifyExcelUrl", result);
    }

    @Test
    public void testUploadIccidExcelModify() {
        // Setup
        final MultipartFile multipartFile = new MockMultipartFile("name", createExcelContent());
        final CommonResult<IccidBatchModifyUploadVO> expectedResult = CommonResult.success(
                IccidBatchModifyUploadVO.builder()
                        .type("success")
                        .msg("请关注修改文件校验结果")
                        .build());
        when(mockSnowflake.nextId()).thenReturn(0L);

        // Run the test
        final CommonResult<IccidBatchModifyUploadVO> result = iccidBatchModifyServiceImplUnderTest.uploadIccidExcelModify(
                multipartFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockIccidBatchModifyRecordsMapper).insert(any(IccidModifyBatchRecordsDO.class));
    }
}
