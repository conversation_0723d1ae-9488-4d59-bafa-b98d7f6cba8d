package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataRecordsMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DmsOssOriginalDataRecordsServiceImplTest {

    @Mock
    private DmsOssOriginalDataRecordsMapper mockDmsOssOriginalDataRecordsMapper;

    @InjectMocks
    private DmsOssOriginalDataRecordsServiceImpl dmsOssOriginalDataRecordsServiceImplUnderTest;

    @Test
    public void testInsertBatch() {
        // Setup
        final List<DmsOssOriginalDataRecordsDO> recordsDOList = List.of(DmsOssOriginalDataRecordsDO.builder().build());

        // Run the test
        dmsOssOriginalDataRecordsServiceImplUnderTest.insertBatch(recordsDOList);

        // Verify the results
        verify(mockDmsOssOriginalDataRecordsMapper).insertBatch(List.of(DmsOssOriginalDataRecordsDO.builder().build()));
    }

    @Test
    public void testQueryByVinSet() {
        // Setup
        final List<DmsOssOriginalDataRecordsDO> expectedResult = List.of(DmsOssOriginalDataRecordsDO.builder().build());

        // Configure DmsOssOriginalDataRecordsMapper.queryByVinSet(...).
        final List<DmsOssOriginalDataRecordsDO> dmsOssOriginalDataRecordsDOS = List.of(
                DmsOssOriginalDataRecordsDO.builder().build());
        when(mockDmsOssOriginalDataRecordsMapper.queryByVinSet(Set.of("value")))
                .thenReturn(dmsOssOriginalDataRecordsDOS);

        // Run the test
        final List<DmsOssOriginalDataRecordsDO> result = dmsOssOriginalDataRecordsServiceImplUnderTest.queryByVinSet(
                Set.of("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryByVinSet_DmsOssOriginalDataRecordsMapperReturnsNoItems() {
        // Setup
        when(mockDmsOssOriginalDataRecordsMapper.queryByVinSet(Set.of("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DmsOssOriginalDataRecordsDO> result = dmsOssOriginalDataRecordsServiceImplUnderTest.queryByVinSet(
                Set.of("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testUpdateBatch() {
        // Setup
        final List<DmsOssOriginalDataRecordsDO> recordsDOList = List.of(DmsOssOriginalDataRecordsDO.builder().build());

        // Run the test
        dmsOssOriginalDataRecordsServiceImplUnderTest.updateBatch(recordsDOList);

        // Verify the results
        verify(mockDmsOssOriginalDataRecordsMapper).updateBatch(List.of(DmsOssOriginalDataRecordsDO.builder().build()));
    }

    @Test
    public void testUpdate() {
        // Setup
        final DmsOssOriginalDataRecordsDO dataRecordsDO = DmsOssOriginalDataRecordsDO.builder().build();

        // Run the test
        dmsOssOriginalDataRecordsServiceImplUnderTest.update(dataRecordsDO);

        // Verify the results
        verify(mockDmsOssOriginalDataRecordsMapper).updateById(DmsOssOriginalDataRecordsDO.builder().build());
    }

    @Test
    public void testInsert() {
        // Setup
        final DmsOssOriginalDataRecordsDO dataRecordsDO = DmsOssOriginalDataRecordsDO.builder().build();

        // Run the test
        dmsOssOriginalDataRecordsServiceImplUnderTest.insert(dataRecordsDO);

        // Verify the results
        verify(mockDmsOssOriginalDataRecordsMapper).insert(DmsOssOriginalDataRecordsDO.builder().build());
    }
}
