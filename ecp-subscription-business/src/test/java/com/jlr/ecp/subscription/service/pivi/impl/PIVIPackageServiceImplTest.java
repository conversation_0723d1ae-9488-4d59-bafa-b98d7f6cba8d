package com.jlr.ecp.subscription.service.pivi.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PIVIPackageServiceImplTest {

    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;

    @InjectMocks
    private PIVIPackageServiceImpl piviPackageServiceImplUnderTest;

    @Test
    public void testInsertBatch() {
        // Setup
        final List<PIVIPackageDO> packageDOList = List.of(PIVIPackageDO.builder()
                .vin("vin")
                .build());

        // Run the test
        piviPackageServiceImplUnderTest.insertBatch(packageDOList);

        // Verify the results
        verify(mockPiviPackageDOMapper).insertBatch(List.of(PIVIPackageDO.builder()
                .vin("vin")
                .build()));
    }

    @Test
    public void testQueryByVinList() {
        // Setup
        final List<PIVIPackageDO> expectedResult = List.of(PIVIPackageDO.builder()
                .vin("vin")
                .build());

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .vin("vin")
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(piviPackageDOS);

        // Run the test
        final List<PIVIPackageDO> result = piviPackageServiceImplUnderTest.queryByVinList(List.of("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryByVinList_PIVIPackageDOMapperReturnsNoItems() {
        // Setup
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PIVIPackageDO> result = piviPackageServiceImplUnderTest.queryByVinList(List.of("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryByVin() {
        // Setup
        final PIVIPackageDO expectedResult = PIVIPackageDO.builder()
                .vin("vin")
                .build();

        // Configure PIVIPackageDOMapper.selectOne(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .vin("vin")
                .build();
        when(mockPiviPackageDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDO);

        // Run the test
        final PIVIPackageDO result = piviPackageServiceImplUnderTest.queryByVin("vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testInsert() {
        // Setup
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .vin("vin")
                .build();

        // Run the test
        piviPackageServiceImplUnderTest.insert(piviPackageDO);

        // Verify the results
        verify(mockPiviPackageDOMapper).insert(PIVIPackageDO.builder()
                .vin("vin")
                .build());
    }
}
