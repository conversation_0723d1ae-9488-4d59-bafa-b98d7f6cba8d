package com.jlr.ecp.subscription.service.amap.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.amap.AmaPRenewalQueryPageDTO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalQueryPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.amap.AmaPRenewalStatusVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewBatchRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewBatchRecordsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.amap.AmaPRenewRecordsDOMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AmaPManualRenewalQueryServiceImplTest {

    @Mock
    private AmaPRenewBatchRecordsDOMapper mockAmaPRenewBatchRecordsDOMapper;
    @Mock
    private AmaPRenewRecordsDOMapper mockAmaPRenewRecordsDOMapper;

    @InjectMocks
    private AmaPManualRenewalQueryServiceImpl amaPManualRenewalQueryServiceImplUnderTest;

    @Test
    public void testQueryAmaPRenewalBatchNo() {
        // Setup
        // Configure AmaPRenewBatchRecordsDOMapper.selectList(...).
        final List<AmaPRenewBatchRecordsDO> amaPRenewBatchRecordsDOS = List.of(AmaPRenewBatchRecordsDO.builder()
                .batchNo(0L)
                .build());
        when(mockAmaPRenewBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(amaPRenewBatchRecordsDOS);

        // Configure AmaPRenewRecordsDOMapper.selectList(...).
        final List<AmaPRenewRecordsDO> amaPRenewRecordsDOS = List.of(AmaPRenewRecordsDO.builder()
                .id(0L)
                .carVin("carVin")
                .renewYear(2020)
                .renewNo(0L)
                .operator("operator")
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .queryResultCode("queryResultCode")
                .build());

        // Run the test
        final String result = amaPManualRenewalQueryServiceImplUnderTest.queryAmaPRenewalBatchNo("0");

        // Verify the results
        assertEquals("0", result);
    }

    @Test
    public void testQueryAmaPRenewalBatchNo_AmaPRenewBatchRecordsDOMapperReturnsNoItems() {
        // Setup
        when(mockAmaPRenewBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure AmaPRenewRecordsDOMapper.selectList(...).
        final List<AmaPRenewRecordsDO> amaPRenewRecordsDOS = List.of(AmaPRenewRecordsDO.builder()
                .id(0L)
                .carVin("carVin")
                .renewYear(2020)
                .renewNo(0L)
                .operator("operator")
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .queryResultCode("queryResultCode")
                .build());
        when(mockAmaPRenewRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(amaPRenewRecordsDOS);

        // Run the test
        final String result = amaPManualRenewalQueryServiceImplUnderTest.queryAmaPRenewalBatchNo("0");

        // Verify the results
        assertEquals("0", result);
    }

    @Test
    public void testQueryAmaPRenewalBatchNo_AmaPRenewRecordsDOMapperReturnsNoItems() {
        // Setup
        when(mockAmaPRenewBatchRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(mockAmaPRenewRecordsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final String result = amaPManualRenewalQueryServiceImplUnderTest.queryAmaPRenewalBatchNo("0");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testQueryAmaPRenewalStatus() {
        // Setup
        final List<AmaPRenewalStatusVO> expectedResult = List.of(AmaPRenewalStatusVO.builder()
                .renewalStatusCode(0)
                .renewalStatusDesc("进行中")
                .build(),
                AmaPRenewalStatusVO.builder()
                        .renewalStatusCode(2)
                        .renewalStatusDesc("成功")
                        .build(),
                AmaPRenewalStatusVO.builder()
                        .renewalStatusCode(3)
                        .renewalStatusDesc("失败")
                        .build());

        // Run the test
        final List<AmaPRenewalStatusVO> result = amaPManualRenewalQueryServiceImplUnderTest.queryAmaPRenewalStatus();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryRenewalRecordPageList() {
        // Setup
        final AmaPRenewalQueryPageDTO queryPageDTO = new AmaPRenewalQueryPageDTO();
        queryPageDTO.setPageNo(0);
        queryPageDTO.setPageSize(0);
        queryPageDTO.setCarVin("carVin");
        queryPageDTO.setBatchNoList(List.of("0"));
        queryPageDTO.setOperateStartTime("2020/01/01");
        queryPageDTO.setOperateEndTime("2021/01/01");
        queryPageDTO.setRenewalStatus(0);
        queryPageDTO.setOperator("operator");
        queryPageDTO.setRenewalYearTimeSort("ASC");
        queryPageDTO.setOperateTimeSort("ASC");

        final PageResult<AmaPRenewalQueryPageVO> expectedResult = new PageResult<>(
                List.of(AmaPRenewalQueryPageVO.builder()
                        .batchNo("0")
                        .carVin("carVin")
                        .renewalStatus(0)
                        .renewalStatusDesc("进行中")
                        .renewalYearCode(1)
                        .renewalYearDesc("一年")
                        .renewalBeforeExpiryDate("2020/01/01")
                        .renewalAfterExpiryDate("2020/01/01")
                        .operateTime("2020/01/01 00:00:00")
                        .operator("operator")
                        .errorDesc("")
                        .build()), 1L);
        Page<AmaPRenewRecordsDO> page = new Page<>();
        AmaPRenewRecordsDO build = AmaPRenewRecordsDO.builder()
                .renewNo(0L)
                .carVin("carVin")
                .renewStatus(0)
                .renewYear(1)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .operator("operator")
                .queryResultCode("200").build();
        build.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        page.setRecords(List.of(build));
        page.setTotal(1L);
        when(mockAmaPRenewRecordsDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<AmaPRenewalQueryPageVO> result = amaPManualRenewalQueryServiceImplUnderTest.queryRenewalRecordPageList(
                queryPageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
