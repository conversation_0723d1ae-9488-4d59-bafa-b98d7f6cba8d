package com.jlr.ecp.subscription.service.oss;

import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleConfigGroupDO;
import com.jlr.ecp.subscription.dal.mysql.vehicle.VehicleConfigGroupMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VehicleConfigGroupServiceImplTest {

    @Mock
    private VehicleConfigGroupMapper mockVehicleConfigGroupMapper;

    @InjectMocks
    private VehicleConfigGroupServiceImpl vehicleConfigGroupServiceImplUnderTest;

    @Test
    public void testGetByConfigCode() {
        // Setup
        final VehicleConfigGroupDO expectedResult = VehicleConfigGroupDO.builder()
                .configCode("configCode")
                .build();

        // Configure VehicleConfigGroupMapper.selectOne(...).
        final VehicleConfigGroupDO vehicleConfigGroupDO = VehicleConfigGroupDO.builder()
                .configCode("configCode")
                .build();
        when(mockVehicleConfigGroupMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vehicleConfigGroupDO);

        // Run the test
        final VehicleConfigGroupDO result = vehicleConfigGroupServiceImplUnderTest.getByConfigCode("configCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
