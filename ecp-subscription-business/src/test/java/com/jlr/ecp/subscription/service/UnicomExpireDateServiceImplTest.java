package com.jlr.ecp.subscription.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.SimCardInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.UnicomCheckDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.vehicle.UnicomCheckMapper;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnicomExpireDateServiceImplTest {

    @Mock
    private UnicomCheckMapper mockUnicomCheckMapper;
    @Mock
    private RedisTemplate<String, Integer> mockRedisTemplate;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private PIVIUnicomService mockPiviUnicomService;

    @InjectMocks
    private UnicomExpireDateServiceImpl unicomExpireDateServiceImplUnderTest;

    @Test
    public void testGetUnicomExpireDate() {
        Page<UnicomCheckDO> piviUnicomCheckDOPage = new Page<>();
        piviUnicomCheckDOPage.setRecords(List.of(UnicomCheckDO.builder()
                .iccid("iccid")
                .tenantId(1) // 设置 tenantId
                .build()));
        // Setup
        when(mockUnicomCheckMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(piviUnicomCheckDOPage);
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        unicomRespData.setSimCardInfo(simCardInfo);
        final ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setExternalBookId("extBookId");
        productBookInfo.setBookStatus("active");
        productBookInfo.setActiveTime("123456");
        productBookInfo.setExpireTime("123456");
        unicomRespData.setProductBookInfoList(List.of(productBookInfo));
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUnicomService.getSimCardInfo("iccid")).thenReturn(unicomRespVO);

        // 配置 RedisTemplate
        ValueOperations<String, Integer> valueOperations = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);

        // Run the test
        unicomExpireDateServiceImplUnderTest.getUnicomExpireDate(1, 2, 10);

        // Verify the results
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
        verify(mockUnicomCheckMapper).updateBatch(anyList());
    }

}
