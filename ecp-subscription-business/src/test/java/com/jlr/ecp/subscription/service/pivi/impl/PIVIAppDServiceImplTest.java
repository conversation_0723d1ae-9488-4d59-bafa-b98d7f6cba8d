package com.jlr.ecp.subscription.service.pivi.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.model.dto.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PIVIAppDServiceImplTest {

    @Mock
    private VcsOrderFulfilmentCallMapper mockCallMapper;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private RestTemplate mockRestTemplateAPPD;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private ApplicationContext mockApplicationContext;
    @Mock
    private Redisson mockRedisson;

    @InjectMocks
    private PIVIAppDServiceImpl piviAppDServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(piviAppDServiceImplUnderTest, "tokenUrl", "tokenUrl");
        ReflectionTestUtils.setField(piviAppDServiceImplUnderTest, "subUrl", "subUrl");
        ReflectionTestUtils.setField(piviAppDServiceImplUnderTest, "getUrl", "getUrl");
        ReflectionTestUtils.setField(piviAppDServiceImplUnderTest, "grantType", "grantType");
        ReflectionTestUtils.setField(piviAppDServiceImplUnderTest, "clientId", "clientId");
        ReflectionTestUtils.setField(piviAppDServiceImplUnderTest, "clientSecret", "clientSecret");
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
    }

    @Test
    public void testCallAppDService() {
        // Setup
        final BaseMessage message = new BaseMessage();
        message.setMessageId("messageId");
        message.setTenantId(0L);
        message.setOrderCode("orderCode");
        message.setVin("vin");
        message.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIAppDServiceImpl.class)).thenReturn(piviAppDServiceImplUnderTest);

        when(mockRedisService.getCacheObject("APPD_ACCESS_TOKEN")).thenReturn(null);

        // Configure RestTemplate.exchange(...).
        AccessTokenResp accessTokenResp = new AccessTokenResp();
        accessTokenResp.setAccessToken("accessToken");
        accessTokenResp.setExpiresIn(10000);
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(JSONObject.parseObject(JSON.toJSONString(accessTokenResp)),
                HttpStatus.OK);
        when(mockRestTemplateAPPD.exchange(anyString(), eq(HttpMethod.POST),
                any(HttpEntity.class), eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

        when(mockLock.tryLock()).thenReturn(true);

        final ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(JSONObject.parseObject("{}"),
                HttpStatus.OK);
        when(mockRestTemplateAPPD.exchange(anyString(), eq(HttpMethod.PUT),
                any(HttpEntity.class), eq(JSONObject.class))).thenReturn(responseEntity);

        // Run the test
        final boolean result = piviAppDServiceImplUnderTest.callAppDService(message, "fulfilmentId", 0L);

        // Verify the results
        assertTrue(result);
        verify(mockRedisService).setCacheObject("APPD_ACCESS_TOKEN", "accessToken", 9400L, TimeUnit.SECONDS);
        verify(mockCallMapper).insert(any(VcsOrderFufilmentCall.class));
    }

    @Test
    public void testAppDManualRenewal() {
        // Setup
        final CommonResult<VcsOrderFufilmentCall> expectedResult = CommonResult.success(VcsOrderFufilmentCall.builder()
                .requestParam("")
                .activationStatus(1)
                .build());

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIAppDServiceImpl.class)).thenReturn(piviAppDServiceImplUnderTest);

        when(mockRedisService.getCacheObject("APPD_ACCESS_TOKEN")).thenReturn("accessToken");

        // Configure RestTemplate.exchange(...).
        when(mockLock.tryLock()).thenReturn(true);

        final ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(JSONObject.parseObject("{}"),
                HttpStatus.OK);
        when(mockRestTemplateAPPD.exchange(anyString(), eq(HttpMethod.PUT),
                any(HttpEntity.class), eq(JSONObject.class))).thenReturn(responseEntity);

        // Run the test
        final CommonResult<VcsOrderFufilmentCall> result = piviAppDServiceImplUnderTest.appDManualRenewal("vin",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(expectedResult.getData().getActivationStatus(), result.getData().getActivationStatus());
    }

    @Test
    public void testGetVinSubscriptions() {
        // Setup
        final AppDSubscriptionResp expectedResult = new AppDSubscriptionResp();
        expectedResult.setQueryResult("请求异常, null");
        expectedResult.setResultCode(2);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIAppDServiceImpl.class)).thenReturn(piviAppDServiceImplUnderTest);

        when(mockRedisService.getCacheObject("APPD_ACCESS_TOKEN")).thenReturn("accessToken");

        // Configure RestTemplate.exchange(...).
        final AppDSubscriptionData appDSubscriptionData1 = new AppDSubscriptionData();
        appDSubscriptionData1.setJlrSubscriptionId(0L);
        appDSubscriptionData1.setSubscriptionId("subscriptionId");
        appDSubscriptionData1.setSubscriberUniqueId("subscriberUniqueId");
        final AppDAttribute appDAttribute = new AppDAttribute();
        appDAttribute.setAttributeName("attributeName");
        appDSubscriptionData1.setAttributes(List.of(appDAttribute));

        when(mockRestTemplateAPPD.exchange(anyString(), eq(HttpMethod.GET),
                any(HttpEntity.class), any(ParameterizedTypeReference.class), anyString())).thenThrow(RestClientException.class);

        // Run the test
        final AppDSubscriptionResp result = piviAppDServiceImplUnderTest.getVinSubscriptions("vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetVinSubscriptions_RestTemplateExchange2ReturnsNoItems() {
        // Setup
        final AppDSubscriptionResp expectedResult = new AppDSubscriptionResp();
        expectedResult.setQueryResult("请求异常, 获取jlrSubscriptionId时accessToken为空");
        expectedResult.setResultCode(3);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIAppDServiceImpl.class)).thenReturn(piviAppDServiceImplUnderTest);

        when(mockRedisService.getCacheObject("APPD_ACCESS_TOKEN")).thenReturn(null);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(PIVIAppDServiceImpl.class)).thenReturn(piviAppDServiceImplUnderTest);

        when(mockRedisService.getCacheObject("APPD_ACCESS_TOKEN")).thenReturn(null);

        // Configure RestTemplate.exchange(...).
        AccessTokenResp accessTokenResp = new AccessTokenResp();
        accessTokenResp.setAccessToken(null);
        accessTokenResp.setExpiresIn(10000);
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(JSONObject.parseObject(JSON.toJSONString(accessTokenResp)),
                HttpStatus.OK);
        when(mockRestTemplateAPPD.exchange(anyString(), eq(HttpMethod.POST),
                any(HttpEntity.class), eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

        // Run the test
        final AppDSubscriptionResp result = piviAppDServiceImplUnderTest.getVinSubscriptions("vin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSubscriptionRequests_RestTemplateThrowsRestClientException() {
        // Setup
        final AppDSubscriptionReq request = new AppDSubscriptionReq();
        request.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        request.setJlrSubscriptionId(0L);
        request.setSubscriptionEvent("event");
        request.setSubscriptionEventDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure RestTemplate.exchange(...).
        final AppDSubscriptionReq appDSubscriptionReq = new AppDSubscriptionReq();
        appDSubscriptionReq.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionReq.setJlrSubscriptionId(0L);
        appDSubscriptionReq.setSubscriptionEvent("event");
        appDSubscriptionReq.setSubscriptionEventDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HttpEntity<AppDSubscriptionReq> requestEntity = new HttpEntity<>(appDSubscriptionReq, new HttpHeaders());
        when(mockRestTemplateAPPD.exchange(anyString(), eq(HttpMethod.PUT),
                any(HttpEntity.class), eq(JSONObject.class))).thenThrow(RestClientException.class);

        // Run the test
        assertThrows(RestClientException.class,
                () -> piviAppDServiceImplUnderTest.subscriptionRequests("accessToken", request));
    }

    @Test
    public void testSubscriptionRequestsFallback() {
        // Setup
        final AppDSubscriptionReq request = new AppDSubscriptionReq();
        request.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        request.setJlrSubscriptionId(0L);
        request.setSubscriptionEvent("event");
        request.setSubscriptionEventDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final VcsOrderFufilmentCall expectedResult = VcsOrderFufilmentCall.builder()
                .activationFailedMsg("message")
                .build();

        // Run the test
        final VcsOrderFufilmentCall result = piviAppDServiceImplUnderTest.subscriptionRequestsFallback(
                new Exception("message"), "accessToken", request);

        // Verify the results
        assertEquals(expectedResult.getActivationFailedMsg(), result.getActivationFailedMsg());
    }

    @Test
    public void testGetAccessTokenFallback() {
        assertEquals(null, piviAppDServiceImplUnderTest.getAccessTokenFallback(new Exception("message")));
    }
}
