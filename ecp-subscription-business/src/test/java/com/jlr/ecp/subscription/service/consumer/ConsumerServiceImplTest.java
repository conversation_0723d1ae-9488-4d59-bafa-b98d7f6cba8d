package com.jlr.ecp.subscription.service.consumer;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.consumer.dto.ConsumerIncontrolDTO;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerIncontrolDO;
import com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerIncontrolMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ConsumerServiceImplTest {

    @Mock
    private ConsumerIncontrolMapper mockConsumerIncontrolMapper;

    private ConsumerServiceImpl consumerServiceImplUnderTest;

    @Before
    public void setUp() {
        consumerServiceImplUnderTest = new ConsumerServiceImpl();
        consumerServiceImplUnderTest.consumerIncontrolMapper = mockConsumerIncontrolMapper;
    }

    @Test
    public void testBindIRC() {
        // Setup
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode("consumerCode");
        consumerIncontrolDTO.setIncontrolId("incontrolId");
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> expectedResult = CommonResult.success(consumerIncontrolDTO);

        // Configure ConsumerIncontrolMapper.selectByIncontrolId(...).
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId("inControlID");
        consumerIncontrolDO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO.setBindChannel("clientId");
        consumerIncontrolDO.setBindStatus(0);
        when(mockConsumerIncontrolMapper.selectByIncontrolId("inControlID", "clientId"))
                .thenReturn(consumerIncontrolDO);

        // Configure ConsumerIncontrolMapper.selectByConsumerCode(...).
        final ConsumerIncontrolDO consumerIncontrolDO1 = new ConsumerIncontrolDO();
        consumerIncontrolDO1.setConsumerCode("consumerCode");
        consumerIncontrolDO1.setIncontrolId("inControlID");
        consumerIncontrolDO1.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO1.setBindChannel("clientId");
        consumerIncontrolDO1.setBindStatus(0);
        when(mockConsumerIncontrolMapper.selectByConsumerCode("consumerCode", "clientId"))
                .thenReturn(consumerIncontrolDO1);

        // Run the test
        final CommonResult<ConsumerIncontrolDTO> result = consumerServiceImplUnderTest.bindIRC("consumerCode",
                "inControlID", "clientId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);
        verify(mockConsumerIncontrolMapper,atLeast(0)).unBindICR("inControlID", "clientId");

        // Confirm ConsumerIncontrolMapper.updateById(...).
        final ConsumerIncontrolDO entity = new ConsumerIncontrolDO();
        entity.setConsumerCode("consumerCode");
        entity.setIncontrolId("inControlID");
        entity.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setBindChannel("clientId");
        entity.setBindStatus(0);
        verify(mockConsumerIncontrolMapper,atLeast(0)).updateById(entity);

        // Confirm ConsumerIncontrolMapper.insert(...).
        final ConsumerIncontrolDO entity1 = new ConsumerIncontrolDO();
        entity1.setConsumerCode("consumerCode");
        entity1.setIncontrolId("inControlID");
        entity1.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity1.setBindChannel("clientId");
        entity1.setBindStatus(0);
        verify(mockConsumerIncontrolMapper,atLeast(0)).insert(entity1);
    }

    @Test
    public void testBindIRC_ConsumerIncontrolMapperSelectByIncontrolIdReturnsNull() {
        // Setup
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode("consumerCode");
        consumerIncontrolDTO.setIncontrolId("incontrolId");
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> expectedResult = CommonResult.success(consumerIncontrolDTO);
        when(mockConsumerIncontrolMapper.selectByIncontrolId("inControlID", "clientId")).thenReturn(null);

        // Configure ConsumerIncontrolMapper.selectByConsumerCode(...).
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId("inControlID");
        consumerIncontrolDO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO.setBindChannel("clientId");
        consumerIncontrolDO.setBindStatus(0);
        when(mockConsumerIncontrolMapper.selectByConsumerCode("consumerCode", "clientId"))
                .thenReturn(consumerIncontrolDO);

        // Run the test
        final CommonResult<ConsumerIncontrolDTO> result = consumerServiceImplUnderTest.bindIRC("consumerCode",
                "inControlID", "clientId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);

        // Confirm ConsumerIncontrolMapper.updateById(...).
        final ConsumerIncontrolDO entity = new ConsumerIncontrolDO();
        entity.setConsumerCode("consumerCode");
        entity.setIncontrolId("inControlID");
        entity.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setBindChannel("clientId");
        entity.setBindStatus(0);
        verify(mockConsumerIncontrolMapper,atLeast(0)).updateById(entity);

        // Confirm ConsumerIncontrolMapper.insert(...).
        final ConsumerIncontrolDO entity1 = new ConsumerIncontrolDO();
        entity1.setConsumerCode("consumerCode");
        entity1.setIncontrolId("inControlID");
        entity1.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity1.setBindChannel("clientId");
        entity1.setBindStatus(0);
        verify(mockConsumerIncontrolMapper,atLeast(0)).insert(entity1);
    }

    @Test
    public void testBindIRC_ConsumerIncontrolMapperSelectByConsumerCodeReturnsNull() {
        // Setup
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode("consumerCode");
        consumerIncontrolDTO.setIncontrolId("incontrolId");
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> expectedResult = CommonResult.success(consumerIncontrolDTO);

        // Configure ConsumerIncontrolMapper.selectByIncontrolId(...).
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId("inControlID");
        consumerIncontrolDO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO.setBindChannel("clientId");
        consumerIncontrolDO.setBindStatus(0);
        when(mockConsumerIncontrolMapper.selectByIncontrolId("inControlID", "clientId"))
                .thenReturn(consumerIncontrolDO);

        when(mockConsumerIncontrolMapper.selectByConsumerCode("consumerCode", "clientId")).thenReturn(null);

        // Run the test
        final CommonResult<ConsumerIncontrolDTO> result = consumerServiceImplUnderTest.bindIRC("consumerCode",
                "inControlID", "clientId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);
        verify(mockConsumerIncontrolMapper,atLeast(0)).unBindICR("inControlID", "clientId");

        // Confirm ConsumerIncontrolMapper.insert(...).
        final ConsumerIncontrolDO entity = new ConsumerIncontrolDO();
        entity.setConsumerCode("consumerCode");
        entity.setIncontrolId("inControlID");
        entity.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setBindChannel("clientId");
        entity.setBindStatus(0);
        verify(mockConsumerIncontrolMapper,atLeast(0)).insert(entity);
    }

    @Test
    public void testUnBindIRC() {
        // Setup
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode("consumerCode");
        consumerIncontrolDTO.setIncontrolId("incontrolId");
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> expectedResult = CommonResult.success(consumerIncontrolDTO);

        // Configure ConsumerIncontrolMapper.selectByConsumerCode(...).
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId("inControlID");
        consumerIncontrolDO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO.setBindChannel("clientId");
        consumerIncontrolDO.setBindStatus(0);
        when(mockConsumerIncontrolMapper.selectByConsumerCode("consumerCode", "clientId"))
                .thenReturn(consumerIncontrolDO);

        // Run the test
        final CommonResult<ConsumerIncontrolDTO> result = consumerServiceImplUnderTest.unBindIRC("consumerCode",
                "inControlID", "clientId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);

        // Confirm ConsumerIncontrolMapper.updateById(...).
        final ConsumerIncontrolDO entity = new ConsumerIncontrolDO();
        entity.setConsumerCode("consumerCode");
        entity.setIncontrolId("inControlID");
        entity.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setBindChannel("clientId");
        entity.setBindStatus(0);
        verify(mockConsumerIncontrolMapper,atLeast(0)).updateById(entity);
    }

    @Test
    public void testUnBindIRC_ConsumerIncontrolMapperSelectByConsumerCodeReturnsNull() {
        // Setup
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode("consumerCode");
        consumerIncontrolDTO.setIncontrolId("incontrolId");
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> expectedResult = CommonResult.success(consumerIncontrolDTO);
        when(mockConsumerIncontrolMapper.selectByConsumerCode("consumerCode", "clientId")).thenReturn(null);

        // Run the test
        final CommonResult<ConsumerIncontrolDTO> result = consumerServiceImplUnderTest.unBindIRC("consumerCode",
                "inControlID", "clientId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetConsumerByCodeOrICR() {
        // Setup
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode("consumerCode");
        consumerIncontrolDTO.setIncontrolId("incontrolId");
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> expectedResult = CommonResult.success(consumerIncontrolDTO);

        // Configure ConsumerIncontrolMapper.selectByConsumerCode(...).
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId("inControlID");
        consumerIncontrolDO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO.setBindChannel("clientId");
        consumerIncontrolDO.setBindStatus(0);
        when(mockConsumerIncontrolMapper.selectByConsumerCode("consumerCode", "clientId"))
                .thenReturn(consumerIncontrolDO);

        // Configure ConsumerIncontrolMapper.selectByIncontrolId(...).
        final ConsumerIncontrolDO consumerIncontrolDO1 = new ConsumerIncontrolDO();
        consumerIncontrolDO1.setConsumerCode("consumerCode");
        consumerIncontrolDO1.setIncontrolId("inControlID");
        consumerIncontrolDO1.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDO1.setBindChannel("clientId");
        consumerIncontrolDO1.setBindStatus(0);
        // when(mockConsumerIncontrolMapper.selectByIncontrolId("inControlID", "clientId"))
        //         .thenReturn(consumerIncontrolDO1);

        // Run the test
        final CommonResult<ConsumerIncontrolDTO> result = consumerServiceImplUnderTest.getConsumerByCodeOrICR(
                "consumerCode", "inControlID", "clientId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);
    }
}
