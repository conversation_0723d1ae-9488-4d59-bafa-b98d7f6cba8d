package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.VcsOrderApi;
import com.jlr.ecp.order.api.order.dto.VCSOrderInfoDTO;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualPageParam;
import com.jlr.ecp.subscription.controller.admin.dto.InvoiceDateManualUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.vo.PIVIInvoiceDateUpdateVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageLogDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PIVIInvoiceDateManualUpdateServiceImplTest {

    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private VcsOrderApi mockVcsOrderApi;
    @Mock
    private PIVIPackageLogDOMapper mockPiviPackageLogDOMapper;
    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;
    @Mock
    private PIVIAppDService mockPiviAppDService;
    @Mock
    private PIVIUnicomService mockPiviUnicomService;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private ManualModifyLogDOService mockManualModifyLogDOService;
    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;

    @InjectMocks
    private PIVIInvoiceDateManualUpdateServiceImpl piviInvoiceDateManualUpdateServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        piviInvoiceDateManualUpdateServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
    }

    @Test
    public void testQueryCarVinFromECP() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success("Q1234567890123456");

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("Q1234567890123456")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        final CommonResult<List<VCSOrderInfoDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockVcsOrderApi.queryPIVIVcsOrderByCarVin("Q1234567890123456")).thenReturn(listCommonResult);

        // Run the test
        final CommonResult<String> result = piviInvoiceDateManualUpdateServiceImplUnderTest.queryCarVinFromECP(
                "Q1234567890123456");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryCarVinFromECP_PIVIPackageDOMapperReturnsNoItems() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.ECP_CAR_VIN_NOT_FOUND);
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure VcsOrderApi.queryPIVIVcsOrderByCarVin(...).
        final VCSOrderInfoDTO vcsOrderInfoDTO = new VCSOrderInfoDTO();
        vcsOrderInfoDTO.setVcsOrderCode("vcsOrderCode");
        vcsOrderInfoDTO.setOrderCode("orderCode");
        vcsOrderInfoDTO.setOrderItemCode("orderItemCode");
        vcsOrderInfoDTO.setConsumerCode("consumerCode");
        vcsOrderInfoDTO.setIncontrolId("incontrolId");

        // Run the test
        final CommonResult<String> result = piviInvoiceDateManualUpdateServiceImplUnderTest.queryCarVinFromECP(
                "Q1234567890123456");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryCarVinFromECP_VcsOrderApiReturnsNoItems() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.ECP_ORDER_FOUND);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("Q1234567890123456")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure VcsOrderApi.queryPIVIVcsOrderByCarVin(...).
        final VCSOrderInfoDTO vcsOrderInfoDTO = new VCSOrderInfoDTO();
        vcsOrderInfoDTO.setVcsOrderCode("vcsOrderCode");
        vcsOrderInfoDTO.setOrderCode("orderCode");
        vcsOrderInfoDTO.setOrderItemCode("orderItemCode");
        vcsOrderInfoDTO.setConsumerCode("consumerCode");
        vcsOrderInfoDTO.setIncontrolId("incontrolId");
        final CommonResult<List<VCSOrderInfoDTO>> listCommonResult = CommonResult.success(List.of(vcsOrderInfoDTO));
        when(mockVcsOrderApi.queryPIVIVcsOrderByCarVin("Q1234567890123456")).thenReturn(listCommonResult);

        // Run the test
        final CommonResult<String> result = piviInvoiceDateManualUpdateServiceImplUnderTest.queryCarVinFromECP(
                "Q1234567890123456");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryManualInvoiceDateByCarVin() {
        // Setup
        final InvoiceDateManualPageParam pageParam = new InvoiceDateManualPageParam();
        pageParam.setPageNo(0);
        pageParam.setPageSize(0);

        final CommonResult<PageResult<PIVIInvoiceDateUpdateVO>> expectedResult = CommonResult.success(
                new PageResult<>(List.of(PIVIInvoiceDateUpdateVO.builder()
                        .modifyTime("")
                        .modifyUser("modifyUser")
                        .carVin("carVin")
                        .invoiceNewDate("2020/01/01")
                        .build()), 0L));
        Page<PIVIPackageLogDO> page = new Page<>();
        page.setRecords(List.of(PIVIPackageLogDO.builder()
                        .carVin("carVin")
                        .modifyUser("modifyUser")
                        .modifyInvoiceDateAfter(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build()));
        when(mockPiviPackageLogDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final CommonResult<PageResult<PIVIInvoiceDateUpdateVO>> result = piviInvoiceDateManualUpdateServiceImplUnderTest.queryManualInvoiceDateByCarVin(
                pageParam);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testManualUpdateInvoiceDate() {
        // Setup
        final InvoiceDateManualUpdateDTO manualUpdateDTO = new InvoiceDateManualUpdateDTO();
        manualUpdateDTO.setCarVin("carVin");
        manualUpdateDTO.setModifyInvoiceDate("2020/01/01 00:00:00");

        final CommonResult<String> expectedResult = CommonResult.success("发票日期修改成功");

        // Configure VcsOrderApi.queryPIVIVcsOrderByCarVin(...).
        final CommonResult<List<VCSOrderInfoDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockVcsOrderApi.queryPIVIVcsOrderByCarVin("carVin")).thenReturn(listCommonResult);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(subscriptionServiceDOS);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure AppDCuRenewRecordsMapper.getByVinAndStatus(...).
        when(mockAppDCuRenewRecordsMapper.getByVinAndStatus("carVin", 4)).thenReturn(null);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("vcsOrderCode");

        // Run the test
        final CommonResult<String> result = piviInvoiceDateManualUpdateServiceImplUnderTest.manualUpdateInvoiceDate(
                manualUpdateDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockPiviPackageLogDOMapper).insert(any(PIVIPackageLogDO.class));
        verify(mockManualModifyLogDOService).recordLog(any(PIVIPackageLogDO.class));
        verify(mockSubscriptionServiceMapper).updateBatch(anyList());
        verify(mockPiviPackageDOMapper).updateBatch(anyList());
    }

    @Test
    public void testManualUpdateInvoiceDate_SubscriptionServiceMapperSelectListReturnsNoItems() {
        // Setup
        final InvoiceDateManualUpdateDTO manualUpdateDTO = new InvoiceDateManualUpdateDTO();
        manualUpdateDTO.setCarVin("carVin");
        manualUpdateDTO.setModifyInvoiceDate("2020/01/01 00:00:00");

        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR);

        // Configure VcsOrderApi.queryPIVIVcsOrderByCarVin(...).
        final CommonResult<List<VCSOrderInfoDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockVcsOrderApi.queryPIVIVcsOrderByCarVin("carVin")).thenReturn(listCommonResult);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(subscriptionServiceDOS);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .vin("vin")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure AppDCuRenewRecordsMapper.getByVinAndStatus(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords = List.of(AppDCuRenewRecords.builder().build());
        when(mockAppDCuRenewRecordsMapper.getByVinAndStatus("carVin", 4)).thenReturn(appDCuRenewRecords);

        // Run the test
        final CommonResult<String> result = piviInvoiceDateManualUpdateServiceImplUnderTest.manualUpdateInvoiceDate(
                manualUpdateDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
