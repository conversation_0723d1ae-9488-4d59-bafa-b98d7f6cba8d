package com.jlr.ecp.subscription.service.fufilment;

import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsFufilmentStatisticDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsFufilmentStatisticMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VcsFufilmentStatisticServiceImplTest {

    @Mock
    private VcsFufilmentStatisticMapper mockVcsFufilmentStatisticMapper;
    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleDOMapper;
    @Mock
    private RedisService mockRedisService;

    @InjectMocks
    private VcsFufilmentStatisticServiceImpl vcsFufilmentStatisticServiceImplUnderTest;

    @Before
    public void setUp() {
        vcsFufilmentStatisticServiceImplUnderTest.redisService = mockRedisService;
    }

    @Test
    public void testGetTabs() {
        // Setup
        final Map<String, Map<String, String>> expectedResult = Map.ofEntries(
                Map.entry("value", Map.ofEntries(Map.entry("value", "value"))));

        // Configure VcsFufilmentStatisticMapper.selectList(...).
        final List<VcsFufilmentStatisticDO> vcsFufilmentStatisticDOS = List.of(VcsFufilmentStatisticDO.builder()
                .seriesCode("seriesCode")
                .brandCode("brandCode")
                .incontrolId("incontrolId")
                .build());
        when(mockVcsFufilmentStatisticMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vcsFufilmentStatisticDOS);


        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .incontrolId("incontrolId")
                .seriesCode("seriesCode")
                .build());
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Run the test
        final Map<String, Map<String, String>> result = vcsFufilmentStatisticServiceImplUnderTest.getTabs("incontrolId",
                "clientId");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetTabs_VcsFufilmentStatisticMapperReturnsNoItems() {
        // Setup
        when(mockVcsFufilmentStatisticMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, Map<String, String>> result = vcsFufilmentStatisticServiceImplUnderTest.getTabs("incontrolId",
                "clientId");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetTabs_RedisServiceReturnsNoItems() {
        // Setup
        final Map<String, Map<String, String>> expectedResult = Map.ofEntries(
                Map.entry("value", Map.ofEntries(Map.entry("value", "value"))));

        // Configure VcsFufilmentStatisticMapper.selectList(...).
        final List<VcsFufilmentStatisticDO> vcsFufilmentStatisticDOS = List.of(VcsFufilmentStatisticDO.builder()
                .seriesCode("seriesCode")
                .brandCode("brandCode")
                .incontrolId("incontrolId")
                .build());
        when(mockVcsFufilmentStatisticMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vcsFufilmentStatisticDOS);


        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .incontrolId("incontrolId")
                .seriesCode("seriesCode")
                .build());
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Run the test
        final Map<String, Map<String, String>> result = vcsFufilmentStatisticServiceImplUnderTest.getTabs("incontrolId",
                "clientId");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetTabs_IncontrolVehicleDOMapperReturnsNoItems() {
        // Setup
        final Map<String, Map<String, String>> expectedResult = Map.ofEntries(
                Map.entry("value", Map.ofEntries(Map.entry("value", "value"))));

        // Configure VcsFufilmentStatisticMapper.selectList(...).
        final List<VcsFufilmentStatisticDO> vcsFufilmentStatisticDOS = List.of(VcsFufilmentStatisticDO.builder()
                .seriesCode("seriesCode")
                .brandCode("brandCode")
                .incontrolId("incontrolId")
                .build());
        when(mockVcsFufilmentStatisticMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(vcsFufilmentStatisticDOS);

        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, Map<String, String>> result = vcsFufilmentStatisticServiceImplUnderTest.getTabs("incontrolId",
                "clientId");

        // Verify the results
        assertThat(result).isNotNull();
    }
}
