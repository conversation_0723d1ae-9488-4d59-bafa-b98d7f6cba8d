package com.jlr.ecp.subscription.service.order.impl;

import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.OrderAppApi;
import com.jlr.ecp.order.api.order.dto.OrderInTransitReqDTO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderCheckServiceImplTest {

    @Mock
    private OrderAppApi mockOrderAppApi;

    @InjectMocks
    private OrderCheckServiceImpl orderCheckServiceImplUnderTest;

    @Test
    public void testCheckOrderInTransit() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success("");
        when(mockOrderAppApi.checkOrderInTransit("vin", 1)).thenReturn(CommonResult.success(false));

        // Run the test
        final CommonResult<String> result = orderCheckServiceImplUnderTest.checkOrderInTransit("vin",
                ServiceTypeEnum.REMOTE);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckOrderInTransit_OrderAppApiReturnsError() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.CALL_ORDER_SERVICE_ERROR);
        when(mockOrderAppApi.checkOrderInTransit("vin", 1))
                .thenReturn(CommonResult.error(new ServiceException(500, "message")));

        // Run the test
        final CommonResult<String> result = orderCheckServiceImplUnderTest.checkOrderInTransit("vin",
                ServiceTypeEnum.REMOTE);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckOrderInTransitByVinList() {
        // Setup
        final CommonResult<List<String>> expectedResult = CommonResult.success(List.of("value"));

        // Configure OrderAppApi.checkOrderInTransitByVinList(...).
        final OrderInTransitReqDTO orderInTransitReqDTO = new OrderInTransitReqDTO();
        orderInTransitReqDTO.setCarVinList(List.of("value"));
        orderInTransitReqDTO.setServiceType(1);
        when(mockOrderAppApi.checkOrderInTransitByVinList(orderInTransitReqDTO))
                .thenReturn(CommonResult.success(List.of("value")));

        // Run the test
        final CommonResult<List<String>> result = orderCheckServiceImplUnderTest.checkOrderInTransitByVinList(
                List.of("value"), ServiceTypeEnum.REMOTE);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckOrderInTransitByVinList_OrderAppApiReturnsError() {
        // Setup
        final CommonResult<List<String>> expectedResult = CommonResult.error(ErrorCodeConstants.CALL_ORDER_SERVICE_ERROR);

        // Configure OrderAppApi.checkOrderInTransitByVinList(...).
        final OrderInTransitReqDTO orderInTransitReqDTO = new OrderInTransitReqDTO();
        orderInTransitReqDTO.setCarVinList(List.of("value"));
        orderInTransitReqDTO.setServiceType(1);
        when(mockOrderAppApi.checkOrderInTransitByVinList(orderInTransitReqDTO))
                .thenReturn(CommonResult.error(new ServiceException(500, "message")));

        // Run the test
        final CommonResult<List<String>> result = orderCheckServiceImplUnderTest.checkOrderInTransitByVinList(
                List.of("value"), ServiceTypeEnum.REMOTE);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
