package com.jlr.ecp.subscription.service.businesserror;

import com.jlr.ecp.subscription.dal.dataobject.businesserror.InterBusiErrorLogDO;
import com.jlr.ecp.subscription.dal.mysql.businesserror.InterBusiErrorLogMapper;
import com.jlr.ecp.subscription.enums.busierror.BusiErrorEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InterBusiErrorLogServiceImplTest {

    @Mock
    private InterBusiErrorLogMapper mockInterBusiErrorLogMapper;

    private InterBusiErrorLogServiceImpl interBusiErrorLogServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        interBusiErrorLogServiceImplUnderTest = new InterBusiErrorLogServiceImpl(mockInterBusiErrorLogMapper);
    }

    @Test
    public void testSaveOrUpdateErrorLog() {
        String businessId = "businessId";
        // Setup
        final InterBusiErrorLogDO expectedResult = new InterBusiErrorLogDO();
        expectedResult.setTraceId("");
        expectedResult.setBusinessId("businessId");
        expectedResult.setBusinessType("ORD_CAR_NOT_FOUND");
        expectedResult.setBusinesParams("businessParams");
        expectedResult.setErrorCount(1);
        expectedResult.setErrorMessage("response");

        // Run the test
        final InterBusiErrorLogDO result = interBusiErrorLogServiceImplUnderTest.saveOrUpdateErrorLog("businessId",
                "response", "businessParams", BusiErrorEnum.ORD_CAR_NOT_FOUND);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm InterBusiErrorLogMapper.insert(...).
        verify(mockInterBusiErrorLogMapper).insert(any(InterBusiErrorLogDO.class));
    }
}
