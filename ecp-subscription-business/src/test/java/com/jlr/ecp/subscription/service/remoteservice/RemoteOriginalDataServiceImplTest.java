package com.jlr.ecp.subscription.service.remoteservice;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.jlr.ecp.subscription.api.vininit.dto.parse.VinInitParseErrorDTO;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import com.jlr.ecp.subscription.dal.mysql.remoteservice.RemoteOriginalDataMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RemoteOriginalDataServiceImplTest {

    @Mock
    private RemoteOriginalDataMapper mockRemoteOriginalDataMapper;

    private RemoteOriginalDataServiceImpl remoteOriginalDataServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        remoteOriginalDataServiceImplUnderTest = new RemoteOriginalDataServiceImpl();
        remoteOriginalDataServiceImplUnderTest.remoteOriginalDataMapper = mockRemoteOriginalDataMapper;
    }

    @Test
    public void testUpdateDataSuccess() {
        // Setup
        // Run the test
        remoteOriginalDataServiceImplUnderTest.updateDataSuccess(Set.of(0L));

        // Verify the results
        verify(mockRemoteOriginalDataMapper).updateBatchToSuccess(Set.of(0L));
    }

    @Test
    public void testUpdateDataFail() {
        // Setup
        // Configure RemoteOriginalDataMapper.selectList(...).
        final RemoteOriginalDataDO remoteOriginalDataDO = new RemoteOriginalDataDO();
        remoteOriginalDataDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remoteOriginalDataDO.setId(0L);
        remoteOriginalDataDO.setStatus(0);
        remoteOriginalDataDO.setFailType("failType");
        remoteOriginalDataDO.setFailDesc("failDesc");
        remoteOriginalDataDO.setMissCount(0);
        final List<RemoteOriginalDataDO> remoteOriginalDataDOS = List.of(remoteOriginalDataDO);
        when(mockRemoteOriginalDataMapper.selectList(any(SFunction.class), any()))
                .thenReturn(remoteOriginalDataDOS);

        // Run the test
        remoteOriginalDataServiceImplUnderTest.updateDataFail(Set.of(0L), "failType", "failDesc");

        // Verify the results
        // Confirm RemoteOriginalDataMapper.updateBatch(...).
        final RemoteOriginalDataDO remoteOriginalDataDO1 = new RemoteOriginalDataDO();
        remoteOriginalDataDO1.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remoteOriginalDataDO1.setId(0L);
        remoteOriginalDataDO1.setStatus(0);
        remoteOriginalDataDO1.setFailType("failType");
        remoteOriginalDataDO1.setFailDesc("failDesc");
        remoteOriginalDataDO1.setMissCount(0);
        verify(mockRemoteOriginalDataMapper).updateBatch(anyList());
    }

    @Test
    public void testUpdateDataFail_RemoteOriginalDataMapperSelectListReturnsNoItems() {
        // Setup
        when(mockRemoteOriginalDataMapper.selectList(any(SFunction.class), any()))
                .thenReturn(Collections.emptyList());

        // Run the test
        remoteOriginalDataServiceImplUnderTest.updateDataFail(Set.of(0L), "failType", "failDesc");

        // Verify the results
        // Confirm RemoteOriginalDataMapper.updateBatch(...).
        final RemoteOriginalDataDO remoteOriginalDataDO = new RemoteOriginalDataDO();
        remoteOriginalDataDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remoteOriginalDataDO.setId(0L);
        remoteOriginalDataDO.setStatus(0);
        remoteOriginalDataDO.setFailType("failType");
        remoteOriginalDataDO.setFailDesc("failDesc");
        remoteOriginalDataDO.setMissCount(0);
        verify(mockRemoteOriginalDataMapper).updateBatch(anyList());
    }

    @Test
    public void testUpdateDateFailByParse() {
        // Setup
        final List<VinInitParseErrorDTO> list = List.of(new VinInitParseErrorDTO(0L, "failDesc"));

        // Configure RemoteOriginalDataMapper.selectList(...).
        final RemoteOriginalDataDO remoteOriginalDataDO = new RemoteOriginalDataDO();
        remoteOriginalDataDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remoteOriginalDataDO.setId(0L);
        remoteOriginalDataDO.setStatus(0);
        remoteOriginalDataDO.setFailType("failType");
        remoteOriginalDataDO.setFailDesc("failDesc");
        remoteOriginalDataDO.setMissCount(0);
        final List<RemoteOriginalDataDO> remoteOriginalDataDOS = List.of(remoteOriginalDataDO);
        when(mockRemoteOriginalDataMapper.selectList(any(SFunction.class), any()))
                .thenReturn(remoteOriginalDataDOS);

        // Run the test
        remoteOriginalDataServiceImplUnderTest.updateDateFailByParse(list, "failType");

        // Verify the results
        // Confirm RemoteOriginalDataMapper.updateBatch(...).
        final RemoteOriginalDataDO remoteOriginalDataDO1 = new RemoteOriginalDataDO();
        remoteOriginalDataDO1.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remoteOriginalDataDO1.setId(0L);
        remoteOriginalDataDO1.setStatus(0);
        remoteOriginalDataDO1.setFailType("failType");
        remoteOriginalDataDO1.setFailDesc("failDesc");
        remoteOriginalDataDO1.setMissCount(0);
        verify(mockRemoteOriginalDataMapper).updateBatch(anyList());
    }

    @Test
    public void testUpdateDateFailByParse_RemoteOriginalDataMapperSelectListReturnsNoItems() {
        // Setup
        final List<VinInitParseErrorDTO> list = List.of(new VinInitParseErrorDTO(0L, "failDesc"));
        when(mockRemoteOriginalDataMapper.selectList(any(SFunction.class), any()))
                .thenReturn(Collections.emptyList());

        // Run the test
        remoteOriginalDataServiceImplUnderTest.updateDateFailByParse(list, "failType");

        // Verify the results
        // Confirm RemoteOriginalDataMapper.updateBatch(...).
        final RemoteOriginalDataDO remoteOriginalDataDO = new RemoteOriginalDataDO();
        remoteOriginalDataDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remoteOriginalDataDO.setId(0L);
        remoteOriginalDataDO.setStatus(0);
        remoteOriginalDataDO.setFailType("failType");
        remoteOriginalDataDO.setFailDesc("failDesc");
        remoteOriginalDataDO.setMissCount(0);
        verify(mockRemoteOriginalDataMapper).updateBatch(anyList());
    }
}
