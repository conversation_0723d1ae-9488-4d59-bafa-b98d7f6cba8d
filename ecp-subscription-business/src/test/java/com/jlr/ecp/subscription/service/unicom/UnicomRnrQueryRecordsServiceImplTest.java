package com.jlr.ecp.subscription.service.unicom;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.unicom.dto.UnicomBatchQueryPageDTO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomRnrBatchQueryListVO;
import com.jlr.ecp.subscription.dal.dataobject.unicom.UnicomRnrQueryRecordsDO;
import com.jlr.ecp.subscription.dal.mysql.unicom.UnicomRnrQueryRecordsMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnicomRnrQueryRecordsServiceImplTest {

    @Mock
    private UnicomRnrQueryRecordsMapper mockUnicomRnrQueryRecordsMapper;

    @InjectMocks
    private UnicomRnrQueryRecordsServiceImpl unicomRnrQueryRecordsServiceImplUnderTest;

    @Test
    public void testSelectQueryRecordsByBatchNo() {
        // Setup
        // Configure UnicomRnrQueryRecordsMapper.selectList(...).
        final List<UnicomRnrQueryRecordsDO> unicomRnrQueryRecordsDOS = List.of(UnicomRnrQueryRecordsDO.builder()
                .id(0L)
                .batchNo(0L)
                .carVin("carVin")
                .queryStatus(0)
                .realNameFlag(0)
                .cardState("cardState")
                .failedType(0)
                .build());
        when(mockUnicomRnrQueryRecordsMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(unicomRnrQueryRecordsDOS);

        // Run the test
        final List<Long> result = unicomRnrQueryRecordsServiceImplUnderTest.selectQueryRecordsByBatchNo(0L);

        // Verify the results
        assertEquals(List.of(0L), result);
    }

    @Test
    public void testGetBatchFilePage() {
        // Setup
        final UnicomBatchQueryPageDTO pageDTO = new UnicomBatchQueryPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("operateTimeSort");
        pageDTO.setBatchNoList(List.of(0L));
        pageDTO.setQueryStatus("queryStatus");
        pageDTO.setCarVin("carVin");
        pageDTO.setOperator("operator");
        pageDTO.setStartTime("startTime");
        pageDTO.setEndTime("endTime");

        final UnicomRnrBatchQueryListVO unicomRnrBatchQueryListVO = new UnicomRnrBatchQueryListVO();
        unicomRnrBatchQueryListVO.setBatchNo(1L);
        unicomRnrBatchQueryListVO.setCarVin("carVin");
        unicomRnrBatchQueryListVO.setQueryStatus(1);
        unicomRnrBatchQueryListVO.setQueryStatusTxt("查询中");
        unicomRnrBatchQueryListVO.setRealNameFlag(1);
        unicomRnrBatchQueryListVO.setRealNameFlagTxt("已实名");
        unicomRnrBatchQueryListVO.setCardState("cardState");
        unicomRnrBatchQueryListVO.setCardStateTxt("cardState");
        unicomRnrBatchQueryListVO.setOperateTime("");
        unicomRnrBatchQueryListVO.setRealNameUpdateTime("查询失败");
        final PageResult<UnicomRnrBatchQueryListVO> expectedResult = new PageResult<>(
                List.of(unicomRnrBatchQueryListVO), 0L);
        Page<UnicomRnrQueryRecordsDO> pageResult = new Page<>();
        pageResult.setRecords(List.of(UnicomRnrQueryRecordsDO.builder()
                .iccid("iccid")
                .queryStatus(1)
                .batchNo(1L)
                .carVin("carVin")
                .cardState("cardState")
                .realNameFlag(1)
                .build()));
        when(mockUnicomRnrQueryRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapperX.class)))
                .thenReturn(pageResult);

        // Run the test
        final PageResult<UnicomRnrBatchQueryListVO> result = unicomRnrQueryRecordsServiceImplUnderTest.getBatchFilePage(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
