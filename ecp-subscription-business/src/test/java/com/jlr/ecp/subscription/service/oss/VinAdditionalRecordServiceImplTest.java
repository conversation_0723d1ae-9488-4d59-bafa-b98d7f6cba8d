package com.jlr.ecp.subscription.service.oss;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.sota.vo.SOTAResultVO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.bau.VinAdditionalRecordVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.SimCardInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataManualDO;
import com.jlr.ecp.subscription.dal.dataobject.oss.DmsOssOriginalDataRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleConfigGroupDO;
import com.jlr.ecp.subscription.dal.mysql.oss.DmsOssOriginalDataManualMapper;
import com.jlr.ecp.subscription.model.dto.*;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIPackageService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.sota.SOTAService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class VinAdditionalRecordServiceImplTest {

    @Mock
    private PIVIPackageService mockPiviPackageService;
    @Mock
    private SOTAService mockSotaService;
    @Mock
    private PIVIUnicomService mockUnicomService;
    @Mock
    private PIVIAppDService mockAppDService;
    @Mock
    private PIVIAmaPService mockAmaPService;
    @Mock
    private VehicleModelMasterDataService mockVehicleModelMasterDataService;
    @Mock
    private VehicleConfigGroupService mockVehicleConfigGroupService;
    @Mock
    private TransactionTemplate mockTransactionTemplate;
    @Mock
    private DmsOssOriginalDataService mockDmsOssOriginalDataService;
    @Mock
    private DmsOssOriginalDataRecordsService mockDmsOssOriginalDataRecordsService;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private DmsOssOriginalDataManualMapper mockDmsOssOriginalDataManualMapper;
    @Mock
    private DmsOssFileRecordsService mockDmsOssFileRecordsService;
    @Mock
    private ManualModifyLogDOService mockManualModifyLogDOService;

    @InjectMocks
    private VinAdditionalRecordServiceImpl vinAdditionalRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        vinAdditionalRecordServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
    }

    @Test
    public void testAdditionalRecord() {
        // Setup
        final VinAdditionalRecordDTO recordDTO = new VinAdditionalRecordDTO();
        recordDTO.setVin("HGCM1234567890000");
        recordDTO.setInvoiceDate("2020-01-01");
        recordDTO.setIccid("12345678901234567890");

        final CommonResult<String> expectedResult = CommonResult.success("数据补录中，请刷新查看结果");

        // Configure PIVIPackageService.queryByVin(...).
        when(mockPiviPackageService.queryByVin("HGCM1234567890000")).thenReturn(null);

        // Configure DmsOssOriginalDataManualMapper.selectOneByCarVin(...).
        when(mockDmsOssOriginalDataManualMapper.selectOneByCarVin("HGCM1234567890000")).thenReturn(null);

        // Configure DmsOssOriginalDataService.getByVin(...).
        final DmsOssOriginalDataDO dmsOssOriginalDataDO = DmsOssOriginalDataDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("HGCM1234567890000")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("groupId")
                .iccid("12345678901234567890")
                .jlrSubscriptionId(0L)
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sourceType(0)
                .build();
        when(mockDmsOssOriginalDataService.getByVin("HGCM1234567890000")).thenReturn(dmsOssOriginalDataDO);

        MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class);
        mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(1L);

        doAnswer(invocation -> {
            Consumer<TransactionStatus> consumer = invocation.getArgument(0);
            consumer.accept(mock(TransactionStatus.class));
            return null;
        }).when(mockTransactionTemplate).executeWithoutResult(any());

        when(mockEcpIdUtil.nextId()).thenReturn(0L);
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Mock TransactionSynchronizationManager.registerSynchronization
        MockedStatic<TransactionSynchronizationManager> transactionSynchronizationManager = mockStatic(TransactionSynchronizationManager.class);
        transactionSynchronizationManager.when(() -> TransactionSynchronizationManager.registerSynchronization(any(TransactionSynchronization.class))).thenAnswer(invocation -> {
            TransactionSynchronization synchronization = invocation.getArgument(0);
            // Manually invoke afterCommit to simulate transaction commit
            synchronization.afterCommit();
            return null;
        });

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("responseCode");
        unicomRespVO.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        simCardInfo.setIccid("12345678901234567890");
        unicomRespData.setSimCardInfo(simCardInfo);
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockUnicomService.getSimCardInfo("12345678901234567890")).thenReturn(unicomRespVO);

        // Configure PIVIAppDService.getVinSubscriptions(...).
        final AppDSubscriptionResp appDSubscriptionResp = new AppDSubscriptionResp();
        final AppDSubscriptionData appDSubscriptionData = new AppDSubscriptionData();
        appDSubscriptionData.setJlrSubscriptionId(0L);
        appDSubscriptionData.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionResp.setResult(List.of(appDSubscriptionData));
        appDSubscriptionResp.setResultCode(0);
        when(mockAppDService.getVinSubscriptions("HGCM1234567890000")).thenReturn(appDSubscriptionResp);

        // Configure PIVIAmaPService.queryAmaPExpireDate(...).
        final AmaPCarInfoResponse amaPCarInfoResponse = new AmaPCarInfoResponse();
        amaPCarInfoResponse.setCode("1");
        amaPCarInfoResponse.setErrDetail("errDetail");
        amaPCarInfoResponse.setMessage("message");
        amaPCarInfoResponse.setData(AmaPCarData.builder()
                .permissions(List.of(AmaPPermissionInfo.builder()
                        .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .build()))
                .build());
        when(mockAmaPService.queryAmaPExpireDate("HGCM1234567890000")).thenReturn(amaPCarInfoResponse);

        // Configure VehicleModelMasterDataService.findDp(...).
        final UserDPResultVO userDPResultVO = new UserDPResultVO();
        userDPResultVO.setVin("HGCM1234567890000");
        userDPResultVO.setBrandCode("brandCode");
        userDPResultVO.setConfigCode("configCode");
        userDPResultVO.setCarSystemModel("PIVI");
        when(mockVehicleModelMasterDataService.findDp("HGCM1234567890000")).thenReturn(userDPResultVO);

        // Configure VehicleConfigGroupService.getByConfigCode(...).

        when(mockEcpIdUtil.nextIdStr()).thenReturn("vcsOrderCode");

        // Configure PIVIAppDService.callAppDService(...).
        when(mockAppDService.callAppDService(any(), eq(null), anyLong())).thenReturn(true);

        // Configure PIVIUnicomService.unicomRenewalByIccid(...).
        final UnicomRespVO unicomRespVO1 = new UnicomRespVO();
        unicomRespVO1.setResponseCode("responseCode");
        unicomRespVO1.setResponseDesc("SUCCESS");
        final UnicomRespData unicomRespData1 = new UnicomRespData();
        final SimCardInfo simCardInfo1 = new SimCardInfo();
        simCardInfo1.setIccid("12345678901234567890");
        unicomRespData1.setSimCardInfo(simCardInfo1);
        unicomRespVO1.setUnicomRespData(unicomRespData1);
        unicomRespVO1.setQueryResult("queryResult");
        final CommonResult<UnicomRespVO> unicomRespVOCommonResult = CommonResult.success(unicomRespVO1);
        when(mockUnicomService.unicomRenewalByIccid(any(), anyString())).thenReturn(unicomRespVOCommonResult);

        // Run the test
        final CommonResult<String> result = vinAdditionalRecordServiceImplUnderTest.additionalRecord(recordDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockTransactionTemplate, times(3)).executeWithoutResult(any(Consumer.class));
        verify(mockDmsOssOriginalDataManualMapper).insert(any(DmsOssOriginalDataManualDO.class));
        verify(mockDmsOssOriginalDataService, times(3)).update(any(DmsOssOriginalDataDO.class));
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
        verify(mockDmsOssOriginalDataManualMapper, times(2)).updateById(any(DmsOssOriginalDataManualDO.class));
        verify(mockManualModifyLogDOService).recordManualLog(any(PIVIPackageLogDO.class));
        verify(mockDmsOssFileRecordsService).updateManualSyncResult(0L);
        verify(mockDmsOssOriginalDataRecordsService).update(any(DmsOssOriginalDataRecordsDO.class));
    }

    @Test
    public void testGetAdditionalRecordPage() {
        // Setup
        final VinAdditionalRecordPageDTO pageDTO = new VinAdditionalRecordPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<VinAdditionalRecordVO> expectedResult = new PageResult<>(
                List.of(VinAdditionalRecordVO.builder()
                        .operateTime("")
                        .vin("carVin")
                        .initialResult("初始化成功")
                        .failReason("errorMessage")
                        .build()), 0L);
        Page<DmsOssOriginalDataManualDO> page = new Page<>();
        page.setRecords(List.of(DmsOssOriginalDataManualDO.builder()
                .carVin("carVin")
                .status(1)
                .errorMessage("errorMessage")
                .build()));
        when(mockDmsOssOriginalDataManualMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<VinAdditionalRecordVO> result = vinAdditionalRecordServiceImplUnderTest.getAdditionalRecordPage(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckSOTA() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("groupId")
                .iccid("iccid")
                .jlrSubscriptionId(0L)
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sourceType(0)
                .build();
        final DmsOssOriginalDataRecordsDO recordsDO = DmsOssOriginalDataRecordsDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .carVin("carVin")
                .sotaResult("sotaResult")
                .appdResult("appdResult")
                .cuResult("cuResult")
                .amapResult("amapResult")
                .dpResult("dpResult")
                .appdSyncResult(0)
                .cuSyncResult(0)
                .build();

        // Configure SOTAService.getSOTAInfoByVin(...).
        final SOTAResultVO sotaResultVO = new SOTAResultVO();
        sotaResultVO.setVin("vin");
        sotaResultVO.setPivi("pivi");
        sotaResultVO.setEsimiccid("iccid");
        sotaResultVO.setQueryResult("sotaResult");
        when(mockSotaService.getSOTAInfoByVin("carVin")).thenReturn(sotaResultVO);

        // Run the test
        final boolean result = vinAdditionalRecordServiceImplUnderTest.checkSOTA(originalDataDO, recordsDO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCheckCU() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("groupId")
                .iccid("iccid")
                .jlrSubscriptionId(0L)
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sourceType(0)
                .build();
        final DmsOssOriginalDataRecordsDO recordsDO = DmsOssOriginalDataRecordsDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .carVin("carVin")
                .sotaResult("sotaResult")
                .appdResult("appdResult")
                .cuResult("cuResult")
                .amapResult("amapResult")
                .dpResult("dpResult")
                .appdSyncResult(0)
                .cuSyncResult(0)
                .build();

        // Configure PIVIUnicomService.getSimCardInfo(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("responseCode");
        unicomRespVO.setResponseDesc("responseDesc");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final SimCardInfo simCardInfo = new SimCardInfo();
        simCardInfo.setIccid("iccid");
        unicomRespData.setSimCardInfo(simCardInfo);
        unicomRespVO.setUnicomRespData(unicomRespData);
        unicomRespVO.setQueryResult("queryResult");
        when(mockUnicomService.getSimCardInfo("iccid")).thenReturn(unicomRespVO);

        // Run the test
        final boolean result = vinAdditionalRecordServiceImplUnderTest.checkCU(originalDataDO, recordsDO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCheckAPPD() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("groupId")
                .iccid("iccid")
                .jlrSubscriptionId(0L)
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sourceType(0)
                .build();
        final DmsOssOriginalDataRecordsDO recordsDO = DmsOssOriginalDataRecordsDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .carVin("carVin")
                .sotaResult("sotaResult")
                .appdResult("appdResult")
                .cuResult("cuResult")
                .amapResult("amapResult")
                .dpResult("dpResult")
                .appdSyncResult(0)
                .cuSyncResult(0)
                .build();

        // Configure PIVIAppDService.getVinSubscriptions(...).
        final AppDSubscriptionResp appDSubscriptionResp = new AppDSubscriptionResp();
        final AppDSubscriptionData appDSubscriptionData = new AppDSubscriptionData();
        appDSubscriptionData.setJlrSubscriptionId(0L);
        appDSubscriptionData.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionResp.setResult(List.of(appDSubscriptionData));
        appDSubscriptionResp.setQueryResult("appdResult");
        appDSubscriptionResp.setResultCode(0);
        when(mockAppDService.getVinSubscriptions("carVin")).thenReturn(appDSubscriptionResp);

        // Run the test
        final boolean result = vinAdditionalRecordServiceImplUnderTest.checkAPPD(originalDataDO, recordsDO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCheckAMAP() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("groupId")
                .iccid("iccid")
                .jlrSubscriptionId(0L)
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sourceType(0)
                .build();
        final DmsOssOriginalDataRecordsDO recordsDO = DmsOssOriginalDataRecordsDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .carVin("carVin")
                .sotaResult("sotaResult")
                .appdResult("appdResult")
                .cuResult("cuResult")
                .amapResult("amapResult")
                .dpResult("dpResult")
                .appdSyncResult(0)
                .cuSyncResult(0)
                .build();

        // Configure PIVIAmaPService.queryAmaPExpireDate(...).
        final AmaPCarInfoResponse amaPCarInfoResponse = new AmaPCarInfoResponse();
        amaPCarInfoResponse.setCode("code");
        amaPCarInfoResponse.setErrDetail("errDetail");
        amaPCarInfoResponse.setMessage("message");
        amaPCarInfoResponse.setData(AmaPCarData.builder()
                .permissions(List.of(AmaPPermissionInfo.builder()
                        .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .build()))
                .build());
        amaPCarInfoResponse.setQueryResult("queryResult");
        when(mockAmaPService.queryAmaPExpireDate("carVin")).thenReturn(amaPCarInfoResponse);

        // Run the test
        final boolean result = vinAdditionalRecordServiceImplUnderTest.checkAMAP(originalDataDO, recordsDO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCheckDP() {
        // Setup
        final DmsOssOriginalDataDO originalDataDO = DmsOssOriginalDataDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .jobDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .carVin("carVin")
                .dmsInvoiceDate("dmsInvoiceDate")
                .status(0)
                .syncStatus(0)
                .sotaResult(0)
                .appdResult(0)
                .cuResult(0)
                .amapResult(0)
                .dpResult(0)
                .piviConfigResult(0)
                .vinMatchResult(0)
                .specialVinConfig("groupId")
                .iccid("iccid")
                .jlrSubscriptionId(0L)
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sourceType(0)
                .build();
        final DmsOssOriginalDataRecordsDO recordsDO = DmsOssOriginalDataRecordsDO.builder()
                .tenantId(0)
                .bauJobId(0L)
                .dataId(0L)
                .carVin("carVin")
                .sotaResult("sotaResult")
                .appdResult("appdResult")
                .cuResult("cuResult")
                .amapResult("amapResult")
                .dpResult("dpResult")
                .appdSyncResult(0)
                .cuSyncResult(0)
                .build();

        // Configure VehicleModelMasterDataService.findDp(...).
        final UserDPResultVO userDPResultVO = new UserDPResultVO();
        userDPResultVO.setVin("vin");
        userDPResultVO.setBrandCode("brandCode");
        userDPResultVO.setConfigCode("configCode");
        userDPResultVO.setCarSystemModel("carSystemModel");
        userDPResultVO.setQueryResult("queryResult");
        when(mockVehicleModelMasterDataService.findDp("carVin")).thenReturn(userDPResultVO);

        // Run the test
        final String result = vinAdditionalRecordServiceImplUnderTest.checkDP(originalDataDO, recordsDO);

        // Verify the results
        assertEquals(null, result);
    }
}
