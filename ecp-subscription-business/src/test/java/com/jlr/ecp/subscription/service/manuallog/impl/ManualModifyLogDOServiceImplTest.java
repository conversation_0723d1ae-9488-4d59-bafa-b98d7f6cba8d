package com.jlr.ecp.subscription.service.manuallog.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageReqDTO;
import com.jlr.ecp.subscription.controller.admin.dto.manuallog.ManualLogPageRespVO;
import com.jlr.ecp.subscription.dal.dataobject.amap.AmaPRenewRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.iccid.IccidModifyRecordsDO;
import com.jlr.ecp.subscription.dal.dataobject.manuallog.ManualModifyLogDO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageLogDO;
import com.jlr.ecp.subscription.dal.mysql.manuallog.ManualModifyLogDOMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ManualModifyLogDOServiceImplTest {

    @Mock
    private ManualModifyLogDOMapper mockManualModifyLogDOMapper;

    @InjectMocks
    private ManualModifyLogDOServiceImpl manualModifyLogDOServiceImplUnderTest;

    @Test
    public void testGetLogPage() {
        // Setup
        final ManualLogPageReqDTO dto = new ManualLogPageReqDTO();
        dto.setPageNo(0);
        dto.setPageSize(0);
        dto.setVin("vin");
        dto.setModifyType(0);
        dto.setStartTime("startTime");
        dto.setEndTime("endTime");
        dto.setOperateTimeSort("operateTimeSort");
        dto.setOperator("operator");

        final ManualLogPageRespVO manualLogPageRespVO = new ManualLogPageRespVO();
        manualLogPageRespVO.setModifyType("1");
        manualLogPageRespVO.setModifyTypeText("在线导航续费");
        final PageResult<ManualLogPageRespVO> expectedResult = new PageResult<>(List.of(manualLogPageRespVO), 0L);
        Page<ManualModifyLogDO> page = new Page<>();
        ManualModifyLogDO modifyLogDO = new ManualModifyLogDO();
        modifyLogDO.setModifyType(1);
        page.setRecords(List.of(modifyLogDO));
        when(mockManualModifyLogDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapperX.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<ManualLogPageRespVO> result = manualModifyLogDOServiceImplUnderTest.getLogPage(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testRecordLog1() {
        // Setup
        final Function<String, ManualModifyLogDO> converter = val -> {
            return ManualModifyLogDO.builder()
                    .id(0L)
                    .carVin("carVin")
                    .modifyType(0)
                    .modifyBeforeValue("modifyBeforeIccid")
                    .modifyAfterValue("modifyAfterIccid")
                    .operator("modifyUser")
                    .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                    .build();
        };

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLog("entity", converter);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(ManualModifyLogDO.builder()
                .id(0L)
                .carVin("carVin")
                .modifyType(0)
                .modifyBeforeValue("modifyBeforeIccid")
                .modifyAfterValue("modifyAfterIccid")
                .operator("modifyUser")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
    }

    @Test
    public void testRecordLogList() {
        // Setup
        final Function<String, ManualModifyLogDO> converter = val -> {
            return ManualModifyLogDO.builder()
                    .id(0L)
                    .carVin("carVin")
                    .modifyType(0)
                    .modifyBeforeValue("modifyBeforeIccid")
                    .modifyAfterValue("modifyAfterIccid")
                    .operator("modifyUser")
                    .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                    .build();
        };

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLogList(List.of("value"), converter);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insertBatch(List.of(ManualModifyLogDO.builder()
                .id(0L)
                .carVin("carVin")
                .modifyType(0)
                .modifyBeforeValue("modifyBeforeIccid")
                .modifyAfterValue("modifyAfterIccid")
                .operator("modifyUser")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build()));
    }

    @Test
    public void testRecordLog2() {
        // Setup
        final AmaPRenewRecordsDO amaPRenewRecordsDO = AmaPRenewRecordsDO.builder()
                .carVin("carVin")
                .operator("modifyUser")
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLog(amaPRenewRecordsDO);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(ManualModifyLogDO.builder()
                .carVin("carVin")
                .modifyType(1)
                .modifyBeforeValue("2020/01/01")
                .modifyAfterValue("2020/01/01")
                .operator("modifyUser")
                .build());
    }

    @Test
    public void testRecordLogAmapList() {
        // Setup
        final List<AmaPRenewRecordsDO> amaPRenewRecordsDOList = List.of(AmaPRenewRecordsDO.builder()
                .carVin("carVin")
                .operator("modifyUser")
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLogAmapList(amaPRenewRecordsDOList);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insertBatch(List.of(ManualModifyLogDO.builder()
                .carVin("carVin")
                .modifyType(1)
                .modifyBeforeValue("2020/01/01")
                .modifyAfterValue("2020/01/01")
                .operator("modifyUser")
                .build()));
    }

    @Test
    public void testRecordLog3() {
        // Setup
        final IccidModifyRecordsDO iccidModifyRecordsDO = IccidModifyRecordsDO.builder()
                .carVin("carVin")
                .modifyBeforeIccid("modifyBeforeIccid")
                .modifyAfterIccid("modifyAfterIccid")
                .build();

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLog(iccidModifyRecordsDO);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(any(ManualModifyLogDO.class));
    }

    @Test
    public void testRecordLogIccIdModifyList() {
        // Setup
        final List<IccidModifyRecordsDO> iccidModifyRecordsDOList = List.of(IccidModifyRecordsDO.builder()
                .carVin("carVin")
                .modifyBeforeIccid("modifyBeforeIccid")
                .modifyAfterIccid("modifyAfterIccid")
                .build());

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLogIccIdModifyList(iccidModifyRecordsDOList);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insertBatch(anyList());
    }

    @Test
    public void testRecordLog4() {
        // Setup
        final AppDCuRenewRecords appDCuRenewRecords = AppDCuRenewRecords.builder()
                .carVin("carVin")
                .renewServiceType(0)
                .operator("modifyUser")
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .renewAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLog(appDCuRenewRecords);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(ManualModifyLogDO.builder()
                .carVin("carVin")
                .modifyType(3)
                .modifyBeforeValue("2020/01/01")
                .modifyAfterValue("2020/01/01")
                .operator("modifyUser")
                .build());
    }

    @Test
    public void testRecordLog5() {
        // Setup
        final RemoteRenewDetailRecords remoteRenewDetailRecords = RemoteRenewDetailRecords.builder()
                .carVin("carVin")
                .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .operator("modifyUser")
                .build();

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLog(remoteRenewDetailRecords);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(ManualModifyLogDO.builder()
                .carVin("carVin")
                .modifyType(7)
                .modifyBeforeValue("2020/01/01")
                .modifyAfterValue("2020/01/01")
                .operator("modifyUser")
                .build());
    }

    @Test
    public void testRecordLog6() {
        // Setup
        final PIVIPackageLogDO piviPackageLogDO = PIVIPackageLogDO.builder()
                .modifyUser("modifyUser")
                .carVin("carVin")
                .modifyInvoiceDateBefore(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyInvoiceDateAfter(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordLog(piviPackageLogDO);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(any(ManualModifyLogDO.class));
    }

    @Test
    public void testRecordManualLog() {
        // Setup
        final PIVIPackageLogDO piviPackageLogDO = PIVIPackageLogDO.builder()
                .modifyUser("modifyUser")
                .carVin("carVin")
                .modifyInvoiceDateBefore(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyInvoiceDateAfter(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        manualModifyLogDOServiceImplUnderTest.recordManualLog(piviPackageLogDO);

        // Verify the results
        verify(mockManualModifyLogDOMapper).insert(any(ManualModifyLogDO.class));
    }
}
