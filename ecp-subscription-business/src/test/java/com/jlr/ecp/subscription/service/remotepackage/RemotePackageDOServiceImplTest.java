package com.jlr.ecp.subscription.service.remotepackage;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackageCreateDTO;
import com.jlr.ecp.subscription.api.remotepackage.dto.RemotePackagePageReqDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.BatchUploadRespVO;
import com.jlr.ecp.subscription.api.remotepackage.vo.RemotePackageListRespVO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.RemotePackageDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.RemotePackageDOMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RemotePackageDOServiceImplTest {

    @Mock
    private RemotePackageDOMapper mockRemotePackageDOMapper;

    @InjectMocks
    private RemotePackageDOServiceImpl remotePackageDOServiceImplUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(remotePackageDOServiceImplUnderTest, "templateUrl", "templateUrl");
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), RemotePackageDO.class);

    }

    @Test
    public void testCreateRemotePackage() {
        // Setup
        final RemotePackageCreateDTO createDTO = new RemotePackageCreateDTO();
        createDTO.setCarSystemModel("carSystemModel");
        createDTO.setPackageCode("packageCode");


        // Run the test
        final Boolean result = remotePackageDOServiceImplUnderTest.createRemotePackage(createDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetPage() {
        // Setup
        final RemotePackagePageReqDTO dto = new RemotePackagePageReqDTO();
        dto.setPageNo(0);
        dto.setPageSize(0);
        dto.setCreatedTimeSort("createdTimeSort");

        final RemotePackageListRespVO remotePackageListRespVO = new RemotePackageListRespVO();
        remotePackageListRespVO.setCarSystemModel("carSystemModel");
        remotePackageListRespVO.setPackageCode("packageCode");
        remotePackageListRespVO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        remotePackageListRespVO.setCreatedUser("createdUser");
        final PageResult<RemotePackageListRespVO> expectedResult = new PageResult<>(List.of(remotePackageListRespVO),
                0L);
        when(mockRemotePackageDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(new Page<>(0L, 0L, 0L, false));

        // Run the test
        final PageResult<RemotePackageListRespVO> result = remotePackageDOServiceImplUnderTest.getPage(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testDeleteByPackageCode() {
        // Setup
        // Configure RemotePackageDOMapper.selectOne(...).
        final RemotePackageDO remotePackageDO = RemotePackageDO.builder()
                .tenantId(0)
                .carSystemModel("carSystemModel")
                .packageCode("packageCode")
                .createdUser("createdUser")
                .build();
        when(mockRemotePackageDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(remotePackageDO);

        when(mockRemotePackageDOMapper.deleteById(RemotePackageDO.builder()
                .tenantId(0)
                .carSystemModel("carSystemModel")
                .packageCode("packageCode")
                .createdUser("createdUser")
                .build())).thenReturn(0);

        // Run the test
        final Boolean result = remotePackageDOServiceImplUnderTest.deleteByPackageCode("packageCode");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testProcessBatchUpload() {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final CommonResult<BatchUploadRespVO> expectedResult = CommonResult.success(new BatchUploadRespVO(0, 0, 0));

        // Configure RemotePackageDOMapper.selectList(...).
        final List<RemotePackageDO> remotePackageDOS = List.of(RemotePackageDO.builder()
                .tenantId(0)
                .carSystemModel("carSystemModel")
                .packageCode("packageCode")
                .createdUser("createdUser")
                .build());
        when(mockRemotePackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(remotePackageDOS);

        // Run the test
        final CommonResult<BatchUploadRespVO> result = remotePackageDOServiceImplUnderTest.processBatchUpload(file,
                "carSystemModel");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testProcessBatchUpload_RemotePackageDOMapperReturnsNoItems() {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final CommonResult<BatchUploadRespVO> expectedResult = CommonResult.success(new BatchUploadRespVO(0, 0, 0));
        when(mockRemotePackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<BatchUploadRespVO> result = remotePackageDOServiceImplUnderTest.processBatchUpload(file,
                "carSystemModel");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTemplateUrl() {
        assertThat(remotePackageDOServiceImplUnderTest.getTemplateUrl()).isEqualTo("templateUrl");
    }

    @Test
    public void testGetExistPackageCode() {
        // Setup
        // Configure RemotePackageDOMapper.selectList(...).
        final List<RemotePackageDO> remotePackageDOS = List.of(RemotePackageDO.builder()
                .tenantId(0)
                .carSystemModel("carSystemModel")
                .packageCode("packageCode")
                .createdUser("createdUser")
                .build());
        when(mockRemotePackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(remotePackageDOS);

        // Run the test
        final List<String> result = remotePackageDOServiceImplUnderTest.getExistPackageCode(List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(List.of("packageCode"));
    }

    @Test
    public void testGetExistPackageCode_RemotePackageDOMapperReturnsNoItems() {
        // Setup
        when(mockRemotePackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = remotePackageDOServiceImplUnderTest.getExistPackageCode(List.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
