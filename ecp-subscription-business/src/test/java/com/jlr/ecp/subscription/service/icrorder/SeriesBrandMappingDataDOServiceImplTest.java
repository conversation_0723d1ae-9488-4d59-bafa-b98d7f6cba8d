package com.jlr.ecp.subscription.service.icrorder;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesBrandMappingDataPageVO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingDataUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingQueryPageDTO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.jlr.ecp.subscription.dal.mysql.icroder.SeriesBrandMappingDataDOMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SeriesBrandMappingDataDOServiceImplTest {

    @Mock
    private RedisService mockRedisService;
    @Mock
    private SeriesBrandMappingDataDOMapper mockSeriesBrandMappingDataDOMapper;

    @InjectMocks
    private SeriesBrandMappingDataDOServiceImpl seriesBrandMappingDataDOServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        seriesBrandMappingDataDOServiceImplUnderTest.redisService = mockRedisService;
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), SeriesBrandMappingDataDO.class);
    }

    @Test
    public void testInitLocalCache() {
        // Setup
        // Configure SeriesBrandMappingDataDOMapper.selectList(...).
        final List<SeriesBrandMappingDataDO> seriesBrandMappingDataDOS = List.of(SeriesBrandMappingDataDO.builder()
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandNameView("brandNameView")
                .build());
        when(mockSeriesBrandMappingDataDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(seriesBrandMappingDataDOS);


        // Run the test
        seriesBrandMappingDataDOServiceImplUnderTest.initLocalCache();
        Map<String, String> map = Maps.newHashMap();
        SeriesMappingVO seriesMappingVO = new SeriesMappingVO();
        seriesMappingVO.setSeriesCode("seriesCode");
        seriesMappingVO.setSeriesName("seriesName");
        seriesMappingVO.setBrandNameView("brandNameView");
        map.put("seriesCode", JSON.toJSONString(seriesMappingVO));

        // Verify the results
        verify(mockRedisService).setCacheMap("global:series:mapping", map);
    }

    @Test
    public void testQueryPageList() {
        // Setup
        final SeriesMappingQueryPageDTO pageDTO = new SeriesMappingQueryPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setSeriesCode("seriesCode");
        pageDTO.setDpSeriesName("dpSeriesName");

        final PageResult<SeriesBrandMappingDataPageVO> expectedResult = new PageResult<>(
                List.of(SeriesBrandMappingDataPageVO.builder()
                        .seriesCode("seriesCode")
                        .seriesName("seriesName")
                        .dpSeriesName("dpSeriesName")
                        .brandNameView("brandNameView")
                        .build()), 0L);
        Page<SeriesBrandMappingDataDO> page = new Page<>();
        page.setRecords(List.of(SeriesBrandMappingDataDO.builder()
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .dpSeriesName("dpSeriesName")
                .brandNameView("brandNameView").build()));
        when(mockSeriesBrandMappingDataDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // Run the test
        final PageResult<SeriesBrandMappingDataPageVO> result = seriesBrandMappingDataDOServiceImplUnderTest.queryPageList(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testEditSeriesBrandMappingData() {
        // Setup
        final SeriesMappingDataUpdateDTO updateDTO = new SeriesMappingDataUpdateDTO();
        updateDTO.setId(0L);
        updateDTO.setSeriesName("seriesName");
        updateDTO.setBrandNameView("brandNameView");
        updateDTO.setRevision(0);

        // Configure SeriesBrandMappingDataDOMapper.selectById(...).
        SeriesBrandMappingDataDO seriesBrandMappingDataDO = SeriesBrandMappingDataDO.builder()
                .id(0L)
                .seriesCode("seriesCode")
                .dpSeriesName("dpSeriesName")
                .seriesName("seriesName")
                .brandNameView("brandNameView")
                .build();
        seriesBrandMappingDataDO.setRevision(0);
        when(mockSeriesBrandMappingDataDOMapper.selectById(0L)).thenReturn(seriesBrandMappingDataDO);

        // Run the test
        final Boolean result = seriesBrandMappingDataDOServiceImplUnderTest.editSeriesBrandMappingData(updateDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelete() {
        // Setup
        // Configure SeriesBrandMappingDataDOMapper.selectById(...).
        SeriesBrandMappingDataDO seriesBrandMappingDataDO = SeriesBrandMappingDataDO.builder()
                .id(0L)
                .seriesCode("seriesCode")
                .dpSeriesName("dpSeriesName")
                .seriesName("seriesName")
                .brandNameView("brandNameView")
                .build();
        seriesBrandMappingDataDO.setRevision(0);
        when(mockSeriesBrandMappingDataDOMapper.selectById(0L)).thenReturn(seriesBrandMappingDataDO);

        // Run the test
        final Boolean result = seriesBrandMappingDataDOServiceImplUnderTest.delete(0L, 0);

        // Verify the results
        assertFalse(result);
    }


    @Test
    public void testCreateSeriesBrandMappingData() {
        // Setup
        final UserDPResultVO dpResultVO = new UserDPResultVO();
        dpResultVO.setVin("vin");
        dpResultVO.setBrandCode("brandCode");
        dpResultVO.setSeriesCode("seriesCode");
        dpResultVO.setSeriesName("dpSeriesName");
        dpResultVO.setCarSystemModel("PIVI");

        // Run the test
        seriesBrandMappingDataDOServiceImplUnderTest.createSeriesBrandMappingData(dpResultVO);

        // Verify the results
        verify(mockSeriesBrandMappingDataDOMapper).insert(any(SeriesBrandMappingDataDO.class));
    }

    @Test
    public void testGetNoCompleteCount() {
        // Setup
        when(mockSeriesBrandMappingDataDOMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        // Run the test
        final Long result = seriesBrandMappingDataDOServiceImplUnderTest.getNoCompleteCount();

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }
}
