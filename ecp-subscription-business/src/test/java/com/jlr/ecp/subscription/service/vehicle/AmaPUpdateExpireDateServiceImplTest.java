package com.jlr.ecp.subscription.service.vehicle;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPExpireDateRequestDTO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.temp.AmaPTempDO;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.mysql.temp.AmaPTempDOMapper;
import com.jlr.ecp.subscription.model.dto.AmaPCarData;
import com.jlr.ecp.subscription.model.dto.AmaPCarInfoResponse;
import com.jlr.ecp.subscription.model.dto.AmaPPermissionInfo;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AmaPUpdateExpireDateServiceImplTest {

    @Mock
    private AmaPTempDOMapper mockAmaPTempDOMapper;
    @Mock
    private PIVIAmaPService mockPiviAmaPService;
    @Mock
    private AmaPProperties mockAmapProperties;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;
    @Mock
    private RedisTemplate<String, Integer> mockRedisTemplate;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;

    @InjectMocks
    private AmaPUpdateExpireDateServiceImpl amaPUpdateExpireDateServiceImplUnderTest;

    @Test
    public void testUpdateAmaPExpireDate() {
        // Setup
        final AmaPExpireDateRequestDTO amaPExpireDateDTO = AmaPExpireDateRequestDTO.builder()
                .pageNo(1)
                .totalPage(2)
                .build();
        Page<AmaPTempDO> pageParam = new Page<>();
        pageParam.setRecords(List.of(AmaPTempDO.builder()
                .vin("vin")
                .build()));
        when(mockAmaPTempDOMapper.selectPage(any(Page.class), eq(null)))
                .thenReturn(pageParam);
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
        when(mockAmapProperties.getPid()).thenReturn("pid");

        // Configure PIVIAmaPService.getCarAmaPInfoBatch(...).
        final AmaPCarInfoResponse amaPCarInfoResponse = new AmaPCarInfoResponse();
        amaPCarInfoResponse.setCode("1");
        amaPCarInfoResponse.setErrDetail("errDetail");
        amaPCarInfoResponse.setMessage("message");
        amaPCarInfoResponse.setResult(false);
        amaPCarInfoResponse.setData(AmaPCarData.builder()
                .permissions(List.of(AmaPPermissionInfo.builder()
                        .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .build()))
                .build());
        when(mockPiviAmaPService.getCarAmaPInfoBatch("pid", "vin")).thenReturn(amaPCarInfoResponse);

        // Configure PIVIPackageDOMapper.selectList(...).
        final List<PIVIPackageDO> piviPackageDOS = List.of(PIVIPackageDO.builder()
                .vin("vin")
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockPiviPackageDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(piviPackageDOS);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(subscriptionServiceDOS);

        // 配置 RedisTemplate
        ValueOperations<String, Integer> valueOperations = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);

        // Run the test
        amaPUpdateExpireDateServiceImplUnderTest.updateAmaPExpireDate(amaPExpireDateDTO);

        // Verify the results
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
        verify(mockPiviPackageDOMapper).updateBatch(anyList());
    }
}
