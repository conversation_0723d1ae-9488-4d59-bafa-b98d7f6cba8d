package com.jlr.ecp.subscription.service.appd.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchRenewalPageDTO;
import com.jlr.ecp.subscription.controller.admin.dto.appd.AppDUcBatchSendDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalPageVO;
import com.jlr.ecp.subscription.controller.admin.vo.appd.AppDUcBatchRenewalUploadVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewBatchRecords;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewBatchRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.excel.pojo.appduc.AppDCuBatchRenewalExcel;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionData;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.properties.AppDUcProperties;
import com.jlr.ecp.subscription.service.appd.AppDCuSingleRenewalService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AppDCuBatchRenewalServiceImplTest {

    @Mock
    private AppDUcProperties mockAppDUcProperties;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private FileService mockFileService;
    @Mock
    private PIVIAppDService mockPiviAppDService;
    @Mock
    private PIVIUnicomService mockPiviUnicomService;
    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;
    @Mock
    private AppDCuRenewBatchRecordsMapper mockAppDCuRenewBatchRecordsMapper;
    @Mock
    private ApplicationContext mockApplicationContext;
    @Mock
    private ThreadPoolTaskExecutor mockSubscribeAsyncThreadPool;
    @Mock
    private AppDCuSingleRenewalService mockAppDCuSingleRenewalService;
    @Mock
    private Redisson mockRedisson;
    @Mock
    private OrderCheckService mockOrderCheckService;

    @InjectMocks
    private AppDCuBatchRenewalServiceImpl appDCuBatchRenewalServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Before
    public void setUp() throws InterruptedException {
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
    }

    @Test
    public void testGetAppDUcTemplateUrl() {
        // Setup
        when(mockAppDUcProperties.getRenewalExcelUrl()).thenReturn("renewalExcelUrl");

        // Run the test
        final String result = appDCuBatchRenewalServiceImplUnderTest.getAppDUcTemplateUrl();

        // Verify the results
        assertEquals("renewalExcelUrl", result);
    }

    @Test
    public void testQueryAppDUcBatchRenewalPageList() {
        // Setup
        final AppDUcBatchRenewalPageDTO pageDTO = new AppDUcBatchRenewalPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("operateTimeSort");

        final PageResult<AppDUcBatchRenewalPageVO> expectedResult = new PageResult<>(
                List.of(AppDUcBatchRenewalPageVO.builder()
                        .operateTime("")
                        .operator("operator1")
                        .checkResultDesc("校验通过")
                        .checkResultStatus(1)
                        .operateStatus(1)
                        .batchNo(1L)
                        .errorDetailPath("path1")
                        .build()), 20L);
        when(mockAppDCuRenewBatchRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(new Page<>(1L, 10L, 20L, true)
                        .setRecords(List.of(AppDCuRenewBatchRecords.builder()
                                .id(1L)
                                .batchNo(1L)
                                .uploadFile("file1")
                                .verifyResult(1)
                                .dealStatus(1)
                                .verifyResultFile("path1")
                                .operator("operator1")
                                .build())));


        // Run the test
        final PageResult<AppDUcBatchRenewalPageVO> result = appDCuBatchRenewalServiceImplUnderTest.queryAppDUcBatchRenewalPageList(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUploadAppDUcExcelRenewal() {
        // Setup
        // 创建一个模拟的 Excel 文件内容
        byte[] excelContent = createExcelContent();
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", // 文件字段名
                "test.xlsx", // 文件名
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // 文件类型
                excelContent // 文件内容
        );
        final CommonResult<AppDUcBatchRenewalUploadVO> expectedResult = CommonResult.success(
                AppDUcBatchRenewalUploadVO.builder()
                        .type("success")
                        .msg("请关注续费文件校验结果")
                        .build());
        when(mockSnowflake.nextId()).thenReturn(0L);

        // Run the test
        final CommonResult<AppDUcBatchRenewalUploadVO> result = appDCuBatchRenewalServiceImplUnderTest.uploadAppDUcExcelRenewal(
                mockFile);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAppDCuRenewBatchRecordsMapper).insert(AppDCuRenewBatchRecords.builder()
                .batchNo(0L)
                .verifyResult(0)
                .dealStatus(0)
                .operator("admin")
                .build());
    }

    private static byte[] createExcelContent() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Cell vinHeader = headerRow.createCell(0);
        vinHeader.setCellValue("VIN");
        Cell appDHeader = headerRow.createCell(1);
        appDHeader.setCellValue("信息娱乐服务续期日期(yyyy/mm/dd)");
        Cell cuHeader = headerRow.createCell(2);
        cuHeader.setCellValue("网络流量续期日期(yyyy/mm/dd)");

        // 填充示例数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("HGCM82633A12"); // 示例 VIN
        dataRow.createCell(1).setCellValue("2020/01/01");
        dataRow.createCell(2).setCellValue("2020/01/01");

        // 将工作簿写入字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0]; // 返回空字节数组以防出错
        }
    }

    @Test
    public void testBatchSendAppDCuRenewal() throws Exception {
        // Setup
        final AppDUcBatchSendDTO appDUcBatchSendDTO = new AppDUcBatchSendDTO();
        appDUcBatchSendDTO.setBatchNo("2");

        final CommonResult<String> expectedResult = CommonResult.success("请求已经发送，请前往续费记录查看结果");

        // 捕获传入 selectList 的参数
        ArgumentCaptor<LambdaQueryWrapper<AppDCuRenewBatchRecords>> queryWrapperCaptor = ArgumentCaptor.forClass(LambdaQueryWrapper.class);

        AtomicInteger count = new AtomicInteger(0);
        // Configure AppDCuRenewBatchRecordsMapper.selectList(...).
        // 模拟 queryAppDUcBatchByDealStatus 返回空列表
        when(mockAppDCuRenewBatchRecordsMapper.selectList(queryWrapperCaptor.capture())).thenAnswer(invocation -> {
            int increment = count.get();
            if (increment == 0) {
                count.getAndIncrement();
                return Collections.emptyList();
            } else  {
                return List.of(AppDCuRenewBatchRecords.builder()
                        .id(0L)
                        .batchNo(0L)
                        .uploadFile("uploadFile")
                        .verifyResult(0)
                        .dealStatus(0)
                        .verifyResultFile("errorDetailPath")
                        .operator("operator")
                        .build());
            }
        });

        // Configure ApplicationContext.getBean(...).
        final AppDCuBatchRenewalServiceImpl appDCuBatchRenewalService = new AppDCuBatchRenewalServiceImpl();
        when(mockApplicationContext.getBean(AppDCuBatchRenewalServiceImpl.class)).thenReturn(appDCuBatchRenewalService);

        when(mockFileService.getFileContentByAllPath("AWS_S3_FILE", "uploadFile")).thenReturn(createExcelContent());

        // Run the test
        final CommonResult<String> result = appDCuBatchRenewalServiceImplUnderTest.batchSendAppDCuRenewal(
                appDUcBatchSendDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchSendAppDCuRenewal_AppDCuRenewBatchRecordsMapperSelectListReturnsNoItems() {
        // Setup
        final AppDUcBatchSendDTO appDUcBatchSendDTO = new AppDUcBatchSendDTO();
        appDUcBatchSendDTO.setBatchNo("2");

        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.AMAP_EXCEL_IS_EMPTY);
        when(mockAppDCuRenewBatchRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        final CommonResult<String> result = appDCuBatchRenewalServiceImplUnderTest.batchSendAppDCuRenewal(
                appDUcBatchSendDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAppDBatchSendRenewalAndUpdate() {
        // Setup
        final List<AppDCuRenewRecords> appdList = List.of(AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("errorDesc")
                .build());

        // Configure AppDCuRenewRecordsMapper.selectList(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords = List.of(AppDCuRenewRecords.builder()
                .id(3L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("errorDesc")
                .build(),
                AppDCuRenewRecords.builder()
                        .id(4L)
                        .renewNo(0L)
                        .carVin("carVin2")
                        .renewServiceType(0)
                        .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .cusOrderId("cusOrderId")
                        .operator("operator")
                        .dataSource(0)
                        .renewStatus(0)
                        .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .orderResultCode("orderResultCode")
                        .errorDesc("errorDesc")
                        .build());
        when(mockAppDCuRenewRecordsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(appDCuRenewRecords);

        // Configure AppDCuRenewRecordsMapper.getByVinListAndStatus(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords1 = List.of(AppDCuRenewRecords.builder()
                .id(1L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(4)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("errorDesc")
                .build());

        when(mockAppDCuRenewRecordsMapper.getByVinListAndStatus(List.of("carVin2", "carVin"), 4, 0L, 1))
                .thenReturn(appDCuRenewRecords1);

        when(mockOrderCheckService.checkOrderInTransitByVinList(List.of("carVin2"), ServiceTypeEnum.PIVI))
                .thenReturn(CommonResult.success(List.of("carVin")));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Configure PIVIAppDService.appDManualRenewal(...).
        final CommonResult<VcsOrderFufilmentCall> result1 = CommonResult.error(500, "");
        when(mockPiviAppDService.appDManualRenewal("carVin2", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(result1);

        // Run the test
        appDCuBatchRenewalServiceImplUnderTest.appDBatchSendRenewalAndUpdate(appdList, 0);

        // Verify the results
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
    }

    @Test
    public void testUnicomBatchSendRenewalAndUpdate() {
        // Setup
        final List<AppDCuRenewRecords> unicomList = List.of(AppDCuRenewRecords.builder()
                .id(0L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("errorDesc")
                .build());

        // Configure AppDCuRenewRecordsMapper.selectList(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords = List.of(AppDCuRenewRecords.builder()
                .id(3L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(0)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("errorDesc")
                .build(),
                AppDCuRenewRecords.builder()
                        .id(4L)
                        .renewNo(0L)
                        .carVin("carVin2")
                        .renewServiceType(0)
                        .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .cusOrderId("cusOrderId")
                        .operator("operator")
                        .dataSource(0)
                        .renewStatus(0)
                        .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .orderResultCode("orderResultCode")
                        .errorDesc("errorDesc")
                        .build());
        when(mockAppDCuRenewRecordsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(appDCuRenewRecords);

        // Configure AppDCuRenewRecordsMapper.getByVinListAndStatus(...).
        final List<AppDCuRenewRecords> appDCuRenewRecords1 = List.of(AppDCuRenewRecords.builder()
                .id(1L)
                .renewNo(0L)
                .carVin("carVin")
                .renewServiceType(0)
                .renewDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .cusOrderId("cusOrderId")
                .operator("operator")
                .dataSource(0)
                .renewStatus(4)
                .renewBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderResultCode("orderResultCode")
                .errorDesc("errorDesc")
                .build());
        when(mockAppDCuRenewRecordsMapper.getByVinListAndStatus(List.of("carVin2", "carVin"), 4, 0L, 2))
                .thenReturn(appDCuRenewRecords1);

        when(mockOrderCheckService.checkOrderInTransitByVinList(List.of("carVin2"), ServiceTypeEnum.PIVI))
                .thenReturn(CommonResult.success(List.of("carVin")));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Configure PIVIUnicomService.unicomManualRenewalJob(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("responseCode");
        unicomRespVO.setResponseDesc("responseDesc");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setBookStatus("bookStatus");
        unicomRespData.setProductBookInfoList(List.of(productBookInfo));
        unicomRespVO.setUnicomRespData(unicomRespData);
        final CommonResult<UnicomRespVO> unicomRespVOCommonResult = CommonResult.success(unicomRespVO);
        when(mockPiviUnicomService.unicomManualRenewalJob("carVin2", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "cusOrderId")).thenReturn(unicomRespVOCommonResult);

        // Run the test
        appDCuBatchRenewalServiceImplUnderTest.unicomBatchSendRenewalAndUpdate(unicomList, 0);

        // Verify the results
        verify(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));
    }

    @Test
    public void testBatchInsertAppDUcRenewalRecord() {
        // Setup
        final List<AppDCuBatchRenewalExcel> batchRenewalExcelList = List.of(AppDCuBatchRenewalExcel.builder()
                .carVin("carVin")
                .appDRenewalDate("appDRenewalDate")
                .unicomRenewalDate("unicomRenewalDate")
                .build());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockSubscribeAsyncThreadPool).execute(any(Runnable.class));

        // Configure PIVIAppDService.getVinSubscriptions(...).
        final AppDSubscriptionResp appDSubscriptionResp = new AppDSubscriptionResp();
        final AppDSubscriptionData appDSubscriptionData = new AppDSubscriptionData();
        appDSubscriptionData.setJlrSubscriptionId(0L);
        appDSubscriptionData.setSubscriptionId("subscriptionId");
        appDSubscriptionData.setSubscriberUniqueId("subscriberUniqueId");
        appDSubscriptionData.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionResp.setResult(List.of(appDSubscriptionData));
        when(mockPiviAppDService.getVinSubscriptions("carVin")).thenReturn(appDSubscriptionResp);

        // Configure PIVIUnicomService.getSimCarInfoByCarVin(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("responseCode");
        unicomRespVO.setResponseDesc("responseDesc");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setBookStatus("bookStatus");
        unicomRespData.setProductBookInfoList(List.of(productBookInfo));
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUnicomService.getSimCarInfoByCarVin("carVin")).thenReturn(unicomRespVO);

        when(mockSnowflake.nextIdStr()).thenReturn("cusOrderId");

        // Run the test
        appDCuBatchRenewalServiceImplUnderTest.batchInsertAppDUcRenewalRecord(batchRenewalExcelList, 0L, "operator");

        // Verify the results
        verify(mockSubscribeAsyncThreadPool, times(2)).execute(any(Runnable.class));
        verify(mockAppDCuRenewRecordsMapper, times(2)).insertBatch(anyList());
    }

    @Test
    public void testDoReadAppDUcRenewalExcel() {
        // Setup
        final List<List<AppDCuBatchRenewalExcel>> expectedResult = List.of(List.of(AppDCuBatchRenewalExcel.builder()
                .carVin("HGCM82633A12")
                .appDRenewalDate("2020/01/01")
                .unicomRenewalDate("2020/01/01")
                .build()));

        // Run the test
        final List<List<AppDCuBatchRenewalExcel>> result = appDCuBatchRenewalServiceImplUnderTest.doReadAppDUcRenewalExcel(
                createExcelContent());

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
