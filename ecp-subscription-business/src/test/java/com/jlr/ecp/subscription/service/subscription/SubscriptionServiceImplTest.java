package com.jlr.ecp.subscription.service.subscription;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchLocalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.dto.Vehicle5000InfoDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespData;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionData;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.impl.PIVIUnicomServiceImpl;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.env.MockEnvironment;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SubscriptionServiceImplTest {

    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;
    @Mock
    private PIVIPackageDOMapper mockPiviPackageDOMapper;
    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleDOMapper;
    @Mock
    private PIVIUnicomServiceImpl mockPiviUniconService;
    @Mock
    private PIVIAppDService mockAppDService;
    @Mock
    private VehicleDmsDOMapper mockVehicleDmsDOMapper;
    @Mock
    private PIPLDataUtil mockPiplDataUtil;
    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;

    @InjectMocks
    private SubscriptionServiceImpl subscriptionServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(subscriptionServiceImplUnderTest, "environment", new MockEnvironment());
        subscriptionServiceImplUnderTest.subscriptionServiceMapper = mockSubscriptionServiceMapper;
        subscriptionServiceImplUnderTest.incontrolVehicleDOMapper = mockIncontrolVehicleDOMapper;
    }

    @Test
    public void testGetExpireDateByVin() {
        // Setup
        final SubscriptionSearchResultDTO expectedResult = new SubscriptionSearchResultDTO();
        expectedResult.setCarSystemModel("carSystemModel");
        expectedResult.setServiceList(List.of(ServiceExpireInfoVO.builder()
                        .serviceName("InControl远程车控服务")
                        .build(),
                ServiceExpireInfoVO.builder()
                        .serviceName("车辆发票日期")
                        .build()));
        final Vehicle5000InfoDTO vehicle5000Info = new Vehicle5000InfoDTO();
        vehicle5000Info.setVin("carVin");
        vehicle5000Info.setBrandName("brandName");
        vehicle5000Info.setConfigName("configName");
        vehicle5000Info.setModelYear("modelYear");
        vehicle5000Info.setConfigCode("configCode");
        vehicle5000Info.setSeriesCode("seriesCode");
        vehicle5000Info.setBrandCode("brandCode");
        vehicle5000Info.setSeriesName("seriesName");
        vehicle5000Info.setCarSystemModel("carSystemModel");
        vehicle5000Info.setHasInfoEntertain("Y");
        expectedResult.setVehicle5000Info(vehicle5000Info);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(subscriptionServiceDOS);

        // Configure PIVIPackageDOMapper.selectOne(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .iccid("iccid")
                .vin("carVin")
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockPiviPackageDOMapper.selectOne(any(SFunction.class), eq("carVin"))).thenReturn(piviPackageDO);

        // Configure IncontrolVehicleDOMapper.selectOne(...).
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(0L)
                .carVin("carVin")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("carSystemModel")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDO);

        // Run the test
        final SubscriptionSearchResultDTO result = subscriptionServiceImplUnderTest.getExpireDateByVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetExpireDateByVin_SubscriptionServiceMapperReturnsNoItems() {
        // Setup
        final SubscriptionSearchResultDTO expectedResult = new SubscriptionSearchResultDTO();
        expectedResult.setCarSystemModel("PIVI");
        expectedResult.setServiceList(List.of(ServiceExpireInfoVO.builder()
                        .serviceName("InControl远程车控服务")
                        .build(),
                ServiceExpireInfoVO.builder()
                        .serviceName("InControl在线服务")
                        .expireDate("2020-01-01")
                        .child(List.of(
                                ServiceExpireInfoVO.builder()
                                        .serviceName("信息娱乐服务")
                                        .build(),
                                ServiceExpireInfoVO.builder()
                                        .serviceName("实时交通信息")
                                        .expireDate("2020-01-01")
                                        .build(),
                                ServiceExpireInfoVO.builder()
                                        .serviceName("网络流量")
                                        .build())
                        ).build(),
                ServiceExpireInfoVO.builder()
                        .serviceName("车辆发票日期")
                        .build()));
        Vehicle5000InfoDTO infoDTO = new Vehicle5000InfoDTO();
        infoDTO.setVin("carVin");
        expectedResult.setVehicle5000Info(infoDTO);
        // Setup
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure PIVIPackageDOMapper.selectOne(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .iccid("iccid")
                .vin("carVin")
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockPiviPackageDOMapper.selectOne(any(SFunction.class), eq("carVin"))).thenReturn(piviPackageDO);

        // Configure PIVIAppDService.getVinSubscriptions(...).
        final AppDSubscriptionResp appDSubscriptionResp = new AppDSubscriptionResp();
        final AppDSubscriptionData appDSubscriptionData = new AppDSubscriptionData();
        appDSubscriptionData.setJlrSubscriptionId(0L);
        appDSubscriptionData.setSubscriptionId("subscriptionId");
        appDSubscriptionData.setExpiresDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appDSubscriptionResp.setResult(List.of(appDSubscriptionData));
        appDSubscriptionResp.setQueryResult("queryResult");
        when(mockAppDService.getVinSubscriptions("carVin")).thenReturn(appDSubscriptionResp);

        // Configure PIVIUnicomServiceImpl.getSimCarInfoByCarVin(...).
        final UnicomRespVO unicomRespVO = new UnicomRespVO();
        unicomRespVO.setResponseCode("responseCode");
        final UnicomRespData unicomRespData = new UnicomRespData();
        final ProductBookInfo productBookInfo = new ProductBookInfo();
        productBookInfo.setBookStatus("bookStatus");
        productBookInfo.setExpireTime("expireTime");
        unicomRespData.setProductBookInfoList(List.of(productBookInfo));
        unicomRespVO.setUnicomRespData(unicomRespData);
        when(mockPiviUniconService.getSimCarInfoByCarVin("carVin")).thenReturn(unicomRespVO);

        final VehicleDmsDO vehicleDmsDO = VehicleDmsDO.builder()
                .invoiceDate("2020/01/01 00:00:00")
                .build();
        when(mockVehicleDmsDOMapper.selectVehicleDmsDOByCarVin("carVin")).thenReturn(vehicleDmsDO);

        // Run the test
        final SubscriptionSearchResultDTO result = subscriptionServiceImplUnderTest.getExpireDateByVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetServicesByVin() {
        // Setup
        final List<SubscriptionServiceDO> expectedResult = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Run the test
        final List<SubscriptionServiceDO> result = subscriptionServiceImplUnderTest.getServicesByVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetRemoteExpireDateByVin() {
        // Setup
        final RemoteSearchResultDTO expectedResult = new RemoteSearchResultDTO();
        expectedResult.setCarVin("carVin");
        expectedResult.setExistInEcp(true);
        expectedResult.setPiviModel(true);
        expectedResult.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setServiceDOList(List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build()));

        // Configure IncontrolVehicleDOMapper.selectOne(...).
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(0L)
                .carVin("carVin")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("PIVI")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOneByCarVin(eq("carVin"))).thenReturn(incontrolVehicleDO);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Run the test
        final RemoteSearchResultDTO result = subscriptionServiceImplUnderTest.getRemoteExpireDateByVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetRemoteExpireDateByVin_SubscriptionServiceMapperReturnsNoItems() {
        // Setup
        final RemoteSearchResultDTO expectedResult = new RemoteSearchResultDTO();
        expectedResult.setCarVin("carVin");
        expectedResult.setExistInEcp(true);
        expectedResult.setPiviModel(false);

        // Configure IncontrolVehicleDOMapper.selectOne(...).
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(0L)
                .carVin("carVin")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("carSystemModel")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOneByCarVin(eq("carVin"))).thenReturn(incontrolVehicleDO);

        // Run the test
        final RemoteSearchResultDTO result = subscriptionServiceImplUnderTest.getRemoteExpireDateByVin("carVin");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetRemoteExpireDateByVinList() {
        // Setup
        final RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
        resultDTO.setCarVin("carVin");
        resultDTO.setIncontrolId("incontrolId");
        resultDTO.setExistInEcp(true);
        resultDTO.setPiviModel(false);
        resultDTO.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setServiceDOList(List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build()));
        final List<RemoteSearchResultDTO> expectedResult = List.of(resultDTO);

        // Configure IncontrolVehicleDOMapper.selectIncontrolVehicleByCarVinList(...).
        final IncontrolVehicleByCarDTO incontrolVehicleByCarDTO = new IncontrolVehicleByCarDTO();
        incontrolVehicleByCarDTO.setCarVin("carVin");
        incontrolVehicleByCarDTO.setIncontrolId("incontrolId");
        incontrolVehicleByCarDTO.setConsumerCode("consumerCode");
        incontrolVehicleByCarDTO.setSeriesCode("seriesCode");
        incontrolVehicleByCarDTO.setCarSystemModel("carSystemModel");
        final List<IncontrolVehicleByCarDTO> incontrolVehicleByCarDTOS = List.of(incontrolVehicleByCarDTO);
        when(mockIncontrolVehicleDOMapper.selectIncontrolVehicleByCarVinList(List.of("carVin")))
                .thenReturn(incontrolVehicleByCarDTOS);

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Run the test
        final List<RemoteSearchResultDTO> result = subscriptionServiceImplUnderTest.getRemoteExpireDateByVinList(
                List.of("carVin"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetRemoteExpireDateByVinList_IncontrolVehicleDOMapperReturnsNoItems() {
        // Setup
        final RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
        resultDTO.setCarVin("carVin");
        resultDTO.setExistInEcp(false);
        resultDTO.setPiviModel(false);
        final List<RemoteSearchResultDTO> expectedResult = List.of(resultDTO);

        when(mockIncontrolVehicleDOMapper.selectIncontrolVehicleByCarVinList(List.of("carVin")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<RemoteSearchResultDTO> result = subscriptionServiceImplUnderTest.getRemoteExpireDateByVinList(
                List.of("carVin"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateRemoteExpireDate() {
        // Setup
        final RemoteRenewDetailRecords renewRecords = RemoteRenewDetailRecords.builder()
                .carVin("carVin")
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        subscriptionServiceImplUnderTest.updateRemoteExpireDate(renewRecords);

        // Verify the results
        verify(mockSubscriptionServiceMapper).update(any(SubscriptionServiceDO.class), any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testGetExpireDateByVinLocal() {
        // Setup
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(0L)
                .carVin("carVin")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("PIVI")
                .build();
        final SubscriptionSearchLocalDTO expectedResult = new SubscriptionSearchLocalDTO();
        expectedResult.setCarSystemModel("PIVI");
        expectedResult.setServiceList(List.of(ServiceExpireInfoVO.builder()
                        .serviceName("InControl远程车控服务")
                        .expireDate("2020-01-01")
                        .build(),
                ServiceExpireInfoVO.builder()
                        .serviceName("InControl在线服务")
                        .expireDate("2020-01-01")
                        .child(List.of(
                                ServiceExpireInfoVO.builder()
                                        .serviceName("信息娱乐服务")
                                        .expireDate("2020-01-01")
                                        .build(),
                                ServiceExpireInfoVO.builder()
                                        .serviceName("实时交通信息")
                                        .expireDate("2020-01-01")
                                        .build(),
                                ServiceExpireInfoVO.builder()
                                        .serviceName("网络流量")
                                        .build())
                        ).build(),
                ServiceExpireInfoVO.builder()
                        .serviceName("车辆发票日期")
                        .expireDate("2020-01-01")
                        .build()));
        Vehicle5000InfoDTO infoDTO = new Vehicle5000InfoDTO();
        infoDTO.setVin("carVin");
        infoDTO.setBrandName("brandName");
        infoDTO.setConfigName("configName");
        infoDTO.setConfigCode("configCode");
        infoDTO.setModelYear("modelYear");
        infoDTO.setSeriesCode("seriesCode");
        infoDTO.setBrandCode("brandCode");
        infoDTO.setSeriesName("seriesName");
        infoDTO.setCarSystemModel("PIVI");
        infoDTO.setHasInfoEntertain("Y");
        infoDTO.setRealName("code");
        infoDTO.setIccid("iccid");
        infoDTO.setVinMix("carVin");
        expectedResult.setVehicle5000Info(infoDTO);

        // Configure SubscriptionServiceMapper.selectListByCarVin(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .servicePackage("REMOTE")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(1)
                .build(),
                SubscriptionServiceDO.builder()
                        .carVin("carVin")
                        .servicePackage("ONLINE-PACK")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .build());
        when(mockSubscriptionServiceMapper.selectListByCarVin("carVin")).thenReturn(subscriptionServiceDOS);

        // Configure PIVIPackageDOMapper.findICCIDByCarVin(...).
        final PIVIPackageDO piviPackageDO = PIVIPackageDO.builder()
                .jlrSubscriptionId(0L)
                .iccid("iccid")
                .vin("vin")
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockPiviPackageDOMapper.findICCIDByCarVin("carVin")).thenReturn(piviPackageDO);

        when(mockPiplDataUtil.getEncryptText("carVin")).thenReturn("carVin");

        // Configure PIVIUnicomServiceImpl.checkVinRNRInfo(...).
        final EsimInfoVO esimInfoVO = new EsimInfoVO();
        esimInfoVO.setVin("vin");
        esimInfoVO.setIccid("iccid");
        esimInfoVO.setRealnameFlag("code");
        esimInfoVO.setCardState("cardState");
        esimInfoVO.setCardStateTxt("cardStateTxt");
        when(mockPiviUniconService.checkVinRNRInfo("carVin")).thenReturn(esimInfoVO);

        final VehicleDmsDO vehicleDmsDO = VehicleDmsDO.builder()
                .invoiceDate("2020/01/01 00:00:00")
                .build();
        when(mockVehicleDmsDOMapper.selectVehicleDmsDOByCarVin("carVin")).thenReturn(vehicleDmsDO);

        // Run the test
        final SubscriptionSearchLocalDTO result = subscriptionServiceImplUnderTest.getExpireDateByVinLocal(
                incontrolVehicleDO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
