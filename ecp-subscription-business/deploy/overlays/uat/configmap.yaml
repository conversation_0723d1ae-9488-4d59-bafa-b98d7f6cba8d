apiVersion: v1
kind: ConfigMap
metadata:
  name: ecp-subscription-service-config
  labels:
    app: ecp-subscription-service-config
data:
  multipart_max_size: "1MB"
#  kafka_addr: b-1.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-2.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9092
#  kafka_v1_addr: b-1.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9094,b-2.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9094
#  redis_addr: redis-dev-ecp-1-001.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn
#  redis_addr_master: master.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn
#  redis_addr_slave: replica.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn
#  aurora_addr_write: rds-mysql-dev-ecp-1.cluster-cbv7bxi3fult.rds.cn-northwest-1.amazonaws.com.cn:3306
#  aurora_addr_reader: rds-mysql-dev-ecp-1.cluster-ro-cbv7bxi3fult.rds.cn-northwest-1.amazonaws.com.cn:3306
#  #TBD MODIFY
#  consumer_addr: http://crm-consumer-center-service:80
  s3_bucket_name_file: "s3-jlr-ecp-file-test"
  s3_bucket_name_image: "s3-jlr-ecp-image-test"
  s3_bucket_name_video: "s3-jlr-ecp-video-test"
  kafka_batch_consume_num: '2000'
 # V1_KAFKA_ENABLE: '0'
  time_query_range: '15'
  java_opts: "-Xms512m -Xmx3200m -XX:MaxGCPauseMillis=200"
  spring_documentation_enabled: "true"
