apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ecp-subscription-service
  name: ecp-subscription-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ecp-subscription-service
  template:
    metadata:
      labels:
        app: ecp-subscription-service
    spec:
      containers:
        - image: public.ecr.aws/nginx/nginx:latest
          #          imagePullPolicy: IfNotPresent
          imagePullPolicy: Always
          name: ecp-subscription-service
          env:
            - name: MULTIPART_MAX_SIZE
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: multipart_max_size
            - name: REDIS_ADDR_MASTER
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: redis_addr_master
            - name: REDIS_ADDR_SLAVE
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: redis_addr_slave
            - name: AURORA_ADDR_WRITE
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: aurora_addr_write
            - name: AURORA_ADDR_READER
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: aurora_addr_reader
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: db_user
            - name: DB_PASSWD
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: db_passwd
            - name: REDIS_PASSWD
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: redis_passwd
            - name: KAFKA_V1_ADDR
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: kafka_v1_addr
            - name: KAFKA_ADDR
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: kafka_addr


            #            - name: LOG_ENVIRONMENT
            #              valueFrom:
            #                configMapKeyRef:
            #                  name: ecp-subscription-service-config
            #                  key: log_environment
            #            - name: LOG_VERSION
            #              valueFrom:
            #                configMapKeyRef:
            #                  name: ecp-subscription-service-config
            #                  key: log_version
            - name: S3_BUCKET_NAME_FILE
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: s3_bucket_name_file
            - name: S3_BUCKET_NAME_IMAGE
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: s3_bucket_name_image
            - name: S3_BUCKET_NAME_VIDEO
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: s3_bucket_name_video
            - name: S3_BUCKET_NAME_VIDEO
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: s3_bucket_name_video

            - name: KAFKA_BATCH_CONSUME_NUM
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: kafka_batch_consume_num

            #            - name: SSL_KEY_STORE_LOCATION_V1
            #              valueFrom:
            #                configMapKeyRef:
            #                  name: ecp-subscription-service-config
            #                  key: ssl_key_store_location_v1
            - name: SPRING_DOCUMENTATION_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: ecp-subscription-service-config
                  key: spring_documentation_enabled
            - name: TRUST_STORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  #                  name: ecp-subscription-service-secret
                  name: ecp-subscription-service-externalsecret
                  key: trust_store_password
            - name: DP_APPID
              valueFrom:
                secretKeyRef:
                  #                  name: ecp-subscription-service-secret
                  name: ecp-subscription-service-externalsecret
                  key: dp_appid
            - name: DP_APPSECRET
              valueFrom:
                secretKeyRef:
                  #                  name: ecp-subscription-service-secret
                  name: ecp-subscription-service-externalsecret
                  key: dp_appsecret
            - name: FORGEROCK_PKCS8
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: forgerock_pkcs8
            - name: FORGEROCK_PUBLICKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: forgerock_publickey
            - name: RSA_2048_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: rsa_2048_private
            - name: RSA_2048_PUBLIC
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: rsa_2048_public
            - name: APIM_CLIENTID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: apim_clientid
            - name: APIM_APIKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: apim_apikey
            - name: LANDROVER_BACKEND_APPKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: landrover_backend_appkey
            - name: LANDROVER_BACKEND_APPSECRET
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: landrover_backend_appsecret
            - name: JAGUAR_APPID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_appid
            - name: JAGUAR_APPSECRET
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_appsecret
            - name: CUSC_JAGUAR_APPID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_jaguar_appid
            - name: CUSC_JAGUAR_APPSECRET
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_jaguar_appsecret
            - name: CUSC_JAGUAR_MERCHANTNO
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_jaguar_merchantno
            - name: CUSC_LANDROVER_APPID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_landrover_appid
            - name: CUSC_LANDROVER_APPSECRET
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_landrover_appsecret
            - name: CUSC_LANDROVER_MERCHANTNO
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_landrover_merchantno
            - name: CUSC_PAYMENT_BASEPATH
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_payment_basepath
            - name: CUSC_INVOICE_BASEPATH
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: cusc_invoice_basepath
            - name: FORGEROCK_TOKEN_URL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: forgerock_token_url
            - name: FORGEROCK_TOKEN_SCOPE
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: forgerock_token_scope
            - name: FORGEROCK_REQUESTURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: forgerock_requesturl
            - name: APIM_SMSURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: apim_smsurl
            - name: LANDROVER_BACKEND_URLLINK
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: landrover_backend_urllink
            - name: JAGUAR_BACKEND_REQUESTURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_backend_requesturl
            - name: JAGUAR_CDT_BASEURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_cdt_baseurl
            - name: JAGUAR_CDT_JLRIDURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_cdt_jlridurl
            - name: JAGUAR_CDT_JLRIDSEARCH
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_cdt_jlridsearch
            - name: JAGUAR_CDT_APPKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_cdt_appkey
            - name: JAGUAR_CDT_SECRETKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_cdt_secretkey
            - name: JAGUAR_ECP_APIM_BASEURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_ecp_apim_baseurl
            - name: JAGUAR_ECP_APIM_REGISTERURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_ecp_apim_registerurl
            - name: JAGUAR_ECP_APIM_LOGINURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_ecp_apim_loginurl
            - name: JAGUAR_PKCS8
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_pkcs8
            - name: JAGUAR_PUBLICKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: jaguar_publickey
            - name: TEMPLATE_REMOTEPKG
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: template_remotepkg
            - name: TSDP_IFASTOKEN
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: tsdp_ifastoken
            - name: TSDP_IFECOMTOKEN
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: tsdp_ifecomtoken
            - name: TSDP_ICRLOGINURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: tsdp_icrloginurl
            - name: TSDP_ICRSUBSCRIPTIONURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: tsdp_icrsubscriptionurl
            - name: TSDP_SUBSCRIPTIONEXPIREURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: tsdp_subscriptionexpireurl
            - name: TSDP_SUBSCRIPTIONRENEWURL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: tsdp_subscriptionrenewurl
            - name: UNICOM_APPKEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: unicom_appkey
            - name: RDS_TRUST_STORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: rds_trust_store_password
            - name: APPD_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: appd_client_secret
            - name: AMAP_URL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_url
            - name: AMAP_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_access_key
            - name: AMAP_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_secret_key
            - name: AMAP_ONE_YEAR_CID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_one_year_cid
            - name: AMAP_THREE_YEAR_CID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_three_year_cid
            - name: AMAP_PID
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_pid
            - name: AMAP_RENEWAL_EXCEL_URL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_renewal_excel_url
            - name: AMAP_QUERY_EXCEL_URL
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: amap_query_excel_url
            - name: AWS_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: aws_access_key
            - name: AWS_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: aws_secret_key
            - name: AWS_ROLE_ARN
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: aws_role_arn
            - name: AWS_TOPIC_ARN
              valueFrom:
                secretKeyRef:
                  name: ecp-subscription-service-externalsecret
                  key: aws_topic_arn

          ports:
            - containerPort: 8009
              name: http
          volumeMounts:
            - mountPath: /home/<USER>/ecp/client.truststore.jks
              name: fdp-secret
              subPath: client.truststore.jks
            - mountPath: /home/<USER>/ecp/rds.truststore.jks
              name: rds-secret
              subPath: rds.truststore.jks

      #      imagePullSecrets:
      #        - name: docker-secret
      securityContext:
        fsGroup: 1000
      volumes:
        - name: fdp-secret
          secret:
            secretName: ecp-subscription-service-secret
            items:
              - key: client.truststore.jks
                path: client.truststore.jks
              # - key: consumer.hub.consumer.hub.audittest.kaas.3stripes.net.jks
              #   path: consumer.hub.consumer.hub.audittest.kaas.3stripes.net.jks
        - name: rds-secret
          secret:
            secretName: ecp-subscription-service-secret
            items:
              - key: rds.truststore.jks
                path: rds.truststore.jks