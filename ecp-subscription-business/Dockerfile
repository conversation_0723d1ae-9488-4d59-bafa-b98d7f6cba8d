ARG AWS_ACCOUNT=************
ARG BASE_REPO=${AWS_ACCOUNT}.dkr.ecr.cn-northwest-1.amazonaws.com.cn
#ARG BASE_REPO=************.dkr.ecr.cn-northwest-1.amazonaws.com.cn
FROM ${BASE_REPO}/arthas:3.5.1-no-jdk as arthas3
# FROM ${BASE_REPO}/openjdk:11.0.10-jdk-buster
FROM public.ecr.aws/amazoncorretto/amazoncorretto:11.0.24-al2

ARG BASE_REPO
ARG AWS_ACCOUNT
ARG BASE_ENV
ARG BASE_JDK
RUN echo "BASE_REPO IS ${BASE_REPO}"

RUN echo "BASE_ENV IS ${BASE_ENV}, BASE_JDK IS ${BASE_JDK} ,AWS_ACCOUNT IS ${AWS_ACCOUNT}"
COPY --from=arthas3 /opt/arthas /opt/arthas

RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone
# RUN bash -c 'touch /opt/loyalty-api-server.jar'

# 默认jar包的名字 注意分隔符为 :- 这里是由jenkinsfile中build dockerfile时传递过来的
# ARG jar_name={jar_name:-sample-teszt-0.0.1-SNAPSHOT.jar}: 这个指令定义了一个构建参数（Build Argument）jar_name，它可以在构建过程中被传递进来。如果未传递该参数，则默认为sample-teszt-0.0.1-SNAPSHOT.jar。
#ARG jar_name=ecp-system-business.jar

# RUN 用于容器内部执行命令
RUN mkdir -p /opt/app
WORKDIR /opt/app

#将项目放到/usr/local/project下
COPY ./target/app.jar /opt/app/app.jar


#ADD ./member-boot.jar /opt/member-boot.jar
#RUN bash -c 'touch /opt/member-boot.jar'

RUN mkdir -p /data
RUN yum install shadow-utils -y
RUN useradd -r -m -u 1001 appuser
RUN chmod -R 777 /data
RUN chmod -R 777 /opt
RUN chmod -R 777 /root


#添加OpenTelemetry Agent
# ADD https://ecp-static-dev.jaguarlandrover.cn/file/aws-opentelemetry-agent.jar /opt/aws-opentelemetry-agent.jar
# RUN chmod -R 777 /opt/aws-opentelemetry-agent.jar
#
#
#
# USER appuser
# # 设置环境变量，将Agent添加到JAVA_TOOL_OPTIONS
# ENV JAVA_TOOL_OPTIONS=-javaagent:/opt/aws-opentelemetry-agent.jar

ENTRYPOINT ["java","-jar","/opt/app/app.jar"]
