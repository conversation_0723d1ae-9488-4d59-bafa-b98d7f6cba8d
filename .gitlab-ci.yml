include:
  - project: 'digital-devsecops-china/devops/gitlab-ci-templates'
    ref: main
    file: 'pipeline/template/.cn-maven-container-template.yml'

stages:
  - validate
  - compile
  - inspect code
  - build
  - dev-deploy
  - test-deploy
  - uat-deploy
  - prod-deploy
  - approve-delivery
  - delivery

variables:
  SNYK_LOG_PATH: $CI_PROJECT_DIR
  SNYK_TOKEN: $SNYK_TOKEN
  SNYK_ORG: $SNYK_ORG

install dependency:
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull-push
  before_script:
    - echo $MAVEN_SETTINGS > $CI_PROJECT_DIR/settings.xml
    - if [ -d ".m2/repository" ]; then ls -al; else mkdir -p .m2/repository; fi
  script:
    - echo "安装依赖并缓存开始 ！！！"
    - cd $DOCKERFILE_DIR && mvn clean install -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -D skipTests -D dependency-check.skip=true -s $CI_PROJECT_DIR/settings.xml

code validate:
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  before_script:
    - echo $MAVEN_SETTINGS > $CI_PROJECT_DIR/settings.xml
  script:
    - echo "前后段代码校验开始 ！！！"
    - cd $DOCKERFILE_DIR && mvn clean validate -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -s $CI_PROJECT_DIR/settings.xml

code compile:
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  before_script:
    - echo $MAVEN_SETTINGS > $CI_PROJECT_DIR/settings.xml
    - ls -al .m2/repository
  script:
    - echo "代码编译开始 ！！！"
    - cd $DOCKERFILE_DIR && mvn clean package -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dmaven.test.skip=$SKIP_TEST -s $CI_PROJECT_DIR/settings.xml
  artifacts:
    paths:
      - $DOCKERFILE_DIR/target/

snyk:
  before_script:
    - echo $MAVEN_SETTINGS > $CI_PROJECT_DIR/settings.xml
  script:
    - docker-entrypoint.sh snyk monitor $SNYK_OPTS -- -s $CI_PROJECT_DIR/settings.xml || EXIT_CODE=$?
    - docker-entrypoint.sh snyk test --json-file-output=snyk-test-output.json $SNYK_OPTS -- -s $CI_PROJECT_DIR/settings.xml || EXIT_CODE=$?
    # we're doing this in `script` instead of `before_script` or `after_script`
    # because some users override `before_script` downstream and `after_script`
    # can't control the job status via exit code
    # use gitlab's collapsing format
    # https://docs.gitlab.com/ee/ci/jobs/index.html#pre-collapse-sections
    - echo -e "\e[0Ksection_start:`date +%s`:install-jq[collapsed=true]\r\e[0KInstalling jq"
    - "(apt-get update && apt-get install -y jq) || exit 101"
    - echo -e "\e[0Ksection_end:`date +%s`:install-jq\r\e[0K"
    - >-
      if [[ $EXIT_CODE -le 1 ]];then
        if [[ $SNYK_FAIL_THRESHOLD =~ critical|high|medium10 ]];then
          crits=$(jq -r '.. .vulnerabilities? // empty | .[] | select(.severity == "critical")' < snyk-test-output.json | jq -s length)
          if [[ $crits -ge 1 ]];then
            message+="Found ${crits} critical vulnerabilities, cannot be more than 0. "
            EXIT_CODE=102
          fi
        else
          message+="Invalid SNYK_FAIL_THRESHOLD: $SNYK_FAIL_THRESHOLD - valid options are critical, high or medium10."
          EXIT_CODE=101
        fi
        if [[ $SNYK_FAIL_THRESHOLD =~ high|medium10 ]];then
          highs=$(jq -r '.. .vulnerabilities? // empty | .[] | select(.severity == "high")' < snyk-test-output.json | jq -s length)
          if [[ $highs -ge 1 ]];then
            message+="Found ${highs} high vulnerabilities, cannot be more than 0. "
            EXIT_CODE=102
          fi
        fi
        if [[ $SNYK_FAIL_THRESHOLD =~ medium10 ]];then
          mediums=$(jq -r '.. .vulnerabilities? // empty | .[] | select(.severity == "medium")' < snyk-test-output.json | jq -s length)
          if [[ $mediums -ge 10 ]];then
            message+="Found ${mediums} medium vulnerabilities, cannot be more than 10."
            EXIT_CODE=102
          fi
        fi
      fi
    - echo "$message"
    - exit $EXIT_CODE


sonarqube:
  dependencies:
    - code compile
  script:
    - sonar-scanner -Dsonar.host.url=https://sonarqube.jaguarlandrover.cn/ -Dsonar.projectKey=$SERVICE_NAME -Dsonar.projectVersion=1.0 -Dsonar.sources=$DOCKERFILE_DIR/src/main/java -Dsonar.java.binaries=$DOCKERFILE_DIR/target/classes -Dsonar.language=java -Dsonar.sourceEncoding=UTF-8 -Dsonar.login=$SONAR_TOKEN  -Dsonar.coverage.exclusions=**/api/**/*,**/core/**/*,**/config/**/*,**/constant/**/*,**/controller/**/*,**/convert/**/*,**/dal/**/*,**/enums/**/*,**/handle/**/*,**/job/**/*,**/kafka/**/*,**/mq/**/*,**/util/**/*,**/dto/**/*,**/excel/**/*,**/file/**/*,**/req/**/*,**/resp/**/*,**/vo/**/* -Dsonar.coverage.jacoco.xmlReportPaths=$DOCKERFILE_DIR/target/site/jacoco/jacoco.xml

credential init:
  stage: build
  image:
    name: sirzeta/awscli-kubectl:latest
  script:
    - echo "认证初始化！！！"
    - export AWS_ACCESS_KEY_ID=$CHARGING_AWS_ACCESS_KEY_ID
    - export AWS_SECRET_ACCESS_KEY=$CHARGING_AWS_SECRET_ACCESS_KEY
    - export AWS_DEFAULT_REGION=$CHARGING_AWS_DEFAULT_REGION
    - sts_output=$(aws sts assume-role
      --role-arn arn:aws-cn:iam::${AWS_ACCOUNT}:role/${ROLE_NAME}
      --role-session-name TemporarySession
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text)
    - access_key_id=$(echo "$sts_output" | awk '{print $1}')
    - secret_access_key=$(echo "$sts_output" | awk '{print $2}')
    - session_token=$(echo "$sts_output" | awk '{print $3}')
    - echo "AWS_ACCESS_KEY_ID=$access_key_id" > ./aws_credentials.txt
    - echo "AWS_SECRET_ACCESS_KEY=$secret_access_key" >> ./aws_credentials.txt
    - echo "AWS_SESSION_TOKEN=$session_token" >> ./aws_credentials.txt
    - cat ./aws_credentials.txt
  artifacts:
    paths:
      - ./aws_credentials.txt
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
      variables:
        AWS_ACCOUNT: $DEV_AWS_ACCOUNT
        ROLE_NAME: $DEV_ROLE_NAME
    - if: $CI_COMMIT_REF_NAME == "test"
      variables:
        AWS_ACCOUNT: $DEV_AWS_ACCOUNT
        ROLE_NAME: $DEV_ROLE_NAME
    - if: $CI_COMMIT_REF_NAME == "uat"
      variables:
        AWS_ACCOUNT: $UAT_AWS_ACCOUNT
        ROLE_NAME: $UAT_ROLE_NAME
    - if: $CI_COMMIT_REF_NAME == "main"
      variables:
        AWS_ACCOUNT: $PROD_AWS_ACCOUNT
        ROLE_NAME: $PROD_ROLE_NAME
docker build:
  stage: build
  needs:
    - credential init
    - code compile
  dependencies:
    - credential init
    - code compile
  before_script:
    - cat ./aws_credentials.txt
    - export $(cat ./aws_credentials.txt)
    - mkdir -p /kaniko/.docker
    - echo "{\"credsStore\":\"ecr-login\"}" > /kaniko/.docker/config.json
  script:
    - /kaniko/executor
      --context $DOCKERFILE_DIR
      --dockerfile $DOCKERFILE_DIR/Dockerfile
      --build-arg AWS_ACCOUNT=$AWS_ACCOUNT
      --build-arg BASE_JDK=abc
      --build-arg BASE_ENV=def
      --destination "${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com.cn/$SERVICE_NAME:latest"
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
      variables:
        AWS_ACCOUNT: $DEV_AWS_ACCOUNT
    - if: $CI_COMMIT_REF_NAME == "test"
      variables:
        AWS_ACCOUNT: $DEV_AWS_ACCOUNT
    - if: $CI_COMMIT_REF_NAME == "uat"
      variables:
        AWS_ACCOUNT: $UAT_AWS_ACCOUNT
    - if: $CI_COMMIT_REF_NAME == "main"
      variables:
        AWS_ACCOUNT: $PROD_AWS_ACCOUNT

deploy to dev:
  stage: dev-deploy
  image:
    name: sirzeta/awscli-kubectl:latest
  before_script:
    - cat ./aws_credentials.txt
    - export $(cat ./aws_credentials.txt)
  script:
    - echo "发布到dev环境 ！！！"
    - aws eks update-kubeconfig --region ${AWS_REGION} --name ${EKS_CLUSTER_NAME}
    - kubectl apply -k $DOCKERFILE_DIR/deploy/overlays/dev/ -n $EKS_NAMESPACE
    - kubectl set image deployment/$SERVICE_NAME  $SERVICE_NAME=$AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com.cn/$SERVICE_NAME:$TAG_NAME -n $EKS_NAMESPACE
    - kubectl rollout restart deployment/$SERVICE_NAME -n $EKS_NAMESPACE
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
      variables:
        EKS_NAMESPACE: "default"
        EKS_CLUSTER_NAME: "eks-dev-ecp-1"
        AWS_ACCOUNT: $DEV_AWS_ACCOUNT

deploy to test:
  stage: test-deploy
  image:
    name: sirzeta/awscli-kubectl:latest
  before_script:
    - cat ./aws_credentials.txt
    - export $(cat ./aws_credentials.txt)
  script:
    - echo "发布到test环境 ！！！"
    - aws eks update-kubeconfig --region ${AWS_REGION} --name ${EKS_CLUSTER_NAME}
    - kubectl apply -k $DOCKERFILE_DIR/deploy/overlays/test/ -n $EKS_NAMESPACE
    - kubectl set image deployment/$SERVICE_NAME $SERVICE_NAME=$AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com.cn/$SERVICE_NAME:$TAG_NAME -n $EKS_NAMESPACE
    - kubectl rollout restart deployment/$SERVICE_NAME -n $EKS_NAMESPACE
  rules:
    - if: $CI_COMMIT_REF_NAME == "test"
      variables:
        EKS_NAMESPACE: "jlr-ecp"
        EKS_CLUSTER_NAME: "eks-test-ecp-1"
        AWS_ACCOUNT: $DEV_AWS_ACCOUNT

deploy to uat:
  stage: uat-deploy
  image:
    name: sirzeta/awscli-kubectl:latest
  before_script:
    - cat ./aws_credentials.txt
    - export $(cat ./aws_credentials.txt)
  script:
    - echo "发布到uat环境 ！！！"
    - aws eks update-kubeconfig --region ${AWS_REGION} --name ${EKS_CLUSTER_NAME}
    - kubectl apply -k $DOCKERFILE_DIR/deploy/overlays/uat/ -n $EKS_NAMESPACE
    - kubectl set image deployment/$SERVICE_NAME  $SERVICE_NAME=$AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com.cn/$SERVICE_NAME:$TAG_NAME -n $EKS_NAMESPACE
    - kubectl rollout restart deployment/$SERVICE_NAME -n $EKS_NAMESPACE
  rules:
    - if: $CI_COMMIT_REF_NAME == "uat"
      when: manual
      variables:
        EKS_NAMESPACE: "jlr-ecp"
        EKS_CLUSTER_NAME: "eks-uat-ecp-1"
        AWS_ACCOUNT: $UAT_AWS_ACCOUNT

deploy to prod:
  stage: prod-deploy
  image:
    name: sirzeta/awscli-kubectl:latest
  before_script:
    - cat ./aws_credentials.txt
    - export $(cat ./aws_credentials.txt)
  script:
    - echo "发布到prod环境 ！！！"
    - aws eks update-kubeconfig --region ${AWS_REGION} --name ${EKS_CLUSTER_NAME}
    - kubectl apply -k $DOCKERFILE_DIR/deploy/overlays/prod/ -n $EKS_NAMESPACE
    - kubectl set image deployment/$SERVICE_NAME  $SERVICE_NAME=$AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com.cn/$SERVICE_NAME:$TAG_NAME -n $EKS_NAMESPACE
    - kubectl rollout restart deployment/$SERVICE_NAME -n $EKS_NAMESPACE
  rules:
    - if: $CI_COMMIT_REF_NAME == "main"
      when: manual
      variables:
        EKS_NAMESPACE: "jlr-ecp"
        EKS_CLUSTER_NAME: "eks-prod-ecp2-1"
        AWS_ACCOUNT: $PROD_AWS_ACCOUNT

approve prod delivery:
  stage: approve-delivery
  needs:
    - deploy to prod

prod delivery:
  stage: delivery
  needs:
    - approve prod delivery